{"__meta": {"id": "01JZ4EWVNARPCSY00ZFQYN1QQV", "datetime": "2025-07-02 02:19:18", "utime": **********.571439, "method": "GET", "uri": "/admin/clients/26/edit", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[02:19:01] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/clients\\/26\\/edit\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.326719, "xdebug_link": null, "collector": "log"}, {"message": "[02:19:18] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 19,\n    \"roles\": [\n        \"jr_bde_team\"\n    ],\n    \"current_path\": \"admin\\/clients\\/26\\/edit\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.563797, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.25267, "end": **********.571465, "duration": 18.31879496574402, "duration_str": "18.32s", "measures": [{"label": "Booting", "start": **********.25267, "relative_start": 0, "end": **********.856176, "relative_end": **********.856176, "duration": 0.****************, "duration_str": "604ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.85619, "relative_start": 0.****************, "end": **********.571468, "relative_end": 3.0994415283203125e-06, "duration": 17.***************, "duration_str": "17.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.274905, "relative_start": 1.****************, "end": **********.278137, "relative_end": **********.278137, "duration": 0.0032320022583007812, "duration_str": "3.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.resources.client-resource.pages.edit-client", "start": **********.520705, "relative_start": 1.****************, "end": **********.520705, "relative_end": **********.520705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b549758d82b4bf5de6d7ca69d7fbc0", "start": **********.260453, "relative_start": 2.****************, "end": **********.260453, "relative_end": **********.260453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c342adb8bf47088911d457d9b620f138", "start": **********.266117, "relative_start": 2.0134470462799072, "end": **********.266117, "relative_end": **********.266117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2dd78ec18edb3015cf5fa7cae9cc806e", "start": **********.27245, "relative_start": 2.019779920578003, "end": **********.27245, "relative_end": **********.27245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.772532, "relative_start": 2.519861936569214, "end": **********.772532, "relative_end": **********.772532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.783882, "relative_start": 2.5312118530273438, "end": **********.783882, "relative_end": **********.783882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.795368, "relative_start": 2.5426979064941406, "end": **********.795368, "relative_end": **********.795368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.80142, "relative_start": 2.5487499237060547, "end": **********.80142, "relative_end": **********.80142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.806364, "relative_start": 2.553694009780884, "end": **********.806364, "relative_end": **********.806364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.81768, "relative_start": 2.565009832382202, "end": **********.81768, "relative_end": **********.81768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.378258, "relative_start": 3.1255879402160645, "end": 1751422743.378258, "relative_end": 1751422743.378258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.387369, "relative_start": 3.1346988677978516, "end": 1751422743.387369, "relative_end": 1751422743.387369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.394506, "relative_start": 3.141835927963257, "end": 1751422743.394506, "relative_end": 1751422743.394506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.398128, "relative_start": 3.1454579830169678, "end": 1751422743.398128, "relative_end": 1751422743.398128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.402515, "relative_start": 3.1498448848724365, "end": 1751422743.402515, "relative_end": 1751422743.402515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.407, "relative_start": 3.154330015182495, "end": 1751422743.407, "relative_end": 1751422743.407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.411647, "relative_start": 3.1589770317077637, "end": 1751422743.411647, "relative_end": 1751422743.411647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.416467, "relative_start": 3.163796901702881, "end": 1751422743.416467, "relative_end": 1751422743.416467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.421548, "relative_start": 3.1688778400421143, "end": 1751422743.421548, "relative_end": 1751422743.421548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1751422743.428154, "relative_start": 3.1754839420318604, "end": 1751422743.428154, "relative_end": 1751422743.428154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751422743.4329, "relative_start": 3.180229902267456, "end": 1751422743.4329, "relative_end": 1751422743.4329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.476526, "relative_start": 3.223855972290039, "end": 1751422743.476526, "relative_end": 1751422743.476526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.480347, "relative_start": 3.2276768684387207, "end": 1751422743.480347, "relative_end": 1751422743.480347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.488877, "relative_start": 3.2362070083618164, "end": 1751422743.488877, "relative_end": 1751422743.488877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.493555, "relative_start": 3.240885019302368, "end": 1751422743.493555, "relative_end": 1751422743.493555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.497128, "relative_start": 3.244457960128784, "end": 1751422743.497128, "relative_end": 1751422743.497128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.503441, "relative_start": 3.2507710456848145, "end": 1751422743.503441, "relative_end": 1751422743.503441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.507398, "relative_start": 3.254727840423584, "end": 1751422743.507398, "relative_end": 1751422743.507398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.516238, "relative_start": 3.2635679244995117, "end": 1751422743.516238, "relative_end": 1751422743.516238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.521018, "relative_start": 3.268347978591919, "end": 1751422743.521018, "relative_end": 1751422743.521018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.525101, "relative_start": 3.272430896759033, "end": 1751422743.525101, "relative_end": 1751422743.525101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.530244, "relative_start": 3.277574062347412, "end": 1751422743.530244, "relative_end": 1751422743.530244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.533959, "relative_start": 3.2812888622283936, "end": 1751422743.533959, "relative_end": 1751422743.533959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.541988, "relative_start": 3.2893178462982178, "end": 1751422743.541988, "relative_end": 1751422743.541988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.546122, "relative_start": 3.293452024459839, "end": 1751422743.546122, "relative_end": 1751422743.546122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.549978, "relative_start": 3.2973079681396484, "end": 1751422743.549978, "relative_end": 1751422743.549978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.556405, "relative_start": 3.3037350177764893, "end": 1751422743.556405, "relative_end": 1751422743.556405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.560789, "relative_start": 3.308119058609009, "end": 1751422743.560789, "relative_end": 1751422743.560789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.570089, "relative_start": 3.3174190521240234, "end": 1751422743.570089, "relative_end": 1751422743.570089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.575176, "relative_start": 3.3225059509277344, "end": 1751422743.575176, "relative_end": 1751422743.575176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.580428, "relative_start": 3.3277578353881836, "end": 1751422743.580428, "relative_end": 1751422743.580428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.586277, "relative_start": 3.3336069583892822, "end": 1751422743.586277, "relative_end": 1751422743.586277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.590337, "relative_start": 3.3376669883728027, "end": 1751422743.590337, "relative_end": 1751422743.590337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.598426, "relative_start": 3.3457560539245605, "end": 1751422743.598426, "relative_end": 1751422743.598426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.60373, "relative_start": 3.351059913635254, "end": 1751422743.60373, "relative_end": 1751422743.60373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.608199, "relative_start": 3.3555288314819336, "end": 1751422743.608199, "relative_end": 1751422743.608199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.61357, "relative_start": 3.3608999252319336, "end": 1751422743.61357, "relative_end": 1751422743.61357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.617769, "relative_start": 3.3650989532470703, "end": 1751422743.617769, "relative_end": 1751422743.617769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.625143, "relative_start": 3.3724730014801025, "end": 1751422743.625143, "relative_end": 1751422743.625143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.628464, "relative_start": 3.375793933868408, "end": 1751422743.628464, "relative_end": 1751422743.628464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.631882, "relative_start": 3.379211902618408, "end": 1751422743.631882, "relative_end": 1751422743.631882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.636849, "relative_start": 3.384178876876831, "end": 1751422743.636849, "relative_end": 1751422743.636849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.640139, "relative_start": 3.3874690532684326, "end": 1751422743.640139, "relative_end": 1751422743.640139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.646725, "relative_start": 3.394054889678955, "end": 1751422743.646725, "relative_end": 1751422743.646725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.650338, "relative_start": 3.39766788482666, "end": 1751422743.650338, "relative_end": 1751422743.650338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.65462, "relative_start": 3.401949882507324, "end": 1751422743.65462, "relative_end": 1751422743.65462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.659551, "relative_start": 3.4068808555603027, "end": 1751422743.659551, "relative_end": 1751422743.659551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422743.662614, "relative_start": 3.4099440574645996, "end": 1751422743.662614, "relative_end": 1751422743.662614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422743.670602, "relative_start": 3.4179320335388184, "end": 1751422743.670602, "relative_end": 1751422743.670602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.674321, "relative_start": 3.4216508865356445, "end": 1751422743.674321, "relative_end": 1751422743.674321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751422743.677593, "relative_start": 3.4249229431152344, "end": 1751422743.677593, "relative_end": 1751422743.677593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1751422743.682101, "relative_start": 3.4294309616088867, "end": 1751422743.682101, "relative_end": 1751422743.682101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751422743.68433, "relative_start": 3.4316599369049072, "end": 1751422743.68433, "relative_end": 1751422743.68433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1751422750.615634, "relative_start": 10.362963914871216, "end": 1751422750.615634, "relative_end": 1751422750.615634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": 1751422750.629424, "relative_start": 10.37675404548645, "end": 1751422750.629424, "relative_end": 1751422750.629424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": 1751422750.643663, "relative_start": 10.390992879867554, "end": 1751422750.643663, "relative_end": 1751422750.643663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422751.151479, "relative_start": 10.89880895614624, "end": 1751422751.151479, "relative_end": 1751422751.151479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422753.899881, "relative_start": 13.647210836410522, "end": 1751422753.899881, "relative_end": 1751422753.899881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422753.915903, "relative_start": 13.663233041763306, "end": 1751422753.915903, "relative_end": 1751422753.915903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751422753.92434, "relative_start": 13.671669960021973, "end": 1751422753.92434, "relative_end": 1751422753.92434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751422753.93031, "relative_start": 13.677639961242676, "end": 1751422753.93031, "relative_end": 1751422753.93031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": 1751422753.938943, "relative_start": 13.686272859573364, "end": 1751422753.938943, "relative_end": 1751422753.938943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.56008, "relative_start": 14.30741000175476, "end": 1751422754.56008, "relative_end": 1751422754.56008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.571976, "relative_start": 14.319305896759033, "end": 1751422754.571976, "relative_end": 1751422754.571976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.581867, "relative_start": 14.32919692993164, "end": 1751422754.581867, "relative_end": 1751422754.581867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.6346, "relative_start": 14.381929874420166, "end": 1751422754.6346, "relative_end": 1751422754.6346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.645129, "relative_start": 14.39245891571045, "end": 1751422754.645129, "relative_end": 1751422754.645129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.654703, "relative_start": 14.402032852172852, "end": 1751422754.654703, "relative_end": 1751422754.654703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1751422754.762818, "relative_start": 14.510148048400879, "end": 1751422754.762818, "relative_end": 1751422754.762818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.802415, "relative_start": 14.549744844436646, "end": 1751422754.802415, "relative_end": 1751422754.802415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.817103, "relative_start": 14.564432859420776, "end": 1751422754.817103, "relative_end": 1751422754.817103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.849223, "relative_start": 14.596552848815918, "end": 1751422754.849223, "relative_end": 1751422754.849223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751422754.864159, "relative_start": 14.611489057540894, "end": 1751422754.864159, "relative_end": 1751422754.864159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1751422754.923162, "relative_start": 14.670491933822632, "end": 1751422754.923162, "relative_end": 1751422754.923162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": 1751422755.011924, "relative_start": 14.759253978729248, "end": 1751422755.011924, "relative_end": 1751422755.011924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": 1751422755.019809, "relative_start": 14.767138957977295, "end": 1751422755.019809, "relative_end": 1751422755.019809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": 1751422755.021771, "relative_start": 14.769100904464722, "end": 1751422755.021771, "relative_end": 1751422755.021771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cfacee1661975405ef8b0adde66e3e9f", "start": 1751422755.025468, "relative_start": 14.77279806137085, "end": 1751422755.025468, "relative_end": 1751422755.025468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.290351, "relative_start": 18.037680864334106, "end": **********.290351, "relative_end": **********.290351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.294646, "relative_start": 18.04197597503662, "end": **********.294646, "relative_end": **********.294646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.518062, "relative_start": 18.265392065048218, "end": **********.518062, "relative_end": **********.518062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.537139, "relative_start": 18.28446888923645, "end": **********.537139, "relative_end": **********.537139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.554957, "relative_start": 18.302286863327026, "end": **********.554957, "relative_end": **********.554957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.563143, "relative_start": 18.31047296524048, "end": **********.563314, "relative_end": **********.563314, "duration": 0.0001709461212158203, "duration_str": "171μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.568959, "relative_start": 18.316288948059082, "end": **********.56904, "relative_end": **********.56904, "duration": 8.106231689453125e-05, "duration_str": "81μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 61030320, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 93, "nb_templates": 93, "templates": [{"name": "1x filament.resources.client-resource.pages.edit-client", "param_count": null, "params": [], "start": **********.52068, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/resources/client-resource/pages/edit-client.blade.phpfilament.resources.client-resource.pages.edit-client", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fresources%2Fclient-resource%2Fpages%2Fedit-client.blade.php&line=1", "ajax": false, "filename": "edit-client.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.resources.client-resource.pages.edit-client"}, {"name": "1x __components::e3b549758d82b4bf5de6d7ca69d7fbc0", "param_count": null, "params": [], "start": **********.260414, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/e3b549758d82b4bf5de6d7ca69d7fbc0.blade.php__components::e3b549758d82b4bf5de6d7ca69d7fbc0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fe3b549758d82b4bf5de6d7ca69d7fbc0.blade.php&line=1", "ajax": false, "filename": "e3b549758d82b4bf5de6d7ca69d7fbc0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b549758d82b4bf5de6d7ca69d7fbc0"}, {"name": "1x __components::c342adb8bf47088911d457d9b620f138", "param_count": null, "params": [], "start": **********.266093, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/c342adb8bf47088911d457d9b620f138.blade.php__components::c342adb8bf47088911d457d9b620f138", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fc342adb8bf47088911d457d9b620f138.blade.php&line=1", "ajax": false, "filename": "c342adb8bf47088911d457d9b620f138.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c342adb8bf47088911d457d9b620f138"}, {"name": "1x __components::2dd78ec18edb3015cf5fa7cae9cc806e", "param_count": null, "params": [], "start": **********.272415, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/2dd78ec18edb3015cf5fa7cae9cc806e.blade.php__components::2dd78ec18edb3015cf5fa7cae9cc806e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F2dd78ec18edb3015cf5fa7cae9cc806e.blade.php&line=1", "ajax": false, "filename": "2dd78ec18edb3015cf5fa7cae9cc806e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2dd78ec18edb3015cf5fa7cae9cc806e"}, {"name": "28x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.772497, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 28, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "12x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.817662, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 12, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "20x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1751422743.37823, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 20, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "4x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751422743.428139, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "2x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": 1751422743.432875, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": 1751422750.629402, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "1x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": 1751422750.643647, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751422753.938924, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "10x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751422754.560058, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 10, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751422754.923142, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": 1751422755.01189, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": 1751422755.01979, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": 1751422755.021753, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "1x __components::cfacee1661975405ef8b0adde66e3e9f", "param_count": null, "params": [], "start": 1751422755.025449, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/cfacee1661975405ef8b0adde66e3e9f.blade.php__components::cfacee1661975405ef8b0adde66e3e9f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fcfacee1661975405ef8b0adde66e3e9f.blade.php&line=1", "ajax": false, "filename": "cfacee1661975405ef8b0adde66e3e9f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cfacee1661975405ef8b0adde66e3e9f"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.290294, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.294622, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.518031, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.537069, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.554331, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 51, "nb_statements": 51, "nb_visible_statements": 51, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07508, "accumulated_duration_str": "75.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'U8YG10c3tOYW3leiFdTMUXCwzGy2gIrark8QAgjO' limit 1", "type": "query", "params": [], "bindings": ["U8YG10c3tOYW3leiFdTMUXCwzGy2gIrark8QAgjO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.2912152, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.704}, {"sql": "select * from `users` where `id` = '19' limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 216}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 187}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}], "start": **********.29753, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:74", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=74", "ajax": false, "filename": "EloquentUserProvider.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.704, "width_percent": 2.184}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 216}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 187}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}], "start": **********.3021772, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:74", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=74", "ajax": false, "filename": "EloquentUserProvider.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.888, "width_percent": 1.332}, {"sql": "delete from `sessions` where `id` = 'U8YG10c3tOYW3leiFdTMUXCwzGy2gIrark8QAgjO'", "type": "query", "params": [], "bindings": ["U8YG10c3tOYW3leiFdTMUXCwzGy2gIrark8QAgjO"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 190}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}], "start": **********.304101, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.22, "width_percent": 0.613}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.311261, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.833, "width_percent": 1.492}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.313543, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.324, "width_percent": 0.879}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.317713, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.204, "width_percent": 0.733}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.3193238, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.936, "width_percent": 0.573}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.3205512, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.509, "width_percent": 0.4}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.32162, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.908, "width_percent": 0.386}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.322675, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.295, "width_percent": 0.466}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/ClientResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 191}, {"index": 25, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}], "start": **********.339606, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.761, "width_percent": 2.97}, {"sql": "select * from `clients` where `created_by` = 19 and `id` = '26' limit 1", "type": "query", "params": [], "bindings": [19, "26"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.344839, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.731, "width_percent": 3.29}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.352606, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.021, "width_percent": 0.746}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.354628, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.767, "width_percent": 7.032}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.3697522, "duration": 0.00663, "duration_str": "6.63ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.799, "width_percent": 8.831}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1751509141, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:242:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:22:\\\"view_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:26:\\\"view_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:24:\\\"create_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:24:\\\"update_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:25:\\\"restore_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:29:\\\"restore_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:27:\\\"replicate_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:25:\\\"reorder_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:24:\\\"delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:28:\\\"delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:30:\\\"force_delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:34:\\\"force_delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:11:\\\"view_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:15:\\\"view_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:13:\\\"create_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:13:\\\"update_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:14:\\\"restore_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:18:\\\"restore_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:16:\\\"replicate_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:14:\\\"reorder_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:13:\\\"delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:17:\\\"delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:19:\\\"force_delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:23:\\\"force_delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:14:\\\"view_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"view_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"create_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:16:\\\"update_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:17:\\\"restore_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:21:\\\"restore_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:19:\\\"replicate_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:17:\\\"reorder_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:16:\\\"delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:20:\\\"delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:22:\\\"force_delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:26:\\\"force_delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:20:\\\"view_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:24:\\\"view_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:22:\\\"create_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:22:\\\"update_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:23:\\\"restore_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:27:\\\"restore_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:25:\\\"replicate_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:23:\\\"reorder_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:22:\\\"delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:26:\\\"delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:28:\\\"force_delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:32:\\\"force_delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:14:\\\"view_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:18:\\\"view_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"create_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:16:\\\"update_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:17:\\\"restore_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:21:\\\"restore_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:19:\\\"replicate_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:17:\\\"reorder_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:16:\\\"delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:20:\\\"delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:22:\\\"force_delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:26:\\\"force_delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:24:\\\"view_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:61;a:4:{s:1:\\\"a\\\";i:62;s:1:\\\"b\\\";s:28:\\\"view_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:62;a:4:{s:1:\\\"a\\\";i:63;s:1:\\\"b\\\";s:26:\\\"create_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:63;a:4:{s:1:\\\"a\\\";i:64;s:1:\\\"b\\\";s:26:\\\"update_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:64;a:4:{s:1:\\\"a\\\";i:65;s:1:\\\"b\\\";s:27:\\\"restore_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:65;a:4:{s:1:\\\"a\\\";i:66;s:1:\\\"b\\\";s:31:\\\"restore_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:66;a:4:{s:1:\\\"a\\\";i:67;s:1:\\\"b\\\";s:29:\\\"replicate_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:67;a:4:{s:1:\\\"a\\\";i:68;s:1:\\\"b\\\";s:27:\\\"reorder_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:68;a:4:{s:1:\\\"a\\\";i:69;s:1:\\\"b\\\";s:26:\\\"delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:69;a:4:{s:1:\\\"a\\\";i:70;s:1:\\\"b\\\";s:30:\\\"delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:70;a:4:{s:1:\\\"a\\\";i:71;s:1:\\\"b\\\";s:32:\\\"force_delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:71;a:4:{s:1:\\\"a\\\";i:72;s:1:\\\"b\\\";s:36:\\\"force_delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:72;a:4:{s:1:\\\"a\\\";i:73;s:1:\\\"b\\\";s:35:\\\"view_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:73;a:4:{s:1:\\\"a\\\";i:74;s:1:\\\"b\\\";s:39:\\\"view_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:74;a:4:{s:1:\\\"a\\\";i:75;s:1:\\\"b\\\";s:37:\\\"create_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:75;a:4:{s:1:\\\"a\\\";i:76;s:1:\\\"b\\\";s:37:\\\"update_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:76;a:4:{s:1:\\\"a\\\";i:77;s:1:\\\"b\\\";s:38:\\\"restore_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:77;a:4:{s:1:\\\"a\\\";i:78;s:1:\\\"b\\\";s:42:\\\"restore_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:78;a:4:{s:1:\\\"a\\\";i:79;s:1:\\\"b\\\";s:40:\\\"replicate_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:79;a:4:{s:1:\\\"a\\\";i:80;s:1:\\\"b\\\";s:38:\\\"reorder_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:80;a:4:{s:1:\\\"a\\\";i:81;s:1:\\\"b\\\";s:37:\\\"delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:81;a:4:{s:1:\\\"a\\\";i:82;s:1:\\\"b\\\";s:41:\\\"delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:82;a:4:{s:1:\\\"a\\\";i:83;s:1:\\\"b\\\";s:43:\\\"force_delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:83;a:4:{s:1:\\\"a\\\";i:84;s:1:\\\"b\\\";s:47:\\\"force_delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:84;a:4:{s:1:\\\"a\\\";i:85;s:1:\\\"b\\\";s:12:\\\"view_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\\\"a\\\";i:86;s:1:\\\"b\\\";s:16:\\\"view_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\\\"a\\\";i:87;s:1:\\\"b\\\";s:14:\\\"create_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\\\"a\\\";i:88;s:1:\\\"b\\\";s:14:\\\"update_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\\\"a\\\";i:89;s:1:\\\"b\\\";s:15:\\\"restore_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:89;a:4:{s:1:\\\"a\\\";i:90;s:1:\\\"b\\\";s:19:\\\"restore_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:90;a:4:{s:1:\\\"a\\\";i:91;s:1:\\\"b\\\";s:17:\\\"replicate_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:91;a:4:{s:1:\\\"a\\\";i:92;s:1:\\\"b\\\";s:15:\\\"reorder_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:92;a:4:{s:1:\\\"a\\\";i:93;s:1:\\\"b\\\";s:14:\\\"delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\\\"a\\\";i:94;s:1:\\\"b\\\";s:18:\\\"delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\\\"a\\\";i:95;s:1:\\\"b\\\";s:20:\\\"force_delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:95;a:4:{s:1:\\\"a\\\";i:96;s:1:\\\"b\\\";s:24:\\\"force_delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:96;a:4:{s:1:\\\"a\\\";i:97;s:1:\\\"b\\\";s:12:\\\"view_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\\\"a\\\";i:98;s:1:\\\"b\\\";s:16:\\\"view_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\\\"a\\\";i:99;s:1:\\\"b\\\";s:14:\\\"create_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\\\"a\\\";i:100;s:1:\\\"b\\\";s:14:\\\"update_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\\\"a\\\";i:101;s:1:\\\"b\\\";s:15:\\\"restore_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:101;a:4:{s:1:\\\"a\\\";i:102;s:1:\\\"b\\\";s:19:\\\"restore_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:102;a:4:{s:1:\\\"a\\\";i:103;s:1:\\\"b\\\";s:17:\\\"replicate_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:103;a:4:{s:1:\\\"a\\\";i:104;s:1:\\\"b\\\";s:15:\\\"reorder_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:104;a:4:{s:1:\\\"a\\\";i:105;s:1:\\\"b\\\";s:14:\\\"delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\\\"a\\\";i:106;s:1:\\\"b\\\";s:18:\\\"delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\\\"a\\\";i:107;s:1:\\\"b\\\";s:20:\\\"force_delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:107;a:4:{s:1:\\\"a\\\";i:108;s:1:\\\"b\\\";s:24:\\\"force_delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:108;a:4:{s:1:\\\"a\\\";i:109;s:1:\\\"b\\\";s:18:\\\"view_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:109;a:4:{s:1:\\\"a\\\";i:110;s:1:\\\"b\\\";s:22:\\\"view_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:110;a:4:{s:1:\\\"a\\\";i:111;s:1:\\\"b\\\";s:20:\\\"create_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:111;a:4:{s:1:\\\"a\\\";i:112;s:1:\\\"b\\\";s:20:\\\"update_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:112;a:4:{s:1:\\\"a\\\";i:113;s:1:\\\"b\\\";s:21:\\\"restore_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:113;a:4:{s:1:\\\"a\\\";i:114;s:1:\\\"b\\\";s:25:\\\"restore_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:114;a:4:{s:1:\\\"a\\\";i:115;s:1:\\\"b\\\";s:23:\\\"replicate_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:115;a:4:{s:1:\\\"a\\\";i:116;s:1:\\\"b\\\";s:21:\\\"reorder_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:116;a:4:{s:1:\\\"a\\\";i:117;s:1:\\\"b\\\";s:20:\\\"delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:117;a:4:{s:1:\\\"a\\\";i:118;s:1:\\\"b\\\";s:24:\\\"delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:118;a:4:{s:1:\\\"a\\\";i:119;s:1:\\\"b\\\";s:26:\\\"force_delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:119;a:4:{s:1:\\\"a\\\";i:120;s:1:\\\"b\\\";s:30:\\\"force_delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:120;a:4:{s:1:\\\"a\\\";i:121;s:1:\\\"b\\\";s:9:\\\"view_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:121;a:4:{s:1:\\\"a\\\";i:122;s:1:\\\"b\\\";s:13:\\\"view_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:122;a:4:{s:1:\\\"a\\\";i:123;s:1:\\\"b\\\";s:11:\\\"create_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:123;a:4:{s:1:\\\"a\\\";i:124;s:1:\\\"b\\\";s:11:\\\"update_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:124;a:4:{s:1:\\\"a\\\";i:125;s:1:\\\"b\\\";s:11:\\\"delete_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:125;a:4:{s:1:\\\"a\\\";i:126;s:1:\\\"b\\\";s:15:\\\"delete_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:126;a:4:{s:1:\\\"a\\\";i:127;s:1:\\\"b\\\";s:33:\\\"view_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:127;a:4:{s:1:\\\"a\\\";i:128;s:1:\\\"b\\\";s:37:\\\"view_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:128;a:4:{s:1:\\\"a\\\";i:129;s:1:\\\"b\\\";s:35:\\\"create_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:129;a:4:{s:1:\\\"a\\\";i:130;s:1:\\\"b\\\";s:35:\\\"update_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:130;a:4:{s:1:\\\"a\\\";i:131;s:1:\\\"b\\\";s:36:\\\"restore_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:131;a:4:{s:1:\\\"a\\\";i:132;s:1:\\\"b\\\";s:40:\\\"restore_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:132;a:4:{s:1:\\\"a\\\";i:133;s:1:\\\"b\\\";s:38:\\\"replicate_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:133;a:4:{s:1:\\\"a\\\";i:134;s:1:\\\"b\\\";s:36:\\\"reorder_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:134;a:4:{s:1:\\\"a\\\";i:135;s:1:\\\"b\\\";s:35:\\\"delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:135;a:4:{s:1:\\\"a\\\";i:136;s:1:\\\"b\\\";s:39:\\\"delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:136;a:4:{s:1:\\\"a\\\";i:137;s:1:\\\"b\\\";s:41:\\\"force_delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:137;a:4:{s:1:\\\"a\\\";i:138;s:1:\\\"b\\\";s:45:\\\"force_delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:138;a:4:{s:1:\\\"a\\\";i:139;s:1:\\\"b\\\";s:10:\\\"view_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:139;a:4:{s:1:\\\"a\\\";i:140;s:1:\\\"b\\\";s:14:\\\"view_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:140;a:4:{s:1:\\\"a\\\";i:141;s:1:\\\"b\\\";s:12:\\\"create_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:141;a:4:{s:1:\\\"a\\\";i:142;s:1:\\\"b\\\";s:12:\\\"update_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:142;a:4:{s:1:\\\"a\\\";i:143;s:1:\\\"b\\\";s:13:\\\"restore_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:143;a:4:{s:1:\\\"a\\\";i:144;s:1:\\\"b\\\";s:17:\\\"restore_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:144;a:4:{s:1:\\\"a\\\";i:145;s:1:\\\"b\\\";s:15:\\\"replicate_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:145;a:4:{s:1:\\\"a\\\";i:146;s:1:\\\"b\\\";s:13:\\\"reorder_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:146;a:4:{s:1:\\\"a\\\";i:147;s:1:\\\"b\\\";s:12:\\\"delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:147;a:4:{s:1:\\\"a\\\";i:148;s:1:\\\"b\\\";s:16:\\\"delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:148;a:4:{s:1:\\\"a\\\";i:149;s:1:\\\"b\\\";s:18:\\\"force_delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:149;a:4:{s:1:\\\"a\\\";i:150;s:1:\\\"b\\\";s:22:\\\"force_delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:150;a:4:{s:1:\\\"a\\\";i:151;s:1:\\\"b\\\";s:9:\\\"view_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:151;a:4:{s:1:\\\"a\\\";i:152;s:1:\\\"b\\\";s:13:\\\"view_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:152;a:4:{s:1:\\\"a\\\";i:153;s:1:\\\"b\\\";s:11:\\\"create_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:153;a:4:{s:1:\\\"a\\\";i:154;s:1:\\\"b\\\";s:11:\\\"update_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:154;a:4:{s:1:\\\"a\\\";i:155;s:1:\\\"b\\\";s:12:\\\"restore_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:155;a:4:{s:1:\\\"a\\\";i:156;s:1:\\\"b\\\";s:16:\\\"restore_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:156;a:4:{s:1:\\\"a\\\";i:157;s:1:\\\"b\\\";s:14:\\\"replicate_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:157;a:4:{s:1:\\\"a\\\";i:158;s:1:\\\"b\\\";s:12:\\\"reorder_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:158;a:4:{s:1:\\\"a\\\";i:159;s:1:\\\"b\\\";s:11:\\\"delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:159;a:4:{s:1:\\\"a\\\";i:160;s:1:\\\"b\\\";s:15:\\\"delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:160;a:4:{s:1:\\\"a\\\";i:161;s:1:\\\"b\\\";s:17:\\\"force_delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:161;a:4:{s:1:\\\"a\\\";i:162;s:1:\\\"b\\\";s:21:\\\"force_delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:162;a:4:{s:1:\\\"a\\\";i:163;s:1:\\\"b\\\";s:17:\\\"page_BdeDashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\\\"a\\\";i:164;s:1:\\\"b\\\";s:22:\\\"page_DashboardSettings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:164;a:4:{s:1:\\\"a\\\";i:165;s:1:\\\"b\\\";s:18:\\\"page_ManageSetting\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:165;a:4:{s:1:\\\"a\\\";i:166;s:1:\\\"b\\\";s:11:\\\"page_Themes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:166;a:4:{s:1:\\\"a\\\";i:167;s:1:\\\"b\\\";s:18:\\\"page_MyProfilePage\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\\\"a\\\";i:168;s:1:\\\"b\\\";s:26:\\\"widget_NotificationsWidget\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:168;a:4:{s:1:\\\"a\\\";i:169;s:1:\\\"b\\\";s:9:\\\"view_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:169;a:4:{s:1:\\\"a\\\";i:170;s:1:\\\"b\\\";s:13:\\\"view_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:170;a:4:{s:1:\\\"a\\\";i:171;s:1:\\\"b\\\";s:11:\\\"create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:171;a:4:{s:1:\\\"a\\\";i:172;s:1:\\\"b\\\";s:11:\\\"update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:172;a:4:{s:1:\\\"a\\\";i:173;s:1:\\\"b\\\";s:12:\\\"restore_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:173;a:4:{s:1:\\\"a\\\";i:174;s:1:\\\"b\\\";s:16:\\\"restore_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:174;a:4:{s:1:\\\"a\\\";i:175;s:1:\\\"b\\\";s:14:\\\"replicate_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:175;a:4:{s:1:\\\"a\\\";i:176;s:1:\\\"b\\\";s:12:\\\"reorder_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:176;a:4:{s:1:\\\"a\\\";i:177;s:1:\\\"b\\\";s:11:\\\"delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:177;a:4:{s:1:\\\"a\\\";i:178;s:1:\\\"b\\\";s:15:\\\"delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:178;a:4:{s:1:\\\"a\\\";i:179;s:1:\\\"b\\\";s:17:\\\"force_delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:179;a:4:{s:1:\\\"a\\\";i:180;s:1:\\\"b\\\";s:21:\\\"force_delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:180;a:4:{s:1:\\\"a\\\";i:181;s:1:\\\"b\\\";s:16:\\\"book:create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:181;a:4:{s:1:\\\"a\\\";i:182;s:1:\\\"b\\\";s:16:\\\"book:update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:182;a:4:{s:1:\\\"a\\\";i:183;s:1:\\\"b\\\";s:16:\\\"book:delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:183;a:4:{s:1:\\\"a\\\";i:184;s:1:\\\"b\\\";s:20:\\\"book:pagination_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:184;a:4:{s:1:\\\"a\\\";i:185;s:1:\\\"b\\\";s:16:\\\"book:detail_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:185;a:4:{s:1:\\\"a\\\";i:186;s:1:\\\"b\\\";s:14:\\\"view_dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:186;a:4:{s:1:\\\"a\\\";i:187;s:1:\\\"b\\\";s:18:\\\"page_Bde_Dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:187;a:4:{s:1:\\\"a\\\";i:188;s:1:\\\"b\\\";s:11:\\\"export_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:188;a:4:{s:1:\\\"a\\\";i:189;s:1:\\\"b\\\";s:12:\\\"view_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:189;a:4:{s:1:\\\"a\\\";i:190;s:1:\\\"b\\\";s:16:\\\"view_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:190;a:4:{s:1:\\\"a\\\";i:191;s:1:\\\"b\\\";s:14:\\\"create_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:191;a:4:{s:1:\\\"a\\\";i:192;s:1:\\\"b\\\";s:14:\\\"update_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:192;a:4:{s:1:\\\"a\\\";i:193;s:1:\\\"b\\\";s:15:\\\"restore_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:193;a:4:{s:1:\\\"a\\\";i:194;s:1:\\\"b\\\";s:19:\\\"restore_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:194;a:4:{s:1:\\\"a\\\";i:195;s:1:\\\"b\\\";s:17:\\\"replicate_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:195;a:4:{s:1:\\\"a\\\";i:196;s:1:\\\"b\\\";s:15:\\\"reorder_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:196;a:4:{s:1:\\\"a\\\";i:197;s:1:\\\"b\\\";s:14:\\\"delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:197;a:4:{s:1:\\\"a\\\";i:198;s:1:\\\"b\\\";s:18:\\\"delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:198;a:4:{s:1:\\\"a\\\";i:199;s:1:\\\"b\\\";s:20:\\\"force_delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:199;a:4:{s:1:\\\"a\\\";i:200;s:1:\\\"b\\\";s:24:\\\"force_delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:200;a:4:{s:1:\\\"a\\\";i:201;s:1:\\\"b\\\";s:9:\\\"view_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:201;a:4:{s:1:\\\"a\\\";i:202;s:1:\\\"b\\\";s:13:\\\"view_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:202;a:4:{s:1:\\\"a\\\";i:203;s:1:\\\"b\\\";s:11:\\\"create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:203;a:4:{s:1:\\\"a\\\";i:204;s:1:\\\"b\\\";s:11:\\\"update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:204;a:4:{s:1:\\\"a\\\";i:205;s:1:\\\"b\\\";s:12:\\\"restore_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:205;a:4:{s:1:\\\"a\\\";i:206;s:1:\\\"b\\\";s:16:\\\"restore_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:206;a:4:{s:1:\\\"a\\\";i:207;s:1:\\\"b\\\";s:14:\\\"replicate_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:207;a:4:{s:1:\\\"a\\\";i:208;s:1:\\\"b\\\";s:12:\\\"reorder_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:208;a:4:{s:1:\\\"a\\\";i:209;s:1:\\\"b\\\";s:11:\\\"delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:209;a:4:{s:1:\\\"a\\\";i:210;s:1:\\\"b\\\";s:15:\\\"delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:210;a:4:{s:1:\\\"a\\\";i:211;s:1:\\\"b\\\";s:17:\\\"force_delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:211;a:4:{s:1:\\\"a\\\";i:212;s:1:\\\"b\\\";s:21:\\\"force_delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:212;a:4:{s:1:\\\"a\\\";i:213;s:1:\\\"b\\\";s:16:\\\"post:create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:213;a:4:{s:1:\\\"a\\\";i:214;s:1:\\\"b\\\";s:16:\\\"post:update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:214;a:4:{s:1:\\\"a\\\";i:215;s:1:\\\"b\\\";s:16:\\\"post:delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:215;a:4:{s:1:\\\"a\\\";i:216;s:1:\\\"b\\\";s:20:\\\"post:pagination_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:216;a:4:{s:1:\\\"a\\\";i:217;s:1:\\\"b\\\";s:16:\\\"post:detail_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:217;a:3:{s:1:\\\"a\\\";i:218;s:1:\\\"b\\\";s:19:\\\"view_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:218;a:3:{s:1:\\\"a\\\";i:219;s:1:\\\"b\\\";s:21:\\\"create_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:219;a:3:{s:1:\\\"a\\\";i:220;s:1:\\\"b\\\";s:21:\\\"update_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:220;a:3:{s:1:\\\"a\\\";i:221;s:1:\\\"b\\\";s:21:\\\"delete_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:221;a:4:{s:1:\\\"a\\\";i:222;s:1:\\\"b\\\";s:25:\\\"view_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:222;a:3:{s:1:\\\"a\\\";i:223;s:1:\\\"b\\\";s:27:\\\"create_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:223;a:3:{s:1:\\\"a\\\";i:224;s:1:\\\"b\\\";s:27:\\\"update_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:224;a:3:{s:1:\\\"a\\\";i:225;s:1:\\\"b\\\";s:27:\\\"delete_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:225;a:3:{s:1:\\\"a\\\";i:226;s:1:\\\"b\\\";s:12:\\\"view_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:226;a:3:{s:1:\\\"a\\\";i:227;s:1:\\\"b\\\";s:14:\\\"create_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:227;a:3:{s:1:\\\"a\\\";i:228;s:1:\\\"b\\\";s:14:\\\"update_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:228;a:3:{s:1:\\\"a\\\";i:229;s:1:\\\"b\\\";s:14:\\\"delete_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:229;a:3:{s:1:\\\"a\\\";i:230;s:1:\\\"b\\\";s:23:\\\"view_any_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:230;a:3:{s:1:\\\"a\\\";i:231;s:1:\\\"b\\\";s:16:\\\"view_any_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:231;a:4:{s:1:\\\"a\\\";i:232;s:1:\\\"b\\\";s:29:\\\"view_any_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:232;a:3:{s:1:\\\"a\\\";i:233;s:1:\\\"b\\\";s:25:\\\"delete_any_pricing::model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:233;a:3:{s:1:\\\"a\\\";i:234;s:1:\\\"b\\\";s:18:\\\"delete_any_product\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:234;a:3:{s:1:\\\"a\\\";i:235;s:1:\\\"b\\\";s:31:\\\"delete_any_project::status::log\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:235;a:3:{s:1:\\\"a\\\";i:236;s:1:\\\"b\\\";s:21:\\\"view_any_project_type\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:236;a:3:{s:1:\\\"a\\\";i:237;s:1:\\\"b\\\";s:22:\\\"view_any_pricing_model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:237;a:3:{s:1:\\\"a\\\";i:238;s:1:\\\"b\\\";s:23:\\\"view_any_incentive_rule\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:238;a:3:{s:1:\\\"a\\\";i:239;s:1:\\\"b\\\";s:27:\\\"view_any_notification_event\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:239;a:3:{s:1:\\\"a\\\";i:240;s:1:\\\"b\\\";s:27:\\\"view_any_dashboard_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:240;a:3:{s:1:\\\"a\\\";i:241;s:1:\\\"b\\\";s:35:\\\"view_any_role_notification_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:241;a:3:{s:1:\\\"a\\\";i:242;s:1:\\\"b\\\";s:37:\\\"view_any_notification_role_preference\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}}s:5:\\\"roles\\\";a:3:{i:0;a:3:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:8:\\\"bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"super_admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:11:\\\"jr_bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1751509141, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:242:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:22:\"view_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:26:\"view_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:24:\"create_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:24:\"update_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:25:\"restore_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:29:\"restore_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:27:\"replicate_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:25:\"reorder_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:24:\"delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:28:\"delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:30:\"force_delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:34:\"force_delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:11:\"view_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:15:\"view_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"create_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:13:\"update_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:14:\"restore_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:18:\"restore_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:16:\"replicate_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:14:\"reorder_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:13:\"delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:17:\"delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"force_delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:23:\"force_delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:14:\"view_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"view_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"create_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"update_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:17:\"restore_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:21:\"restore_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:19:\"replicate_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:17:\"reorder_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:16:\"delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:22:\"force_delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:26:\"force_delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:24:\"view_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:22:\"create_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:22:\"update_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:23:\"restore_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:27:\"restore_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:25:\"replicate_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:23:\"reorder_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:22:\"delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:26:\"delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:28:\"force_delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:32:\"force_delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:14:\"view_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:18:\"view_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"create_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:16:\"update_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:17:\"restore_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:21:\"restore_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:19:\"replicate_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:17:\"reorder_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:16:\"delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:20:\"delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:22:\"force_delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:26:\"force_delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:24:\"view_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:28:\"view_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:26:\"create_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:26:\"update_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:27:\"restore_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:31:\"restore_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:29:\"replicate_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:27:\"reorder_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:26:\"delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:30:\"delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:32:\"force_delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:36:\"force_delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:35:\"view_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:39:\"view_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:37:\"create_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:37:\"update_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:38:\"restore_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:42:\"restore_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:40:\"replicate_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:38:\"reorder_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:37:\"delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:41:\"delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:43:\"force_delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:47:\"force_delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"view_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:16:\"view_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:14:\"create_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:14:\"update_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:15:\"restore_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:19:\"restore_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:17:\"replicate_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:15:\"reorder_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:14:\"delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:18:\"delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:20:\"force_delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:24:\"force_delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:12:\"view_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:16:\"view_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:14:\"create_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:14:\"update_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:15:\"restore_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:19:\"restore_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:17:\"replicate_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:15:\"reorder_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:14:\"delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:18:\"delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"force_delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:24:\"force_delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:18:\"view_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:22:\"view_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:20:\"create_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:20:\"update_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:21:\"restore_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:25:\"restore_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:23:\"replicate_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:21:\"reorder_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:20:\"delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:117;a:4:{s:1:\"a\";i:118;s:1:\"b\";s:24:\"delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:118;a:4:{s:1:\"a\";i:119;s:1:\"b\";s:26:\"force_delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:119;a:4:{s:1:\"a\";i:120;s:1:\"b\";s:30:\"force_delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:120;a:4:{s:1:\"a\";i:121;s:1:\"b\";s:9:\"view_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:121;a:4:{s:1:\"a\";i:122;s:1:\"b\";s:13:\"view_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:122;a:4:{s:1:\"a\";i:123;s:1:\"b\";s:11:\"create_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:123;a:4:{s:1:\"a\";i:124;s:1:\"b\";s:11:\"update_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:124;a:4:{s:1:\"a\";i:125;s:1:\"b\";s:11:\"delete_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:125;a:4:{s:1:\"a\";i:126;s:1:\"b\";s:15:\"delete_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:126;a:4:{s:1:\"a\";i:127;s:1:\"b\";s:33:\"view_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:127;a:4:{s:1:\"a\";i:128;s:1:\"b\";s:37:\"view_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:128;a:4:{s:1:\"a\";i:129;s:1:\"b\";s:35:\"create_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:129;a:4:{s:1:\"a\";i:130;s:1:\"b\";s:35:\"update_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:130;a:4:{s:1:\"a\";i:131;s:1:\"b\";s:36:\"restore_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:131;a:4:{s:1:\"a\";i:132;s:1:\"b\";s:40:\"restore_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:132;a:4:{s:1:\"a\";i:133;s:1:\"b\";s:38:\"replicate_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:133;a:4:{s:1:\"a\";i:134;s:1:\"b\";s:36:\"reorder_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:134;a:4:{s:1:\"a\";i:135;s:1:\"b\";s:35:\"delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:135;a:4:{s:1:\"a\";i:136;s:1:\"b\";s:39:\"delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:136;a:4:{s:1:\"a\";i:137;s:1:\"b\";s:41:\"force_delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:137;a:4:{s:1:\"a\";i:138;s:1:\"b\";s:45:\"force_delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:138;a:4:{s:1:\"a\";i:139;s:1:\"b\";s:10:\"view_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:139;a:4:{s:1:\"a\";i:140;s:1:\"b\";s:14:\"view_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:140;a:4:{s:1:\"a\";i:141;s:1:\"b\";s:12:\"create_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:141;a:4:{s:1:\"a\";i:142;s:1:\"b\";s:12:\"update_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:142;a:4:{s:1:\"a\";i:143;s:1:\"b\";s:13:\"restore_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:143;a:4:{s:1:\"a\";i:144;s:1:\"b\";s:17:\"restore_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:144;a:4:{s:1:\"a\";i:145;s:1:\"b\";s:15:\"replicate_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:145;a:4:{s:1:\"a\";i:146;s:1:\"b\";s:13:\"reorder_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:146;a:4:{s:1:\"a\";i:147;s:1:\"b\";s:12:\"delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:147;a:4:{s:1:\"a\";i:148;s:1:\"b\";s:16:\"delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:148;a:4:{s:1:\"a\";i:149;s:1:\"b\";s:18:\"force_delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:149;a:4:{s:1:\"a\";i:150;s:1:\"b\";s:22:\"force_delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:150;a:4:{s:1:\"a\";i:151;s:1:\"b\";s:9:\"view_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:151;a:4:{s:1:\"a\";i:152;s:1:\"b\";s:13:\"view_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:152;a:4:{s:1:\"a\";i:153;s:1:\"b\";s:11:\"create_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:153;a:4:{s:1:\"a\";i:154;s:1:\"b\";s:11:\"update_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:154;a:4:{s:1:\"a\";i:155;s:1:\"b\";s:12:\"restore_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:155;a:4:{s:1:\"a\";i:156;s:1:\"b\";s:16:\"restore_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:156;a:4:{s:1:\"a\";i:157;s:1:\"b\";s:14:\"replicate_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:157;a:4:{s:1:\"a\";i:158;s:1:\"b\";s:12:\"reorder_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:158;a:4:{s:1:\"a\";i:159;s:1:\"b\";s:11:\"delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:159;a:4:{s:1:\"a\";i:160;s:1:\"b\";s:15:\"delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:160;a:4:{s:1:\"a\";i:161;s:1:\"b\";s:17:\"force_delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:161;a:4:{s:1:\"a\";i:162;s:1:\"b\";s:21:\"force_delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:162;a:4:{s:1:\"a\";i:163;s:1:\"b\";s:17:\"page_BdeDashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\"a\";i:164;s:1:\"b\";s:22:\"page_DashboardSettings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:164;a:4:{s:1:\"a\";i:165;s:1:\"b\";s:18:\"page_ManageSetting\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:165;a:4:{s:1:\"a\";i:166;s:1:\"b\";s:11:\"page_Themes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:166;a:4:{s:1:\"a\";i:167;s:1:\"b\";s:18:\"page_MyProfilePage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\"a\";i:168;s:1:\"b\";s:26:\"widget_NotificationsWidget\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:168;a:4:{s:1:\"a\";i:169;s:1:\"b\";s:9:\"view_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:169;a:4:{s:1:\"a\";i:170;s:1:\"b\";s:13:\"view_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:170;a:4:{s:1:\"a\";i:171;s:1:\"b\";s:11:\"create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:171;a:4:{s:1:\"a\";i:172;s:1:\"b\";s:11:\"update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:172;a:4:{s:1:\"a\";i:173;s:1:\"b\";s:12:\"restore_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:173;a:4:{s:1:\"a\";i:174;s:1:\"b\";s:16:\"restore_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:174;a:4:{s:1:\"a\";i:175;s:1:\"b\";s:14:\"replicate_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:175;a:4:{s:1:\"a\";i:176;s:1:\"b\";s:12:\"reorder_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:176;a:4:{s:1:\"a\";i:177;s:1:\"b\";s:11:\"delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:177;a:4:{s:1:\"a\";i:178;s:1:\"b\";s:15:\"delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:178;a:4:{s:1:\"a\";i:179;s:1:\"b\";s:17:\"force_delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:179;a:4:{s:1:\"a\";i:180;s:1:\"b\";s:21:\"force_delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:180;a:4:{s:1:\"a\";i:181;s:1:\"b\";s:16:\"book:create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:181;a:4:{s:1:\"a\";i:182;s:1:\"b\";s:16:\"book:update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:182;a:4:{s:1:\"a\";i:183;s:1:\"b\";s:16:\"book:delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:183;a:4:{s:1:\"a\";i:184;s:1:\"b\";s:20:\"book:pagination_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:184;a:4:{s:1:\"a\";i:185;s:1:\"b\";s:16:\"book:detail_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:185;a:4:{s:1:\"a\";i:186;s:1:\"b\";s:14:\"view_dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:186;a:4:{s:1:\"a\";i:187;s:1:\"b\";s:18:\"page_Bde_Dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:187;a:4:{s:1:\"a\";i:188;s:1:\"b\";s:11:\"export_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:188;a:4:{s:1:\"a\";i:189;s:1:\"b\";s:12:\"view_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:189;a:4:{s:1:\"a\";i:190;s:1:\"b\";s:16:\"view_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:190;a:4:{s:1:\"a\";i:191;s:1:\"b\";s:14:\"create_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:191;a:4:{s:1:\"a\";i:192;s:1:\"b\";s:14:\"update_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:192;a:4:{s:1:\"a\";i:193;s:1:\"b\";s:15:\"restore_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:193;a:4:{s:1:\"a\";i:194;s:1:\"b\";s:19:\"restore_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:194;a:4:{s:1:\"a\";i:195;s:1:\"b\";s:17:\"replicate_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:195;a:4:{s:1:\"a\";i:196;s:1:\"b\";s:15:\"reorder_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:196;a:4:{s:1:\"a\";i:197;s:1:\"b\";s:14:\"delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:197;a:4:{s:1:\"a\";i:198;s:1:\"b\";s:18:\"delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:198;a:4:{s:1:\"a\";i:199;s:1:\"b\";s:20:\"force_delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:199;a:4:{s:1:\"a\";i:200;s:1:\"b\";s:24:\"force_delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:200;a:4:{s:1:\"a\";i:201;s:1:\"b\";s:9:\"view_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:201;a:4:{s:1:\"a\";i:202;s:1:\"b\";s:13:\"view_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:202;a:4:{s:1:\"a\";i:203;s:1:\"b\";s:11:\"create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:203;a:4:{s:1:\"a\";i:204;s:1:\"b\";s:11:\"update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:204;a:4:{s:1:\"a\";i:205;s:1:\"b\";s:12:\"restore_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:205;a:4:{s:1:\"a\";i:206;s:1:\"b\";s:16:\"restore_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:206;a:4:{s:1:\"a\";i:207;s:1:\"b\";s:14:\"replicate_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:207;a:4:{s:1:\"a\";i:208;s:1:\"b\";s:12:\"reorder_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:208;a:4:{s:1:\"a\";i:209;s:1:\"b\";s:11:\"delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:209;a:4:{s:1:\"a\";i:210;s:1:\"b\";s:15:\"delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:210;a:4:{s:1:\"a\";i:211;s:1:\"b\";s:17:\"force_delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:211;a:4:{s:1:\"a\";i:212;s:1:\"b\";s:21:\"force_delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:212;a:4:{s:1:\"a\";i:213;s:1:\"b\";s:16:\"post:create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:213;a:4:{s:1:\"a\";i:214;s:1:\"b\";s:16:\"post:update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:214;a:4:{s:1:\"a\";i:215;s:1:\"b\";s:16:\"post:delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:215;a:4:{s:1:\"a\";i:216;s:1:\"b\";s:20:\"post:pagination_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:216;a:4:{s:1:\"a\";i:217;s:1:\"b\";s:16:\"post:detail_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:217;a:3:{s:1:\"a\";i:218;s:1:\"b\";s:19:\"view_pricing::model\";s:1:\"c\";s:3:\"web\";}i:218;a:3:{s:1:\"a\";i:219;s:1:\"b\";s:21:\"create_pricing::model\";s:1:\"c\";s:3:\"web\";}i:219;a:3:{s:1:\"a\";i:220;s:1:\"b\";s:21:\"update_pricing::model\";s:1:\"c\";s:3:\"web\";}i:220;a:3:{s:1:\"a\";i:221;s:1:\"b\";s:21:\"delete_pricing::model\";s:1:\"c\";s:3:\"web\";}i:221;a:4:{s:1:\"a\";i:222;s:1:\"b\";s:25:\"view_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:222;a:3:{s:1:\"a\";i:223;s:1:\"b\";s:27:\"create_project::status::log\";s:1:\"c\";s:3:\"web\";}i:223;a:3:{s:1:\"a\";i:224;s:1:\"b\";s:27:\"update_project::status::log\";s:1:\"c\";s:3:\"web\";}i:224;a:3:{s:1:\"a\";i:225;s:1:\"b\";s:27:\"delete_project::status::log\";s:1:\"c\";s:3:\"web\";}i:225;a:3:{s:1:\"a\";i:226;s:1:\"b\";s:12:\"view_product\";s:1:\"c\";s:3:\"web\";}i:226;a:3:{s:1:\"a\";i:227;s:1:\"b\";s:14:\"create_product\";s:1:\"c\";s:3:\"web\";}i:227;a:3:{s:1:\"a\";i:228;s:1:\"b\";s:14:\"update_product\";s:1:\"c\";s:3:\"web\";}i:228;a:3:{s:1:\"a\";i:229;s:1:\"b\";s:14:\"delete_product\";s:1:\"c\";s:3:\"web\";}i:229;a:3:{s:1:\"a\";i:230;s:1:\"b\";s:23:\"view_any_pricing::model\";s:1:\"c\";s:3:\"web\";}i:230;a:3:{s:1:\"a\";i:231;s:1:\"b\";s:16:\"view_any_product\";s:1:\"c\";s:3:\"web\";}i:231;a:4:{s:1:\"a\";i:232;s:1:\"b\";s:29:\"view_any_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:232;a:3:{s:1:\"a\";i:233;s:1:\"b\";s:25:\"delete_any_pricing::model\";s:1:\"c\";s:0:\"\";}i:233;a:3:{s:1:\"a\";i:234;s:1:\"b\";s:18:\"delete_any_product\";s:1:\"c\";s:0:\"\";}i:234;a:3:{s:1:\"a\";i:235;s:1:\"b\";s:31:\"delete_any_project::status::log\";s:1:\"c\";s:0:\"\";}i:235;a:3:{s:1:\"a\";i:236;s:1:\"b\";s:21:\"view_any_project_type\";s:1:\"c\";s:0:\"\";}i:236;a:3:{s:1:\"a\";i:237;s:1:\"b\";s:22:\"view_any_pricing_model\";s:1:\"c\";s:0:\"\";}i:237;a:3:{s:1:\"a\";i:238;s:1:\"b\";s:23:\"view_any_incentive_rule\";s:1:\"c\";s:0:\"\";}i:238;a:3:{s:1:\"a\";i:239;s:1:\"b\";s:27:\"view_any_notification_event\";s:1:\"c\";s:0:\"\";}i:239;a:3:{s:1:\"a\";i:240;s:1:\"b\";s:27:\"view_any_dashboard_settings\";s:1:\"c\";s:0:\"\";}i:240;a:3:{s:1:\"a\";i:241;s:1:\"b\";s:35:\"view_any_role_notification_settings\";s:1:\"c\";s:0:\"\";}i:241;a:3:{s:1:\"a\";i:242;s:1:\"b\";s:37:\"view_any_notification_role_preference\";s:1:\"c\";s:0:\"\";}}s:5:\"roles\";a:3:{i:0;a:3:{s:1:\"a\";i:15;s:1:\"b\";s:8:\"bde_team\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"super_admin\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:16;s:1:\"b\";s:11:\"jr_bde_team\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.399951, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.63, "width_percent": 5.101}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (19) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.409959, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.731, "width_percent": 2.824}, {"sql": "select count(*) as aggregate from `projects` where `projects`.`client_id` = 26 and `projects`.`client_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422750.397661, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.555, "width_percent": 2.797}, {"sql": "select * from `projects` where `projects`.`client_id` = 26 and `projects`.`client_id` is not null order by `projects`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422750.401172, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.352, "width_percent": 3.609}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422750.4052188, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.961, "width_percent": 3.316}, {"sql": "select `project_types`.`name`, `project_types`.`id` from `project_types` order by `project_types`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751422753.894979, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.278, "width_percent": 2.824}, {"sql": "select `pricing_models`.`name`, `pricing_models`.`id` from `pricing_models` order by `pricing_models`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751422753.912409, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.101, "width_percent": 0.879}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.7135649, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.98, "width_percent": 0.946}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.7161639, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.926, "width_percent": 6.66}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.723527, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.586, "width_percent": 1.265}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.7373781, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.851, "width_percent": 0.906}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.739065, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.757, "width_percent": 0.906}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.7408128, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.662, "width_percent": 1.052}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": 1751422754.7429821, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.714, "width_percent": 0.839}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": 1751422754.744628, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.554, "width_percent": 0.919}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": 1751422754.74679, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.473, "width_percent": 1.225}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100, 107)", "type": "query", "params": [], "bindings": [26, 100, 107], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422754.749101, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.698, "width_percent": 1.625}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100, 107) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [26, 100, 107], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422754.7514188, "duration": 0.00605, "duration_str": "6.05ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.323, "width_percent": 8.058}, {"sql": "select * from `projects` where `projects`.`id` in (161, 162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751422754.758498, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.381, "width_percent": 1.052}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.793094, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.433, "width_percent": 1.026}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.795245, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.459, "width_percent": 1.012}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.7969918, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.471, "width_percent": 0.773}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.808676, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.243, "width_percent": 1.066}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.8107018, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.309, "width_percent": 1.039}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.812584, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.348, "width_percent": 0.959}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.839532, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.307, "width_percent": 1.199}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.841573, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.506, "width_percent": 1.132}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.843626, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.638, "width_percent": 1.145}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.855639, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.783, "width_percent": 1.052}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.857668, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.835, "width_percent": 1.145}, {"sql": "select * from `milestones` where `project_id` = 162 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": 1751422754.8597438, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.981, "width_percent": 0.999}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 19 and `read_at` is null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": 1751422755.015912, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.98, "width_percent": 2.85}, {"sql": "select * from `app_notifications` where `user_id` = 19 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": 1751422755.02277, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.83, "width_percent": 0.892}, {"sql": "select * from `sessions` where `id` = 'BIEcqYBZP2qXWVcqZ5VJ1FXnyWVD2VmYX4lwIlz2' limit 1", "type": "query", "params": [], "bindings": ["BIEcqYBZP2qXWVcqZ5VJ1FXnyWVD2VmYX4lwIlz2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.565214, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.722, "width_percent": 0.759}, {"sql": "insert into `sessions` (`payload`, `last_activity`, `user_id`, `ip_address`, `user_agent`, `id`) values ('YTo1OntzOjY6Il90b2tlbiI7czo0MDoiYll4cmFOb1ZRMVdNTkw5NWxqZFNXQzhERDFCbHZLdllHbG1sdk1VbyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTk7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiQ0aTMvQkYxaEtIS1pHTWlYb05FeEJ1RkVOQTFyZmVFQUhXZHRKbTN5NEk0Q1pTeDBlUlNjVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDM6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9jbGllbnRzLzI2L2VkaXQiO319', **********, 19, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'BIEcqYBZP2qXWVcqZ5VJ1FXnyWVD2VmYX4lwIlz2')", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiYll4cmFOb1ZRMVdNTkw5NWxqZFNXQzhERDFCbHZLdllHbG1sdk1VbyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTk7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiQ0aTMvQkYxaEtIS1pHTWlYb05FeEJ1RkVOQTFyZmVFQUhXZHRKbTN5NEk0Q1pTeDBlUlNjVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDM6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9jbGllbnRzLzI2L2VkaXQiO319", **********, 19, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "BIEcqYBZP2qXWVcqZ5VJ1FXnyWVD2VmYX4lwIlz2"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 157}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 141}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.5666838, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:157", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=157", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "157"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.482, "width_percent": 1.518}]}, "models": {"data": {"App\\Models\\Role": {"value": 281, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Permission": {"value": 242, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 37, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Project": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\PricingModel": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Client": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}}, "count": 582, "is_counter": true}, "livewire": {"data": {"app.filament.resources.client-resource.pages.edit-client #oLxCXuItEWGMgB3WtXv0": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:16 [\n      \"id\" => 26\n      \"company_email\" => \"<EMAIL>\"\n      \"phone\" => null\n      \"address\" => null\n      \"contact_person\" => null\n      \"status\" => \"active\"\n      \"created_at\" => \"2025-07-01T12:10:48.000000Z\"\n      \"updated_at\" => \"2025-07-01T12:10:48.000000Z\"\n      \"company_name\" => \" Global FinServe LLP\"\n      \"tax_id\" => \"07GFPL9988B2Z3\"\n      \"registered_address\" => \"45, Connaught Place, New Delhi\"\n      \"official_email\" => null\n      \"company_number\" => \"+91-9654321987\"\n      \"personnel_details\" => array:1 [\n        \"ed1f8954-8db9-4b58-ba82-798f0b79b51f\" => array:7 [\n          \"name\" => \"<PERSON><PERSON><PERSON>\"\n          \"skype\" => null\n          \"department\" => \"Finance\"\n          \"designation\" => \"Finance Head\"\n          \"mobile_number\" => \"8899771122\"\n          \"official_email\" => \"<EMAIL>\"\n          \"whatsapp_number\" => \"8899771122\"\n        ]\n      ]\n      \"social_media_access\" => array:8 [\n        \"dc2ebf48-7231-4788-af07-75330aed3f87\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"instagram\"\n          \"username\" => null\n        ]\n        \"0bac7392-5929-4c05-add8-8117c7ee7145\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"youtube\"\n          \"username\" => null\n        ]\n        \"ffe50b7c-abfd-43c7-89e5-40fc2dc63446\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"twitter\"\n          \"username\" => null\n        ]\n        \"c45c0285-b2f7-4102-8bae-4b846500b7ac\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"website_gsa\"\n          \"username\" => null\n        ]\n        \"6cba474c-62ae-4dc6-90f0-3fed32f7c1f5\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"website_ga\"\n          \"username\" => null\n        ]\n        \"b5403610-3cfb-4f87-9379-bf391eeb8305\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"facebook\"\n          \"username\" => null\n        ]\n        \"1c0fb7f2-c63f-4318-9d6c-951880453836\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"gmb\"\n          \"username\" => null\n        ]\n        \"24f9bd5f-f6a6-4d24-b03b-77a15163ac02\" => array:3 [\n          \"password\" => null\n          \"platform\" => \"linkedin\"\n          \"username\" => null\n        ]\n      ]\n      \"created_by\" => 19\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/clients/26/edit\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => \"0\"\n    \"record\" => App\\Models\\Client {#2324\n      #connection: \"mysql\"\n      #table: \"clients\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #original: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"id\" => \"integer\"\n        \"personnel_details\" => \"array\"\n        \"social_media_access\" => \"array\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"company_name\"\n        1 => \"company_email\"\n        2 => \"phone\"\n        3 => \"address\"\n        4 => \"contact_person\"\n        5 => \"status\"\n        6 => \"tax_id\"\n        7 => \"registered_address\"\n        8 => \"official_email\"\n        9 => \"company_number\"\n        10 => \"personnel_details\"\n        11 => \"social_media_access\"\n        12 => \"created_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.client-resource.pages.edit-client\"\n  \"component\" => \"App\\Filament\\Resources\\ClientResource\\Pages\\EditClient\"\n  \"id\" => \"oLxCXuItEWGMgB3WtXv0\"\n]", "app.filament.resources.client-resource.relation-managers.projects-relation-manager #7Ea3ejXqzDuCy2wpIDJp": "array:4 [\n  \"data\" => array:42 [\n    \"tempGeneratedMilestones\" => null\n    \"tempGeneratedPayments\" => null\n    \"ownerRecord\" => App\\Models\\Client {#2324\n      #connection: \"mysql\"\n      #table: \"clients\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"<PERSON><PERSON><PERSON>\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #original: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"id\" => \"integer\"\n        \"personnel_details\" => \"array\"\n        \"social_media_access\" => \"array\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"company_name\"\n        1 => \"company_email\"\n        2 => \"phone\"\n        3 => \"address\"\n        4 => \"contact_person\"\n        5 => \"status\"\n        6 => \"tax_id\"\n        7 => \"registered_address\"\n        8 => \"official_email\"\n        9 => \"company_number\"\n        10 => \"personnel_details\"\n        11 => \"social_media_access\"\n        12 => \"created_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"pageClass\" => \"App\\Filament\\Resources\\ClientResource\\Pages\\EditClient\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeTab\" => null\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => array:1 [\n      \"product\" => array:1 [\n        \"title\" => false\n      ]\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => array:3 [\n      \"project_type_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"pricing_model_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.client-resource.relation-managers.projects-relation-manager\"\n  \"component\" => \"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\"\n  \"id\" => \"7Ea3ejXqzDuCy2wpIDJp\"\n]", "app.filament.resources.client-resource.relation-managers.milestones-relation-manager #2AWWLZ6rRKOUXyDsgiQk": "array:4 [\n  \"data\" => array:40 [\n    \"ownerRecord\" => App\\Models\\Client {#2324\n      #connection: \"mysql\"\n      #table: \"clients\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"<PERSON><PERSON><PERSON>\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #original: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"id\" => \"integer\"\n        \"personnel_details\" => \"array\"\n        \"social_media_access\" => \"array\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"company_name\"\n        1 => \"company_email\"\n        2 => \"phone\"\n        3 => \"address\"\n        4 => \"contact_person\"\n        5 => \"status\"\n        6 => \"tax_id\"\n        7 => \"registered_address\"\n        8 => \"official_email\"\n        9 => \"company_number\"\n        10 => \"personnel_details\"\n        11 => \"social_media_access\"\n        12 => \"created_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"pageClass\" => \"App\\Filament\\Resources\\ClientResource\\Pages\\EditClient\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeTab\" => null\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => null\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.client-resource.relation-managers.milestones-relation-manager\"\n  \"component\" => \"App\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager\"\n  \"id\" => \"2AWWLZ6rRKOUXyDsgiQk\"\n]", "filament.livewire.global-search #Sd0Ld9Pl9GXnwpHaPB5F": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"Sd0Ld9Pl9GXnwpHaPB5F\"\n]", "app.filament.widgets.notification-components.notification-bell #fGmWvvgisAPazZ5408wA": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 0\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"fGmWvvgisAPazZ5408wA\"\n]", "filament.livewire.notifications #IU0IwrBOzTsOYz76hCtt": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3702\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"IU0IwrBOzTsOYz76hCtt\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 103, "messages": [{"message": "[\n  ability => update_client,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1752311471 data-indent-pad=\"  \"><span class=sf-dump-note>update_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">update_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752311471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414969, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Client(id=26),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Client)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1682641493 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Client(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\Client(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; Object(App\\Models\\Client)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682641493\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415923, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-648171560 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648171560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.503238, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-742231949 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742231949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.503417, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1295787275 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295787275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.512309, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-195745635 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195745635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.512491, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-765710370 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765710370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.517211, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1575323704 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575323704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.517436, "xdebug_link": null}, {"message": "[\n  ability => create_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582542238 data-indent-pad=\"  \"><span class=sf-dump-note>create_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582542238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.380826, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647489674 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647489674\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.381004, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-887588131 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887588131\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.387103, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989378178 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989378178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.387261, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-408182396 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408182396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.390317, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1928870591 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928870591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.390489, "xdebug_link": null}, {"message": "[\n  ability => create_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1372391138 data-indent-pad=\"  \"><span class=sf-dump-note>create_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372391138\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.610534, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1498521284 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498521284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.610765, "xdebug_link": null}, {"message": "[\n  ability => create_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1611074718 data-indent-pad=\"  \"><span class=sf-dump-note>create_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611074718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.615053, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989546355 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989546355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.615221, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-619626269 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619626269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.622281, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2088548892 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088548892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.622496, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1903037731 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903037731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.628519, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2048007066 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048007066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.628752, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-845407435 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845407435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.642822, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-872211811 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872211811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422750.643004, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1736078777 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736078777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.502921, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1457637495 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457637495\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.503157, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-798351925 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798351925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.507654, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1948865856 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948865856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.507847, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-396363225 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396363225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.552849, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-29517615 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29517615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.553044, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-334744978 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334744978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.557159, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-152466766 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152466766\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.557423, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-553280831 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553280831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.571196, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-746785038 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746785038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.571402, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-733843836 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733843836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.581146, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=161),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-263958136 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=161)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=161)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263958136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.581351, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1165609987 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165609987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.591995, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1007764850 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007764850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.592207, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-561881573 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561881573\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.596619, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1437817187 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437817187\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.596836, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-555914438 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555914438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.626961, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1307514101 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307514101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.627148, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1323424095 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323424095\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.632263, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1375352263 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375352263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.632542, "xdebug_link": null}, {"message": "[\n  ability => update_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2137633087 data-indent-pad=\"  \"><span class=sf-dump-note>update_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137633087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.644321, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-457923144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457923144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.644564, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852264264 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852264264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.653802, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=162),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-40849590 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=162)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=162)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40849590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.654025, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2079499692 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2079499692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.800332, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-895587460 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895587460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.800583, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1994661655 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994661655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.816166, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-242980118 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242980118\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.816366, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1174117415 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174117415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.847487, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-34111374 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34111374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.84769, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1601071226 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601071226\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.863357, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-906973319 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906973319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.863569, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1704160942 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704160942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.928243, "xdebug_link": null}, {"message": "[\n  ability => view_any_app::notification,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1888852233 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">view_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888852233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.932277, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-814994196 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814994196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.93254, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2113650907 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113650907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.933475, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-367752266 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367752266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.935332, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1123872643 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123872643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.935497, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-856605311 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856605311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.936631, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1486906531 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486906531\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.939377, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-634806622 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634806622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.939544, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive::rule,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-103204562 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive::rule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_incentive::rule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103204562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.943031, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1398361035 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398361035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.943189, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-635224206 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635224206\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.944331, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1016452786 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016452786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.946003, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-839059343 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839059343\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.946163, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::event,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-231045072 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">view_any_notification::event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231045072\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.949572, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2127186823 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127186823\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.949729, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::role::preference,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1609691157 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::role::preference </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"39 characters\">view_any_notification::role::preference</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609691157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.953277, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-948199768 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948199768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.953444, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861106997 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861106997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.95427, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2075721888 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075721888\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.956687, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1906572023 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906572023\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.956865, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-904337800 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904337800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.957957, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1905961095 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905961095\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.959849, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-739017755 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739017755\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.960053, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-325368441 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325368441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.963759, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1900749377 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900749377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.963931, "xdebug_link": null}, {"message": "[\n  ability => view_any_product,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1511536264 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511536264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.967215, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-474787526 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474787526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.9674, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309618713 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309618713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.968203, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1622000949 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622000949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.97004, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-618278656 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618278656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.970196, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::status::log,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1522421026 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::status::log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">view_any_project::status::log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522421026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.974072, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1811627403 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811627403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422754.974274, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::type,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-121705527 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::type </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_project::type</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121705527\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.977197, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1152723334 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152723334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.977403, "xdebug_link": null}, {"message": "[\n  ability => view_any_role::notification::settings,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1130347865 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role::notification::settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view_any_role::notification::settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130347865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.980812, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2095319262 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095319262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.980998, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2056795952 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056795952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.983753, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1611183862 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611183862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.983926, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-674856220 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674856220\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.987612, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505767769 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505767769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.987821, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-728307257 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728307257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.991803, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 19,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-180385807 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180385807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422754.991976, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-567502387 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567502387\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422755.001846, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-547728989 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547728989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751422755.00221, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1341094081 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341094081\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422755.004516, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1880690354 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880690354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751422755.004686, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/clients/26/edit", "action_name": "filament.admin.resources.clients.edit", "controller_action": "App\\Filament\\Resources\\ClientResource\\Pages\\EditClient", "uri": "GET admin/clients/{record}/edit", "controller": "App\\Filament\\Resources\\ClientResource\\Pages\\EditClient@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/clients", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "18.32s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"896 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFCYTVXZHdYUjdjNE1uVkFnVjlqcGc9PSIsInZhbHVlIjoiTnVlWWdmVDZraFBFWHNuc0VBVFNhK3lvNGtpd2V0cENSOWJQZ3pNWlBVbFRyOU9aRS9vVTA3OFFKYnBBLzN3bDR1YlJUcG9RckIrSDFXa3RUeU91b25ZN0p2a0N5UWtZVUtTNnBGZ1FTSEludk5HWUtTUURncFFrdFVyZXBUUGZhaGNXbEphL0JXK3lESWY5VnFHVkt0V0MvZDk0c1h4MTVnMEF5OUtEVzFmUFA5Y1hvdU00eG84SFRLZDd1MzRidnR0RFVSQkdSTVdmZEpMZis5TVJYNkhOamM2UnJCQldpa2tmeVBXbzNxOD0iLCJtYWMiOiI5NDY3NWVkYzY3M2IwYmRlNjA4MmY1YjY2YWIxZDExMTg0NzY0Y2ExNTBhNmEwYTgxNDdjY2MwOGI0NjJhMjg4IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImpzdXNlaEhWR1Fiamh6U2t3ZUYvUkE9PSIsInZhbHVlIjoia0JVb1VBNTlWWUtyNXBjc0M5QmlxQ1BOQlRuRG8rVUNaSVhTbmdiOHUzS2VCSmx6NUc4aEFwRHdPY3hGRHZ2MTVaR3pxNTVhcjhZb2l5VWR0cUlDbDJMNXZIbFY2QXI3dm9la011ODVwcjduMkJZMUNuM1ozb1lqekVZaG00QjgiLCJtYWMiOiJiOTBmNDI2YTAzYjliMzY4Nzk1YTBkNjEwNGQxNmZiY2FhZDZhYWU2ZjJhOWIyZTlhNGFlYjM3MmFmOGE2NDE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-485876871 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|Gkx81hLUil4MDhe2uFzUzCfsIebikPpqVYwj58Dl98Kha2jZhNtTChXt3mHw|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U8YG10c3tOYW3leiFdTMUXCwzGy2gIrark8QAgjO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485876871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 02:19:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im8rZzZsVkRKUU0zSHZjamxqeTkzVWc9PSIsInZhbHVlIjoiWnBObWdqWDlNa1FMd1hPZkZiaWRzOVhXQlFGVmhmY2JnOE1Xd3VjcUZRcjdNd21ZUit6VURYem1GUUpndno5LzdQRTcvZVM2RldmTTVzK3RHRjNaNmR3dE1Wa042L3d1Umd6ell2N0QvdUpEK0d2d0w3N1BpOEM4RkMzOXY3ajAiLCJtYWMiOiI5NDAyMDhhYjQ5NjMxNjc1ZDYyNjkxY2RmMTkyN2ExZjYyMzAyMDkzZjdjNzUwZGI4ZTc0MzZhZTEyNzI5Y2RkIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 04:19:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IjdycGtDSFJFdDl5Qkl5a1JQdkkzSXc9PSIsInZhbHVlIjoiMmREbDFRUVFrQndRamlScGtrSnJaQnkxdnhSTHIzSDlxTkZ0a2R5UTRjTWpjakJyUGhFNTJ5UVVVTnNxNEViYjlRMDVheGsyelFlOVpyZFJnbzlma3FqM2lGellMemdxWTdiWW5mS0pHa0NkWjM2Vm5lblh3K3ZMSHhjZVhTQ2QiLCJtYWMiOiI3OGQxMmNiMTgxMDgyNmNhNWM1ZjNlOTBkNjRjNDZkMTkxM2U0NDZhMjY3ZjE4ZGQ3MjE5MDUyZDNmOWI3YjQwIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 04:19:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im8rZzZsVkRKUU0zSHZjamxqeTkzVWc9PSIsInZhbHVlIjoiWnBObWdqWDlNa1FMd1hPZkZiaWRzOVhXQlFGVmhmY2JnOE1Xd3VjcUZRcjdNd21ZUit6VURYem1GUUpndno5LzdQRTcvZVM2RldmTTVzK3RHRjNaNmR3dE1Wa042L3d1Umd6ell2N0QvdUpEK0d2d0w3N1BpOEM4RkMzOXY3ajAiLCJtYWMiOiI5NDAyMDhhYjQ5NjMxNjc1ZDYyNjkxY2RmMTkyN2ExZjYyMzAyMDkzZjdjNzUwZGI4ZTc0MzZhZTEyNzI5Y2RkIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 04:19:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IjdycGtDSFJFdDl5Qkl5a1JQdkkzSXc9PSIsInZhbHVlIjoiMmREbDFRUVFrQndRamlScGtrSnJaQnkxdnhSTHIzSDlxTkZ0a2R5UTRjTWpjakJyUGhFNTJ5UVVVTnNxNEViYjlRMDVheGsyelFlOVpyZFJnbzlma3FqM2lGellMemdxWTdiWW5mS0pHa0NkWjM2Vm5lblh3K3ZMSHhjZVhTQ2QiLCJtYWMiOiI3OGQxMmNiMTgxMDgyNmNhNWM1ZjNlOTBkNjRjNDZkMTkxM2U0NDZhMjY3ZjE4ZGQ3MjE5MDUyZDNmOWI3YjQwIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 04:19:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-897517419 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bYxraNoVQ1WMNL95ljdSWC8DD1BlvKvYGlmlvMUo</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897517419\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/clients/26/edit", "action_name": "filament.admin.resources.clients.edit", "controller_action": "App\\Filament\\Resources\\ClientResource\\Pages\\EditClient"}, "badge": null}}