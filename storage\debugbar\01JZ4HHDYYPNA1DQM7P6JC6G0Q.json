{"__meta": {"id": "01JZ4HHDYYPNA1DQM7P6JC6G0Q", "datetime": "2025-07-02 03:05:29", "utime": **********.823957, "method": "GET", "uri": "/admin/projects", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[03:05:04] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/projects\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.022191, "xdebug_link": null, "collector": "log"}, {"message": "[03:05:29] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 4,\n    \"roles\": [\n        \"bde_team\"\n    ],\n    \"current_path\": \"admin\\/projects\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.815184, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751425500.232813, "end": **********.823984, "duration": 29.59117102622986, "duration_str": "29.59s", "measures": [{"label": "Booting", "start": 1751425500.232813, "relative_start": 0, "end": **********.913504, "relative_end": **********.913504, "duration": 1.****************, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.913523, "relative_start": 1.****************, "end": **********.823987, "relative_end": 3.0994415283203125e-06, "duration": 27.**************, "duration_str": "27.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.892445, "relative_start": 3.****************, "end": **********.906097, "relative_end": **********.906097, "duration": 0.013651847839355469, "duration_str": "13.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.683514, "relative_start": 11.***************, "end": **********.683514, "relative_end": **********.683514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.969135, "relative_start": 14.***************, "end": **********.969135, "relative_end": **********.969135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425520.449211, "relative_start": 20.216398000717163, "end": 1751425520.449211, "relative_end": 1751425520.449211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425521.633999, "relative_start": 21.401186227798462, "end": 1751425521.633999, "relative_end": 1751425521.633999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425521.648229, "relative_start": 21.41541600227356, "end": 1751425521.648229, "relative_end": 1751425521.648229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425521.661255, "relative_start": 21.428442001342773, "end": 1751425521.661255, "relative_end": 1751425521.661255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425522.201415, "relative_start": 21.968602180480957, "end": 1751425522.201415, "relative_end": 1751425522.201415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425522.207113, "relative_start": 21.974300146102905, "end": 1751425522.207113, "relative_end": 1751425522.207113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425522.213977, "relative_start": 21.98116421699524, "end": 1751425522.213977, "relative_end": 1751425522.213977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751425522.218516, "relative_start": 21.985703229904175, "end": 1751425522.218516, "relative_end": 1751425522.218516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751425522.42425, "relative_start": 22.191437005996704, "end": 1751425522.42425, "relative_end": 1751425522.42425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": 1751425522.915234, "relative_start": 22.68242120742798, "end": 1751425522.915234, "relative_end": 1751425522.915234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425526.040804, "relative_start": 25.80799102783203, "end": 1751425526.040804, "relative_end": 1751425526.040804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425526.448763, "relative_start": 26.21595001220703, "end": 1751425526.448763, "relative_end": 1751425526.448763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425526.459696, "relative_start": 26.22688317298889, "end": 1751425526.459696, "relative_end": 1751425526.459696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425527.02437, "relative_start": 26.79155707359314, "end": 1751425527.02437, "relative_end": 1751425527.02437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425527.060892, "relative_start": 26.828079223632812, "end": 1751425527.060892, "relative_end": 1751425527.060892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751425527.070888, "relative_start": 26.838075160980225, "end": 1751425527.070888, "relative_end": 1751425527.070888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.584931, "relative_start": 29.352118015289307, "end": **********.584931, "relative_end": **********.584931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.721687, "relative_start": 29.488874197006226, "end": **********.721687, "relative_end": **********.721687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.726703, "relative_start": 29.493890047073364, "end": **********.726703, "relative_end": **********.726703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.728279, "relative_start": 29.495466232299805, "end": **********.728279, "relative_end": **********.728279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.732286, "relative_start": 29.499473094940186, "end": **********.732286, "relative_end": **********.732286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.733546, "relative_start": 29.500733137130737, "end": **********.733546, "relative_end": **********.733546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.790396, "relative_start": 29.55758309364319, "end": **********.790396, "relative_end": **********.790396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.79503, "relative_start": 29.562217235565186, "end": **********.79503, "relative_end": **********.79503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.808023, "relative_start": 29.575210094451904, "end": **********.808023, "relative_end": **********.808023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.809271, "relative_start": 29.5764582157135, "end": **********.809271, "relative_end": **********.809271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.810351, "relative_start": 29.577538013458252, "end": **********.810351, "relative_end": **********.810351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.81446, "relative_start": 29.581647157669067, "end": **********.814658, "relative_end": **********.814658, "duration": 0.00019788742065429688, "duration_str": "198μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.819547, "relative_start": 29.58673405647278, "end": **********.820278, "relative_end": **********.820278, "duration": 0.0007309913635253906, "duration_str": "731μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 58686368, "peak_usage_str": "56MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 29, "nb_templates": 29, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.683483, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.969105, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425520.449048, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425521.633978, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425521.648207, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425521.661231, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425522.201392, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425522.207092, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425522.213956, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751425522.218496, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1751425522.424229, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751425522.915196, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425526.040755, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425526.448714, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425526.459668, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425527.024341, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425527.060863, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751425527.070845, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.5849, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.72166, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.726682, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.72826, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.732267, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.733528, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.790348, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.795007, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.808002, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.80925, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.810335, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 27, "nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.037249999999999985, "accumulated_duration_str": "37.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'sh4D97DpG3HRcFFPDYhBMHuhwQMvAM5aunnjXeuU' limit 1", "type": "query", "params": [], "bindings": ["sh4D97DpG3HRcFFPDYhBMHuhwQMvAM5aunnjXeuU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.918376, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.04}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.942973, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.04, "width_percent": 2.443}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.948201, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.483, "width_percent": 1.96}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9626331, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.443, "width_percent": 1.772}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9837542, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.215, "width_percent": 1.664}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.992911, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.879, "width_percent": 1.799}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.9960642, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.678, "width_percent": 1.745}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.997814, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.423, "width_percent": 1.852}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.013006, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.275, "width_percent": 1.96}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.015131, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.235, "width_percent": 1.584}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.0401099, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.819, "width_percent": 5.235}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.047128, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.054, "width_percent": 1.611}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.049185, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.664, "width_percent": 9.477}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.063656, "duration": 0.00647, "duration_str": "6.47ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.141, "width_percent": 17.369}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1751511904, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:242:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:22:\\\"view_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:26:\\\"view_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:24:\\\"create_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:24:\\\"update_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:25:\\\"restore_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:29:\\\"restore_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:27:\\\"replicate_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:25:\\\"reorder_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:24:\\\"delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:28:\\\"delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:30:\\\"force_delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:34:\\\"force_delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:11:\\\"view_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:15:\\\"view_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:13:\\\"create_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:13:\\\"update_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:14:\\\"restore_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:18:\\\"restore_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:16:\\\"replicate_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:14:\\\"reorder_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:13:\\\"delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:17:\\\"delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:19:\\\"force_delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:23:\\\"force_delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:14:\\\"view_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"view_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"create_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:16:\\\"update_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:17:\\\"restore_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:21:\\\"restore_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:19:\\\"replicate_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:17:\\\"reorder_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:16:\\\"delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:20:\\\"delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:22:\\\"force_delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:26:\\\"force_delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:20:\\\"view_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:24:\\\"view_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:22:\\\"create_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:22:\\\"update_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:23:\\\"restore_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:27:\\\"restore_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:25:\\\"replicate_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:23:\\\"reorder_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:22:\\\"delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:26:\\\"delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:28:\\\"force_delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:32:\\\"force_delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:14:\\\"view_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:18:\\\"view_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"create_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:16:\\\"update_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:17:\\\"restore_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:21:\\\"restore_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:19:\\\"replicate_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:17:\\\"reorder_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:16:\\\"delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:20:\\\"delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:22:\\\"force_delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:26:\\\"force_delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:24:\\\"view_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:61;a:4:{s:1:\\\"a\\\";i:62;s:1:\\\"b\\\";s:28:\\\"view_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:62;a:4:{s:1:\\\"a\\\";i:63;s:1:\\\"b\\\";s:26:\\\"create_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:63;a:4:{s:1:\\\"a\\\";i:64;s:1:\\\"b\\\";s:26:\\\"update_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:64;a:4:{s:1:\\\"a\\\";i:65;s:1:\\\"b\\\";s:27:\\\"restore_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:65;a:4:{s:1:\\\"a\\\";i:66;s:1:\\\"b\\\";s:31:\\\"restore_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:66;a:4:{s:1:\\\"a\\\";i:67;s:1:\\\"b\\\";s:29:\\\"replicate_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:67;a:4:{s:1:\\\"a\\\";i:68;s:1:\\\"b\\\";s:27:\\\"reorder_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:68;a:4:{s:1:\\\"a\\\";i:69;s:1:\\\"b\\\";s:26:\\\"delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:69;a:4:{s:1:\\\"a\\\";i:70;s:1:\\\"b\\\";s:30:\\\"delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:70;a:4:{s:1:\\\"a\\\";i:71;s:1:\\\"b\\\";s:32:\\\"force_delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:71;a:4:{s:1:\\\"a\\\";i:72;s:1:\\\"b\\\";s:36:\\\"force_delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:72;a:4:{s:1:\\\"a\\\";i:73;s:1:\\\"b\\\";s:35:\\\"view_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:73;a:4:{s:1:\\\"a\\\";i:74;s:1:\\\"b\\\";s:39:\\\"view_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:74;a:4:{s:1:\\\"a\\\";i:75;s:1:\\\"b\\\";s:37:\\\"create_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:75;a:4:{s:1:\\\"a\\\";i:76;s:1:\\\"b\\\";s:37:\\\"update_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:76;a:4:{s:1:\\\"a\\\";i:77;s:1:\\\"b\\\";s:38:\\\"restore_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:77;a:4:{s:1:\\\"a\\\";i:78;s:1:\\\"b\\\";s:42:\\\"restore_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:78;a:4:{s:1:\\\"a\\\";i:79;s:1:\\\"b\\\";s:40:\\\"replicate_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:79;a:4:{s:1:\\\"a\\\";i:80;s:1:\\\"b\\\";s:38:\\\"reorder_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:80;a:4:{s:1:\\\"a\\\";i:81;s:1:\\\"b\\\";s:37:\\\"delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:81;a:4:{s:1:\\\"a\\\";i:82;s:1:\\\"b\\\";s:41:\\\"delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:82;a:4:{s:1:\\\"a\\\";i:83;s:1:\\\"b\\\";s:43:\\\"force_delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:83;a:4:{s:1:\\\"a\\\";i:84;s:1:\\\"b\\\";s:47:\\\"force_delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:84;a:4:{s:1:\\\"a\\\";i:85;s:1:\\\"b\\\";s:12:\\\"view_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\\\"a\\\";i:86;s:1:\\\"b\\\";s:16:\\\"view_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\\\"a\\\";i:87;s:1:\\\"b\\\";s:14:\\\"create_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\\\"a\\\";i:88;s:1:\\\"b\\\";s:14:\\\"update_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\\\"a\\\";i:89;s:1:\\\"b\\\";s:15:\\\"restore_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:89;a:4:{s:1:\\\"a\\\";i:90;s:1:\\\"b\\\";s:19:\\\"restore_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:90;a:4:{s:1:\\\"a\\\";i:91;s:1:\\\"b\\\";s:17:\\\"replicate_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:91;a:4:{s:1:\\\"a\\\";i:92;s:1:\\\"b\\\";s:15:\\\"reorder_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:92;a:4:{s:1:\\\"a\\\";i:93;s:1:\\\"b\\\";s:14:\\\"delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\\\"a\\\";i:94;s:1:\\\"b\\\";s:18:\\\"delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\\\"a\\\";i:95;s:1:\\\"b\\\";s:20:\\\"force_delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:95;a:4:{s:1:\\\"a\\\";i:96;s:1:\\\"b\\\";s:24:\\\"force_delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:96;a:4:{s:1:\\\"a\\\";i:97;s:1:\\\"b\\\";s:12:\\\"view_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\\\"a\\\";i:98;s:1:\\\"b\\\";s:16:\\\"view_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\\\"a\\\";i:99;s:1:\\\"b\\\";s:14:\\\"create_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\\\"a\\\";i:100;s:1:\\\"b\\\";s:14:\\\"update_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\\\"a\\\";i:101;s:1:\\\"b\\\";s:15:\\\"restore_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:101;a:4:{s:1:\\\"a\\\";i:102;s:1:\\\"b\\\";s:19:\\\"restore_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:102;a:4:{s:1:\\\"a\\\";i:103;s:1:\\\"b\\\";s:17:\\\"replicate_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:103;a:4:{s:1:\\\"a\\\";i:104;s:1:\\\"b\\\";s:15:\\\"reorder_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:104;a:4:{s:1:\\\"a\\\";i:105;s:1:\\\"b\\\";s:14:\\\"delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\\\"a\\\";i:106;s:1:\\\"b\\\";s:18:\\\"delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\\\"a\\\";i:107;s:1:\\\"b\\\";s:20:\\\"force_delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:107;a:4:{s:1:\\\"a\\\";i:108;s:1:\\\"b\\\";s:24:\\\"force_delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:108;a:4:{s:1:\\\"a\\\";i:109;s:1:\\\"b\\\";s:18:\\\"view_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:109;a:4:{s:1:\\\"a\\\";i:110;s:1:\\\"b\\\";s:22:\\\"view_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:110;a:4:{s:1:\\\"a\\\";i:111;s:1:\\\"b\\\";s:20:\\\"create_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:111;a:4:{s:1:\\\"a\\\";i:112;s:1:\\\"b\\\";s:20:\\\"update_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:112;a:4:{s:1:\\\"a\\\";i:113;s:1:\\\"b\\\";s:21:\\\"restore_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:113;a:4:{s:1:\\\"a\\\";i:114;s:1:\\\"b\\\";s:25:\\\"restore_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:114;a:4:{s:1:\\\"a\\\";i:115;s:1:\\\"b\\\";s:23:\\\"replicate_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:115;a:4:{s:1:\\\"a\\\";i:116;s:1:\\\"b\\\";s:21:\\\"reorder_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:116;a:4:{s:1:\\\"a\\\";i:117;s:1:\\\"b\\\";s:20:\\\"delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:117;a:4:{s:1:\\\"a\\\";i:118;s:1:\\\"b\\\";s:24:\\\"delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:118;a:4:{s:1:\\\"a\\\";i:119;s:1:\\\"b\\\";s:26:\\\"force_delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:119;a:4:{s:1:\\\"a\\\";i:120;s:1:\\\"b\\\";s:30:\\\"force_delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:120;a:4:{s:1:\\\"a\\\";i:121;s:1:\\\"b\\\";s:9:\\\"view_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:121;a:4:{s:1:\\\"a\\\";i:122;s:1:\\\"b\\\";s:13:\\\"view_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:122;a:4:{s:1:\\\"a\\\";i:123;s:1:\\\"b\\\";s:11:\\\"create_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:123;a:4:{s:1:\\\"a\\\";i:124;s:1:\\\"b\\\";s:11:\\\"update_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:124;a:4:{s:1:\\\"a\\\";i:125;s:1:\\\"b\\\";s:11:\\\"delete_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:125;a:4:{s:1:\\\"a\\\";i:126;s:1:\\\"b\\\";s:15:\\\"delete_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:126;a:4:{s:1:\\\"a\\\";i:127;s:1:\\\"b\\\";s:33:\\\"view_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:127;a:4:{s:1:\\\"a\\\";i:128;s:1:\\\"b\\\";s:37:\\\"view_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:128;a:4:{s:1:\\\"a\\\";i:129;s:1:\\\"b\\\";s:35:\\\"create_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:129;a:4:{s:1:\\\"a\\\";i:130;s:1:\\\"b\\\";s:35:\\\"update_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:130;a:4:{s:1:\\\"a\\\";i:131;s:1:\\\"b\\\";s:36:\\\"restore_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:131;a:4:{s:1:\\\"a\\\";i:132;s:1:\\\"b\\\";s:40:\\\"restore_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:132;a:4:{s:1:\\\"a\\\";i:133;s:1:\\\"b\\\";s:38:\\\"replicate_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:133;a:4:{s:1:\\\"a\\\";i:134;s:1:\\\"b\\\";s:36:\\\"reorder_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:134;a:4:{s:1:\\\"a\\\";i:135;s:1:\\\"b\\\";s:35:\\\"delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:135;a:4:{s:1:\\\"a\\\";i:136;s:1:\\\"b\\\";s:39:\\\"delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:136;a:4:{s:1:\\\"a\\\";i:137;s:1:\\\"b\\\";s:41:\\\"force_delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:137;a:4:{s:1:\\\"a\\\";i:138;s:1:\\\"b\\\";s:45:\\\"force_delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:138;a:4:{s:1:\\\"a\\\";i:139;s:1:\\\"b\\\";s:10:\\\"view_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:139;a:4:{s:1:\\\"a\\\";i:140;s:1:\\\"b\\\";s:14:\\\"view_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:140;a:4:{s:1:\\\"a\\\";i:141;s:1:\\\"b\\\";s:12:\\\"create_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:141;a:4:{s:1:\\\"a\\\";i:142;s:1:\\\"b\\\";s:12:\\\"update_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:142;a:4:{s:1:\\\"a\\\";i:143;s:1:\\\"b\\\";s:13:\\\"restore_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:143;a:4:{s:1:\\\"a\\\";i:144;s:1:\\\"b\\\";s:17:\\\"restore_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:144;a:4:{s:1:\\\"a\\\";i:145;s:1:\\\"b\\\";s:15:\\\"replicate_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:145;a:4:{s:1:\\\"a\\\";i:146;s:1:\\\"b\\\";s:13:\\\"reorder_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:146;a:4:{s:1:\\\"a\\\";i:147;s:1:\\\"b\\\";s:12:\\\"delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:147;a:4:{s:1:\\\"a\\\";i:148;s:1:\\\"b\\\";s:16:\\\"delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:148;a:4:{s:1:\\\"a\\\";i:149;s:1:\\\"b\\\";s:18:\\\"force_delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:149;a:4:{s:1:\\\"a\\\";i:150;s:1:\\\"b\\\";s:22:\\\"force_delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:150;a:4:{s:1:\\\"a\\\";i:151;s:1:\\\"b\\\";s:9:\\\"view_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:151;a:4:{s:1:\\\"a\\\";i:152;s:1:\\\"b\\\";s:13:\\\"view_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:152;a:4:{s:1:\\\"a\\\";i:153;s:1:\\\"b\\\";s:11:\\\"create_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:153;a:4:{s:1:\\\"a\\\";i:154;s:1:\\\"b\\\";s:11:\\\"update_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:154;a:4:{s:1:\\\"a\\\";i:155;s:1:\\\"b\\\";s:12:\\\"restore_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:155;a:4:{s:1:\\\"a\\\";i:156;s:1:\\\"b\\\";s:16:\\\"restore_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:156;a:4:{s:1:\\\"a\\\";i:157;s:1:\\\"b\\\";s:14:\\\"replicate_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:157;a:4:{s:1:\\\"a\\\";i:158;s:1:\\\"b\\\";s:12:\\\"reorder_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:158;a:4:{s:1:\\\"a\\\";i:159;s:1:\\\"b\\\";s:11:\\\"delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:159;a:4:{s:1:\\\"a\\\";i:160;s:1:\\\"b\\\";s:15:\\\"delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:160;a:4:{s:1:\\\"a\\\";i:161;s:1:\\\"b\\\";s:17:\\\"force_delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:161;a:4:{s:1:\\\"a\\\";i:162;s:1:\\\"b\\\";s:21:\\\"force_delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:162;a:4:{s:1:\\\"a\\\";i:163;s:1:\\\"b\\\";s:17:\\\"page_BdeDashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\\\"a\\\";i:164;s:1:\\\"b\\\";s:22:\\\"page_DashboardSettings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:164;a:4:{s:1:\\\"a\\\";i:165;s:1:\\\"b\\\";s:18:\\\"page_ManageSetting\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:165;a:4:{s:1:\\\"a\\\";i:166;s:1:\\\"b\\\";s:11:\\\"page_Themes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:166;a:4:{s:1:\\\"a\\\";i:167;s:1:\\\"b\\\";s:18:\\\"page_MyProfilePage\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\\\"a\\\";i:168;s:1:\\\"b\\\";s:26:\\\"widget_NotificationsWidget\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:168;a:4:{s:1:\\\"a\\\";i:169;s:1:\\\"b\\\";s:9:\\\"view_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:169;a:4:{s:1:\\\"a\\\";i:170;s:1:\\\"b\\\";s:13:\\\"view_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:170;a:4:{s:1:\\\"a\\\";i:171;s:1:\\\"b\\\";s:11:\\\"create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:171;a:4:{s:1:\\\"a\\\";i:172;s:1:\\\"b\\\";s:11:\\\"update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:172;a:4:{s:1:\\\"a\\\";i:173;s:1:\\\"b\\\";s:12:\\\"restore_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:173;a:4:{s:1:\\\"a\\\";i:174;s:1:\\\"b\\\";s:16:\\\"restore_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:174;a:4:{s:1:\\\"a\\\";i:175;s:1:\\\"b\\\";s:14:\\\"replicate_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:175;a:4:{s:1:\\\"a\\\";i:176;s:1:\\\"b\\\";s:12:\\\"reorder_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:176;a:4:{s:1:\\\"a\\\";i:177;s:1:\\\"b\\\";s:11:\\\"delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:177;a:4:{s:1:\\\"a\\\";i:178;s:1:\\\"b\\\";s:15:\\\"delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:178;a:4:{s:1:\\\"a\\\";i:179;s:1:\\\"b\\\";s:17:\\\"force_delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:179;a:4:{s:1:\\\"a\\\";i:180;s:1:\\\"b\\\";s:21:\\\"force_delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:180;a:4:{s:1:\\\"a\\\";i:181;s:1:\\\"b\\\";s:16:\\\"book:create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:181;a:4:{s:1:\\\"a\\\";i:182;s:1:\\\"b\\\";s:16:\\\"book:update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:182;a:4:{s:1:\\\"a\\\";i:183;s:1:\\\"b\\\";s:16:\\\"book:delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:183;a:4:{s:1:\\\"a\\\";i:184;s:1:\\\"b\\\";s:20:\\\"book:pagination_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:184;a:4:{s:1:\\\"a\\\";i:185;s:1:\\\"b\\\";s:16:\\\"book:detail_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:185;a:4:{s:1:\\\"a\\\";i:186;s:1:\\\"b\\\";s:14:\\\"view_dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:186;a:4:{s:1:\\\"a\\\";i:187;s:1:\\\"b\\\";s:18:\\\"page_Bde_Dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:187;a:4:{s:1:\\\"a\\\";i:188;s:1:\\\"b\\\";s:11:\\\"export_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:188;a:4:{s:1:\\\"a\\\";i:189;s:1:\\\"b\\\";s:12:\\\"view_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:189;a:4:{s:1:\\\"a\\\";i:190;s:1:\\\"b\\\";s:16:\\\"view_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:190;a:4:{s:1:\\\"a\\\";i:191;s:1:\\\"b\\\";s:14:\\\"create_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:191;a:4:{s:1:\\\"a\\\";i:192;s:1:\\\"b\\\";s:14:\\\"update_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:192;a:4:{s:1:\\\"a\\\";i:193;s:1:\\\"b\\\";s:15:\\\"restore_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:193;a:4:{s:1:\\\"a\\\";i:194;s:1:\\\"b\\\";s:19:\\\"restore_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:194;a:4:{s:1:\\\"a\\\";i:195;s:1:\\\"b\\\";s:17:\\\"replicate_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:195;a:4:{s:1:\\\"a\\\";i:196;s:1:\\\"b\\\";s:15:\\\"reorder_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:196;a:4:{s:1:\\\"a\\\";i:197;s:1:\\\"b\\\";s:14:\\\"delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:197;a:4:{s:1:\\\"a\\\";i:198;s:1:\\\"b\\\";s:18:\\\"delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:198;a:4:{s:1:\\\"a\\\";i:199;s:1:\\\"b\\\";s:20:\\\"force_delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:199;a:4:{s:1:\\\"a\\\";i:200;s:1:\\\"b\\\";s:24:\\\"force_delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:200;a:4:{s:1:\\\"a\\\";i:201;s:1:\\\"b\\\";s:9:\\\"view_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:201;a:4:{s:1:\\\"a\\\";i:202;s:1:\\\"b\\\";s:13:\\\"view_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:202;a:4:{s:1:\\\"a\\\";i:203;s:1:\\\"b\\\";s:11:\\\"create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:203;a:4:{s:1:\\\"a\\\";i:204;s:1:\\\"b\\\";s:11:\\\"update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:204;a:4:{s:1:\\\"a\\\";i:205;s:1:\\\"b\\\";s:12:\\\"restore_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:205;a:4:{s:1:\\\"a\\\";i:206;s:1:\\\"b\\\";s:16:\\\"restore_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:206;a:4:{s:1:\\\"a\\\";i:207;s:1:\\\"b\\\";s:14:\\\"replicate_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:207;a:4:{s:1:\\\"a\\\";i:208;s:1:\\\"b\\\";s:12:\\\"reorder_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:208;a:4:{s:1:\\\"a\\\";i:209;s:1:\\\"b\\\";s:11:\\\"delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:209;a:4:{s:1:\\\"a\\\";i:210;s:1:\\\"b\\\";s:15:\\\"delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:210;a:4:{s:1:\\\"a\\\";i:211;s:1:\\\"b\\\";s:17:\\\"force_delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:211;a:4:{s:1:\\\"a\\\";i:212;s:1:\\\"b\\\";s:21:\\\"force_delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:212;a:4:{s:1:\\\"a\\\";i:213;s:1:\\\"b\\\";s:16:\\\"post:create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:213;a:4:{s:1:\\\"a\\\";i:214;s:1:\\\"b\\\";s:16:\\\"post:update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:214;a:4:{s:1:\\\"a\\\";i:215;s:1:\\\"b\\\";s:16:\\\"post:delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:215;a:4:{s:1:\\\"a\\\";i:216;s:1:\\\"b\\\";s:20:\\\"post:pagination_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:216;a:4:{s:1:\\\"a\\\";i:217;s:1:\\\"b\\\";s:16:\\\"post:detail_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:217;a:3:{s:1:\\\"a\\\";i:218;s:1:\\\"b\\\";s:19:\\\"view_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:218;a:3:{s:1:\\\"a\\\";i:219;s:1:\\\"b\\\";s:21:\\\"create_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:219;a:3:{s:1:\\\"a\\\";i:220;s:1:\\\"b\\\";s:21:\\\"update_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:220;a:3:{s:1:\\\"a\\\";i:221;s:1:\\\"b\\\";s:21:\\\"delete_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:221;a:4:{s:1:\\\"a\\\";i:222;s:1:\\\"b\\\";s:25:\\\"view_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:222;a:3:{s:1:\\\"a\\\";i:223;s:1:\\\"b\\\";s:27:\\\"create_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:223;a:3:{s:1:\\\"a\\\";i:224;s:1:\\\"b\\\";s:27:\\\"update_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:224;a:3:{s:1:\\\"a\\\";i:225;s:1:\\\"b\\\";s:27:\\\"delete_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:225;a:3:{s:1:\\\"a\\\";i:226;s:1:\\\"b\\\";s:12:\\\"view_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:226;a:3:{s:1:\\\"a\\\";i:227;s:1:\\\"b\\\";s:14:\\\"create_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:227;a:3:{s:1:\\\"a\\\";i:228;s:1:\\\"b\\\";s:14:\\\"update_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:228;a:3:{s:1:\\\"a\\\";i:229;s:1:\\\"b\\\";s:14:\\\"delete_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:229;a:3:{s:1:\\\"a\\\";i:230;s:1:\\\"b\\\";s:23:\\\"view_any_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:230;a:3:{s:1:\\\"a\\\";i:231;s:1:\\\"b\\\";s:16:\\\"view_any_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:231;a:4:{s:1:\\\"a\\\";i:232;s:1:\\\"b\\\";s:29:\\\"view_any_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:232;a:3:{s:1:\\\"a\\\";i:233;s:1:\\\"b\\\";s:25:\\\"delete_any_pricing::model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:233;a:3:{s:1:\\\"a\\\";i:234;s:1:\\\"b\\\";s:18:\\\"delete_any_product\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:234;a:3:{s:1:\\\"a\\\";i:235;s:1:\\\"b\\\";s:31:\\\"delete_any_project::status::log\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:235;a:3:{s:1:\\\"a\\\";i:236;s:1:\\\"b\\\";s:21:\\\"view_any_project_type\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:236;a:3:{s:1:\\\"a\\\";i:237;s:1:\\\"b\\\";s:22:\\\"view_any_pricing_model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:237;a:3:{s:1:\\\"a\\\";i:238;s:1:\\\"b\\\";s:23:\\\"view_any_incentive_rule\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:238;a:3:{s:1:\\\"a\\\";i:239;s:1:\\\"b\\\";s:27:\\\"view_any_notification_event\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:239;a:3:{s:1:\\\"a\\\";i:240;s:1:\\\"b\\\";s:27:\\\"view_any_dashboard_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:240;a:3:{s:1:\\\"a\\\";i:241;s:1:\\\"b\\\";s:35:\\\"view_any_role_notification_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:241;a:3:{s:1:\\\"a\\\";i:242;s:1:\\\"b\\\";s:37:\\\"view_any_notification_role_preference\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}}s:5:\\\"roles\\\";a:3:{i:0;a:3:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:8:\\\"bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"super_admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:11:\\\"jr_bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1751511904, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:242:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:22:\"view_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:26:\"view_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:24:\"create_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:24:\"update_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:25:\"restore_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:29:\"restore_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:27:\"replicate_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:25:\"reorder_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:24:\"delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:28:\"delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:30:\"force_delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:34:\"force_delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:11:\"view_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:15:\"view_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"create_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:13:\"update_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:14:\"restore_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:18:\"restore_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:16:\"replicate_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:14:\"reorder_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:13:\"delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:17:\"delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"force_delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:23:\"force_delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:14:\"view_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"view_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"create_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"update_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:17:\"restore_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:21:\"restore_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:19:\"replicate_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:17:\"reorder_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:16:\"delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:22:\"force_delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:26:\"force_delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:24:\"view_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:22:\"create_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:22:\"update_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:23:\"restore_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:27:\"restore_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:25:\"replicate_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:23:\"reorder_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:22:\"delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:26:\"delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:28:\"force_delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:32:\"force_delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:14:\"view_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:18:\"view_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"create_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:16:\"update_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:17:\"restore_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:21:\"restore_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:19:\"replicate_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:17:\"reorder_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:16:\"delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:20:\"delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:22:\"force_delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:26:\"force_delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:24:\"view_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:28:\"view_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:26:\"create_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:26:\"update_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:27:\"restore_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:31:\"restore_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:29:\"replicate_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:27:\"reorder_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:26:\"delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:30:\"delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:32:\"force_delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:36:\"force_delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:35:\"view_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:39:\"view_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:37:\"create_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:37:\"update_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:38:\"restore_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:42:\"restore_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:40:\"replicate_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:38:\"reorder_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:37:\"delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:41:\"delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:43:\"force_delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:47:\"force_delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"view_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:16:\"view_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:14:\"create_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:14:\"update_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:15:\"restore_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:19:\"restore_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:17:\"replicate_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:15:\"reorder_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:14:\"delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:18:\"delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:20:\"force_delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:24:\"force_delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:12:\"view_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:16:\"view_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:14:\"create_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:14:\"update_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:15:\"restore_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:19:\"restore_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:17:\"replicate_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:15:\"reorder_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:14:\"delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:18:\"delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"force_delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:24:\"force_delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:18:\"view_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:22:\"view_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:20:\"create_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:20:\"update_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:21:\"restore_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:25:\"restore_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:23:\"replicate_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:21:\"reorder_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:20:\"delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:117;a:4:{s:1:\"a\";i:118;s:1:\"b\";s:24:\"delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:118;a:4:{s:1:\"a\";i:119;s:1:\"b\";s:26:\"force_delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:119;a:4:{s:1:\"a\";i:120;s:1:\"b\";s:30:\"force_delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:120;a:4:{s:1:\"a\";i:121;s:1:\"b\";s:9:\"view_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:121;a:4:{s:1:\"a\";i:122;s:1:\"b\";s:13:\"view_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:122;a:4:{s:1:\"a\";i:123;s:1:\"b\";s:11:\"create_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:123;a:4:{s:1:\"a\";i:124;s:1:\"b\";s:11:\"update_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:124;a:4:{s:1:\"a\";i:125;s:1:\"b\";s:11:\"delete_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:125;a:4:{s:1:\"a\";i:126;s:1:\"b\";s:15:\"delete_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:126;a:4:{s:1:\"a\";i:127;s:1:\"b\";s:33:\"view_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:127;a:4:{s:1:\"a\";i:128;s:1:\"b\";s:37:\"view_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:128;a:4:{s:1:\"a\";i:129;s:1:\"b\";s:35:\"create_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:129;a:4:{s:1:\"a\";i:130;s:1:\"b\";s:35:\"update_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:130;a:4:{s:1:\"a\";i:131;s:1:\"b\";s:36:\"restore_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:131;a:4:{s:1:\"a\";i:132;s:1:\"b\";s:40:\"restore_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:132;a:4:{s:1:\"a\";i:133;s:1:\"b\";s:38:\"replicate_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:133;a:4:{s:1:\"a\";i:134;s:1:\"b\";s:36:\"reorder_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:134;a:4:{s:1:\"a\";i:135;s:1:\"b\";s:35:\"delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:135;a:4:{s:1:\"a\";i:136;s:1:\"b\";s:39:\"delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:136;a:4:{s:1:\"a\";i:137;s:1:\"b\";s:41:\"force_delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:137;a:4:{s:1:\"a\";i:138;s:1:\"b\";s:45:\"force_delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:138;a:4:{s:1:\"a\";i:139;s:1:\"b\";s:10:\"view_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:139;a:4:{s:1:\"a\";i:140;s:1:\"b\";s:14:\"view_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:140;a:4:{s:1:\"a\";i:141;s:1:\"b\";s:12:\"create_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:141;a:4:{s:1:\"a\";i:142;s:1:\"b\";s:12:\"update_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:142;a:4:{s:1:\"a\";i:143;s:1:\"b\";s:13:\"restore_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:143;a:4:{s:1:\"a\";i:144;s:1:\"b\";s:17:\"restore_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:144;a:4:{s:1:\"a\";i:145;s:1:\"b\";s:15:\"replicate_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:145;a:4:{s:1:\"a\";i:146;s:1:\"b\";s:13:\"reorder_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:146;a:4:{s:1:\"a\";i:147;s:1:\"b\";s:12:\"delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:147;a:4:{s:1:\"a\";i:148;s:1:\"b\";s:16:\"delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:148;a:4:{s:1:\"a\";i:149;s:1:\"b\";s:18:\"force_delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:149;a:4:{s:1:\"a\";i:150;s:1:\"b\";s:22:\"force_delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:150;a:4:{s:1:\"a\";i:151;s:1:\"b\";s:9:\"view_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:151;a:4:{s:1:\"a\";i:152;s:1:\"b\";s:13:\"view_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:152;a:4:{s:1:\"a\";i:153;s:1:\"b\";s:11:\"create_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:153;a:4:{s:1:\"a\";i:154;s:1:\"b\";s:11:\"update_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:154;a:4:{s:1:\"a\";i:155;s:1:\"b\";s:12:\"restore_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:155;a:4:{s:1:\"a\";i:156;s:1:\"b\";s:16:\"restore_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:156;a:4:{s:1:\"a\";i:157;s:1:\"b\";s:14:\"replicate_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:157;a:4:{s:1:\"a\";i:158;s:1:\"b\";s:12:\"reorder_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:158;a:4:{s:1:\"a\";i:159;s:1:\"b\";s:11:\"delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:159;a:4:{s:1:\"a\";i:160;s:1:\"b\";s:15:\"delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:160;a:4:{s:1:\"a\";i:161;s:1:\"b\";s:17:\"force_delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:161;a:4:{s:1:\"a\";i:162;s:1:\"b\";s:21:\"force_delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:162;a:4:{s:1:\"a\";i:163;s:1:\"b\";s:17:\"page_BdeDashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\"a\";i:164;s:1:\"b\";s:22:\"page_DashboardSettings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:164;a:4:{s:1:\"a\";i:165;s:1:\"b\";s:18:\"page_ManageSetting\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:165;a:4:{s:1:\"a\";i:166;s:1:\"b\";s:11:\"page_Themes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:166;a:4:{s:1:\"a\";i:167;s:1:\"b\";s:18:\"page_MyProfilePage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\"a\";i:168;s:1:\"b\";s:26:\"widget_NotificationsWidget\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:168;a:4:{s:1:\"a\";i:169;s:1:\"b\";s:9:\"view_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:169;a:4:{s:1:\"a\";i:170;s:1:\"b\";s:13:\"view_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:170;a:4:{s:1:\"a\";i:171;s:1:\"b\";s:11:\"create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:171;a:4:{s:1:\"a\";i:172;s:1:\"b\";s:11:\"update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:172;a:4:{s:1:\"a\";i:173;s:1:\"b\";s:12:\"restore_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:173;a:4:{s:1:\"a\";i:174;s:1:\"b\";s:16:\"restore_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:174;a:4:{s:1:\"a\";i:175;s:1:\"b\";s:14:\"replicate_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:175;a:4:{s:1:\"a\";i:176;s:1:\"b\";s:12:\"reorder_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:176;a:4:{s:1:\"a\";i:177;s:1:\"b\";s:11:\"delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:177;a:4:{s:1:\"a\";i:178;s:1:\"b\";s:15:\"delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:178;a:4:{s:1:\"a\";i:179;s:1:\"b\";s:17:\"force_delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:179;a:4:{s:1:\"a\";i:180;s:1:\"b\";s:21:\"force_delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:180;a:4:{s:1:\"a\";i:181;s:1:\"b\";s:16:\"book:create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:181;a:4:{s:1:\"a\";i:182;s:1:\"b\";s:16:\"book:update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:182;a:4:{s:1:\"a\";i:183;s:1:\"b\";s:16:\"book:delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:183;a:4:{s:1:\"a\";i:184;s:1:\"b\";s:20:\"book:pagination_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:184;a:4:{s:1:\"a\";i:185;s:1:\"b\";s:16:\"book:detail_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:185;a:4:{s:1:\"a\";i:186;s:1:\"b\";s:14:\"view_dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:186;a:4:{s:1:\"a\";i:187;s:1:\"b\";s:18:\"page_Bde_Dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:187;a:4:{s:1:\"a\";i:188;s:1:\"b\";s:11:\"export_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:188;a:4:{s:1:\"a\";i:189;s:1:\"b\";s:12:\"view_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:189;a:4:{s:1:\"a\";i:190;s:1:\"b\";s:16:\"view_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:190;a:4:{s:1:\"a\";i:191;s:1:\"b\";s:14:\"create_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:191;a:4:{s:1:\"a\";i:192;s:1:\"b\";s:14:\"update_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:192;a:4:{s:1:\"a\";i:193;s:1:\"b\";s:15:\"restore_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:193;a:4:{s:1:\"a\";i:194;s:1:\"b\";s:19:\"restore_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:194;a:4:{s:1:\"a\";i:195;s:1:\"b\";s:17:\"replicate_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:195;a:4:{s:1:\"a\";i:196;s:1:\"b\";s:15:\"reorder_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:196;a:4:{s:1:\"a\";i:197;s:1:\"b\";s:14:\"delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:197;a:4:{s:1:\"a\";i:198;s:1:\"b\";s:18:\"delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:198;a:4:{s:1:\"a\";i:199;s:1:\"b\";s:20:\"force_delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:199;a:4:{s:1:\"a\";i:200;s:1:\"b\";s:24:\"force_delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:200;a:4:{s:1:\"a\";i:201;s:1:\"b\";s:9:\"view_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:201;a:4:{s:1:\"a\";i:202;s:1:\"b\";s:13:\"view_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:202;a:4:{s:1:\"a\";i:203;s:1:\"b\";s:11:\"create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:203;a:4:{s:1:\"a\";i:204;s:1:\"b\";s:11:\"update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:204;a:4:{s:1:\"a\";i:205;s:1:\"b\";s:12:\"restore_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:205;a:4:{s:1:\"a\";i:206;s:1:\"b\";s:16:\"restore_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:206;a:4:{s:1:\"a\";i:207;s:1:\"b\";s:14:\"replicate_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:207;a:4:{s:1:\"a\";i:208;s:1:\"b\";s:12:\"reorder_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:208;a:4:{s:1:\"a\";i:209;s:1:\"b\";s:11:\"delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:209;a:4:{s:1:\"a\";i:210;s:1:\"b\";s:15:\"delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:210;a:4:{s:1:\"a\";i:211;s:1:\"b\";s:17:\"force_delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:211;a:4:{s:1:\"a\";i:212;s:1:\"b\";s:21:\"force_delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:212;a:4:{s:1:\"a\";i:213;s:1:\"b\";s:16:\"post:create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:213;a:4:{s:1:\"a\";i:214;s:1:\"b\";s:16:\"post:update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:214;a:4:{s:1:\"a\";i:215;s:1:\"b\";s:16:\"post:delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:215;a:4:{s:1:\"a\";i:216;s:1:\"b\";s:20:\"post:pagination_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:216;a:4:{s:1:\"a\";i:217;s:1:\"b\";s:16:\"post:detail_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:217;a:3:{s:1:\"a\";i:218;s:1:\"b\";s:19:\"view_pricing::model\";s:1:\"c\";s:3:\"web\";}i:218;a:3:{s:1:\"a\";i:219;s:1:\"b\";s:21:\"create_pricing::model\";s:1:\"c\";s:3:\"web\";}i:219;a:3:{s:1:\"a\";i:220;s:1:\"b\";s:21:\"update_pricing::model\";s:1:\"c\";s:3:\"web\";}i:220;a:3:{s:1:\"a\";i:221;s:1:\"b\";s:21:\"delete_pricing::model\";s:1:\"c\";s:3:\"web\";}i:221;a:4:{s:1:\"a\";i:222;s:1:\"b\";s:25:\"view_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:222;a:3:{s:1:\"a\";i:223;s:1:\"b\";s:27:\"create_project::status::log\";s:1:\"c\";s:3:\"web\";}i:223;a:3:{s:1:\"a\";i:224;s:1:\"b\";s:27:\"update_project::status::log\";s:1:\"c\";s:3:\"web\";}i:224;a:3:{s:1:\"a\";i:225;s:1:\"b\";s:27:\"delete_project::status::log\";s:1:\"c\";s:3:\"web\";}i:225;a:3:{s:1:\"a\";i:226;s:1:\"b\";s:12:\"view_product\";s:1:\"c\";s:3:\"web\";}i:226;a:3:{s:1:\"a\";i:227;s:1:\"b\";s:14:\"create_product\";s:1:\"c\";s:3:\"web\";}i:227;a:3:{s:1:\"a\";i:228;s:1:\"b\";s:14:\"update_product\";s:1:\"c\";s:3:\"web\";}i:228;a:3:{s:1:\"a\";i:229;s:1:\"b\";s:14:\"delete_product\";s:1:\"c\";s:3:\"web\";}i:229;a:3:{s:1:\"a\";i:230;s:1:\"b\";s:23:\"view_any_pricing::model\";s:1:\"c\";s:3:\"web\";}i:230;a:3:{s:1:\"a\";i:231;s:1:\"b\";s:16:\"view_any_product\";s:1:\"c\";s:3:\"web\";}i:231;a:4:{s:1:\"a\";i:232;s:1:\"b\";s:29:\"view_any_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:232;a:3:{s:1:\"a\";i:233;s:1:\"b\";s:25:\"delete_any_pricing::model\";s:1:\"c\";s:0:\"\";}i:233;a:3:{s:1:\"a\";i:234;s:1:\"b\";s:18:\"delete_any_product\";s:1:\"c\";s:0:\"\";}i:234;a:3:{s:1:\"a\";i:235;s:1:\"b\";s:31:\"delete_any_project::status::log\";s:1:\"c\";s:0:\"\";}i:235;a:3:{s:1:\"a\";i:236;s:1:\"b\";s:21:\"view_any_project_type\";s:1:\"c\";s:0:\"\";}i:236;a:3:{s:1:\"a\";i:237;s:1:\"b\";s:22:\"view_any_pricing_model\";s:1:\"c\";s:0:\"\";}i:237;a:3:{s:1:\"a\";i:238;s:1:\"b\";s:23:\"view_any_incentive_rule\";s:1:\"c\";s:0:\"\";}i:238;a:3:{s:1:\"a\";i:239;s:1:\"b\";s:27:\"view_any_notification_event\";s:1:\"c\";s:0:\"\";}i:239;a:3:{s:1:\"a\";i:240;s:1:\"b\";s:27:\"view_any_dashboard_settings\";s:1:\"c\";s:0:\"\";}i:240;a:3:{s:1:\"a\";i:241;s:1:\"b\";s:35:\"view_any_role_notification_settings\";s:1:\"c\";s:0:\"\";}i:241;a:3:{s:1:\"a\";i:242;s:1:\"b\";s:37:\"view_any_notification_role_preference\";s:1:\"c\";s:0:\"\";}}s:5:\"roles\";a:3:{i:0;a:3:{s:1:\"a\";i:15;s:1:\"b\";s:8:\"bde_team\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"super_admin\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:16;s:1:\"b\";s:11:\"jr_bde_team\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.104542, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.51, "width_percent": 13.987}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.118277, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.497, "width_percent": 2.148}, {"sql": "select count(*) as aggregate from `projects` where `user_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.141263, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.644, "width_percent": 3.544}, {"sql": "select * from `projects` where `user_id` = 4 order by `projects`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.14472, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.188, "width_percent": 3.302}, {"sql": "select * from `clients` where `clients`.`id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.147138, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.49, "width_percent": 3.732}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.149717, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.221, "width_percent": 2.631}, {"sql": "select `clients`.`company_name`, `clients`.`id` from `clients` where `created_by` = 4 order by `clients`.`company_name` asc", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751425521.629122, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.852, "width_percent": 2.899}, {"sql": "select `project_types`.`name`, `project_types`.`id` from `project_types` order by `project_types`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751425521.643892, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.752, "width_percent": 2.846}, {"sql": "select `pricing_models`.`name`, `pricing_models`.`id` from `pricing_models` order by `pricing_models`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1751425521.658093, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.597, "width_percent": 1.772}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 4 and `read_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.724247, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.369, "width_percent": 2.282}, {"sql": "select * from `app_notifications` where `user_id` = 4 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.729207, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.651, "width_percent": 3.409}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.7353458, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.06, "width_percent": 1.987}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMXVLeW10NUl0RExEYjZ4dk1DdW5hRW9aWkc1WFdYcGo0SjZDams0VSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzY6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9wcm9qZWN0cyI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRvLmszU0JEMVF0ejU2aEsyVS9zQ1MuSzlINzh6cUd0M1A3eUQwYWYxak9qeUpHU2tkOU5MVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'sh4D97DpG3HRcFFPDYhBMHuhwQMvAM5aunnjXeuU'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiMXVLeW10NUl0RExEYjZ4dk1DdW5hRW9aWkc1WFdYcGo0SjZDams0VSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzY6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9wcm9qZWN0cyI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRvLmszU0JEMVF0ejU2aEsyVS9zQ1MuSzlINzh6cUd0M1A3eUQwYWYxak9qeUpHU2tkOU5MVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 4, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "sh4D97DpG3HRcFFPDYhBMHuhwQMvAM5aunnjXeuU"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.816768, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.047, "width_percent": 2.953}]}, "models": {"data": {"App\\Models\\Role": {"value": 281, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Permission": {"value": 242, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Project": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\PricingModel": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Client": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}}, "count": 531, "is_counter": true}, "livewire": {"data": {"app.filament.resources.project-resource.pages.list-projects #caS1KubIsxAu6gfFPr7t": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:6 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"client_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"project_type_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"pricing_model_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"start_date\" => array:2 [\n        \"start_from\" => null\n        \"start_until\" => null\n      ]\n      \"end_date\" => array:2 [\n        \"end_from\" => null\n        \"end_until\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:1 [\n      \"client\" => array:1 [\n        \"company_name\" => true\n      ]\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.project-resource.pages.list-projects\"\n  \"component\" => \"App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects\"\n  \"id\" => \"caS1KubIsxAu6gfFPr7t\"\n]", "filament.livewire.global-search #dqi6msHdnxZHEaQpDcJ3": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"dqi6msHdnxZHEaQpDcJ3\"\n]", "app.filament.widgets.notification-components.notification-bell #2xaKtILpSJWPxtoUYcsZ": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 1\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"2xaKtILpSJWPxtoUYcsZ\"\n]", "filament.livewire.notifications #Sa4xfiWM2iE5OG9sOOUI": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3434\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"Sa4xfiWM2iE5OG9sOOUI\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 63, "messages": [{"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-319874050 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319874050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.123589, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059115839 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059115839\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.12433, "xdebug_link": null}, {"message": "[\n  ability => reorder_project,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-861428494 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">reorder_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861428494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.145306, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Project,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752027729 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752027729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.145529, "xdebug_link": null}, {"message": "[\n  ability => create_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1654714377 data-indent-pad=\"  \"><span class=sf-dump-note>create_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654714377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237144, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1342963547 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342963547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.237377, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-464947099 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464947099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.245366, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752749171 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752749171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.245578, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1963145951 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963145951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425525.692888, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=160),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1258197986 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=160)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=160)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258197986\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425525.693149, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-618983048 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618983048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425526.459055, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=160),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-665492527 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=160)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=160)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665492527\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425526.459254, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1913749942 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913749942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425527.020437, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=163),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1595496924 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=163)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=163)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595496924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425527.021585, "xdebug_link": null}, {"message": "[\n  ability => delete_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-358864210 data-indent-pad=\"  \"><span class=sf-dump-note>delete_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358864210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425527.068987, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Project(id=163),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1516452678 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Project(id=163)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Project(id=163)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516452678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751425527.070279, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-78458719 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78458719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.59258, "xdebug_link": null}, {"message": "[\n  ability => view_any_app::notification,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110676043 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">view_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110676043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595843, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1504799512 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504799512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596042, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1213907579 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213907579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596855, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-560626279 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560626279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599263, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-778318669 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778318669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599467, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-291400992 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291400992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600497, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944318145 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944318145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613969, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-504268787 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504268787\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614177, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive::rule,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1515302975 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive::rule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_incentive::rule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515302975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624504, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2120834686 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120834686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624711, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-850492068 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850492068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.625455, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-290467165 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290467165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62805, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478530391 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478530391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.628307, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::event,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907670525 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">view_any_notification::event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907670525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.631825, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-735922063 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735922063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.632021, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::role::preference,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1333373401 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::role::preference </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"39 characters\">view_any_notification::role::preference</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333373401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.635304, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-72337431 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72337431\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.635515, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-838399576 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838399576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.639977, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1677902526 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677902526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.642933, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-711777589 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711777589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.643136, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-150295173 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150295173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.644406, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-469461278 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469461278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646598, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1230809334 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230809334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646797, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-629548230 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629548230\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.65038, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1362072333 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362072333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.650589, "xdebug_link": null}, {"message": "[\n  ability => view_any_product,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2101024196 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101024196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.656577, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1693535646 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693535646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.656796, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1405908302 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405908302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.657919, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1704859354 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704859354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.660206, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2040000455 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040000455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66041, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::status::log,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14258275 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::status::log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">view_any_project::status::log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14258275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.666692, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1246652586 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246652586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.666916, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::type,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-259978791 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::type </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_project::type</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259978791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.670938, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-461167773 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461167773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.67116, "xdebug_link": null}, {"message": "[\n  ability => view_any_role::notification::settings,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-78337785 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role::notification::settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view_any_role::notification::settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78337785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.677075, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-9498702 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9498702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.677275, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1307912838 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307912838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.680088, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091583616 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091583616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.68029, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1694485611 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694485611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.683376, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-492276351 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492276351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.683569, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1492050174 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492050174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.688745, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 4,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2103801309 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103801309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.688939, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-262445971 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262445971\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.699275, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1663979693 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663979693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.699499, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2067531981 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067531981\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.702257, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-743470316 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743470316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.702445, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/projects", "action_name": "filament.admin.resources.projects.index", "controller_action": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects", "uri": "GET admin/projects", "controller": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/projects", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "29.6s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/projects/163/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFSOG8yR3RDVWZJWjY0Q3RHN3lScWc9PSIsInZhbHVlIjoiSWlBR25rbCtWT0tnaXhTaWdQT3hpY0JsVXZwaFRRdkR3bHN4azYyVEpBWFB3cUduT3JGa1AydVlucXRWUmdZcWg3d0NBc2RrVDFoUTYwSG9Hb3pYYnZxdzBBSUZMZFU5RDAzT1ovSmIyeTFJUXhVWHM2VDJEQ3RVV3J5M2hKL3ZCbURRaTh1S1EvSUFYRFU4N2xrbkpNSXlXcHlvYTBCRXJMTXRPVXRheUgwZ25lWEZpdW9nNzZEMU94YXZ3RHl6a2wrYzBvcytZeit6bmJhMWNUWDE0WmN2YUgzUysyQVhQZXIrUXhKY2FTND0iLCJtYWMiOiIyNmY0MDgyZDE4OTU3MjMxNWZjZDY4MDVjYmJhODNkZDgwNTgyNmVhNzk0MDgzM2VjNGZlNzJkODUyNDY2M2EyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJZVTBjNnJlK1RNVEpHekZYSW1PVXc9PSIsInZhbHVlIjoiaEtYSm9OUkw3WVoyUFhWTTZ1ay9EWklRKzZ1N2J4SVNOblhsVFlRYWVvTWprS1Bnc2JiWHBFR1BDQTJZc0l3TXpxaXdXcS9NcEp2ZUVqK3FlMzZzZXhYN01McGJkKy8vT1p2RXRLSU1pUVlORDhmR2E4UFFPUDdKckRxZkxka2EiLCJtYWMiOiIwY2VkYjBlOGY2MjFkYWQ4MzNkN2M3NjQ3NWNhMTAyOTAyNjBmODZkMjM0OGUyZGM3ZTE0NTI5ZmYyNWRhZDE2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Im1oWWEyaG8vNUdRRWJGUGJrYnNqR0E9PSIsInZhbHVlIjoiOVlzTmxqTm9rT2I1L2g4V0pzRUkybnZvMkQ3YnJWRFB6VjRnOGcxVXJlTExKSndlN1BFTGZRV3ZjNENGWkhpdURSNEVIb3ZFMHRLOFNTc3BGdDE0RFBuU3JCbmFhRENkR2tWeFNJeHlpUW5wNGFoM054bU16alB3NHNZbGxKd04iLCJtYWMiOiJmYjJlZjEwYmQ1M2I1YThkNTY5ZWEyNDljYWJlYTA1OWQ1ODU4MmEyMmRmYmVlMGY4MTFiNTFkNmRiOGVkZTY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1085468972 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|WtIvEJRLEdyj3oRmTejngCex4xA1T8lugZk2CzEFthb484TUeY26XbAO5uaq|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1uKymt5ItDLDb6xvMCunaEoZZG5XWXpj4J6Cjk4U</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sh4D97DpG3HRcFFPDYhBMHuhwQMvAM5aunnjXeuU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085468972\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-161149638 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 03:05:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlR5VnR1NTNybkpkSDg2THNLNVY1Q3c9PSIsInZhbHVlIjoiT0NrZHJncGZZanlxejF3ZUlMTzRFd1RXRk1yMHdPSGRhc3ZqZE1LUnFsdEVWQk1JZ1pjQWZsMlc2Zk5uMU44UnN1WEVxR3BPMG85YnJNeVd5czFHRkxKMk12Vjd6UTVRM0h3dmZnZmNvNyttTE1MOVlPMTVHU3pvVnNVTzg0NTQiLCJtYWMiOiJmYjk3YzE2ZWY1NmI4ZDAwMDBkOGE4MTc3MzYzNDg3OTJkNzM2ZjBhNDNjZDJiYjJlNjI1M2ZkNmU5ZTE4YmZlIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 05:05:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6Iit2bE04UmRONXdWdnowLzBtQVlkOVE9PSIsInZhbHVlIjoiYVNFbitNSzVRWnZQU216L1U1M0lSY2VFWnAySk9iU2FsVzI2MWJvTmlyWkxKRld0Znk0UXlDQ0VKZU9aTWVEbnQ1UThkTlVxTEtidGRrVm43K3hPNW8xY1R5TC90bTg3VTNGcHJ4USt1NVBkNXlHL1JDd3A2UVVwU01MRlMrWDciLCJtYWMiOiI0Yjg3M2Y5YWYxMTJiMDQzMDczZTU0OWEyNDk2YWYwNjQ3NzQxZDRjMDdhYjNjZmQ5OTgzMGNlZWU4NTg0M2M2IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 05:05:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlR5VnR1NTNybkpkSDg2THNLNVY1Q3c9PSIsInZhbHVlIjoiT0NrZHJncGZZanlxejF3ZUlMTzRFd1RXRk1yMHdPSGRhc3ZqZE1LUnFsdEVWQk1JZ1pjQWZsMlc2Zk5uMU44UnN1WEVxR3BPMG85YnJNeVd5czFHRkxKMk12Vjd6UTVRM0h3dmZnZmNvNyttTE1MOVlPMTVHU3pvVnNVTzg0NTQiLCJtYWMiOiJmYjk3YzE2ZWY1NmI4ZDAwMDBkOGE4MTc3MzYzNDg3OTJkNzM2ZjBhNDNjZDJiYjJlNjI1M2ZkNmU5ZTE4YmZlIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 05:05:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6Iit2bE04UmRONXdWdnowLzBtQVlkOVE9PSIsInZhbHVlIjoiYVNFbitNSzVRWnZQU216L1U1M0lSY2VFWnAySk9iU2FsVzI2MWJvTmlyWkxKRld0Znk0UXlDQ0VKZU9aTWVEbnQ1UThkTlVxTEtidGRrVm43K3hPNW8xY1R5TC90bTg3VTNGcHJ4USt1NVBkNXlHL1JDd3A2UVVwU01MRlMrWDciLCJtYWMiOiI0Yjg3M2Y5YWYxMTJiMDQzMDczZTU0OWEyNDk2YWYwNjQ3NzQxZDRjMDdhYjNjZmQ5OTgzMGNlZWU4NTg0M2M2IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 05:05:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161149638\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1836836770 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1uKymt5ItDLDb6xvMCunaEoZZG5XWXpj4J6Cjk4U</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836836770\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/projects", "action_name": "filament.admin.resources.projects.index", "controller_action": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects"}, "badge": null}}