{"__meta": {"id": "01JZ4J87MFFK1S38MRP53XRCYQ", "datetime": "2025-07-02 03:17:57", "utime": **********.008606, "method": "GET", "uri": "/admin/notification-events", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[03:17:55] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/notification-events\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.373622, "xdebug_link": null, "collector": "log"}, {"message": "[03:17:57] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/notification-events\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.001057, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751426273.346172, "end": **********.008624, "duration": 3.662451982498169, "duration_str": "3.66s", "measures": [{"label": "Booting", "start": 1751426273.346172, "relative_start": 0, "end": **********.464905, "relative_end": **********.464905, "duration": 1.****************, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.464921, "relative_start": 1.****************, "end": **********.008626, "relative_end": 1.9073486328125e-06, "duration": 2.****************, "duration_str": "2.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.326855, "relative_start": 1.****************, "end": **********.330501, "relative_end": **********.330501, "duration": 0.0036461353302001953, "duration_str": "3.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.51638, "relative_start": 2.***************, "end": **********.51638, "relative_end": **********.51638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.529116, "relative_start": 2.***************, "end": **********.529116, "relative_end": **********.529116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.541253, "relative_start": 2.1950809955596924, "end": **********.541253, "relative_end": **********.541253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.555871, "relative_start": 2.2096989154815674, "end": **********.555871, "relative_end": **********.555871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.566257, "relative_start": 2.2200849056243896, "end": **********.566257, "relative_end": **********.566257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.576027, "relative_start": 2.2298548221588135, "end": **********.576027, "relative_end": **********.576027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.581763, "relative_start": 2.235590934753418, "end": **********.581763, "relative_end": **********.581763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.596369, "relative_start": 2.250196933746338, "end": **********.596369, "relative_end": **********.596369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.029311, "relative_start": 2.683138847351074, "end": 1751426276.029311, "relative_end": 1751426276.029311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.0368, "relative_start": 2.6906278133392334, "end": 1751426276.0368, "relative_end": 1751426276.0368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.071389, "relative_start": 2.725216865539551, "end": 1751426276.071389, "relative_end": 1751426276.071389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.078739, "relative_start": 2.7325668334960938, "end": 1751426276.078739, "relative_end": 1751426276.078739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.108284, "relative_start": 2.7621119022369385, "end": 1751426276.108284, "relative_end": 1751426276.108284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.112835, "relative_start": 2.766662836074829, "end": 1751426276.112835, "relative_end": 1751426276.112835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.157699, "relative_start": 2.8115270137786865, "end": 1751426276.157699, "relative_end": 1751426276.157699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.161851, "relative_start": 2.815678834915161, "end": 1751426276.161851, "relative_end": 1751426276.161851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.195715, "relative_start": 2.8495428562164307, "end": 1751426276.195715, "relative_end": 1751426276.195715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.200662, "relative_start": 2.854489803314209, "end": 1751426276.200662, "relative_end": 1751426276.200662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.272677, "relative_start": 2.9265048503875732, "end": 1751426276.272677, "relative_end": 1751426276.272677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.289535, "relative_start": 2.9433629512786865, "end": 1751426276.289535, "relative_end": 1751426276.289535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.359674, "relative_start": 3.0135018825531006, "end": 1751426276.359674, "relative_end": 1751426276.359674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.367206, "relative_start": 3.021034002304077, "end": 1751426276.367206, "relative_end": 1751426276.367206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.416977, "relative_start": 3.0708048343658447, "end": 1751426276.416977, "relative_end": 1751426276.416977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.429897, "relative_start": 3.0837249755859375, "end": 1751426276.429897, "relative_end": 1751426276.429897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.487223, "relative_start": 3.1410508155822754, "end": 1751426276.487223, "relative_end": 1751426276.487223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.500213, "relative_start": 3.154040813446045, "end": 1751426276.500213, "relative_end": 1751426276.500213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.566492, "relative_start": 3.220319986343384, "end": 1751426276.566492, "relative_end": 1751426276.566492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.572027, "relative_start": 3.2258548736572266, "end": 1751426276.572027, "relative_end": 1751426276.572027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.636113, "relative_start": 3.28994083404541, "end": 1751426276.636113, "relative_end": 1751426276.636113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.649675, "relative_start": 3.3035027980804443, "end": 1751426276.649675, "relative_end": 1751426276.649675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.689412, "relative_start": 3.3432400226593018, "end": 1751426276.689412, "relative_end": 1751426276.689412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.693646, "relative_start": 3.3474738597869873, "end": 1751426276.693646, "relative_end": 1751426276.693646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.716455, "relative_start": 3.3702828884124756, "end": 1751426276.716455, "relative_end": 1751426276.716455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.721619, "relative_start": 3.3754467964172363, "end": 1751426276.721619, "relative_end": 1751426276.721619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.755255, "relative_start": 3.4090828895568848, "end": 1751426276.755255, "relative_end": 1751426276.755255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.765692, "relative_start": 3.4195199012756348, "end": 1751426276.765692, "relative_end": 1751426276.765692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.807424, "relative_start": 3.461251974105835, "end": 1751426276.807424, "relative_end": 1751426276.807424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751426276.814908, "relative_start": 3.468735933303833, "end": 1751426276.814908, "relative_end": 1751426276.814908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1751426276.844763, "relative_start": 3.4985909461975098, "end": 1751426276.844763, "relative_end": 1751426276.844763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1751426276.865913, "relative_start": 3.5197408199310303, "end": 1751426276.865913, "relative_end": 1751426276.865913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": 1751426276.909133, "relative_start": 3.5629608631134033, "end": 1751426276.909133, "relative_end": 1751426276.909133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": 1751426276.912624, "relative_start": 3.5664517879486084, "end": 1751426276.912624, "relative_end": 1751426276.912624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": 1751426276.913803, "relative_start": 3.5676310062408447, "end": 1751426276.913803, "relative_end": 1751426276.913803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": 1751426276.916489, "relative_start": 3.570316791534424, "end": 1751426276.916489, "relative_end": 1751426276.916489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": 1751426276.917515, "relative_start": 3.571342945098877, "end": 1751426276.917515, "relative_end": 1751426276.917515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": 1751426276.921556, "relative_start": 3.5753839015960693, "end": 1751426276.921556, "relative_end": 1751426276.921556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": 1751426276.921831, "relative_start": 3.5756587982177734, "end": 1751426276.921831, "relative_end": 1751426276.921831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": 1751426276.924273, "relative_start": 3.5781009197235107, "end": 1751426276.924273, "relative_end": 1751426276.924273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": 1751426276.924788, "relative_start": 3.57861590385437, "end": 1751426276.924788, "relative_end": 1751426276.924788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": 1751426276.926609, "relative_start": 3.580436944961548, "end": 1751426276.926609, "relative_end": 1751426276.926609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": 1751426276.926848, "relative_start": 3.5806758403778076, "end": 1751426276.926848, "relative_end": 1751426276.926848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": 1751426276.928285, "relative_start": 3.5821127891540527, "end": 1751426276.928285, "relative_end": 1751426276.928285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": 1751426276.928469, "relative_start": 3.582296848297119, "end": 1751426276.928469, "relative_end": 1751426276.928469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": 1751426276.987107, "relative_start": 3.640934944152832, "end": 1751426276.987107, "relative_end": 1751426276.987107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": 1751426276.988286, "relative_start": 3.6421139240264893, "end": 1751426276.988286, "relative_end": 1751426276.988286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": 1751426276.994108, "relative_start": 3.6479358673095703, "end": 1751426276.994108, "relative_end": 1751426276.994108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": 1751426276.995143, "relative_start": 3.64897084236145, "end": 1751426276.995143, "relative_end": 1751426276.995143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": 1751426276.996243, "relative_start": 3.650070905685425, "end": 1751426276.996243, "relative_end": 1751426276.996243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.000294, "relative_start": 3.6541218757629395, "end": **********.000485, "relative_end": **********.000485, "duration": 0.00019097328186035156, "duration_str": "191μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.00567, "relative_start": 3.6594979763031006, "end": **********.005761, "relative_end": **********.005761, "duration": 9.083747863769531e-05, "duration_str": "91μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 55695032, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 58, "nb_templates": 58, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.516354, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "1x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.529096, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "2x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.541232, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "3x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.555853, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.596322, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "30x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751426276.029293, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 30, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1751426276.844748, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1751426276.865886, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": 1751426276.909115, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": 1751426276.912609, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": 1751426276.913788, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": 1751426276.916474, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": 1751426276.917501, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": 1751426276.987088, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": 1751426276.988268, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1751426276.994097, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": 1751426276.995131, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": 1751426276.996227, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 22, "nb_statements": 22, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01639, "accumulated_duration_str": "16.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3418, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 9.518}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.349125, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.518, "width_percent": 6.955}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.353977, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.473, "width_percent": 4.21}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.35957, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.683, "width_percent": 4.332}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.3614118, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.015, "width_percent": 2.563}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.365333, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.578, "width_percent": 3.051}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.366751, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.628, "width_percent": 2.074}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.367912, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.703, "width_percent": 1.83}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.369019, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.533, "width_percent": 1.83}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.370126, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.364, "width_percent": 1.83}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.383953, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.194, "width_percent": 11.775}, {"sql": "select count(*) as aggregate from `notification_events`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.50378, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.969, "width_percent": 3.661}, {"sql": "select * from `notification_events` order by `notification_events`.`id` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.506105, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.63, "width_percent": 8.725}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1751426276.886881, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.355, "width_percent": 3.905}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": 1751426276.910476, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.26, "width_percent": 4.82}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": 1751426276.91455, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.08, "width_percent": 4.393}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": 1751426276.920137, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.473, "width_percent": 3.234}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": 1751426276.922421, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.707, "width_percent": 3.6}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": 1751426276.925337, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.306, "width_percent": 3.966}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": 1751426276.927282, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.272, "width_percent": 2.685}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": 1751426276.928892, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.957, "width_percent": 2.563}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL25vdGlmaWNhdGlvbi1ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjY6InRhYmxlcyI7YToxOntzOjQxOiI4MjUyY2ZhNTYwODM4ZWZjMDAzOTYyODM0MWYzYTQ2Zl9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL25vdGlmaWNhdGlvbi1ldmVudHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjY6InRhYmxlcyI7YToxOntzOjQxOiI4MjUyY2ZhNTYwODM4ZWZjMDAzOTYyODM0MWYzYTQ2Zl9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.002793, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.519, "width_percent": 8.481}]}, "models": {"data": {"App\\Models\\NotificationEvent": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 27, "is_counter": true}, "livewire": {"data": {"app.filament.resources.notification-event-resource.pages.list-notification-events #RCjyS4AqEvxVrtD6YTNI": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_active\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_hierarchical\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:1 [\n      \"created_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.notification-event-resource.pages.list-notification-events\"\n  \"component\" => \"App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents\"\n  \"id\" => \"RCjyS4AqEvxVrtD6YTNI\"\n]", "filament.livewire.global-search #UiexhUaCB2zn7sHo6Bp8": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"UiexhUaCB2zn7sHo6Bp8\"\n]", "app.filament.widgets.notification-components.notification-bell #cviMIDnDNVjzZnR3HpYE": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 6\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"cviMIDnDNVjzZnR3HpYE\"\n]", "filament.livewire.notifications #t7G4ozkdbAaihyq2h5kx": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2806\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"t7G4ozkdbAaihyq2h5kx\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 121, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1398605992 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398605992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.391474, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729795924 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729795924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.432615, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-861748094 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861748094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.439381, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-738582613 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738582613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.474446, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1593603258 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593603258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.618092, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1011100034 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011100034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621277, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1795738400 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795738400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.023563, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076090075 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076090075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.025263, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1376191306 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376191306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.027101, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752854318 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752854318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.036058, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1332662027 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332662027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.043773, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2097125631 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097125631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.046005, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-86856993 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86856993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.064467, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-733552889 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733552889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.066595, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921801591 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921801591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.069139, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1304157975 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304157975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.07839, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-540057058 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540057058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.083826, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-410667571 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410667571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.085677, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1516781040 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516781040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.101228, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-899493883 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899493883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.102977, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1338152109 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338152109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.105238, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-567207987 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567207987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.112575, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814767937 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814767937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.117918, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1451257282 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451257282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.120631, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1392537256 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392537256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.151069, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-403322610 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403322610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.152867, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-861647361 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861647361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.155104, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-932815707 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932815707\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.161591, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2061454757 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2061454757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.167689, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-25978137 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25978137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.170448, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-574071464 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574071464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.187591, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067637553 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067637553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.189293, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1644465038 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644465038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.193587, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1730429234 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730429234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.200321, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1891818952 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891818952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.205922, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1559240518 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559240518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.216362, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-938374693 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938374693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.259286, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1188142210 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188142210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.263197, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1860619933 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860619933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.267414, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1123661473 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123661473\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.288596, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1004418561 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004418561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.306067, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-931496707 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931496707\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.314729, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-340491506 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340491506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.347565, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1813590952 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813590952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.350486, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1380880978 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380880978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.354301, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-923060191 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923060191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.366842, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1270990937 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270990937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.372901, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-588505068 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-588505068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.376277, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1499940325 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499940325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.410537, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1399888138 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399888138\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.411606, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-281349032 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281349032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.413489, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1124049988 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124049988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.429121, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-824765613 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824765613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.443307, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1818063595 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818063595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.448052, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-546788632 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546788632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.482042, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674102047 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674102047\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.483848, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-581750011 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581750011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.485936, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2040842216 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040842216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.499433, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-907050463 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907050463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.511954, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1244754924 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244754924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.51711, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-263690927 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263690927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.552107, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1275417686 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275417686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.555124, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1929669661 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929669661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.56152, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1162925723 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162925723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.571701, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-440030833 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440030833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.583139, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2143274031 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143274031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.587927, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-745430057 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745430057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.62385, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-892454893 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892454893\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.627141, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-764624676 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764624676\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.632443, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-982607571 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982607571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.648893, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1792500828 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792500828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.662751, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-609159364 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609159364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.667874, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-289279601 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289279601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.682705, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1071514056 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071514056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.6859, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-835536904 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835536904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.688082, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1489544752 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489544752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.693407, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-301606490 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301606490\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.697601, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-693034686 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693034686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.699263, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805832710 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805832710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.712518, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-391075498 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391075498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.713668, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-178889080 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178889080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.715142, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1860688354 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860688354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.721156, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-803370078 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803370078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.727346, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-704863620 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704863620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.729729, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-68380341 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68380341\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.745905, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1937158172 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937158172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.748065, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1332348659 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332348659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.751149, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-903764214 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903764214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.765175, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827602714 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827602714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.773688, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-475078836 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475078836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.776774, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-519439242 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-519439242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.798305, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1195990117 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195990117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.801415, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-891948116 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891948116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.804416, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-71626235 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71626235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.814544, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-932483916 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932483916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.872002, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1709225908 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709225908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.872409, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1099266383 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099266383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.87448, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-879186941 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879186941\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.874779, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-171398035 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171398035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.875213, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2068773954 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068773954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.875494, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1453574982 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453574982\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.875857, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-384992916 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384992916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.876353, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-400397602 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400397602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.876681, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1496315791 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496315791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.877172, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2094380791 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094380791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.877575, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1903181378 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903181378\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.87824, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-94051362 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94051362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.878563, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1790648314 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790648314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.879075, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-422542421 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422542421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.879429, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-865982053 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865982053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.879633, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-348305701 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348305701\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.880322, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1968933806 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968933806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.880947, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-794785105 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794785105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.881307, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-283455308 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283455308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.881766, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2068726666 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068726666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.882399, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310692039 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310692039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.882982, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555264492 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555264492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.883578, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-496462522 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496462522\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.884168, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1742744638 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742744638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.888973, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1376958447 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376958447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.889695, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1765918529 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765918529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751426276.900242, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/notification-events", "action_name": "filament.admin.resources.notification-events.index", "controller_action": "App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents", "uri": "GET admin/notification-events", "controller": "App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/notification-events", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "3.66s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjR2dHN6U3R1NndVbmQyTzlNTWdmSXc9PSIsInZhbHVlIjoibE0wSzYvZ1pXdGNiMU5aMVF4UFVnVWdiMHpjQ2NXeEFXT0p4M1Q5ZzRUTndOb2Q0U1Z6Rm5RT1k3WHdZVHdBY3dwUXB4S3czMW5CcVRaRHVaeFN6U2JKVkpqS2o2c0prK1ZTNld4ZXdhQ04rV05GdHpoeEdLcmNDa1ZveUZ3azYiLCJtYWMiOiIzYTI1ZDk0MWRlYjgxNzgzYjRjY2M3NWJiYzlhZmI2OTc5NTQ3MWZkZjNkMDRlMzc1MzcxNDVhODFkODAxMmUzIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InFKZTlJMldBZXRSNFNFR29YUTBBWHc9PSIsInZhbHVlIjoiaGI3MzdzRnlkWXBjV1VDSENYVG52TkJVOWVYTnBDZnNmME44M29IckxYNlRndDE3dFMzejJrV2JyUXBYR3RZcnk2dmd5SjJkeE5uVDdJVll1TXYweG1aZzRrcnY5aGZ4aGlxdXMveDR0YUVyOVB6Q2dXTU5xL1Mya3BjZlJmajciLCJtYWMiOiJiYzBmNzkxOWViZmRkNGZmZjk3ZGU0MmI0OWM5YzgwZDAzNjE4MDQzYWY5NmFlMjcyNTdmYWRiMzk5NjVlMjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1966414916 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966414916\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486332001 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 03:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imh2S1p3aFhYdXN0ZGpnR0pWV3F6SWc9PSIsInZhbHVlIjoiRmUrZnAveUZMWnQwMWlsNWhqb1I3cUN6RWhHUkltcmFhUlpFN3E1OVMxV1JlRmdFWlNKN2kyL0txTmlGQkhwUXByOFNmZ213elRVck41cUlReTA4V0MxRmx3REF2cjBEWFd4TDc1YXlocHlLanY5cjIwekNmUmtnZ21sWlNETTMiLCJtYWMiOiJiMmQ2MmIxYWE2NzY5YjhmMjEwMTk3YmRkNjlkNGIwMDg0NGQ2NzA5ZjIwYjJjOTU5MGNmYmVhOTgzYjg0YmM1IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 05:17:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IlVpbzBFVFFaZHYwdDBVMk1EU3VQVnc9PSIsInZhbHVlIjoibWxvb0xKRHBxU2N0THU1V1RHdVcraldhRlp2REFXZTRKRzk0cVNEM0Q0c1RKeTJYeEpJYm1TK21rS0p4TzJESG5oR1dCTE1QTUpvSHdDK0V3NEF0RU9QYXBYUTV3cUlBVUI2R0lCM0h2T0diSkZCK05aaDZvRkI1YzRENzlzV1kiLCJtYWMiOiIxYmU0ZjRkMjNhY2EyYTI3OWIwODJkODQxNDhhMzViY2M5Y2E4Y2IzN2U5YWUwYjBlZTFhYTdiNmE0YzI4MmMxIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 05:17:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imh2S1p3aFhYdXN0ZGpnR0pWV3F6SWc9PSIsInZhbHVlIjoiRmUrZnAveUZMWnQwMWlsNWhqb1I3cUN6RWhHUkltcmFhUlpFN3E1OVMxV1JlRmdFWlNKN2kyL0txTmlGQkhwUXByOFNmZ213elRVck41cUlReTA4V0MxRmx3REF2cjBEWFd4TDc1YXlocHlLanY5cjIwekNmUmtnZ21sWlNETTMiLCJtYWMiOiJiMmQ2MmIxYWE2NzY5YjhmMjEwMTk3YmRkNjlkNGIwMDg0NGQ2NzA5ZjIwYjJjOTU5MGNmYmVhOTgzYjg0YmM1IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 05:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IlVpbzBFVFFaZHYwdDBVMk1EU3VQVnc9PSIsInZhbHVlIjoibWxvb0xKRHBxU2N0THU1V1RHdVcraldhRlp2REFXZTRKRzk0cVNEM0Q0c1RKeTJYeEpJYm1TK21rS0p4TzJESG5oR1dCTE1QTUpvSHdDK0V3NEF0RU9QYXBYUTV3cUlBVUI2R0lCM0h2T0diSkZCK05aaDZvRkI1YzRENzlzV1kiLCJtYWMiOiIxYmU0ZjRkMjNhY2EyYTI3OWIwODJkODQxNDhhMzViY2M5Y2E4Y2IzN2U5YWUwYjBlZTFhYTdiNmE0YzI4MmMxIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 05:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486332001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-518976025 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/admin/notification-events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518976025\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/notification-events", "action_name": "filament.admin.resources.notification-events.index", "controller_action": "App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents"}, "badge": null}}