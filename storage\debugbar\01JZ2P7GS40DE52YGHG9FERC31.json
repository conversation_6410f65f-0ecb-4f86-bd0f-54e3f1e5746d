{"__meta": {"id": "01JZ2P7GS40DE52YGHG9FERC31", "datetime": "2025-07-01 09:48:59", "utime": **********.045394, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751363337.135157, "end": **********.04541, "duration": 1.9102528095245361, "duration_str": "1.91s", "measures": [{"label": "Booting", "start": 1751363337.135157, "relative_start": 0, "end": **********.128291, "relative_end": **********.128291, "duration": 0.****************, "duration_str": "993ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.128303, "relative_start": 0.****************, "end": **********.045412, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "917ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.439301, "relative_start": 1.****************, "end": **********.441759, "relative_end": **********.441759, "duration": 0.0024580955505371094, "duration_str": "2.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.647565, "relative_start": 1.****************, "end": **********.647565, "relative_end": **********.647565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.656703, "relative_start": 1.****************, "end": **********.656703, "relative_end": **********.656703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.659136, "relative_start": 1.5239789485931396, "end": **********.659136, "relative_end": **********.659136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.661645, "relative_start": 1.5264878273010254, "end": **********.661645, "relative_end": **********.661645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.682234, "relative_start": 1.547076940536499, "end": **********.682234, "relative_end": **********.682234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.688054, "relative_start": 1.5528969764709473, "end": **********.688054, "relative_end": **********.688054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.692372, "relative_start": 1.5572149753570557, "end": **********.692372, "relative_end": **********.692372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.704784, "relative_start": 1.569626808166504, "end": **********.704784, "relative_end": **********.704784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.706767, "relative_start": 1.5716099739074707, "end": **********.706767, "relative_end": **********.706767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.710297, "relative_start": 1.5751399993896484, "end": **********.710297, "relative_end": **********.710297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.720093, "relative_start": 1.5849359035491943, "end": **********.720093, "relative_end": **********.720093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.72198, "relative_start": 1.5868229866027832, "end": **********.72198, "relative_end": **********.72198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.725316, "relative_start": 1.5901589393615723, "end": **********.725316, "relative_end": **********.725316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.736233, "relative_start": 1.6010758876800537, "end": **********.736233, "relative_end": **********.736233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.738879, "relative_start": 1.6037218570709229, "end": **********.738879, "relative_end": **********.738879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.744115, "relative_start": 1.6089580059051514, "end": **********.744115, "relative_end": **********.744115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.753664, "relative_start": 1.618506908416748, "end": **********.753664, "relative_end": **********.753664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.755851, "relative_start": 1.6206939220428467, "end": **********.755851, "relative_end": **********.755851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.760166, "relative_start": 1.6250088214874268, "end": **********.760166, "relative_end": **********.760166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.771197, "relative_start": 1.6360399723052979, "end": **********.771197, "relative_end": **********.771197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.772945, "relative_start": 1.6377878189086914, "end": **********.772945, "relative_end": **********.772945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.776224, "relative_start": 1.6410667896270752, "end": **********.776224, "relative_end": **********.776224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.784774, "relative_start": 1.6496169567108154, "end": **********.784774, "relative_end": **********.784774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.788258, "relative_start": 1.6531009674072266, "end": **********.788258, "relative_end": **********.788258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.795732, "relative_start": 1.6605749130249023, "end": **********.795732, "relative_end": **********.795732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.808789, "relative_start": 1.6736319065093994, "end": **********.808789, "relative_end": **********.808789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.812951, "relative_start": 1.6777939796447754, "end": **********.812951, "relative_end": **********.812951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.819822, "relative_start": 1.6846649646759033, "end": **********.819822, "relative_end": **********.819822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.834396, "relative_start": 1.6992387771606445, "end": **********.834396, "relative_end": **********.834396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.836859, "relative_start": 1.7017018795013428, "end": **********.836859, "relative_end": **********.836859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.842968, "relative_start": 1.707810878753662, "end": **********.842968, "relative_end": **********.842968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.857397, "relative_start": 1.7222399711608887, "end": **********.857397, "relative_end": **********.857397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.860072, "relative_start": 1.724914789199829, "end": **********.860072, "relative_end": **********.860072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.864327, "relative_start": 1.7291698455810547, "end": **********.864327, "relative_end": **********.864327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.876153, "relative_start": 1.7409958839416504, "end": **********.876153, "relative_end": **********.876153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.879708, "relative_start": 1.7445509433746338, "end": **********.879708, "relative_end": **********.879708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.888681, "relative_start": 1.753523826599121, "end": **********.888681, "relative_end": **********.888681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.896346, "relative_start": 1.7611889839172363, "end": **********.896346, "relative_end": **********.896346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.899771, "relative_start": 1.7646138668060303, "end": **********.899771, "relative_end": **********.899771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.904509, "relative_start": 1.7693519592285156, "end": **********.904509, "relative_end": **********.904509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.917905, "relative_start": 1.7827479839324951, "end": **********.917905, "relative_end": **********.917905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.922674, "relative_start": 1.7875168323516846, "end": **********.922674, "relative_end": **********.922674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.928067, "relative_start": 1.792909860610962, "end": **********.928067, "relative_end": **********.928067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.939878, "relative_start": 1.8047208786010742, "end": **********.939878, "relative_end": **********.939878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.942396, "relative_start": 1.8072388172149658, "end": **********.942396, "relative_end": **********.942396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.947053, "relative_start": 1.8118958473205566, "end": **********.947053, "relative_end": **********.947053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.959755, "relative_start": 1.8245978355407715, "end": **********.959755, "relative_end": **********.959755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.962583, "relative_start": 1.8274259567260742, "end": **********.962583, "relative_end": **********.962583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.970382, "relative_start": 1.8352248668670654, "end": **********.970382, "relative_end": **********.970382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.979119, "relative_start": 1.8439619541168213, "end": **********.979119, "relative_end": **********.979119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.983123, "relative_start": 1.847965955734253, "end": **********.983123, "relative_end": **********.983123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.987744, "relative_start": 1.8525869846343994, "end": **********.987744, "relative_end": **********.987744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.000445, "relative_start": 1.8652877807617188, "end": **********.000445, "relative_end": **********.000445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.004466, "relative_start": 1.8693089485168457, "end": **********.004466, "relative_end": **********.004466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.008412, "relative_start": 1.8732547760009766, "end": **********.008412, "relative_end": **********.008412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.013188, "relative_start": 1.878030776977539, "end": **********.013188, "relative_end": **********.013188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.020911, "relative_start": 1.885753870010376, "end": **********.020911, "relative_end": **********.020911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.031127, "relative_start": 1.8959698677062988, "end": **********.031127, "relative_end": **********.031127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.041994, "relative_start": 1.906836986541748, "end": **********.043591, "relative_end": **********.043591, "duration": 0.0015969276428222656, "duration_str": "1.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56600648, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 58, "nb_templates": 58, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.647549, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.659124, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.66163, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.682217, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.69236, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.013168, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 42, "nb_statements": 42, "nb_visible_statements": 42, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02356, "accumulated_duration_str": "23.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.4467769, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.504}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.467211, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.504, "width_percent": 2.929}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.471097, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.433, "width_percent": 1.952}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.473816, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.385, "width_percent": 1.443}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.475048, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.829, "width_percent": 1.358}, {"sql": "select * from `roles` where `roles`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.485194, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.187, "width_percent": 2.759}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.497296, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.946, "width_percent": 3.65}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.5456522, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.596, "width_percent": 6.409}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'bde_team' and `roles`.`id` <> '15'", "type": "query", "params": [], "bindings": ["bde_team", "15"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.56932, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.005, "width_percent": 2.844}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/RefreshesPermissionCache.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\RefreshesPermissionCache.php", "line": 12}], "start": **********.57837, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.849, "width_percent": 1.952}, {"sql": "select * from `permissions` where (`name` = 'view_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.579702, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.801, "width_percent": 1.825}, {"sql": "select * from `permissions` where (`name` = 'view_any_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.5808911, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.626, "width_percent": 3.396}, {"sql": "select * from `permissions` where (`name` = 'view_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.583328, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.022, "width_percent": 2.165}, {"sql": "select * from `permissions` where (`name` = 'view_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.584792, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.187, "width_percent": 2.419}, {"sql": "select * from `permissions` where (`name` = 'create_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.586365, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.606, "width_percent": 1.783}, {"sql": "select * from `permissions` where (`name` = 'update_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.587585, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.389, "width_percent": 1.783}, {"sql": "select * from `permissions` where (`name` = 'delete_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.588883, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.171, "width_percent": 1.698}, {"sql": "select * from `permissions` where (`name` = 'delete_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.590004, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.869, "width_percent": 2.25}, {"sql": "select * from `permissions` where (`name` = 'view_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.591222, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.119, "width_percent": 1.698}, {"sql": "select * from `permissions` where (`name` = 'view_any_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.59248, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.817, "width_percent": 1.528}, {"sql": "select * from `permissions` where (`name` = 'view_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.593666, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.345, "width_percent": 1.613}, {"sql": "select * from `permissions` where (`name` = 'view_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.594717, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.958, "width_percent": 1.57}, {"sql": "select * from `permissions` where (`name` = 'create_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.595896, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.528, "width_percent": 1.528}, {"sql": "select * from `permissions` where (`name` = 'update_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.597086, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.056, "width_percent": 12.224}, {"sql": "select * from `permissions` where (`name` = 'delete_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6008132, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.28, "width_percent": 2.08}, {"sql": "select * from `permissions` where (`name` = 'delete_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.602268, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.36, "width_percent": 2.377}, {"sql": "select * from `permissions` where (`name` = 'view_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6036239, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.737, "width_percent": 1.655}, {"sql": "select * from `permissions` where (`name` = 'view_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6046178, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.392, "width_percent": 1.74}, {"sql": "select * from `permissions` where (`name` = 'create_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.605859, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.132, "width_percent": 1.528}, {"sql": "select * from `permissions` where (`name` = 'update_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.607005, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.66, "width_percent": 1.613}, {"sql": "select * from `permissions` where (`name` = 'delete_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.607976, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.273, "width_percent": 1.358}, {"sql": "select * from `permissions` where (`name` = 'delete_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.60893, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.632, "width_percent": 1.358}, {"sql": "select * from `permissions` where (`name` = 'view_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6099741, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.99, "width_percent": 1.952}, {"sql": "select * from `permissions` where (`name` = 'view_any_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.611044, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.942, "width_percent": 1.528}, {"sql": "select * from `permissions` where (`name` = 'create_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.612132, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.47, "width_percent": 1.358}, {"sql": "select * from `permissions` where (`name` = 'update_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.613019, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.829, "width_percent": 1.358}, {"sql": "select * from `permissions` where (`name` = 'delete_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.613887, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.187, "width_percent": 1.188}, {"sql": "select * from `permissions` where (`name` = 'page_BdeDashboard' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_BdeDashboard", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.615417, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.375, "width_percent": 3.565}, {"sql": "select * from `permissions` where (`name` = 'page_MyProfilePage' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_MyProfilePage", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6172419, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.941, "width_percent": 2.419}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 15", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.618997, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:453", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=453", "ajax": false, "filename": "HasPermissions.php", "line": "453"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.36, "width_percent": 3.523}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 15), (2, 15), (13, 15), (14, 15), (15, 15), (16, 15), (21, 15), (22, 15), (25, 15), (26, 15), (49, 15), (50, 15), (51, 15), (52, 15), (57, 15), (58, 15), (85, 15), (86, 15), (87, 15), (88, 15), (93, 15), (94, 15), (97, 15), (98, 15), (99, 15), (100, 15), (105, 15), (163, 15), (167, 15)", "type": "query", "params": [], "bindings": [1, 15, 2, 15, 13, 15, 14, 15, 15, 15, 16, 15, 21, 15, 22, 15, 25, 15, 26, 15, 49, 15, 50, 15, 51, 15, 52, 15, 57, 15, 58, 15, 85, 15, 86, 15, 87, 15, 88, 15, 93, 15, 94, 15, 97, 15, 98, 15, 99, 15, 100, 15, 105, 15, 163, 15, 167, 15], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}], "start": **********.62175, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:406", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=406", "ajax": false, "filename": "HasPermissions.php", "line": "406"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.883, "width_percent": 2.929}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 554}], "start": **********.6234982, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.812, "width_percent": 1.188}]}, "models": {"data": {"App\\Models\\Permission": {"value": 29, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 32, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #gcNWqKGmv3egTTB2d8H6": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => Illuminate\\Support\\Collection {#3608\n      #items: array:29 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"view_client\"\n        3 => \"view_any_client\"\n        4 => \"create_client\"\n        5 => \"update_client\"\n        6 => \"delete_client\"\n        7 => \"delete_any_client\"\n        8 => \"view_incentive\"\n        9 => \"view_any_incentive\"\n        10 => \"view_milestone\"\n        11 => \"view_any_milestone\"\n        12 => \"create_milestone\"\n        13 => \"update_milestone\"\n        14 => \"delete_milestone\"\n        15 => \"delete_any_milestone\"\n        16 => \"view_payment\"\n        17 => \"view_any_payment\"\n        18 => \"create_payment\"\n        19 => \"update_payment\"\n        20 => \"delete_payment\"\n        21 => \"delete_any_payment\"\n        22 => \"view_project\"\n        23 => \"view_any_project\"\n        24 => \"create_project\"\n        25 => \"update_project\"\n        26 => \"delete_project\"\n        27 => \"page_BdeDashboard\"\n        28 => \"page_MyProfilePage\"\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"data\" => array:26 [\n      \"id\" => 15\n      \"name\" => \"bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:2 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n      ]\n      \"client\" => array:6 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n        5 => \"delete_any_client\"\n      ]\n      \"incentive\" => array:2 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n      ]\n      \"incentive::rule\" => []\n      \"milestone\" => array:6 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n        5 => \"delete_any_milestone\"\n      ]\n      \"notification::event\" => []\n      \"notification::role::preference\" => []\n      \"payment\" => array:6 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n        5 => \"delete_any_payment\"\n      ]\n      \"pricing::model\" => []\n      \"product\" => []\n      \"project\" => array:5 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n      ]\n      \"project::status::log\" => []\n      \"project::type\" => []\n      \"role\" => []\n      \"role::notification::settings\" => []\n      \"user\" => []\n      \"pages_tab\" => array:2 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => []\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#3958\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #original: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"gcNWqKGmv3egTTB2d8H6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1883349474 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883349474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.50243, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1746911208 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746911208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.504716, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-498815386 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498815386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025766, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-154264552 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154264552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026327, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@save<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Resources/Pages/EditRecord.php:134-177</a>", "middleware": "web", "duration": "1.91s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-37033796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-37033796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1181877630 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"3036 characters\">{&quot;data&quot;:{&quot;permissions&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;,&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;,&quot;delete_any_client&quot;,&quot;view_incentive&quot;,&quot;view_any_incentive&quot;,&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;,&quot;delete_any_milestone&quot;,&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;,&quot;delete_any_payment&quot;,&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;,&quot;page_BdeDashboard&quot;,&quot;page_MyProfilePage&quot;],{&quot;class&quot;:&quot;Illuminate\\\\Support\\\\Collection&quot;,&quot;s&quot;:&quot;clctn&quot;}],&quot;data&quot;:[{&quot;id&quot;:15,&quot;name&quot;:&quot;bde_team&quot;,&quot;guard_name&quot;:&quot;web&quot;,&quot;created_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;select_all&quot;:false,&quot;app::notification&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[[&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;,&quot;delete_any_client&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive&quot;:[[&quot;view_incentive&quot;,&quot;view_any_incentive&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive::rule&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;milestone&quot;:[[&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;,&quot;delete_any_milestone&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::event&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::role::preference&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;payment&quot;:[[&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;,&quot;delete_any_payment&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;pricing::model&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;product&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;project&quot;:[[&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::status::log&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::type&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;role&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;role::notification::settings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;user&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;pages_tab&quot;:[[&quot;page_BdeDashboard&quot;,&quot;page_MyProfilePage&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;widgets_tab&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;custom_permissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;team_id&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/localhost:8000\\/admin\\/shield\\/roles&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Role&quot;,&quot;key&quot;:15,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;gcNWqKGmv3egTTB2d8H6&quot;,&quot;name&quot;:&quot;app.filament.resources.role-resource.pages.edit-role&quot;,&quot;path&quot;:&quot;admin\\/shield\\/roles\\/15\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b89e7ec8813402f4e680e5d2ac76a116b56d730af10f72cd5eeff1c8680fb68f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181877630\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-881967947 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3592</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ink2RWxWL2I3WVF5cDBWbGhvZGxZUmc9PSIsInZhbHVlIjoiRnlDaEJBSWkweVEzWlRIVTdkWDZ3ZDlhU0s3cmNPOUxaY0tTWWxHZjAwQW45Vk01bkIrdmJGK0Jnc1dONDYxYXVQQ3NWcVJwQzdiZ2ZDLzlIV0RxUWlTMTJTd2Z3VVZjTUtnc0xvN0lIRU44dzk2eE9HdXd0SEhYY283R0l6MysiLCJtYWMiOiI2MmRkMWJmYTNlYWIxNWQxMTNlY2MxNDE5ZWRlZDQ5YWQzMmEzNzgyNTE5MTNiYmIzMTA0YzZjZDI2Njc1MzFmIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IjFJTlphZVduRGNXb01MRG9UbmdJTGc9PSIsInZhbHVlIjoiUWVYQitmOExwamF5Zjc2ZTlBZGc3Szg3L0xMakowNElmbFJmRnZOWUpNZWVnM0NwUmE4VGtwbEdUbDlFOVF3VFBVNUhhQXh5TzR0MmQvTUxjVlg2TXFuWkZreVBGc1pjNmlOV3dXeUlSSlhtMmh4azJ5Ym1QM0F3YnQ2SDg3WUYiLCJtYWMiOiI2ZDgxNTk4NWRiMzE1MWJmNTMwMTFiNWIyNjIzZTNiZTM2NjY0MTk4NTM1ZDk5ZDRjMTBhYjE1ODczNjk2M2FlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881967947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1823807492 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823807492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1243253612 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:48:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243253612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-159151580 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f491af7-99fd-46e3-909d-fa1fe41c58a5</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159151580\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}