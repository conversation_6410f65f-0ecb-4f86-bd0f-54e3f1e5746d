<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AppNotificationResource\Pages;

use App\Models\AppNotification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AppNotificationResource extends Resource
{
    protected static ?string $model = AppNotification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';
    protected static ?string $navigationGroup = 'Notifications';
    protected static ?int $navigationSort = 3;

    protected static ?string $modelLabel = 'Notification';
    protected static ?string $pluralModelLabel = 'Notifications';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Except super admin, all users can only see their own notifications
        if (auth()->check() && !auth()->user()->hasRole('super_admin')) {
            $query->where('user_id', auth()->id());
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Notification Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->disabled(fn() => auth()->check() && !auth()->user()->hasRole('super_admin')),

                        Forms\Components\Select::make('notification_event_id')
                            ->relationship('notificationEvent', 'display_name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('message')
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'sent' => 'Sent',
                                'failed' => 'Failed',
                            ])
                            ->default('pending')
                            ->required(),

                        Forms\Components\DateTimePicker::make('read_at')
                            ->label('Read At'),

                        Forms\Components\KeyValue::make('data')
                            ->keyLabel('Field')
                            ->valueLabel('Value')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('notificationEvent.display_name')
                    ->label('Event')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('message')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'sent' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('read_at')
                    ->label('Read')
                    ->boolean()
                    ->getStateUsing(fn($record) => $record->read_at !== null)
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->visible(fn() => auth()->user()->hasRole('super_admin')),

                Tables\Filters\SelectFilter::make('module')
                    ->label('Module')
                    ->relationship('notificationEvent', 'module')
                    ->options([
                        'projects' => 'Projects',
                        'clients' => 'Clients',
                        'payments' => 'Payments',
                        'milestones' => 'Milestones',
                        'incentives' => 'Incentives',
                        'users' => 'Users',
                        'system' => 'System',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'failed' => 'Failed',
                    ]),

                Tables\Filters\TernaryFilter::make('read')
                    ->label('Read')
                    ->queries(
                        true: fn(Builder $query) => $query->whereNotNull('read_at'),
                        false: fn(Builder $query) => $query->whereNull('read_at'),
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('mark_read')
                    ->label('Mark as Read')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (AppNotification $record): void {
                        $record->update(['read_at' => now()]);
                    })
                    ->visible(fn(AppNotification $record) => $record->read_at === null),
                Tables\Actions\Action::make('mark_unread')
                    ->label('Mark as Unread')
                    ->icon('heroicon-o-x-mark')
                    ->color('gray')
                    ->action(function (AppNotification $record): void {
                        $record->update(['read_at' => null]);
                    })
                    ->visible(fn(AppNotification $record) => $record->read_at !== null),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_read_bulk')
                        ->label('Mark as Read')
                        ->icon('heroicon-o-check')
                        ->action(function (Collection $records): void {
                            $records->each(function ($record) {
                                $record->update(['read_at' => now()]);
                            });
                        }),
                    Tables\Actions\BulkAction::make('mark_unread_bulk')
                        ->label('Mark as Unread')
                        ->icon('heroicon-o-x-mark')
                        ->action(function (Collection $records): void {
                            $records->each(function ($record) {
                                $record->update(['read_at' => null]);
                            });
                        }),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAppNotifications::route('/'),
            'create' => Pages\CreateAppNotification::route('/create'),
            'edit' => Pages\EditAppNotification::route('/{record}/edit'),
        ];
    }
}
