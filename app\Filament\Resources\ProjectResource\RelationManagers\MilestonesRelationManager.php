<?php

namespace App\Filament\Resources\ProjectResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Closure;

class MilestonesRelationManager extends RelationManager
{
    protected static string $relationship = 'milestones';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Repeater::make('milestones')
                    ->label('Milestone Details')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Milestone Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(65535),
                        Forms\Components\DatePicker::make('due_date')
                            ->label('Due Date')
                            ->required(),
                        Forms\Components\TextInput::make('percentage')
                            ->label('Percentage')
                            ->numeric()
                            ->suffix('%')
                            ->maxValue(100)
                            ->required(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project) return false;

                                $project->load('pricingModel');
                                $isTMProject = $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                $isHourDuration = $project->duration_unit === 'hours';

                                return !($isTMProject || $isHourDuration);
                            })
                            ->visible(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project) return true;

                                $project->load('pricingModel');
                                $isTMProject = $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                $isHourDuration = $project->duration_unit === 'hours';

                                return !($isTMProject || $isHourDuration);
                            }),
                        Forms\Components\TextInput::make('hours')
                            ->label('Hours')
                            ->numeric()
                            ->suffix(' hrs')
                            ->minValue(0)
                            ->required(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project) return false;

                                $project->load('pricingModel');
                                $isTMProject = $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                $isHourDuration = $project->duration_unit === 'hours';

                                return $isTMProject || $isHourDuration;
                            })
                            ->visible(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project) return false;

                                $project->load('pricingModel');
                                $isTMProject = $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                $isHourDuration = $project->duration_unit === 'hours';

                                return $isTMProject || $isHourDuration;
                            })
                            ->default(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project || $project->duration_unit !== 'hours') return null;

                                return $project->duration;
                            }),
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->required()
                            ->numeric()
                            ->prefix(function (callable $get) {
                                $project = $this->getOwnerRecord();
                                if (!$project) return '';

                                return match($project->currency) {
                                    'USD' => '$',
                                    'INR' => '₹',
                                    'EUR' => '€',
                                    'GBP' => '£',
                                    default => $project->currency ?? ''
                                };
                            }),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'delayed' => 'Delayed'
                            ])
                            ->default('pending')
                            ->required()
                            ->live(),
                        Forms\Components\Section::make('Payment Information')
                            ->description('Update payment details for this milestone')
                            ->schema([
                                Forms\Components\Grid::make(3)->schema([
                                    Forms\Components\DatePicker::make('payment_due_date')
                                        ->label('Payment Due Date')
                                        ->format('Y-m-d')
                                        ->displayFormat('Y-m-d'),
                                    Forms\Components\DatePicker::make('payment_paid_date')
                                        ->label('Payment Paid Date')
                                        ->format('Y-m-d')
                                        ->displayFormat('Y-m-d'),
                                    Forms\Components\Select::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'bank_transfer' => 'Bank Transfer',
                                            'credit_card' => 'Credit Card',
                                            'upi' => 'UPI',
                                            'cash' => 'Cash',
                                            'cheque' => 'Cheque',
                                            'other' => 'Other',
                                        ]),
                                ]),
                                Forms\Components\Grid::make(3)->schema([
                                    Forms\Components\TextInput::make('transaction_id')
                                        ->label('Transaction ID')
                                        ->maxLength(255),
                                    Forms\Components\Select::make('payment_status')
                                        ->label('Payment Status')
                                        ->options([
                                            'pending' => 'Pending',
                                            'paid' => 'Paid',
                                            'overdue' => 'Overdue',
                                            'cancelled' => 'Cancelled',
                                        ])
                                        ->default('pending'),
                                    Forms\Components\TextInput::make('payment_amount')
                                        ->label('Payment Amount')
                                        ->numeric()
                                        ->prefix(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project) return '';

                                            return match($project->currency) {
                                                'USD' => '$',
                                                'INR' => '₹',
                                                'EUR' => '€',
                                                'GBP' => '£',
                                                default => $project->currency ?? ''
                                            };
                                        })
                                        ->readOnly(),
                                ]),
                                Forms\Components\Textarea::make('payment_notes')
                                    ->label('Payment Notes')
                                    ->maxLength(65535)
                                    ->columnSpanFull(),
                            ])
                            ->visible(function (callable $get) {
                                return $get('status') === 'completed';
                            })
                            ->collapsible()
                            ->collapsed(),
                    ])
                    ->minItems(0)
                    ->createItemButtonLabel('Add Another Milestone')
                    ->addable(function ($get) {
                        $currentItems = $get('milestones') ?? [];
                        return count($currentItems) < 1;
                    })
                    ->reorderable()
                    ->collapsible()
                    ->itemLabel(fn (array $state): ?string => $state['title'] ?? 'New Milestone')
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->modifyQueryUsing(function ($query) {
                $projectId = $this->getOwnerRecord()->id;

                // Get all unmerged milestones for this project
                $milestones = \App\Models\Milestone::where('project_id', $projectId)
                    ->where('is_merged', false)
                    ->orderBy('due_date', 'asc')
                    ->orderBy('created_at', 'asc')
                    ->get();

                $selectedMilestoneIds = [];

                if ($milestones->isNotEmpty()) {
                    // Find the earliest milestone that isn't merged into another one
                    $earliestUnmerged = $milestones->first(function ($milestone) {
                        return $milestone->merged_with_milestone_id === null;
                    });

                    // If all milestones are merged into others, get the one with the earliest due date
                    if (!$earliestUnmerged) {
                        $earliestUnmerged = $milestones->first();
                    }

                    if ($earliestUnmerged) {
                        $selectedMilestoneIds[] = $earliestUnmerged->id;
                    }
                }

                if (!empty($selectedMilestoneIds)) {
                    $query->whereIn('milestones.id', $selectedMilestoneIds);
                } else {
                    $query->where('milestones.id', -1); // Fallback to show nothing
                }

                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable(['milestones.title'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->numeric()
                    ->suffix('%')
                    ->sortable()
                    ->visible(false),
                Tables\Columns\TextColumn::make('hours')
                    ->numeric()
                    ->suffix(' hrs')
                    ->sortable()
                    ->visible(function ($record) {
                        if ($record && $record->project) {
                            $record->project->load('pricingModel');
                            return $record->project->pricingModel && in_array($record->project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                        }
                        return false;
                    }),
                Tables\Columns\TextColumn::make('amount')
                    ->money(fn ($record) => $record->project->currency ?? 'INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'delayed' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('addMilestones')
                    ->label('Add Milestones')
                    ->icon('heroicon-o-plus-circle')
                    ->color('primary')
                    ->modalHeading('Add Milestones')
                    ->modalDescription('Add one or more milestones for this project')
                    ->modalWidth('7xl')
                    ->form([
                        Forms\Components\Repeater::make('milestones')
                            ->label('Milestone Details')
                            ->schema([
                                Forms\Components\Grid::make(6)->schema([
                                    Forms\Components\TextInput::make('title')
                                        ->label('Title')
                                        ->required()
                                        ->maxLength(255),
                                    Forms\Components\Textarea::make('description')
                                        ->label('Description')
                                        ->maxLength(65535),
                                    Forms\Components\DatePicker::make('due_date')
                                        ->label('Due Date')
                                        ->required(),
                                    Forms\Components\TextInput::make('percentage')
                                        ->label('Percentage')
                                        ->numeric()
                                        ->suffix('%')
                                        ->maxValue(100)
                                        ->required(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project || !$project->pricingModel) return false;

                                            return !in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                        })
                                        ->visible(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project || !$project->pricingModel) return true;

                                            return !in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                        }),
                                    Forms\Components\TextInput::make('hours')
                                        ->label('Hours')
                                        ->numeric()
                                        ->suffix(' hrs')
                                        ->minValue(0)
                                        ->required(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project || !$project->pricingModel) return false;

                                            return in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                        })
                                        ->visible(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project || !$project->pricingModel) return false;

                                            return in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                        }),
                                    Forms\Components\TextInput::make('amount')
                                        ->label('Amount')
                                        ->required()
                                        ->numeric()
                                        ->prefix(function (callable $get) {
                                            $project = $this->getOwnerRecord();
                                            if (!$project) return '';

                                            return match($project->currency) {
                                                'USD' => '$',
                                                'INR' => '₹',
                                                'EUR' => '€',
                                                'GBP' => '£',
                                                default => $project->currency ?? ''
                                            };
                                        }),
                                    Forms\Components\Select::make('status')
                                        ->label('Status')
                                        ->options([
                                            'pending' => 'Pending',
                                            'in_progress' => 'In Progress',
                                            'completed' => 'Completed',
                                            'delayed' => 'Delayed'
                                        ])
                                        ->default('pending')
                                        ->required(),
                                ]),
                            ])
                            ->minItems(0)
                            ->addable(false)
                            ->createItemButtonLabel('Add Another Milestone')
                            ->reorderable(),
                    ])
                    ->action(function (array $data) {
                        $projectId = $this->getOwnerRecord()->id;

                        foreach ($data['milestones'] as $milestoneData) {
                            \App\Models\Milestone::create(array_merge($milestoneData, ['project_id' => $projectId]));
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('editAll')
                    ->icon('heroicon-o-pencil-square')
                    ->label('')
                    ->modalWidth('7xl')
                    ->form(function ($record) {
                        // Get all non-merged milestones
                        $milestones = \App\Models\Milestone::where('project_id', $record->project_id)
                            ->where('is_merged', false)
                            ->orderBy('due_date', 'asc')
                            ->orderBy('created_at', 'asc')
                            ->get()
                            ->map(function ($milestone) {
                                $milestoneArray = $milestone->toArray();

                                if ($milestone->due_date) {
                                    if ($milestone->due_date instanceof \Carbon\Carbon) {
                                        $milestoneArray['due_date'] = $milestone->due_date->format('Y-m-d');
                                    } elseif (is_string($milestone->due_date)) {
                                        $milestoneArray['due_date'] = \Carbon\Carbon::parse($milestone->due_date)->format('Y-m-d');
                                    } else {
                                        $milestoneArray['due_date'] = $milestone->due_date;
                                    }
                                } else {
                                    $milestoneArray['due_date'] = null;
                                }

                                $payment = \App\Models\Payment::where('milestone_id', $milestone->id)->first();
                                if ($payment) {
                                    $milestoneArray['payment_due_date'] = $payment->due_date ? $payment->due_date->format('Y-m-d') : null;
                                    $milestoneArray['payment_paid_date'] = $payment->paid_date ? $payment->paid_date->format('Y-m-d') : null;
                                    $milestoneArray['payment_method'] = $payment->payment_method;
                                    $milestoneArray['transaction_id'] = $payment->transaction_id;
                                    $milestoneArray['payment_status'] = $payment->status;
                                    $milestoneArray['payment_amount'] = $payment->amount;
                                    $milestoneArray['payment_notes'] = $payment->notes;
                                } else {
                                    $milestoneArray['payment_due_date'] = $milestoneArray['due_date'];
                                    $milestoneArray['payment_paid_date'] = null;
                                    $milestoneArray['payment_method'] = null;
                                    $milestoneArray['transaction_id'] = null;
                                    $milestoneArray['payment_status'] = 'pending';
                                    $milestoneArray['payment_amount'] = $milestone->amount;
                                    $milestoneArray['payment_notes'] = null;
                                }

                                return $milestoneArray;
                            });

                        return [
                            Forms\Components\Repeater::make('milestones')
                                ->label('Milestones')
                                ->schema([
                                    Forms\Components\Hidden::make('id'),
                                    Forms\Components\Grid::make(6)->schema([
                                        Forms\Components\TextInput::make('title')
                                            ->label('Title')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->maxLength(65535),
                                        Forms\Components\DatePicker::make('due_date')
                                            ->label('Due Date')
                                            ->required()
                                            ->format('Y-m-d')
                                            ->displayFormat('Y-m-d')
                                            ->live()
                                            ->afterStateUpdated(function (callable $get, callable $set, $state) use ($record) {
                                                if (!$state || !$record) {
                                                    return;
                                                }

                                                $milestones = $get('../../milestones') ?? [];
                                                if (empty($milestones)) {
                                                    return;
                                                }

                                                $project = $record->project;
                                                if (!$project) {
                                                    return;
                                                }

                                                $currentMilestoneId = $get('id');
                                                $currentIndex = null;

                                                // Convert milestones to array and find current index
                                                $milestonesArray = array_values($milestones);
                                                foreach ($milestonesArray as $index => $milestone) {
                                                    if (isset($milestone['id']) && $milestone['id'] == $currentMilestoneId) {
                                                        $currentIndex = $index;
                                                        break;
                                                    }
                                                }

                                                if ($currentIndex === null) {
                                                    return;
                                                }

                                                $currentDate = \Carbon\Carbon::parse($state);
                                                $durationUnit = $project->duration_unit ?? 'months';

                                                // Update subsequent milestones
                                                for ($i = $currentIndex + 1; $i < count($milestonesArray); $i++) {
                                                    $intervalCount = $i - $currentIndex;

                                                    $newDate = match($durationUnit) {
                                                        'hours' => $currentDate->copy()->addHours($intervalCount),
                                                        'days' => $currentDate->copy()->addDays($intervalCount),
                                                        'weeks' => $currentDate->copy()->addWeeks($intervalCount),
                                                        'months' => $currentDate->copy()->addMonthsNoOverflow($intervalCount),
                                                        'years' => $currentDate->copy()->addYears($intervalCount),
                                                        default => $currentDate->copy()->addMonthsNoOverflow($intervalCount)
                                                    };

                                                    // Find the key for this milestone in the original $milestones array
                                                    $milestoneKey = array_keys($milestones)[$i];
                                                    $set("../../milestones.{$milestoneKey}.due_date", $newDate->format('Y-m-d'));
                                                }
                                            }),
                                        Forms\Components\TextInput::make('percentage')
                                            ->label('Percentage')
                                            ->numeric()
                                            ->suffix('%')
                                            ->maxValue(100)
                                            ->required(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return !($project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']));
                                            })
                                            ->visible(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return !($project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']));
                                            }),
                                        Forms\Components\TextInput::make('hours')
                                            ->label('Hours')
                                            ->numeric()
                                            ->suffix(' hrs')
                                            ->minValue(0)
                                            ->required(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                            })
                                            ->visible(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                            }),
                                        Forms\Components\TextInput::make('amount')
                                            ->label('Amount')
                                            ->required()
                                            ->numeric()
                                            ->prefix(function () use ($record) {
                                                if (!$record || !$record->project) return '';

                                                $currency = $record->project->currency;
                                                return match($currency) {
                                                    'USD' => '$',
                                                    'INR' => '₹',
                                                    'EUR' => '€',
                                                    'GBP' => '£',
                                                    default => $currency ?? ''
                                                };
                                            }),
                                        Forms\Components\Select::make('status')
                                            ->label('Status')
                                            ->options([
                                                'pending' => 'Pending',
                                                'in_progress' => 'In Progress',
                                                'completed' => 'Completed',
                                                'delayed' => 'Delayed'
                                            ])
                                            ->required()
                                            ->live(),
                                    ]),
                                    Forms\Components\Fieldset::make('Payment Information')
                                        ->schema([
                                            Forms\Components\Grid::make(6)->schema([
                                                Forms\Components\DatePicker::make('payment_due_date')
                                                    ->label('Due Date')
                                                    ->format('Y-m-d')
                                                    ->displayFormat('Y-m-d'),
                                                Forms\Components\DatePicker::make('payment_paid_date')
                                                    ->label('Paid Date')
                                                    ->format('Y-m-d')
                                                    ->displayFormat('Y-m-d'),
                                                Forms\Components\Select::make('payment_status')
                                                    ->label('Status')
                                                    ->options([
                                                        'pending' => 'Pending',
                                                        'paid' => 'Paid',
                                                        'overdue' => 'Overdue',
                                                        'cancelled' => 'Cancelled',
                                                    ])
                                                    ->default('pending'),
                                                Forms\Components\Select::make('payment_method')
                                                    ->label('Method')
                                                    ->options([
                                                        'bank_transfer' => 'Bank Transfer',
                                                        'credit_card' => 'Credit Card',
                                                        'upi' => 'UPI',
                                                        'cash' => 'Cash',
                                                        'cheque' => 'Cheque',
                                                        'other' => 'Other',
                                                    ]),
                                                Forms\Components\TextInput::make('transaction_id')
                                                    ->label('Transaction ID')
                                                    ->maxLength(255),
                                                Forms\Components\TextInput::make('payment_amount')
                                                    ->label('Payment Amount')
                                                    ->numeric()
                                                    ->prefix(function () use ($record) {
                                                        if (!$record || !$record->project) return '';

                                                        $currency = $record->project->currency;
                                                        return match($currency) {
                                                            'USD' => '$',
                                                            'INR' => '₹',
                                                            'EUR' => '€',
                                                            'GBP' => '£',
                                                            default => $currency ?? ''
                                                        };
                                                    })
                                                    ->readOnly(),
                                            ]),
                                            Forms\Components\Textarea::make('payment_notes')
                                                ->label('Notes')
                                                ->maxLength(65535)
                                                ->columnSpanFull(),
                                        ])
                                        ->visible(function (callable $get) {
                                            return $get('status') === 'completed';
                                        }),
                                    Forms\Components\Fieldset::make('Merge Milestones')
                                        ->schema([
                                            Forms\Components\Section::make('📋 Merged Milestones Information')
                                                ->schema([
                                                    Forms\Components\Placeholder::make('merged_milestones_guide')
                                                        ->label('')
                                                        ->content(function (callable $get) {
                                                            $milestoneId = $get('id');
                                                            if (!$milestoneId) return '';

                                                            $mergedMilestones = \App\Models\Milestone::where('merged_with_milestone_id', $milestoneId)
                                                                ->where('is_merged', true)
                                                                ->get();

                                                            if ($mergedMilestones->isEmpty()) {
                                                                return '';
                                                            }

                                                            $info = ['🔗 Merged Milestones:'];
                                                            foreach ($mergedMilestones as $merged) {
                                                                $info[] = "   • {$merged->title}";
                                                                $info[] = "     └─ Original: \${$merged->original_amount} / {$merged->original_percentage}%";
                                                                if ($merged->original_due_date) {
                                                                    $info[] = "     └─ Due Date: {$merged->original_due_date->format('Y-m-d')}";
                                                                }
                                                                $info[] = "";
                                                            }

                                                            return implode("\n", $info);
                                                        })
                                                        ->columnSpanFull(),
                                                ])
                                                ->visible(function (callable $get) {
                                                    $milestoneId = $get('id');
                                                    if (!$milestoneId) return false;

                                                    return \App\Models\Milestone::where('merged_with_milestone_id', $milestoneId)
                                                        ->where('is_merged', true)
                                                        ->exists();
                                                })
                                                ->collapsible()
                                                ->collapsed(false),
                                            Forms\Components\Grid::make(2)->schema([
                                                Forms\Components\Select::make('merge_milestone_ids')
                                                    ->label('🔗 Select Milestones to Merge into This One')
                                                    ->multiple()
                                                    ->options(function (callable $get) {
                                                        $currentMilestoneId = $get('id');
                                                        if (!$currentMilestoneId) return [];

                                                        $currentMilestone = \App\Models\Milestone::find($currentMilestoneId);
                                                        if (!$currentMilestone) return [];

                                                        $projectId = $currentMilestone->project_id;
                                                        if (!$projectId) return [];

                                                        return \App\Models\Milestone::where('project_id', $projectId)
                                                            ->where('id', '!=', $currentMilestoneId)
                                                            ->where('is_merged', false)
                                                            ->whereNull('merged_with_milestone_id')
                                                            ->get()
                                                            ->mapWithKeys(function ($milestone) {
                                                                return [
                                                                    $milestone->id => sprintf(
                                                                        "%s (%s %.2f, %.2f%%)",
                                                                        $milestone->title,
                                                                        $milestone->project->currency === 'USD' ? '$' : '₹',
                                                                        $milestone->amount,
                                                                        $milestone->percentage
                                                                    )
                                                                ];
                                                            });
                                                    })
                                                    ->searchable()
                                                    ->placeholder('Select milestones to merge')
                                                    ->helperText('Selected milestones will be merged when you submit the form'),

                                                Forms\Components\Select::make('unmerge_milestone_ids')
                                                    ->label('🔓 Select Merged Milestones to Unmerge')
                                                    ->multiple()
                                                    ->options(function (callable $get) {
                                                        $currentMilestoneId = $get('id');
                                                        if (!$currentMilestoneId) return [];

                                                        $mergedMilestones = \App\Models\Milestone::where('merged_with_milestone_id', $currentMilestoneId)
                                                            ->where('is_merged', true)
                                                            ->get();

                                                        return $mergedMilestones->mapWithKeys(function ($milestone) {
                                                            return [
                                                                $milestone->id => $milestone->title . ' (Original: $' . $milestone->original_amount . ', ' . $milestone->original_percentage . '%)'
                                                            ];
                                                        });
                                                    })
                                                    ->searchable()
                                                    ->placeholder('Select milestones to unmerge')
                                                    ->helperText('Selected milestones will be unmerged when you submit the form')
                                                    ->visible(function (callable $get) {
                                                        $currentMilestoneId = $get('id');
                                                        if (!$currentMilestoneId) return false;

                                                        $mergedCount = \App\Models\Milestone::where('merged_with_milestone_id', $currentMilestoneId)
                                                            ->where('is_merged', true)
                                                            ->count();

                                                        return $mergedCount > 0;
                                                    }),
                                            ])
                                        ])
                                ])
                                ->default($milestones)
                                ->minItems(0)
                                ->addable(false)
                                ->createItemButtonLabel('+ Add Milestone')
                                ->reorderable()
                                ->collapsible()
                                ->itemLabel(fn (array $state): ?string => $state['title'] ?? 'New Milestone')
                                ->rules([
                                    function () use ($record) {
                                        return function (string $attribute, $value, Closure $fail) use ($record) {
                                            $project = $record->project;

                                            $totalPercentage = collect($value)->sum('percentage');
                                            $totalAmount = collect($value)->sum('amount');

                                            // Use round() to handle floating point precision issues
                                            $roundedTotalPercentage = round($totalPercentage, 2);
                                            $roundedTotalAmount = round($totalAmount, 2);
                                            $roundedProjectTotal = round($project->total_payment, 2);

                                            if ($roundedTotalPercentage > 100) {
                                                $availablePercentage = 100;
                                                $fail("The total percentage cannot exceed 100%. Available: {$availablePercentage}%, Trying to set: {$roundedTotalPercentage}%");
                                            }

                                            if ($roundedTotalAmount > $roundedProjectTotal) {
                                                $fail("The total amount cannot exceed the project total payment of {$roundedProjectTotal}. Available: {$roundedProjectTotal}, Trying to set: {$roundedTotalAmount}");
                                            }
                                        };
                                    },
                                ])
                        ];
                    })
                    ->action(function ($record, $data) {
                        $projectId = $record->project_id;
                        $existingIds = collect($data['milestones'])->pluck('id')->filter();

                        // Delete removed milestones (and their associated payments)
                        $milestonesToDelete = \App\Models\Milestone::where('project_id', $projectId)
                            ->whereNotIn('id', $existingIds)
                            ->where('is_merged', false)
                            ->get();

                        foreach ($milestonesToDelete as $milestone) {
                            \App\Models\Payment::where('milestone_id', $milestone->id)->delete();
                            $milestone->delete();
                        }

                        // Track which milestones were merged in this operation
                        $mergedMilestoneIds = [];

                        // Process each milestone in the form
                        foreach ($data['milestones'] as $milestoneData) {
                            // Skip processing if this milestone was just merged
                            if (in_array($milestoneData['id'] ?? null, $mergedMilestoneIds)) {
                                continue;
                            }

                            // Extract payment data
                            $paymentData = [
                                'due_date' => $milestoneData['payment_due_date'] ?? null,
                                'paid_date' => $milestoneData['payment_paid_date'] ?? null,
                                'payment_method' => $milestoneData['payment_method'] ?? null,
                                'transaction_id' => $milestoneData['transaction_id'] ?? null,
                                'status' => $milestoneData['payment_status'] ?? 'pending',
                                'amount' => $milestoneData['payment_amount'] ?? $milestoneData['amount'],
                                'notes' => $milestoneData['payment_notes'] ?? null,
                            ];

                            // Remove payment fields from milestone data
                            $cleanMilestoneData = collect($milestoneData)->except([
                                'payment_due_date', 'payment_paid_date', 'payment_method',
                                'transaction_id', 'payment_status', 'payment_amount', 'payment_notes',
                                'merge_milestone_ids', 'unmerge_milestone_ids'
                            ])->toArray();

                            if (isset($milestoneData['id']) && !empty($milestoneData['id'])) {
                                // Update existing milestone
                                $milestone = \App\Models\Milestone::find($milestoneData['id']);

                                // Check if this milestone will be a merge target
                                $willBeMergeTarget = isset($milestoneData['merge_milestone_ids']) && !empty($milestoneData['merge_milestone_ids']);

                                if (!$willBeMergeTarget) {
                                    $milestone->update($cleanMilestoneData);
                                }

                                // Handle merge functionality on form submission
                                if (isset($milestoneData['merge_milestone_ids']) && !empty($milestoneData['merge_milestone_ids'])) {
                                    Log::info('Merge process started', [
                                        'target_milestone_id' => $milestone->id,
                                        'merge_milestone_ids' => $milestoneData['merge_milestone_ids']
                                    ]);

                                    $totalAmount = $milestone->amount;
                                    $totalPercentage = $milestone->percentage;
                                    $totalHours = $milestone->hours;
                                    $latestDueDate = $milestone->due_date;

                                    // Store target milestone's original values if not already stored
                                    $targetOriginalAmount = $milestone->original_amount ?? $milestone->amount;
                                    $targetOriginalPercentage = $milestone->original_percentage ?? $milestone->percentage;
                                    $targetOriginalHours = $milestone->original_hours ?? $milestone->hours;
                                    $targetOriginalDueDate = $milestone->original_due_date ?? $milestone->due_date;

                                    foreach ($milestoneData['merge_milestone_ids'] as $mergeId) {
                                        $milestoneToMerge = \App\Models\Milestone::find($mergeId);
                                        if (!$milestoneToMerge || $milestoneToMerge->is_merged) continue;

                                        $totalAmount += $milestoneToMerge->amount;
                                        $totalPercentage += $milestoneToMerge->percentage;
                                        $totalHours += $milestoneToMerge->hours;

                                        if ($milestoneToMerge->due_date && (!$latestDueDate || $milestoneToMerge->due_date > $latestDueDate)) {
                                            $latestDueDate = $milestoneToMerge->due_date;
                                        }

                                        // Mark milestone as merged
                                        $updateData = [
                                            'is_merged' => true,
                                            'merged_with_milestone_id' => $milestone->id,
                                            'original_amount' => $milestoneToMerge->amount,
                                            'original_percentage' => $milestoneToMerge->percentage,
                                            'original_hours' => $milestoneToMerge->hours,
                                            'original_due_date' => $milestoneToMerge->due_date,
                                        ];

                                        $milestoneToMerge->update($updateData);
                                        $mergedMilestoneIds[] = $milestoneToMerge->id;

                                        // Delete associated payments
                                        \App\Models\Payment::where('milestone_id', $milestoneToMerge->id)->delete();
                                    }

                                    // Update target milestone with combined values
                                    $milestone->update([
                                        'amount' => round($totalAmount, 2),
                                        'percentage' => round($totalPercentage, 2),
                                        'hours' => round($totalHours, 2),
                                        'due_date' => $latestDueDate,
                                        'original_amount' => $targetOriginalAmount,
                                        'original_percentage' => $targetOriginalPercentage,
                                        'original_hours' => $targetOriginalHours,
                                        'original_due_date' => $targetOriginalDueDate,
                                    ]);

                                    // Update payment amount to match new milestone amount
                                    $paymentData['amount'] = round($totalAmount, 2);
                                }

                                // Handle unmerge functionality on form submission
                                if (isset($milestoneData['unmerge_milestone_ids']) && !empty($milestoneData['unmerge_milestone_ids'])) {
                                    $totalAmount = $milestone->amount;
                                    $totalPercentage = $milestone->percentage;
                                    $totalHours = $milestone->hours;

                                    $milestonesToUnmerge = \App\Models\Milestone::whereIn('id', $milestoneData['unmerge_milestone_ids'])
                                        ->where('merged_with_milestone_id', $milestone->id)
                                        ->where('is_merged', true)
                                        ->get();

                                    foreach ($milestonesToUnmerge as $milestoneToUnmerge) {
                                        $totalAmount -= $milestoneToUnmerge->original_amount;
                                        $totalPercentage -= $milestoneToUnmerge->original_percentage;
                                        $totalHours -= $milestoneToUnmerge->original_hours;

                                        // Restore original values
                                        $milestoneToUnmerge->update([
                                            'is_merged' => false,
                                            'merged_with_milestone_id' => null,
                                            'amount' => $milestoneToUnmerge->original_amount,
                                            'percentage' => $milestoneToUnmerge->original_percentage,
                                            'hours' => $milestoneToUnmerge->original_hours,
                                            'due_date' => $milestoneToUnmerge->original_due_date,
                                            'original_amount' => null,
                                            'original_percentage' => null,
                                            'original_hours' => null,
                                            'original_due_date' => null,
                                        ]);
                                    }

                                    // Check if all merged milestones are unmerged
                                    $remainingMerged = \App\Models\Milestone::where('merged_with_milestone_id', $milestone->id)
                                        ->where('is_merged', true)
                                        ->count();

                                    // If no more merged milestones, restore target's original values
                                    $targetDueDate = $remainingMerged > 0 ? $milestone->due_date : $milestone->original_due_date;
                                    $targetOriginalDueDate = $remainingMerged > 0 ? $milestone->original_due_date : null;

                                    // Update target milestone after unmerging
                                    $milestone->update([
                                        'amount' => round($totalAmount, 2),
                                        'percentage' => round($totalPercentage, 2),
                                        'hours' => round($totalHours, 2),
                                        'due_date' => $targetDueDate,
                                        'original_due_date' => $targetOriginalDueDate,
                                    ]);
                                }

                                // Update or create payment for this milestone
                                $payment = \App\Models\Payment::where('milestone_id', $milestone->id)->first();
                                if ($payment) {
                                    $payment->update($paymentData);
                                } else {
                                    \App\Models\Payment::create(array_merge($paymentData, [
                                        'project_id' => $projectId,
                                        'milestone_id' => $milestone->id,
                                    ]));
                                }
                            } else {
                                // Create new milestone
                                $milestone = \App\Models\Milestone::create(array_merge(
                                    $cleanMilestoneData,
                                    ['project_id' => $projectId]
                                ));

                                // Create payment for new milestone
                                \App\Models\Payment::create(array_merge($paymentData, [
                                    'project_id' => $projectId,
                                    'milestone_id' => $milestone->id,
                                ]));
                            }
                        }
                    })
                    ->successNotification(
                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Milestones Updated')
                            ->body('Milestones have been successfully updated.')
                    )
                    ->after(function () {
                        $this->dispatch('refresh');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->label('')
                    ->requiresConfirmation(),
            ]);
    }

    public function getOwnerRecord(): Model
    {
        return $this->ownerRecord;
    }
}
