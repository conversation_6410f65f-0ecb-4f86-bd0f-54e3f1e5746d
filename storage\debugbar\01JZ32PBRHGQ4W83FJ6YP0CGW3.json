{"__meta": {"id": "01JZ32PBRHGQ4W83FJ6YP0CGW3", "datetime": "2025-07-01 13:26:48", "utime": **********.338819, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751376406.525801, "end": **********.338846, "duration": 1.8130450248718262, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1751376406.525801, "relative_start": 0, "end": **********.344375, "relative_end": **********.344375, "duration": 0.****************, "duration_str": "819ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.344391, "relative_start": 0.****************, "end": **********.33885, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "994ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.317575, "relative_start": 1.***************, "end": **********.321862, "relative_end": **********.321862, "duration": 0.004287004470825195, "duration_str": "4.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.335697, "relative_start": 1.****************, "end": **********.336404, "relative_end": **********.336404, "duration": 0.0007071495056152344, "duration_str": "707μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.336429, "relative_start": 1.****************, "end": **********.336463, "relative_end": **********.336463, "duration": 3.3855438232421875e-05, "duration_str": "34μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "1.81s", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1925757258 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925757258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1739944665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1739944665\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1218097784 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6ImJvNGVGYkttblBnY0s0cnlwYVU3YVE9PSIsInZhbHVlIjoiOHQ4VjFQWXNmTUs1b3l1TGxVamZmd3BnYTlQcnI3bmtGUDhZV3hHSXB4UVZtclZXZmQ3NTdSd21PZ2FsWTFRVGkraURDTkJWemdTanVPTC9iaDgwd3ZvR3lxakluU1FKS2ExSGFjNFI2dkdVN0dzTmVCYUJMSXBWSjB3cmFCUDhsOU53SVE3UnJLejJ6RkVhNzdVSFJjcXZQcDZWUHRIUExCNXppdjVKeDF3VUFXNEc4RUVhc0lJaDdIK0xrckZCSkplendQSGg3VnV6SUpNWmFwcURDalJ4RHdrZEtXS3R0aXl1Z1Vma0p0dz0iLCJtYWMiOiI1ZDQzN2Q2MTk5YTA0YzE3NGNiMzg0ZTljY2EzODM1NWNkMWE1NmVlZDJlMzI0OWU2MWVhZGM1NTYxODQ2YTI1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNRcTdRZEhUVW1OaTdTNmFrdVpYaEE9PSIsInZhbHVlIjoiWDhDeEh3N040UUtWTE9DTzZ4NzkyeDZSc1VBeEVMTmVsT3VWMW1kNVd3ZnFpTUlPbTBWa3VuN0NDUWNVUXNDYkJHaWJYSGFGZUZEZWZQL2pOWExmaWtVVHdGdXRhSndUaTFPWkdPSGNScWtRSHM5cHR5YkhLVDRSaENYOHlteDkiLCJtYWMiOiJmZDk1Y2ExODhhY2Q5Y2Q5ZDE2ZTg1OWJjNTE4MGRkNjQ5MDUxZDZmOWYxN2Y3MmQ5Y2QzOTRhNGUwNjljZjZkIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkdwaXdZUTYxaFlNMHJhOFdSOVl2Vnc9PSIsInZhbHVlIjoiNjBJL2lnTGZmQTJCaCtpL09wcldOYUhSTEV3YWYxOUN5ek9zRWdIb0xBTXRrbW5QMHVtWkkrdVZaRmorQTF3bDNwWW1rb29GL2FuaUJxMHJQSElwOU1uN20wWWhEUlRLcHg1QWNLbjBKbUx5OFNMSHhFQXM0UDh1RnJwUThDK0QiLCJtYWMiOiI3OGM1M2FmYWFhNGZhYzk3MDhkZDRmNTliYzUyMTMwY2IwYzNjMzFkZDExZjAzNWU3MTM2ZjFkMzFkYTkwZmZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218097784\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1802783692 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>sb-login</span>\" => \"<span class=sf-dump-str title=\"683 characters\">dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM</span>\"\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6ImJvNGVGYkttblBnY0s0cnlwYVU3YVE9PSIsInZhbHVlIjoiOHQ4VjFQWXNmTUs1b3l1TGxVamZmd3BnYTlQcnI3bmtGUDhZV3hHSXB4UVZtclZXZmQ3NTdSd21PZ2FsWTFRVGkraURDTkJWemdTanVPTC9iaDgwd3ZvR3lxakluU1FKS2ExSGFjNFI2dkdVN0dzTmVCYUJMSXBWSjB3cmFCUDhsOU53SVE3UnJLejJ6RkVhNzdVSFJjcXZQcDZWUHRIUExCNXppdjVKeDF3VUFXNEc4RUVhc0lJaDdIK0xrckZCSkplendQSGg3VnV6SUpNWmFwcURDalJ4RHdrZEtXS3R0aXl1Z1Vma0p0dz0iLCJtYWMiOiI1ZDQzN2Q2MTk5YTA0YzE3NGNiMzg0ZTljY2EzODM1NWNkMWE1NmVlZDJlMzI0OWU2MWVhZGM1NTYxODQ2YTI1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlNRcTdRZEhUVW1OaTdTNmFrdVpYaEE9PSIsInZhbHVlIjoiWDhDeEh3N040UUtWTE9DTzZ4NzkyeDZSc1VBeEVMTmVsT3VWMW1kNVd3ZnFpTUlPbTBWa3VuN0NDUWNVUXNDYkJHaWJYSGFGZUZEZWZQL2pOWExmaWtVVHdGdXRhSndUaTFPWkdPSGNScWtRSHM5cHR5YkhLVDRSaENYOHlteDkiLCJtYWMiOiJmZDk1Y2ExODhhY2Q5Y2Q5ZDE2ZTg1OWJjNTE4MGRkNjQ5MDUxZDZmOWYxN2Y3MmQ5Y2QzOTRhNGUwNjljZjZkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdwaXdZUTYxaFlNMHJhOFdSOVl2Vnc9PSIsInZhbHVlIjoiNjBJL2lnTGZmQTJCaCtpL09wcldOYUhSTEV3YWYxOUN5ek9zRWdIb0xBTXRrbW5QMHVtWkkrdVZaRmorQTF3bDNwWW1rb29GL2FuaUJxMHJQSElwOU1uN20wWWhEUlRLcHg1QWNLbjBKbUx5OFNMSHhFQXM0UDh1RnJwUThDK0QiLCJtYWMiOiI3OGM1M2FmYWFhNGZhYzk3MDhkZDRmNTliYzUyMTMwY2IwYzNjMzFkZDExZjAzNWU3MTM2ZjFkMzFkYTkwZmZhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802783692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-109304216 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 13:26:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 09:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 13:26:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109304216\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1812984407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1812984407\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}