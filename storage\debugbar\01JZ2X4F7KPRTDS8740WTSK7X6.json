{"__meta": {"id": "01JZ2X4F7KPRTDS8740WTSK7X6", "datetime": "2025-07-01 11:49:39", "utime": **********.187889, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.073695, "end": **********.187913, "duration": 26.11421799659729, "duration_str": "26.11s", "measures": [{"label": "Booting", "start": **********.073695, "relative_start": 0, "end": **********.492786, "relative_end": **********.492786, "duration": 0.*****************, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492796, "relative_start": 0.****************, "end": **********.187916, "relative_end": 3.0994415283203125e-06, "duration": 25.***************, "duration_str": "25.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.714624, "relative_start": 0.****************, "end": **********.71674, "relative_end": **********.71674, "duration": 0.002115964889526367, "duration_str": "2.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.66161, "relative_start": 7.***************, "end": **********.66161, "relative_end": **********.66161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.907667, "relative_start": 9.***************, "end": **********.907667, "relative_end": **********.907667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.782008, "relative_start": 10.70831298828125, "end": **********.782008, "relative_end": **********.782008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1751370566.188528, "relative_start": 13.114833116531372, "end": 1751370566.188528, "relative_end": 1751370566.188528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370569.722788, "relative_start": 16.64909315109253, "end": 1751370569.722788, "relative_end": 1751370569.722788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370570.45164, "relative_start": 17.377944946289062, "end": 1751370570.45164, "relative_end": 1751370570.45164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370570.463608, "relative_start": 17.389913082122803, "end": 1751370570.463608, "relative_end": 1751370570.463608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370570.485437, "relative_start": 17.411741971969604, "end": 1751370570.485437, "relative_end": 1751370570.485437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370571.130916, "relative_start": 18.057221174240112, "end": 1751370571.130916, "relative_end": 1751370571.130916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370571.134914, "relative_start": 18.061218976974487, "end": 1751370571.134914, "relative_end": 1751370571.134914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370571.142232, "relative_start": 18.06853699684143, "end": 1751370571.142232, "relative_end": 1751370571.142232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1751370571.146401, "relative_start": 18.0727059841156, "end": 1751370571.146401, "relative_end": 1751370571.146401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751370571.658502, "relative_start": 18.58480715751648, "end": 1751370571.658502, "relative_end": 1751370571.658502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1751370571.67018, "relative_start": 18.596485137939453, "end": 1751370571.67018, "relative_end": 1751370571.67018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751370573.537496, "relative_start": 20.46380114555359, "end": 1751370573.537496, "relative_end": 1751370573.537496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751370573.554684, "relative_start": 20.4809889793396, "end": 1751370573.554684, "relative_end": 1751370573.554684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.175686, "relative_start": 26.101990938186646, "end": **********.175686, "relative_end": **********.175686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.183676, "relative_start": 26.109981060028076, "end": **********.185079, "relative_end": **********.185079, "duration": 0.0014030933380126953, "duration_str": "1.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59646336, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 17, "nb_templates": 17, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.661582, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.907643, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.781982, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1751370566.188494, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370569.72276, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370570.451624, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370570.463588, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370570.485418, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370571.1309, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370571.134898, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370571.142214, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1751370571.146384, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": 1751370571.658481, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": 1751370571.670164, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751370573.537456, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751370573.554662, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.175671, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02167, "accumulated_duration_str": "21.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV' limit 1", "type": "query", "params": [], "bindings": ["UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.7213242, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.907}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.732592, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.907, "width_percent": 2.4}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.735671, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.307, "width_percent": 2.4}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.738245, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.707, "width_percent": 1.384}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7395608, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.091, "width_percent": 1.938}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.753939, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.029, "width_percent": 3.276}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.757504, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.305, "width_percent": 2.446}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.759069, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.751, "width_percent": 14.582}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.7666528, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.334, "width_percent": 19.797}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1751456953, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:242:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:22:\\\"view_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:26:\\\"view_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:24:\\\"create_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:24:\\\"update_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:25:\\\"restore_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:29:\\\"restore_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:27:\\\"replicate_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:25:\\\"reorder_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:24:\\\"delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:28:\\\"delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:30:\\\"force_delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:34:\\\"force_delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:11:\\\"view_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:15:\\\"view_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:13:\\\"create_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:13:\\\"update_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:14:\\\"restore_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:18:\\\"restore_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:16:\\\"replicate_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:14:\\\"reorder_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:13:\\\"delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:17:\\\"delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:19:\\\"force_delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:23:\\\"force_delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:14:\\\"view_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"view_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"create_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:16:\\\"update_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:17:\\\"restore_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:21:\\\"restore_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:19:\\\"replicate_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:17:\\\"reorder_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:16:\\\"delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:20:\\\"delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:22:\\\"force_delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:26:\\\"force_delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:20:\\\"view_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:24:\\\"view_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:22:\\\"create_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:22:\\\"update_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:23:\\\"restore_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:27:\\\"restore_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:25:\\\"replicate_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:23:\\\"reorder_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:22:\\\"delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:26:\\\"delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:28:\\\"force_delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:32:\\\"force_delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:14:\\\"view_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:18:\\\"view_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"create_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:16:\\\"update_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:17:\\\"restore_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:21:\\\"restore_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:19:\\\"replicate_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:17:\\\"reorder_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:16:\\\"delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:20:\\\"delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:22:\\\"force_delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:26:\\\"force_delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:24:\\\"view_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:61;a:4:{s:1:\\\"a\\\";i:62;s:1:\\\"b\\\";s:28:\\\"view_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:62;a:4:{s:1:\\\"a\\\";i:63;s:1:\\\"b\\\";s:26:\\\"create_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:63;a:4:{s:1:\\\"a\\\";i:64;s:1:\\\"b\\\";s:26:\\\"update_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:64;a:4:{s:1:\\\"a\\\";i:65;s:1:\\\"b\\\";s:27:\\\"restore_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:65;a:4:{s:1:\\\"a\\\";i:66;s:1:\\\"b\\\";s:31:\\\"restore_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:66;a:4:{s:1:\\\"a\\\";i:67;s:1:\\\"b\\\";s:29:\\\"replicate_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:67;a:4:{s:1:\\\"a\\\";i:68;s:1:\\\"b\\\";s:27:\\\"reorder_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:68;a:4:{s:1:\\\"a\\\";i:69;s:1:\\\"b\\\";s:26:\\\"delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:69;a:4:{s:1:\\\"a\\\";i:70;s:1:\\\"b\\\";s:30:\\\"delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:70;a:4:{s:1:\\\"a\\\";i:71;s:1:\\\"b\\\";s:32:\\\"force_delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:71;a:4:{s:1:\\\"a\\\";i:72;s:1:\\\"b\\\";s:36:\\\"force_delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:72;a:4:{s:1:\\\"a\\\";i:73;s:1:\\\"b\\\";s:35:\\\"view_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:73;a:4:{s:1:\\\"a\\\";i:74;s:1:\\\"b\\\";s:39:\\\"view_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:74;a:4:{s:1:\\\"a\\\";i:75;s:1:\\\"b\\\";s:37:\\\"create_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:75;a:4:{s:1:\\\"a\\\";i:76;s:1:\\\"b\\\";s:37:\\\"update_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:76;a:4:{s:1:\\\"a\\\";i:77;s:1:\\\"b\\\";s:38:\\\"restore_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:77;a:4:{s:1:\\\"a\\\";i:78;s:1:\\\"b\\\";s:42:\\\"restore_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:78;a:4:{s:1:\\\"a\\\";i:79;s:1:\\\"b\\\";s:40:\\\"replicate_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:79;a:4:{s:1:\\\"a\\\";i:80;s:1:\\\"b\\\";s:38:\\\"reorder_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:80;a:4:{s:1:\\\"a\\\";i:81;s:1:\\\"b\\\";s:37:\\\"delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:81;a:4:{s:1:\\\"a\\\";i:82;s:1:\\\"b\\\";s:41:\\\"delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:82;a:4:{s:1:\\\"a\\\";i:83;s:1:\\\"b\\\";s:43:\\\"force_delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:83;a:4:{s:1:\\\"a\\\";i:84;s:1:\\\"b\\\";s:47:\\\"force_delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:84;a:4:{s:1:\\\"a\\\";i:85;s:1:\\\"b\\\";s:12:\\\"view_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\\\"a\\\";i:86;s:1:\\\"b\\\";s:16:\\\"view_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\\\"a\\\";i:87;s:1:\\\"b\\\";s:14:\\\"create_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\\\"a\\\";i:88;s:1:\\\"b\\\";s:14:\\\"update_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\\\"a\\\";i:89;s:1:\\\"b\\\";s:15:\\\"restore_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:89;a:4:{s:1:\\\"a\\\";i:90;s:1:\\\"b\\\";s:19:\\\"restore_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:90;a:4:{s:1:\\\"a\\\";i:91;s:1:\\\"b\\\";s:17:\\\"replicate_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:91;a:4:{s:1:\\\"a\\\";i:92;s:1:\\\"b\\\";s:15:\\\"reorder_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:92;a:4:{s:1:\\\"a\\\";i:93;s:1:\\\"b\\\";s:14:\\\"delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\\\"a\\\";i:94;s:1:\\\"b\\\";s:18:\\\"delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\\\"a\\\";i:95;s:1:\\\"b\\\";s:20:\\\"force_delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:95;a:4:{s:1:\\\"a\\\";i:96;s:1:\\\"b\\\";s:24:\\\"force_delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:96;a:4:{s:1:\\\"a\\\";i:97;s:1:\\\"b\\\";s:12:\\\"view_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\\\"a\\\";i:98;s:1:\\\"b\\\";s:16:\\\"view_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\\\"a\\\";i:99;s:1:\\\"b\\\";s:14:\\\"create_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\\\"a\\\";i:100;s:1:\\\"b\\\";s:14:\\\"update_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\\\"a\\\";i:101;s:1:\\\"b\\\";s:15:\\\"restore_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:101;a:4:{s:1:\\\"a\\\";i:102;s:1:\\\"b\\\";s:19:\\\"restore_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:102;a:4:{s:1:\\\"a\\\";i:103;s:1:\\\"b\\\";s:17:\\\"replicate_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:103;a:4:{s:1:\\\"a\\\";i:104;s:1:\\\"b\\\";s:15:\\\"reorder_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:104;a:4:{s:1:\\\"a\\\";i:105;s:1:\\\"b\\\";s:14:\\\"delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\\\"a\\\";i:106;s:1:\\\"b\\\";s:18:\\\"delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\\\"a\\\";i:107;s:1:\\\"b\\\";s:20:\\\"force_delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:107;a:4:{s:1:\\\"a\\\";i:108;s:1:\\\"b\\\";s:24:\\\"force_delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:108;a:4:{s:1:\\\"a\\\";i:109;s:1:\\\"b\\\";s:18:\\\"view_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:109;a:4:{s:1:\\\"a\\\";i:110;s:1:\\\"b\\\";s:22:\\\"view_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:110;a:4:{s:1:\\\"a\\\";i:111;s:1:\\\"b\\\";s:20:\\\"create_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:111;a:4:{s:1:\\\"a\\\";i:112;s:1:\\\"b\\\";s:20:\\\"update_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:112;a:4:{s:1:\\\"a\\\";i:113;s:1:\\\"b\\\";s:21:\\\"restore_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:113;a:4:{s:1:\\\"a\\\";i:114;s:1:\\\"b\\\";s:25:\\\"restore_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:114;a:4:{s:1:\\\"a\\\";i:115;s:1:\\\"b\\\";s:23:\\\"replicate_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:115;a:4:{s:1:\\\"a\\\";i:116;s:1:\\\"b\\\";s:21:\\\"reorder_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:116;a:4:{s:1:\\\"a\\\";i:117;s:1:\\\"b\\\";s:20:\\\"delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:117;a:4:{s:1:\\\"a\\\";i:118;s:1:\\\"b\\\";s:24:\\\"delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:118;a:4:{s:1:\\\"a\\\";i:119;s:1:\\\"b\\\";s:26:\\\"force_delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:119;a:4:{s:1:\\\"a\\\";i:120;s:1:\\\"b\\\";s:30:\\\"force_delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:120;a:4:{s:1:\\\"a\\\";i:121;s:1:\\\"b\\\";s:9:\\\"view_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:121;a:4:{s:1:\\\"a\\\";i:122;s:1:\\\"b\\\";s:13:\\\"view_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:122;a:4:{s:1:\\\"a\\\";i:123;s:1:\\\"b\\\";s:11:\\\"create_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:123;a:4:{s:1:\\\"a\\\";i:124;s:1:\\\"b\\\";s:11:\\\"update_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:124;a:4:{s:1:\\\"a\\\";i:125;s:1:\\\"b\\\";s:11:\\\"delete_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:125;a:4:{s:1:\\\"a\\\";i:126;s:1:\\\"b\\\";s:15:\\\"delete_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:126;a:4:{s:1:\\\"a\\\";i:127;s:1:\\\"b\\\";s:33:\\\"view_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:127;a:4:{s:1:\\\"a\\\";i:128;s:1:\\\"b\\\";s:37:\\\"view_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:128;a:4:{s:1:\\\"a\\\";i:129;s:1:\\\"b\\\";s:35:\\\"create_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:129;a:4:{s:1:\\\"a\\\";i:130;s:1:\\\"b\\\";s:35:\\\"update_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:130;a:4:{s:1:\\\"a\\\";i:131;s:1:\\\"b\\\";s:36:\\\"restore_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:131;a:4:{s:1:\\\"a\\\";i:132;s:1:\\\"b\\\";s:40:\\\"restore_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:132;a:4:{s:1:\\\"a\\\";i:133;s:1:\\\"b\\\";s:38:\\\"replicate_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:133;a:4:{s:1:\\\"a\\\";i:134;s:1:\\\"b\\\";s:36:\\\"reorder_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:134;a:4:{s:1:\\\"a\\\";i:135;s:1:\\\"b\\\";s:35:\\\"delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:135;a:4:{s:1:\\\"a\\\";i:136;s:1:\\\"b\\\";s:39:\\\"delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:136;a:4:{s:1:\\\"a\\\";i:137;s:1:\\\"b\\\";s:41:\\\"force_delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:137;a:4:{s:1:\\\"a\\\";i:138;s:1:\\\"b\\\";s:45:\\\"force_delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:138;a:4:{s:1:\\\"a\\\";i:139;s:1:\\\"b\\\";s:10:\\\"view_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:139;a:4:{s:1:\\\"a\\\";i:140;s:1:\\\"b\\\";s:14:\\\"view_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:140;a:4:{s:1:\\\"a\\\";i:141;s:1:\\\"b\\\";s:12:\\\"create_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:141;a:4:{s:1:\\\"a\\\";i:142;s:1:\\\"b\\\";s:12:\\\"update_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:142;a:4:{s:1:\\\"a\\\";i:143;s:1:\\\"b\\\";s:13:\\\"restore_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:143;a:4:{s:1:\\\"a\\\";i:144;s:1:\\\"b\\\";s:17:\\\"restore_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:144;a:4:{s:1:\\\"a\\\";i:145;s:1:\\\"b\\\";s:15:\\\"replicate_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:145;a:4:{s:1:\\\"a\\\";i:146;s:1:\\\"b\\\";s:13:\\\"reorder_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:146;a:4:{s:1:\\\"a\\\";i:147;s:1:\\\"b\\\";s:12:\\\"delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:147;a:4:{s:1:\\\"a\\\";i:148;s:1:\\\"b\\\";s:16:\\\"delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:148;a:4:{s:1:\\\"a\\\";i:149;s:1:\\\"b\\\";s:18:\\\"force_delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:149;a:4:{s:1:\\\"a\\\";i:150;s:1:\\\"b\\\";s:22:\\\"force_delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:150;a:4:{s:1:\\\"a\\\";i:151;s:1:\\\"b\\\";s:9:\\\"view_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:151;a:4:{s:1:\\\"a\\\";i:152;s:1:\\\"b\\\";s:13:\\\"view_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:152;a:4:{s:1:\\\"a\\\";i:153;s:1:\\\"b\\\";s:11:\\\"create_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:153;a:4:{s:1:\\\"a\\\";i:154;s:1:\\\"b\\\";s:11:\\\"update_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:154;a:4:{s:1:\\\"a\\\";i:155;s:1:\\\"b\\\";s:12:\\\"restore_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:155;a:4:{s:1:\\\"a\\\";i:156;s:1:\\\"b\\\";s:16:\\\"restore_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:156;a:4:{s:1:\\\"a\\\";i:157;s:1:\\\"b\\\";s:14:\\\"replicate_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:157;a:4:{s:1:\\\"a\\\";i:158;s:1:\\\"b\\\";s:12:\\\"reorder_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:158;a:4:{s:1:\\\"a\\\";i:159;s:1:\\\"b\\\";s:11:\\\"delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:159;a:4:{s:1:\\\"a\\\";i:160;s:1:\\\"b\\\";s:15:\\\"delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:160;a:4:{s:1:\\\"a\\\";i:161;s:1:\\\"b\\\";s:17:\\\"force_delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:161;a:4:{s:1:\\\"a\\\";i:162;s:1:\\\"b\\\";s:21:\\\"force_delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:162;a:4:{s:1:\\\"a\\\";i:163;s:1:\\\"b\\\";s:17:\\\"page_BdeDashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\\\"a\\\";i:164;s:1:\\\"b\\\";s:22:\\\"page_DashboardSettings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:164;a:4:{s:1:\\\"a\\\";i:165;s:1:\\\"b\\\";s:18:\\\"page_ManageSetting\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:165;a:4:{s:1:\\\"a\\\";i:166;s:1:\\\"b\\\";s:11:\\\"page_Themes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:166;a:4:{s:1:\\\"a\\\";i:167;s:1:\\\"b\\\";s:18:\\\"page_MyProfilePage\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\\\"a\\\";i:168;s:1:\\\"b\\\";s:26:\\\"widget_NotificationsWidget\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:168;a:4:{s:1:\\\"a\\\";i:169;s:1:\\\"b\\\";s:9:\\\"view_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:169;a:4:{s:1:\\\"a\\\";i:170;s:1:\\\"b\\\";s:13:\\\"view_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:170;a:4:{s:1:\\\"a\\\";i:171;s:1:\\\"b\\\";s:11:\\\"create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:171;a:4:{s:1:\\\"a\\\";i:172;s:1:\\\"b\\\";s:11:\\\"update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:172;a:4:{s:1:\\\"a\\\";i:173;s:1:\\\"b\\\";s:12:\\\"restore_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:173;a:4:{s:1:\\\"a\\\";i:174;s:1:\\\"b\\\";s:16:\\\"restore_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:174;a:4:{s:1:\\\"a\\\";i:175;s:1:\\\"b\\\";s:14:\\\"replicate_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:175;a:4:{s:1:\\\"a\\\";i:176;s:1:\\\"b\\\";s:12:\\\"reorder_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:176;a:4:{s:1:\\\"a\\\";i:177;s:1:\\\"b\\\";s:11:\\\"delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:177;a:4:{s:1:\\\"a\\\";i:178;s:1:\\\"b\\\";s:15:\\\"delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:178;a:4:{s:1:\\\"a\\\";i:179;s:1:\\\"b\\\";s:17:\\\"force_delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:179;a:4:{s:1:\\\"a\\\";i:180;s:1:\\\"b\\\";s:21:\\\"force_delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:180;a:4:{s:1:\\\"a\\\";i:181;s:1:\\\"b\\\";s:16:\\\"book:create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:181;a:4:{s:1:\\\"a\\\";i:182;s:1:\\\"b\\\";s:16:\\\"book:update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:182;a:4:{s:1:\\\"a\\\";i:183;s:1:\\\"b\\\";s:16:\\\"book:delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:183;a:4:{s:1:\\\"a\\\";i:184;s:1:\\\"b\\\";s:20:\\\"book:pagination_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:184;a:4:{s:1:\\\"a\\\";i:185;s:1:\\\"b\\\";s:16:\\\"book:detail_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:185;a:4:{s:1:\\\"a\\\";i:186;s:1:\\\"b\\\";s:14:\\\"view_dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:186;a:4:{s:1:\\\"a\\\";i:187;s:1:\\\"b\\\";s:18:\\\"page_Bde_Dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:187;a:4:{s:1:\\\"a\\\";i:188;s:1:\\\"b\\\";s:11:\\\"export_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:188;a:4:{s:1:\\\"a\\\";i:189;s:1:\\\"b\\\";s:12:\\\"view_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:189;a:4:{s:1:\\\"a\\\";i:190;s:1:\\\"b\\\";s:16:\\\"view_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:190;a:4:{s:1:\\\"a\\\";i:191;s:1:\\\"b\\\";s:14:\\\"create_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:191;a:4:{s:1:\\\"a\\\";i:192;s:1:\\\"b\\\";s:14:\\\"update_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:192;a:4:{s:1:\\\"a\\\";i:193;s:1:\\\"b\\\";s:15:\\\"restore_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:193;a:4:{s:1:\\\"a\\\";i:194;s:1:\\\"b\\\";s:19:\\\"restore_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:194;a:4:{s:1:\\\"a\\\";i:195;s:1:\\\"b\\\";s:17:\\\"replicate_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:195;a:4:{s:1:\\\"a\\\";i:196;s:1:\\\"b\\\";s:15:\\\"reorder_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:196;a:4:{s:1:\\\"a\\\";i:197;s:1:\\\"b\\\";s:14:\\\"delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:197;a:4:{s:1:\\\"a\\\";i:198;s:1:\\\"b\\\";s:18:\\\"delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:198;a:4:{s:1:\\\"a\\\";i:199;s:1:\\\"b\\\";s:20:\\\"force_delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:199;a:4:{s:1:\\\"a\\\";i:200;s:1:\\\"b\\\";s:24:\\\"force_delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:200;a:4:{s:1:\\\"a\\\";i:201;s:1:\\\"b\\\";s:9:\\\"view_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:201;a:4:{s:1:\\\"a\\\";i:202;s:1:\\\"b\\\";s:13:\\\"view_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:202;a:4:{s:1:\\\"a\\\";i:203;s:1:\\\"b\\\";s:11:\\\"create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:203;a:4:{s:1:\\\"a\\\";i:204;s:1:\\\"b\\\";s:11:\\\"update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:204;a:4:{s:1:\\\"a\\\";i:205;s:1:\\\"b\\\";s:12:\\\"restore_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:205;a:4:{s:1:\\\"a\\\";i:206;s:1:\\\"b\\\";s:16:\\\"restore_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:206;a:4:{s:1:\\\"a\\\";i:207;s:1:\\\"b\\\";s:14:\\\"replicate_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:207;a:4:{s:1:\\\"a\\\";i:208;s:1:\\\"b\\\";s:12:\\\"reorder_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:208;a:4:{s:1:\\\"a\\\";i:209;s:1:\\\"b\\\";s:11:\\\"delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:209;a:4:{s:1:\\\"a\\\";i:210;s:1:\\\"b\\\";s:15:\\\"delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:210;a:4:{s:1:\\\"a\\\";i:211;s:1:\\\"b\\\";s:17:\\\"force_delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:211;a:4:{s:1:\\\"a\\\";i:212;s:1:\\\"b\\\";s:21:\\\"force_delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:212;a:4:{s:1:\\\"a\\\";i:213;s:1:\\\"b\\\";s:16:\\\"post:create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:213;a:4:{s:1:\\\"a\\\";i:214;s:1:\\\"b\\\";s:16:\\\"post:update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:214;a:4:{s:1:\\\"a\\\";i:215;s:1:\\\"b\\\";s:16:\\\"post:delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:215;a:4:{s:1:\\\"a\\\";i:216;s:1:\\\"b\\\";s:20:\\\"post:pagination_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:216;a:4:{s:1:\\\"a\\\";i:217;s:1:\\\"b\\\";s:16:\\\"post:detail_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:217;a:4:{s:1:\\\"a\\\";i:218;s:1:\\\"b\\\";s:19:\\\"view_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:218;a:4:{s:1:\\\"a\\\";i:219;s:1:\\\"b\\\";s:21:\\\"create_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:219;a:4:{s:1:\\\"a\\\";i:220;s:1:\\\"b\\\";s:21:\\\"update_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:220;a:4:{s:1:\\\"a\\\";i:221;s:1:\\\"b\\\";s:21:\\\"delete_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:221;a:4:{s:1:\\\"a\\\";i:222;s:1:\\\"b\\\";s:25:\\\"view_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:222;a:4:{s:1:\\\"a\\\";i:223;s:1:\\\"b\\\";s:27:\\\"create_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:223;a:4:{s:1:\\\"a\\\";i:224;s:1:\\\"b\\\";s:27:\\\"update_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:224;a:4:{s:1:\\\"a\\\";i:225;s:1:\\\"b\\\";s:27:\\\"delete_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:225;a:3:{s:1:\\\"a\\\";i:226;s:1:\\\"b\\\";s:12:\\\"view_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:226;a:3:{s:1:\\\"a\\\";i:227;s:1:\\\"b\\\";s:14:\\\"create_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:227;a:3:{s:1:\\\"a\\\";i:228;s:1:\\\"b\\\";s:14:\\\"update_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:228;a:3:{s:1:\\\"a\\\";i:229;s:1:\\\"b\\\";s:14:\\\"delete_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:229;a:3:{s:1:\\\"a\\\";i:230;s:1:\\\"b\\\";s:23:\\\"view_any_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:230;a:3:{s:1:\\\"a\\\";i:231;s:1:\\\"b\\\";s:16:\\\"view_any_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:231;a:3:{s:1:\\\"a\\\";i:232;s:1:\\\"b\\\";s:29:\\\"view_any_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:232;a:3:{s:1:\\\"a\\\";i:233;s:1:\\\"b\\\";s:25:\\\"delete_any_pricing::model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:233;a:3:{s:1:\\\"a\\\";i:234;s:1:\\\"b\\\";s:18:\\\"delete_any_product\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:234;a:3:{s:1:\\\"a\\\";i:235;s:1:\\\"b\\\";s:31:\\\"delete_any_project::status::log\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:235;a:3:{s:1:\\\"a\\\";i:236;s:1:\\\"b\\\";s:21:\\\"view_any_project_type\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:236;a:3:{s:1:\\\"a\\\";i:237;s:1:\\\"b\\\";s:22:\\\"view_any_pricing_model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:237;a:3:{s:1:\\\"a\\\";i:238;s:1:\\\"b\\\";s:23:\\\"view_any_incentive_rule\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:238;a:3:{s:1:\\\"a\\\";i:239;s:1:\\\"b\\\";s:27:\\\"view_any_notification_event\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:239;a:3:{s:1:\\\"a\\\";i:240;s:1:\\\"b\\\";s:27:\\\"view_any_dashboard_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:240;a:3:{s:1:\\\"a\\\";i:241;s:1:\\\"b\\\";s:35:\\\"view_any_role_notification_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:241;a:3:{s:1:\\\"a\\\";i:242;s:1:\\\"b\\\";s:37:\\\"view_any_notification_role_preference\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}}s:5:\\\"roles\\\";a:3:{i:0;a:3:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:8:\\\"bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"super_admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:11:\\\"jr_bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1751456953, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:242:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:22:\"view_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:26:\"view_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:24:\"create_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:24:\"update_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:25:\"restore_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:29:\"restore_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:27:\"replicate_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:25:\"reorder_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:24:\"delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:28:\"delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:30:\"force_delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:34:\"force_delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:11:\"view_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:15:\"view_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"create_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:13:\"update_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:14:\"restore_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:18:\"restore_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:16:\"replicate_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:14:\"reorder_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:13:\"delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:17:\"delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"force_delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:23:\"force_delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:14:\"view_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"view_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"create_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"update_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:17:\"restore_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:21:\"restore_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:19:\"replicate_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:17:\"reorder_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:16:\"delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:22:\"force_delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:26:\"force_delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:24:\"view_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:22:\"create_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:22:\"update_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:23:\"restore_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:27:\"restore_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:25:\"replicate_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:23:\"reorder_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:22:\"delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:26:\"delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:28:\"force_delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:32:\"force_delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:14:\"view_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:18:\"view_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"create_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:16:\"update_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:17:\"restore_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:21:\"restore_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:19:\"replicate_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:17:\"reorder_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:16:\"delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:20:\"delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:22:\"force_delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:26:\"force_delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:24:\"view_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:28:\"view_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:26:\"create_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:26:\"update_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:27:\"restore_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:31:\"restore_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:29:\"replicate_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:27:\"reorder_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:26:\"delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:30:\"delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:32:\"force_delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:36:\"force_delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:35:\"view_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:39:\"view_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:37:\"create_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:37:\"update_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:38:\"restore_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:42:\"restore_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:40:\"replicate_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:38:\"reorder_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:37:\"delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:41:\"delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:43:\"force_delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:47:\"force_delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"view_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:16:\"view_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:14:\"create_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:14:\"update_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:15:\"restore_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:19:\"restore_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:17:\"replicate_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:15:\"reorder_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:14:\"delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:18:\"delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:20:\"force_delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:24:\"force_delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:12:\"view_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:16:\"view_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:14:\"create_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:14:\"update_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:15:\"restore_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:19:\"restore_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:17:\"replicate_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:15:\"reorder_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:14:\"delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:18:\"delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"force_delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:24:\"force_delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:18:\"view_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:22:\"view_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:20:\"create_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:20:\"update_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:21:\"restore_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:25:\"restore_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:23:\"replicate_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:21:\"reorder_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:20:\"delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:117;a:4:{s:1:\"a\";i:118;s:1:\"b\";s:24:\"delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:118;a:4:{s:1:\"a\";i:119;s:1:\"b\";s:26:\"force_delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:119;a:4:{s:1:\"a\";i:120;s:1:\"b\";s:30:\"force_delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:120;a:4:{s:1:\"a\";i:121;s:1:\"b\";s:9:\"view_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:121;a:4:{s:1:\"a\";i:122;s:1:\"b\";s:13:\"view_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:122;a:4:{s:1:\"a\";i:123;s:1:\"b\";s:11:\"create_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:123;a:4:{s:1:\"a\";i:124;s:1:\"b\";s:11:\"update_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:124;a:4:{s:1:\"a\";i:125;s:1:\"b\";s:11:\"delete_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:125;a:4:{s:1:\"a\";i:126;s:1:\"b\";s:15:\"delete_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:126;a:4:{s:1:\"a\";i:127;s:1:\"b\";s:33:\"view_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:127;a:4:{s:1:\"a\";i:128;s:1:\"b\";s:37:\"view_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:128;a:4:{s:1:\"a\";i:129;s:1:\"b\";s:35:\"create_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:129;a:4:{s:1:\"a\";i:130;s:1:\"b\";s:35:\"update_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:130;a:4:{s:1:\"a\";i:131;s:1:\"b\";s:36:\"restore_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:131;a:4:{s:1:\"a\";i:132;s:1:\"b\";s:40:\"restore_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:132;a:4:{s:1:\"a\";i:133;s:1:\"b\";s:38:\"replicate_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:133;a:4:{s:1:\"a\";i:134;s:1:\"b\";s:36:\"reorder_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:134;a:4:{s:1:\"a\";i:135;s:1:\"b\";s:35:\"delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:135;a:4:{s:1:\"a\";i:136;s:1:\"b\";s:39:\"delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:136;a:4:{s:1:\"a\";i:137;s:1:\"b\";s:41:\"force_delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:137;a:4:{s:1:\"a\";i:138;s:1:\"b\";s:45:\"force_delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:138;a:4:{s:1:\"a\";i:139;s:1:\"b\";s:10:\"view_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:139;a:4:{s:1:\"a\";i:140;s:1:\"b\";s:14:\"view_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:140;a:4:{s:1:\"a\";i:141;s:1:\"b\";s:12:\"create_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:141;a:4:{s:1:\"a\";i:142;s:1:\"b\";s:12:\"update_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:142;a:4:{s:1:\"a\";i:143;s:1:\"b\";s:13:\"restore_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:143;a:4:{s:1:\"a\";i:144;s:1:\"b\";s:17:\"restore_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:144;a:4:{s:1:\"a\";i:145;s:1:\"b\";s:15:\"replicate_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:145;a:4:{s:1:\"a\";i:146;s:1:\"b\";s:13:\"reorder_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:146;a:4:{s:1:\"a\";i:147;s:1:\"b\";s:12:\"delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:147;a:4:{s:1:\"a\";i:148;s:1:\"b\";s:16:\"delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:148;a:4:{s:1:\"a\";i:149;s:1:\"b\";s:18:\"force_delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:149;a:4:{s:1:\"a\";i:150;s:1:\"b\";s:22:\"force_delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:150;a:4:{s:1:\"a\";i:151;s:1:\"b\";s:9:\"view_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:151;a:4:{s:1:\"a\";i:152;s:1:\"b\";s:13:\"view_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:152;a:4:{s:1:\"a\";i:153;s:1:\"b\";s:11:\"create_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:153;a:4:{s:1:\"a\";i:154;s:1:\"b\";s:11:\"update_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:154;a:4:{s:1:\"a\";i:155;s:1:\"b\";s:12:\"restore_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:155;a:4:{s:1:\"a\";i:156;s:1:\"b\";s:16:\"restore_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:156;a:4:{s:1:\"a\";i:157;s:1:\"b\";s:14:\"replicate_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:157;a:4:{s:1:\"a\";i:158;s:1:\"b\";s:12:\"reorder_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:158;a:4:{s:1:\"a\";i:159;s:1:\"b\";s:11:\"delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:159;a:4:{s:1:\"a\";i:160;s:1:\"b\";s:15:\"delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:160;a:4:{s:1:\"a\";i:161;s:1:\"b\";s:17:\"force_delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:161;a:4:{s:1:\"a\";i:162;s:1:\"b\";s:21:\"force_delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:162;a:4:{s:1:\"a\";i:163;s:1:\"b\";s:17:\"page_BdeDashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\"a\";i:164;s:1:\"b\";s:22:\"page_DashboardSettings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:164;a:4:{s:1:\"a\";i:165;s:1:\"b\";s:18:\"page_ManageSetting\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:165;a:4:{s:1:\"a\";i:166;s:1:\"b\";s:11:\"page_Themes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:166;a:4:{s:1:\"a\";i:167;s:1:\"b\";s:18:\"page_MyProfilePage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\"a\";i:168;s:1:\"b\";s:26:\"widget_NotificationsWidget\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:168;a:4:{s:1:\"a\";i:169;s:1:\"b\";s:9:\"view_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:169;a:4:{s:1:\"a\";i:170;s:1:\"b\";s:13:\"view_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:170;a:4:{s:1:\"a\";i:171;s:1:\"b\";s:11:\"create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:171;a:4:{s:1:\"a\";i:172;s:1:\"b\";s:11:\"update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:172;a:4:{s:1:\"a\";i:173;s:1:\"b\";s:12:\"restore_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:173;a:4:{s:1:\"a\";i:174;s:1:\"b\";s:16:\"restore_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:174;a:4:{s:1:\"a\";i:175;s:1:\"b\";s:14:\"replicate_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:175;a:4:{s:1:\"a\";i:176;s:1:\"b\";s:12:\"reorder_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:176;a:4:{s:1:\"a\";i:177;s:1:\"b\";s:11:\"delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:177;a:4:{s:1:\"a\";i:178;s:1:\"b\";s:15:\"delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:178;a:4:{s:1:\"a\";i:179;s:1:\"b\";s:17:\"force_delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:179;a:4:{s:1:\"a\";i:180;s:1:\"b\";s:21:\"force_delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:180;a:4:{s:1:\"a\";i:181;s:1:\"b\";s:16:\"book:create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:181;a:4:{s:1:\"a\";i:182;s:1:\"b\";s:16:\"book:update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:182;a:4:{s:1:\"a\";i:183;s:1:\"b\";s:16:\"book:delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:183;a:4:{s:1:\"a\";i:184;s:1:\"b\";s:20:\"book:pagination_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:184;a:4:{s:1:\"a\";i:185;s:1:\"b\";s:16:\"book:detail_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:185;a:4:{s:1:\"a\";i:186;s:1:\"b\";s:14:\"view_dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:186;a:4:{s:1:\"a\";i:187;s:1:\"b\";s:18:\"page_Bde_Dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:187;a:4:{s:1:\"a\";i:188;s:1:\"b\";s:11:\"export_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:188;a:4:{s:1:\"a\";i:189;s:1:\"b\";s:12:\"view_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:189;a:4:{s:1:\"a\";i:190;s:1:\"b\";s:16:\"view_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:190;a:4:{s:1:\"a\";i:191;s:1:\"b\";s:14:\"create_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:191;a:4:{s:1:\"a\";i:192;s:1:\"b\";s:14:\"update_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:192;a:4:{s:1:\"a\";i:193;s:1:\"b\";s:15:\"restore_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:193;a:4:{s:1:\"a\";i:194;s:1:\"b\";s:19:\"restore_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:194;a:4:{s:1:\"a\";i:195;s:1:\"b\";s:17:\"replicate_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:195;a:4:{s:1:\"a\";i:196;s:1:\"b\";s:15:\"reorder_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:196;a:4:{s:1:\"a\";i:197;s:1:\"b\";s:14:\"delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:197;a:4:{s:1:\"a\";i:198;s:1:\"b\";s:18:\"delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:198;a:4:{s:1:\"a\";i:199;s:1:\"b\";s:20:\"force_delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:199;a:4:{s:1:\"a\";i:200;s:1:\"b\";s:24:\"force_delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:200;a:4:{s:1:\"a\";i:201;s:1:\"b\";s:9:\"view_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:201;a:4:{s:1:\"a\";i:202;s:1:\"b\";s:13:\"view_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:202;a:4:{s:1:\"a\";i:203;s:1:\"b\";s:11:\"create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:203;a:4:{s:1:\"a\";i:204;s:1:\"b\";s:11:\"update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:204;a:4:{s:1:\"a\";i:205;s:1:\"b\";s:12:\"restore_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:205;a:4:{s:1:\"a\";i:206;s:1:\"b\";s:16:\"restore_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:206;a:4:{s:1:\"a\";i:207;s:1:\"b\";s:14:\"replicate_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:207;a:4:{s:1:\"a\";i:208;s:1:\"b\";s:12:\"reorder_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:208;a:4:{s:1:\"a\";i:209;s:1:\"b\";s:11:\"delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:209;a:4:{s:1:\"a\";i:210;s:1:\"b\";s:15:\"delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:210;a:4:{s:1:\"a\";i:211;s:1:\"b\";s:17:\"force_delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:211;a:4:{s:1:\"a\";i:212;s:1:\"b\";s:21:\"force_delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:212;a:4:{s:1:\"a\";i:213;s:1:\"b\";s:16:\"post:create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:213;a:4:{s:1:\"a\";i:214;s:1:\"b\";s:16:\"post:update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:214;a:4:{s:1:\"a\";i:215;s:1:\"b\";s:16:\"post:delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:215;a:4:{s:1:\"a\";i:216;s:1:\"b\";s:20:\"post:pagination_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:216;a:4:{s:1:\"a\";i:217;s:1:\"b\";s:16:\"post:detail_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:217;a:4:{s:1:\"a\";i:218;s:1:\"b\";s:19:\"view_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:218;a:4:{s:1:\"a\";i:219;s:1:\"b\";s:21:\"create_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:219;a:4:{s:1:\"a\";i:220;s:1:\"b\";s:21:\"update_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:220;a:4:{s:1:\"a\";i:221;s:1:\"b\";s:21:\"delete_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:221;a:4:{s:1:\"a\";i:222;s:1:\"b\";s:25:\"view_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:222;a:4:{s:1:\"a\";i:223;s:1:\"b\";s:27:\"create_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:223;a:4:{s:1:\"a\";i:224;s:1:\"b\";s:27:\"update_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:224;a:4:{s:1:\"a\";i:225;s:1:\"b\";s:27:\"delete_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:225;a:3:{s:1:\"a\";i:226;s:1:\"b\";s:12:\"view_product\";s:1:\"c\";s:3:\"web\";}i:226;a:3:{s:1:\"a\";i:227;s:1:\"b\";s:14:\"create_product\";s:1:\"c\";s:3:\"web\";}i:227;a:3:{s:1:\"a\";i:228;s:1:\"b\";s:14:\"update_product\";s:1:\"c\";s:3:\"web\";}i:228;a:3:{s:1:\"a\";i:229;s:1:\"b\";s:14:\"delete_product\";s:1:\"c\";s:3:\"web\";}i:229;a:3:{s:1:\"a\";i:230;s:1:\"b\";s:23:\"view_any_pricing::model\";s:1:\"c\";s:3:\"web\";}i:230;a:3:{s:1:\"a\";i:231;s:1:\"b\";s:16:\"view_any_product\";s:1:\"c\";s:3:\"web\";}i:231;a:3:{s:1:\"a\";i:232;s:1:\"b\";s:29:\"view_any_project::status::log\";s:1:\"c\";s:3:\"web\";}i:232;a:3:{s:1:\"a\";i:233;s:1:\"b\";s:25:\"delete_any_pricing::model\";s:1:\"c\";s:0:\"\";}i:233;a:3:{s:1:\"a\";i:234;s:1:\"b\";s:18:\"delete_any_product\";s:1:\"c\";s:0:\"\";}i:234;a:3:{s:1:\"a\";i:235;s:1:\"b\";s:31:\"delete_any_project::status::log\";s:1:\"c\";s:0:\"\";}i:235;a:3:{s:1:\"a\";i:236;s:1:\"b\";s:21:\"view_any_project_type\";s:1:\"c\";s:0:\"\";}i:236;a:3:{s:1:\"a\";i:237;s:1:\"b\";s:22:\"view_any_pricing_model\";s:1:\"c\";s:0:\"\";}i:237;a:3:{s:1:\"a\";i:238;s:1:\"b\";s:23:\"view_any_incentive_rule\";s:1:\"c\";s:0:\"\";}i:238;a:3:{s:1:\"a\";i:239;s:1:\"b\";s:27:\"view_any_notification_event\";s:1:\"c\";s:0:\"\";}i:239;a:3:{s:1:\"a\";i:240;s:1:\"b\";s:27:\"view_any_dashboard_settings\";s:1:\"c\";s:0:\"\";}i:240;a:3:{s:1:\"a\";i:241;s:1:\"b\";s:35:\"view_any_role_notification_settings\";s:1:\"c\";s:0:\"\";}i:241;a:3:{s:1:\"a\";i:242;s:1:\"b\";s:37:\"view_any_notification_role_preference\";s:1:\"c\";s:0:\"\";}}s:5:\"roles\";a:3:{i:0;a:3:{s:1:\"a\";i:15;s:1:\"b\";s:8:\"bde_team\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"super_admin\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:16;s:1:\"b\";s:11:\"jr_bde_team\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.788458, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.131, "width_percent": 4.892}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.793282, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.022, "width_percent": 3.23}, {"sql": "select * from `payments` where exists (select * from `projects` where `payments`.`project_id` = `projects`.`id` and `user_id` = 4) and payments.id IN (\nSELECT MAX(p2.id)\nFROM payments p2\nWHERE p2.project_id = payments.project_id\nGROUP BY p2.project_id\n) and `payments`.`id` = '398' limit 1", "type": "query", "params": [], "bindings": [4, "398"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.829871, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.252, "width_percent": 4.892}, {"sql": "select * from `projects` where `projects`.`id` in (160)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.831802, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.144, "width_percent": 2.492}, {"sql": "select * from `clients` where `clients`.`id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 29, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 30, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 31, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.83285, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.636, "width_percent": 2.353}, {"sql": "select * from `milestones` where `milestones`.`id` in (99)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.833857, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.989, "width_percent": 2.584}, {"sql": "select * from `payments` where `project_id` = 160 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [160], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/PaymentResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\PaymentResource.php", "line": 490}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 264}], "start": **********.837539, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PaymentResource.php:490", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/PaymentResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\PaymentResource.php", "line": 490}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FPaymentResource.php&line=490", "ajax": false, "filename": "PaymentResource.php", "line": "490"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.574, "width_percent": 3.646}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `projects` where `payments`.`project_id` = `projects`.`id` and `user_id` = 4) and payments.id IN (\nSELECT MAX(p2.id)\nFROM payments p2\nWHERE p2.project_id = payments.project_id\nGROUP BY p2.project_id\n)", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6212451, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.219, "width_percent": 5.399}, {"sql": "select * from `payments` where exists (select * from `projects` where `payments`.`project_id` = `projects`.`id` and `user_id` = 4) and payments.id IN (\nSELECT MAX(p2.id)\nFROM payments p2\nWHERE p2.project_id = payments.project_id\nGROUP BY p2.project_id\n) order by `payments`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.623478, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.618, "width_percent": 8.445}, {"sql": "select * from `projects` where `projects`.`id` in (160)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6262898, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.063, "width_percent": 2.769}, {"sql": "select * from `clients` where `clients`.`id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.62763, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.832, "width_percent": 1.615}, {"sql": "select * from `milestones` where `milestones`.`id` in (99)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.628793, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.447, "width_percent": 1.523}, {"sql": "select `projects`.`title`, `projects`.`id` from `projects` order by `projects`.`title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751370570.447873, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.97, "width_percent": 2.4}, {"sql": "select `clients`.`company_name`, `clients`.`id` from `clients` order by `clients`.`company_name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": 1751370570.459938, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.37, "width_percent": 2.63}]}, "models": {"data": {"App\\Models\\Role": {"value": 472, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Permission": {"value": 242, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Payment": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\Project": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Client": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 730, "is_counter": true}, "livewire": {"data": {"app.filament.resources.payment-resource.pages.list-payments #BgiJPwWI4nsCvAl7RzvL": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:7 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"project_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"client\" => array:1 [\n        \"value\" => null\n      ]\n      \"payment_method\" => array:1 [\n        \"value\" => null\n      ]\n      \"due_date\" => array:2 [\n        \"due_from\" => null\n        \"due_until\" => null\n      ]\n      \"paid_date\" => array:2 [\n        \"paid_from\" => null\n        \"paid_until\" => null\n      ]\n      \"amount_range\" => array:2 [\n        \"amount_from\" => null\n        \"amount_to\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => array:3 [\n      0 => []\n      1 => []\n      2 => []\n    ]\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => []\n    \"defaultTableActionArguments\" => []\n    \"defaultTableActionRecord\" => []\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.payment-resource.pages.list-payments\"\n  \"component\" => \"App\\Filament\\Resources\\PaymentResource\\Pages\\ListPayments\"\n  \"id\" => \"BgiJPwWI4nsCvAl7RzvL\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 12, "messages": [{"message": "[\n  ability => create_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-749037610 data-indent-pad=\"  \"><span class=sf-dump-note>create_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749037610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796274, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-471826619 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471826619\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796721, "xdebug_link": null}, {"message": "[\n  ability => {{ Reorder }},\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1539467949 data-indent-pad=\"  \"><span class=sf-dump-note>{{ Reorder }} </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">{{ Reorder }}</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539467949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.803585, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Payment,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-879612651 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879612651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.803684, "xdebug_link": null}, {"message": "[\n  ability => delete_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-187689584 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187689584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827084, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-667382320 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667382320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.827181, "xdebug_link": null}, {"message": "[\n  ability => update_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1474212380 data-indent-pad=\"  \"><span class=sf-dump-note>update_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">update_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474212380\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370572.474408, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Payment(id=398),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Payment)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-267198754 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Payment(id=398)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Payment(id=398)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Payment)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267198754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370572.474662, "xdebug_link": null}, {"message": "[\n  ability => delete_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1893457569 data-indent-pad=\"  \"><span class=sf-dump-note>delete_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893457569\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370573.522943, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Payment(id=398),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Payment)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1903448136 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Payment(id=398)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Payment(id=398)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Payment)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903448136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370573.523141, "xdebug_link": null}, {"message": "[\n  ability => delete_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2072906092 data-indent-pad=\"  \"><span class=sf-dump-note>delete_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072906092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370573.553671, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Payment(id=398),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Payment)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426455268 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Payment(id=398)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\Payment(id=398)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; Object(App\\Models\\Payment)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426455268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751370573.554096, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\PaymentResource\\Pages\\ListPayments@unmountTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=320\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=320\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:320-367</a>", "middleware": "web", "duration": "26.12s", "peak_memory": "66MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1037964876 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1037964876\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1162800551 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"5630 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;project_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;payment_method&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;due_date&quot;:[{&quot;due_from&quot;:null,&quot;due_until&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;paid_date&quot;:[{&quot;paid_from&quot;:null,&quot;paid_until&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;amount_range&quot;:[{&quot;amount_from&quot;:null,&quot;amount_to&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;editAll&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;payments&quot;:[{&quot;27e1beba-a1e4-46b6-871b-c6129b414a3e&quot;:[{&quot;id&quot;:392,&quot;project_id&quot;:160,&quot;milestone_id&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;paid_date&quot;:null,&quot;status&quot;:&quot;pending&quot;,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;f64e7d20-b798-4d22-bf4a-10e2e2194985&quot;:[{&quot;id&quot;:396,&quot;project_id&quot;:160,&quot;milestone_id&quot;:96,&quot;amount&quot;:&quot;2000.00&quot;,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;paid_date&quot;:&quot;2025-08-01&quot;,&quot;status&quot;:&quot;paid&quot;,&quot;payment_method&quot;:&quot;bank_transfer&quot;,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:51.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:43:24.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;5d6df651-ee44-488b-a4e3-252f5a697a45&quot;:[{&quot;id&quot;:393,&quot;project_id&quot;:160,&quot;milestone_id&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;paid_date&quot;:null,&quot;status&quot;:&quot;pending&quot;,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;7654a6b7-0977-46fc-a51f-2fe407f85e36&quot;:[{&quot;id&quot;:394,&quot;project_id&quot;:160,&quot;milestone_id&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;paid_date&quot;:null,&quot;status&quot;:&quot;pending&quot;,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;7522b87f-deb6-487d-b32d-2d4d7e17ed97&quot;:[{&quot;id&quot;:397,&quot;project_id&quot;:160,&quot;milestone_id&quot;:98,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;paid_date&quot;:&quot;2025-10-01&quot;,&quot;status&quot;:&quot;paid&quot;,&quot;payment_method&quot;:&quot;bank_transfer&quot;,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:51.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:45:48.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;820a5363-3c3a-45f1-9244-ddf7e11d4b92&quot;:[{&quot;id&quot;:395,&quot;project_id&quot;:160,&quot;milestone_id&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;paid_date&quot;:null,&quot;status&quot;:&quot;pending&quot;,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;d70aadfa-c130-4e62-a668-f4008e0590ec&quot;:[{&quot;id&quot;:398,&quot;project_id&quot;:160,&quot;milestone_id&quot;:99,&quot;amount&quot;:&quot;1000.00&quot;,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;paid_date&quot;:&quot;2025-11-01&quot;,&quot;status&quot;:&quot;paid&quot;,&quot;payment_method&quot;:&quot;bank_transfer&quot;,&quot;transaction_id&quot;:null,&quot;notes&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:51.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:45:48.000000Z&quot;,&quot;exchange_rate_source&quot;:null,&quot;from_currency&quot;:null,&quot;to_currency&quot;:null,&quot;exchange_rate&quot;:null,&quot;converted_amount&quot;:null,&quot;incentive_amount&quot;:null,&quot;converted_incentive_amount&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:&quot;398&quot;,&quot;defaultTableAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionRecord&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;BgiJPwWI4nsCvAl7RzvL&quot;,&quot;name&quot;:&quot;app.filament.resources.payment-resource.pages.list-payments&quot;,&quot;path&quot;:&quot;admin\\/payments&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2601356f2fd6576e188b74b9e8d92999ee7ae9fbbf4826d2acc48fd68397257e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"18 characters\">unmountTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-const>false</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-const>false</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162800551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1525041923 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6485</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/payments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InRnSUFIeWtvempaTktBRjBFWE9McGc9PSIsInZhbHVlIjoiQ2tWYXhmRjdyL1ArclpXeXFqL2JOUmJXa0tNL0ZSOVVjNDBackZSOWMvTWZtdG9JRVBjQ3JIL1dqcmxCZXRGVGwvTzBkVHp6cnFHSVBlMVFIMVZNZVY3bWEzTWlqaHRsZkUxMisyaldVMVNyOGlXT0FHajZuL2k2cGJldlI3TkZ5dFA4LysrckdjdWlZNUM1ZEFjRXgzZUlyQWQzZGZqNSsxN0JiTHRHaEZCZTlDaEhsNVFSYmtXSHphbjZZQm9oZHAwekxJZGR1KzdlV2FIbEV3UlA1Qzd5ZlkzNWkzd01XUGl5SVd3MGREWT0iLCJtYWMiOiI2NGZlZTAzMWUxNGI0ZWNjNDQ4YWFkZTgwZmE1MGRjN2U5ZDY2ZGEyZmExNzNiM2M5NTZjMzkyOTc5ODczMzhkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImJDM05MUWV2aGNrT3MxbEFwSTh2dlE9PSIsInZhbHVlIjoiR1JzdTVrd01DWC9NYUI4em5XMmZqN3dhU25FLy9VR2NhOUdmZFJzZ1k5VW9vdXRaUnIrdzBmYkRtdWhQRnkrQ2FvSm1uSWd5QmsydW5tYTM1d0EyeS9pS1hvMWVCcHV5M2NleExla0toM21kN3U4SzhKT1gxSEpWTFBwbEpnWEIiLCJtYWMiOiI1NTY0ZDdhYjdlNGE0MGFmYWU3ZTFkNzE4MDYzZGY4MDBjNDQ3OTk1NDRlNDdjMmVjYmQ5OGM2NTQ3ZDY1NTlmIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ikd6OXJrWkwzcDNlQ3JXVFdUOEdtQXc9PSIsInZhbHVlIjoiaUovQkhtNysvUHBqNlowenRXNG8rM2tiaXo2bzcrNUxuUGdBMEc0VmdCM25TYU8ybktPUmYzTUtEekdYZElnZ0Q3ZHN3WWZzYTRGRTlaTkpYTFdCbmlFV1BWYUYxT3ByS0h1b09pbHZ6WmFGSnpvMzBpZ2kzZnNMUXFaV29DeGwiLCJtYWMiOiJjNTRmOGU5MWVkMDI1YTI2MzE4MjAzYzUxMmRiYmI5ZmVjMWNkZjQwZjJjMDU1NDZiZjA2ZTE2MWE5OTc5MWM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525041923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-702199913 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|N8eoFvYEnIkXq8v1Jp9cXichQYhN1SR8YPk8jKbVbaThkHwX5k1OMDiVGjY5|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702199913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1156507968 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:49:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156507968\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-877908367 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/payments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877908367\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}