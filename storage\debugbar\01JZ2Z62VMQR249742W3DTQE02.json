{"__meta": {"id": "01JZ2Z62VMQR249742W3DTQE02", "datetime": "2025-07-01 12:25:29", "utime": **********.205336, "method": "GET", "uri": "/admin/shield/roles/16/edit", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[12:25:25] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/shield\\/roles\\/16\\/edit\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.50706, "xdebug_link": null, "collector": "log"}, {"message": "[12:25:29] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/shield\\/roles\\/16\\/edit\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.199889, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751372724.745685, "end": **********.205369, "duration": 4.459683895111084, "duration_str": "4.46s", "measures": [{"label": "Booting", "start": 1751372724.745685, "relative_start": 0, "end": **********.131348, "relative_end": **********.131348, "duration": 0.****************, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.131358, "relative_start": 0.*****************, "end": **********.205372, "relative_end": 3.0994415283203125e-06, "duration": 4.***************, "duration_str": "4.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.461308, "relative_start": 0.****************, "end": **********.465, "relative_end": **********.465, "duration": 0.0036919116973876953, "duration_str": "3.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.486433, "relative_start": 1.****************, "end": **********.486433, "relative_end": **********.486433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.494471, "relative_start": 1.****************, "end": **********.494471, "relative_end": **********.494471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.497418, "relative_start": 1.7517328262329102, "end": **********.497418, "relative_end": **********.497418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": 1751372727.093494, "relative_start": 2.347808837890625, "end": 1751372727.093494, "relative_end": 1751372727.093494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.712839, "relative_start": 3.967153787612915, "end": 1751372728.712839, "relative_end": 1751372728.712839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.716283, "relative_start": 3.970597982406616, "end": 1751372728.716283, "relative_end": 1751372728.716283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.723314, "relative_start": 3.9776289463043213, "end": 1751372728.723314, "relative_end": 1751372728.723314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.761794, "relative_start": 4.016108989715576, "end": 1751372728.761794, "relative_end": 1751372728.761794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.763948, "relative_start": 4.01826286315918, "end": 1751372728.763948, "relative_end": 1751372728.763948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.767629, "relative_start": 4.021943807601929, "end": 1751372728.767629, "relative_end": 1751372728.767629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.778103, "relative_start": 4.0324180126190186, "end": 1751372728.778103, "relative_end": 1751372728.778103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.781939, "relative_start": 4.036253929138184, "end": 1751372728.781939, "relative_end": 1751372728.781939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.78722, "relative_start": 4.041534900665283, "end": 1751372728.78722, "relative_end": 1751372728.78722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.79673, "relative_start": 4.051044940948486, "end": 1751372728.79673, "relative_end": 1751372728.79673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.799419, "relative_start": 4.053733825683594, "end": 1751372728.799419, "relative_end": 1751372728.799419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.803488, "relative_start": 4.05780291557312, "end": 1751372728.803488, "relative_end": 1751372728.803488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.810958, "relative_start": 4.065272808074951, "end": 1751372728.810958, "relative_end": 1751372728.810958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.812976, "relative_start": 4.067290782928467, "end": 1751372728.812976, "relative_end": 1751372728.812976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.818045, "relative_start": 4.072359800338745, "end": 1751372728.818045, "relative_end": 1751372728.818045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.829611, "relative_start": 4.08392596244812, "end": 1751372728.829611, "relative_end": 1751372728.829611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.83173, "relative_start": 4.086044788360596, "end": 1751372728.83173, "relative_end": 1751372728.83173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.837621, "relative_start": 4.091935873031616, "end": 1751372728.837621, "relative_end": 1751372728.837621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.845824, "relative_start": 4.100138902664185, "end": 1751372728.845824, "relative_end": 1751372728.845824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.847928, "relative_start": 4.102242946624756, "end": 1751372728.847928, "relative_end": 1751372728.847928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.851482, "relative_start": 4.105796813964844, "end": 1751372728.851482, "relative_end": 1751372728.851482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.859301, "relative_start": 4.113615989685059, "end": 1751372728.859301, "relative_end": 1751372728.859301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.861518, "relative_start": 4.115832805633545, "end": 1751372728.861518, "relative_end": 1751372728.861518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.866551, "relative_start": 4.120865821838379, "end": 1751372728.866551, "relative_end": 1751372728.866551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.875004, "relative_start": 4.129318952560425, "end": 1751372728.875004, "relative_end": 1751372728.875004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.877096, "relative_start": 4.131410837173462, "end": 1751372728.877096, "relative_end": 1751372728.877096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.880973, "relative_start": 4.1352880001068115, "end": 1751372728.880973, "relative_end": 1751372728.880973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.891388, "relative_start": 4.145702838897705, "end": 1751372728.891388, "relative_end": 1751372728.891388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.893668, "relative_start": 4.147982835769653, "end": 1751372728.893668, "relative_end": 1751372728.893668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.899347, "relative_start": 4.1536619663238525, "end": 1751372728.899347, "relative_end": 1751372728.899347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.910552, "relative_start": 4.164866924285889, "end": 1751372728.910552, "relative_end": 1751372728.910552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.914255, "relative_start": 4.168569803237915, "end": 1751372728.914255, "relative_end": 1751372728.914255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.920598, "relative_start": 4.174912929534912, "end": 1751372728.920598, "relative_end": 1751372728.920598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.931566, "relative_start": 4.185880899429321, "end": 1751372728.931566, "relative_end": 1751372728.931566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.934291, "relative_start": 4.188605785369873, "end": 1751372728.934291, "relative_end": 1751372728.934291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.939448, "relative_start": 4.193763017654419, "end": 1751372728.939448, "relative_end": 1751372728.939448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.951006, "relative_start": 4.205320835113525, "end": 1751372728.951006, "relative_end": 1751372728.951006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.954273, "relative_start": 4.208587884902954, "end": 1751372728.954273, "relative_end": 1751372728.954273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.957375, "relative_start": 4.2116899490356445, "end": 1751372728.957375, "relative_end": 1751372728.957375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.964446, "relative_start": 4.218760967254639, "end": 1751372728.964446, "relative_end": 1751372728.964446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.966512, "relative_start": 4.220826864242554, "end": 1751372728.966512, "relative_end": 1751372728.966512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.970442, "relative_start": 4.224756956100464, "end": 1751372728.970442, "relative_end": 1751372728.970442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.986453, "relative_start": 4.240767955780029, "end": 1751372728.986453, "relative_end": 1751372728.986453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751372728.990624, "relative_start": 4.244938850402832, "end": 1751372728.990624, "relative_end": 1751372728.990624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": 1751372728.995222, "relative_start": 4.249536991119385, "end": 1751372728.995222, "relative_end": 1751372728.995222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.005221, "relative_start": 4.259535789489746, "end": **********.005221, "relative_end": **********.005221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.007205, "relative_start": 4.261519908905029, "end": **********.007205, "relative_end": **********.007205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.010602, "relative_start": 4.264916896820068, "end": **********.010602, "relative_end": **********.010602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.020056, "relative_start": 4.274370908737183, "end": **********.020056, "relative_end": **********.020056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.022639, "relative_start": 4.276953935623169, "end": **********.022639, "relative_end": **********.022639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.029783, "relative_start": 4.284097909927368, "end": **********.029783, "relative_end": **********.029783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.03722, "relative_start": 4.291534900665283, "end": **********.03722, "relative_end": **********.03722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.040761, "relative_start": 4.2950758934021, "end": **********.040761, "relative_end": **********.040761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.053102, "relative_start": 4.307416915893555, "end": **********.053102, "relative_end": **********.053102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.085175, "relative_start": 4.339489936828613, "end": **********.085175, "relative_end": **********.085175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.115435, "relative_start": 4.3697497844696045, "end": **********.115435, "relative_end": **********.115435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.118195, "relative_start": 4.372509956359863, "end": **********.118195, "relative_end": **********.118195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.119233, "relative_start": 4.373547792434692, "end": **********.119233, "relative_end": **********.119233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.122219, "relative_start": 4.3765339851379395, "end": **********.122219, "relative_end": **********.122219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.123116, "relative_start": 4.3774309158325195, "end": **********.123116, "relative_end": **********.123116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.127538, "relative_start": 4.381852865219116, "end": **********.127538, "relative_end": **********.127538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.12792, "relative_start": 4.382234811782837, "end": **********.12792, "relative_end": **********.12792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.130261, "relative_start": 4.384575843811035, "end": **********.130261, "relative_end": **********.130261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.130652, "relative_start": 4.384966850280762, "end": **********.130652, "relative_end": **********.130652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.185508, "relative_start": 4.4398229122161865, "end": **********.185508, "relative_end": **********.185508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.186478, "relative_start": 4.440792798995972, "end": **********.186478, "relative_end": **********.186478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.193693, "relative_start": 4.448007822036743, "end": **********.193693, "relative_end": **********.193693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.194713, "relative_start": 4.449028015136719, "end": **********.194713, "relative_end": **********.194713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.19574, "relative_start": 4.450054883956909, "end": **********.19574, "relative_end": **********.19574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.19932, "relative_start": 4.453634977340698, "end": **********.199466, "relative_end": **********.199466, "duration": 0.00014591217041015625, "duration_str": "146μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.202388, "relative_start": 4.456702947616577, "end": **********.202432, "relative_end": **********.202432, "duration": 4.38690185546875e-05, "duration_str": "44μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 57347624, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 73, "nb_templates": 73, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.486409, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.497399, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751372727.093477, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751372728.712822, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": 1751372728.723295, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.037209, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.085156, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.115413, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.118177, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.119221, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "3x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.122207, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "3x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.123106, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.185491, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.186459, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.193675, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.194697, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.195721, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 22, "nb_statements": 22, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02355, "accumulated_duration_str": "23.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.478213, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.057}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.482999, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.057, "width_percent": 3.779}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4887311, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.837, "width_percent": 2.675}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.493497, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.512, "width_percent": 2.505}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.4951801, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.017, "width_percent": 1.274}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.4987528, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.291, "width_percent": 1.783}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.499867, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.074, "width_percent": 1.783}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.500908, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.858, "width_percent": 1.444}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.502198, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.301, "width_percent": 1.741}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.503698, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.042, "width_percent": 1.953}, {"sql": "select * from `roles` where `id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.512333, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.996, "width_percent": 2.972}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.5182068, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.968, "width_percent": 4.798}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.5865839, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.766, "width_percent": 16.561}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.5967941, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.327, "width_percent": 3.439}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 24, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 28, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 29, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 229}], "start": **********.6015148, "duration": 0.00605, "duration_str": "6.05ms", "memory": 0, "memory_str": null, "filename": "Role.php:191", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=191", "ajax": false, "filename": "Role.php", "line": "191"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.766, "width_percent": 25.69}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.1037319, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.456, "width_percent": 1.783}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.1163728, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.24, "width_percent": 3.907}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.119927, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.146, "width_percent": 4.628}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.125324, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.775, "width_percent": 5.945}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.128661, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.72, "width_percent": 2.887}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.131316, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.607, "width_percent": 3.015}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTYvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTYvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.201245, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.622, "width_percent": 2.378}]}, "models": {"data": {"App\\Models\\Permission": {"value": 225, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 234, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #dDbaj3NzED4N4GiEvBES": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => null\n    \"data\" => array:26 [\n      \"id\" => 16\n      \"name\" => \"jr_bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:18.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:18.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:6 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"create_app::notification\"\n        3 => \"update_app::notification\"\n        4 => \"delete_app::notification\"\n        5 => \"delete_any_app::notification\"\n      ]\n      \"client\" => array:6 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n        5 => \"delete_any_client\"\n      ]\n      \"incentive\" => array:6 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n        2 => \"create_incentive\"\n        3 => \"update_incentive\"\n        4 => \"delete_incentive\"\n        5 => \"delete_any_incentive\"\n      ]\n      \"incentive::rule\" => array:6 [\n        0 => \"view_incentive::rule\"\n        1 => \"view_any_incentive::rule\"\n        2 => \"create_incentive::rule\"\n        3 => \"update_incentive::rule\"\n        4 => \"delete_incentive::rule\"\n        5 => \"delete_any_incentive::rule\"\n      ]\n      \"milestone\" => array:6 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n        5 => \"delete_any_milestone\"\n      ]\n      \"notification::event\" => array:6 [\n        0 => \"view_notification::event\"\n        1 => \"view_any_notification::event\"\n        2 => \"create_notification::event\"\n        3 => \"update_notification::event\"\n        4 => \"delete_notification::event\"\n        5 => \"delete_any_notification::event\"\n      ]\n      \"notification::role::preference\" => array:6 [\n        0 => \"view_notification::role::preference\"\n        1 => \"view_any_notification::role::preference\"\n        2 => \"create_notification::role::preference\"\n        3 => \"update_notification::role::preference\"\n        4 => \"delete_notification::role::preference\"\n        5 => \"delete_any_notification::role::preference\"\n      ]\n      \"payment\" => array:6 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n        5 => \"delete_any_payment\"\n      ]\n      \"pricing::model\" => array:4 [\n        0 => \"view_pricing::model\"\n        1 => \"create_pricing::model\"\n        2 => \"update_pricing::model\"\n        3 => \"delete_pricing::model\"\n      ]\n      \"product\" => []\n      \"project\" => array:6 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n        5 => \"delete_any_project\"\n      ]\n      \"project::status::log\" => array:4 [\n        0 => \"view_project::status::log\"\n        1 => \"create_project::status::log\"\n        2 => \"update_project::status::log\"\n        3 => \"delete_project::status::log\"\n      ]\n      \"project::type\" => array:6 [\n        0 => \"view_project::type\"\n        1 => \"view_any_project::type\"\n        2 => \"create_project::type\"\n        3 => \"update_project::type\"\n        4 => \"delete_project::type\"\n        5 => \"delete_any_project::type\"\n      ]\n      \"role\" => array:6 [\n        0 => \"view_role\"\n        1 => \"view_any_role\"\n        2 => \"create_role\"\n        3 => \"update_role\"\n        4 => \"delete_role\"\n        5 => \"delete_any_role\"\n      ]\n      \"role::notification::settings\" => array:6 [\n        0 => \"view_role::notification::settings\"\n        1 => \"view_any_role::notification::settings\"\n        2 => \"create_role::notification::settings\"\n        3 => \"update_role::notification::settings\"\n        4 => \"delete_role::notification::settings\"\n        5 => \"delete_any_role::notification::settings\"\n      ]\n      \"user\" => array:6 [\n        0 => \"view_user\"\n        1 => \"view_any_user\"\n        2 => \"create_user\"\n        3 => \"update_user\"\n        4 => \"delete_user\"\n        5 => \"delete_any_user\"\n      ]\n      \"pages_tab\" => array:5 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_DashboardSettings\"\n        2 => \"page_ManageSetting\"\n        3 => \"page_Themes\"\n        4 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => array:134 [\n        0 => \"book:create_book\"\n        1 => \"book:delete_book\"\n        2 => \"book:detail_book\"\n        3 => \"book:pagination_book\"\n        4 => \"book:update_book\"\n        5 => \"create_book\"\n        6 => \"create_contact\"\n        7 => \"create_post\"\n        8 => \"create_token\"\n        9 => \"delete_any_book\"\n        10 => \"delete_any_contact\"\n        11 => \"delete_any_post\"\n        12 => \"delete_any_token\"\n        13 => \"delete_book\"\n        14 => \"delete_contact\"\n        15 => \"delete_post\"\n        16 => \"delete_token\"\n        17 => \"export_book\"\n        18 => \"force_delete_any_app::notification\"\n        19 => \"force_delete_any_book\"\n        20 => \"force_delete_any_client\"\n        21 => \"force_delete_any_contact\"\n        22 => \"force_delete_any_incentive\"\n        23 => \"force_delete_any_incentive::rule\"\n        24 => \"force_delete_any_milestone\"\n        25 => \"force_delete_any_notification::event\"\n        26 => \"force_delete_any_notification::role::preference\"\n        27 => \"force_delete_any_payment\"\n        28 => \"force_delete_any_post\"\n        29 => \"force_delete_any_project\"\n        30 => \"force_delete_any_project::type\"\n        31 => \"force_delete_any_role::notification::settings\"\n        32 => \"force_delete_any_token\"\n        33 => \"force_delete_any_user\"\n        34 => \"force_delete_app::notification\"\n        35 => \"force_delete_book\"\n        36 => \"force_delete_client\"\n        37 => \"force_delete_contact\"\n        38 => \"force_delete_incentive\"\n        39 => \"force_delete_incentive::rule\"\n        40 => \"force_delete_milestone\"\n        41 => \"force_delete_notification::event\"\n        42 => \"force_delete_notification::role::preference\"\n        43 => \"force_delete_payment\"\n        44 => \"force_delete_post\"\n        45 => \"force_delete_project\"\n        46 => \"force_delete_project::type\"\n        47 => \"force_delete_role::notification::settings\"\n        48 => \"force_delete_token\"\n        49 => \"force_delete_user\"\n        50 => \"page_Bde_Dashboard\"\n        51 => \"post:create_post\"\n        52 => \"post:delete_post\"\n        53 => \"post:detail_post\"\n        54 => \"post:pagination_post\"\n        55 => \"post:update_post\"\n        56 => \"reorder_app::notification\"\n        57 => \"reorder_book\"\n        58 => \"reorder_client\"\n        59 => \"reorder_contact\"\n        60 => \"reorder_incentive\"\n        61 => \"reorder_incentive::rule\"\n        62 => \"reorder_milestone\"\n        63 => \"reorder_notification::event\"\n        64 => \"reorder_notification::role::preference\"\n        65 => \"reorder_payment\"\n        66 => \"reorder_post\"\n        67 => \"reorder_project\"\n        68 => \"reorder_project::type\"\n        69 => \"reorder_role::notification::settings\"\n        70 => \"reorder_token\"\n        71 => \"reorder_user\"\n        72 => \"replicate_app::notification\"\n        73 => \"replicate_book\"\n        74 => \"replicate_client\"\n        75 => \"replicate_contact\"\n        76 => \"replicate_incentive\"\n        77 => \"replicate_incentive::rule\"\n        78 => \"replicate_milestone\"\n        79 => \"replicate_notification::event\"\n        80 => \"replicate_notification::role::preference\"\n        81 => \"replicate_payment\"\n        82 => \"replicate_post\"\n        83 => \"replicate_project\"\n        84 => \"replicate_project::type\"\n        85 => \"replicate_role::notification::settings\"\n        86 => \"replicate_token\"\n        87 => \"replicate_user\"\n        88 => \"restore_any_app::notification\"\n        89 => \"restore_any_book\"\n        90 => \"restore_any_client\"\n        91 => \"restore_any_contact\"\n        92 => \"restore_any_incentive\"\n        93 => \"restore_any_incentive::rule\"\n        94 => \"restore_any_milestone\"\n        95 => \"restore_any_notification::event\"\n        96 => \"restore_any_notification::role::preference\"\n        97 => \"restore_any_payment\"\n        98 => \"restore_any_post\"\n        99 => \"restore_any_project\"\n        100 => \"restore_any_project::type\"\n        101 => \"restore_any_role::notification::settings\"\n        102 => \"restore_any_token\"\n        103 => \"restore_any_user\"\n        104 => \"restore_app::notification\"\n        105 => \"restore_book\"\n        106 => \"restore_client\"\n        107 => \"restore_contact\"\n        108 => \"restore_incentive\"\n        109 => \"restore_incentive::rule\"\n        110 => \"restore_milestone\"\n        111 => \"restore_notification::event\"\n        112 => \"restore_notification::role::preference\"\n        113 => \"restore_payment\"\n        114 => \"restore_post\"\n        115 => \"restore_project\"\n        116 => \"restore_project::type\"\n        117 => \"restore_role::notification::settings\"\n        118 => \"restore_token\"\n        119 => \"restore_user\"\n        120 => \"update_book\"\n        121 => \"update_contact\"\n        122 => \"update_post\"\n        123 => \"update_token\"\n        124 => \"view_any_book\"\n        125 => \"view_any_contact\"\n        126 => \"view_any_post\"\n        127 => \"view_any_token\"\n        128 => \"view_book\"\n        129 => \"view_contact\"\n        130 => \"view_dashboard\"\n        131 => \"view_post\"\n        132 => \"view_token\"\n        133 => \"widget_NotificationsWidget\"\n      ]\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#2347\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 16\n        \"name\" => \"jr_bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:18\"\n        \"updated_at\" => \"2025-06-18 15:11:18\"\n      ]\n      #original: array:5 [\n        \"id\" => 16\n        \"name\" => \"jr_bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:18\"\n        \"updated_at\" => \"2025-06-18 15:11:18\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2871\n          #items: array:225 [\n            0 => App\\Models\\Permission {#3507\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => App\\Models\\Permission {#3482\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => App\\Models\\Permission {#3483\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => App\\Models\\Permission {#3484\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => App\\Models\\Permission {#3485\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => App\\Models\\Permission {#3486\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => App\\Models\\Permission {#3487\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => App\\Models\\Permission {#3488\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => App\\Models\\Permission {#3489\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => App\\Models\\Permission {#3490\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => App\\Models\\Permission {#3491\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => App\\Models\\Permission {#3492\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => App\\Models\\Permission {#3493\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            13 => App\\Models\\Permission {#3494\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            14 => App\\Models\\Permission {#3495\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            15 => App\\Models\\Permission {#3496\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            16 => App\\Models\\Permission {#3497\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            17 => App\\Models\\Permission {#3498\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            18 => App\\Models\\Permission {#3499\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            19 => App\\Models\\Permission {#3500\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            20 => App\\Models\\Permission {#3501\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            21 => App\\Models\\Permission {#3481\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            22 => App\\Models\\Permission {#3480\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            23 => App\\Models\\Permission {#3479\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            24 => App\\Models\\Permission {#3478\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            25 => App\\Models\\Permission {#3477\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            26 => App\\Models\\Permission {#3476\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            27 => App\\Models\\Permission {#3475\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            28 => App\\Models\\Permission {#3474\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            29 => App\\Models\\Permission {#3473\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            30 => App\\Models\\Permission {#3472\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            31 => App\\Models\\Permission {#3471\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            32 => App\\Models\\Permission {#3470\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            33 => App\\Models\\Permission {#3469\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            34 => App\\Models\\Permission {#3468\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            35 => App\\Models\\Permission {#3467\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            36 => App\\Models\\Permission {#3466\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            37 => App\\Models\\Permission {#3465\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            38 => App\\Models\\Permission {#3464\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            39 => App\\Models\\Permission {#3463\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            40 => App\\Models\\Permission {#3462\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            41 => App\\Models\\Permission {#3461\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            42 => App\\Models\\Permission {#3460\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            43 => App\\Models\\Permission {#3459\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            44 => App\\Models\\Permission {#3458\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            45 => App\\Models\\Permission {#3457\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            46 => App\\Models\\Permission {#3456\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            47 => App\\Models\\Permission {#3455\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            48 => App\\Models\\Permission {#3454\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            49 => App\\Models\\Permission {#3453\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            50 => App\\Models\\Permission {#3452\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            51 => App\\Models\\Permission {#3451\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            52 => App\\Models\\Permission {#3450\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n               …10\n            }\n            53 => App\\Models\\Permission {#3449 …37}\n            54 => App\\Models\\Permission {#3448 …37}\n            55 => App\\Models\\Permission {#3447 …37}\n            56 => App\\Models\\Permission {#3446 …37}\n            57 => App\\Models\\Permission {#3445 …37}\n            58 => App\\Models\\Permission {#3444 …37}\n            59 => App\\Models\\Permission {#3443 …37}\n            60 => App\\Models\\Permission {#3442 …37}\n            61 => App\\Models\\Permission {#3441 …37}\n            62 => App\\Models\\Permission {#3440 …37}\n            63 => App\\Models\\Permission {#3439 …37}\n            64 => App\\Models\\Permission {#3438 …37}\n            65 => App\\Models\\Permission {#3437 …37}\n            66 => App\\Models\\Permission {#3436 …37}\n            67 => App\\Models\\Permission {#3435 …37}\n            68 => App\\Models\\Permission {#3434 …37}\n            69 => App\\Models\\Permission {#3433 …37}\n            70 => App\\Models\\Permission {#3432 …37}\n            71 => App\\Models\\Permission {#3431 …37}\n            72 => App\\Models\\Permission {#3430 …37}\n            73 => App\\Models\\Permission {#3429 …37}\n            74 => App\\Models\\Permission {#3428 …37}\n            75 => App\\Models\\Permission {#3427 …37}\n            76 => App\\Models\\Permission {#3426 …37}\n            77 => App\\Models\\Permission {#3425 …37}\n            78 => App\\Models\\Permission {#3424 …37}\n            79 => App\\Models\\Permission {#3423 …37}\n            80 => App\\Models\\Permission {#3422 …37}\n            81 => App\\Models\\Permission {#3421 …37}\n            82 => App\\Models\\Permission {#3420 …37}\n            83 => App\\Models\\Permission {#3419 …37}\n            84 => App\\Models\\Permission {#3418 …37}\n            85 => App\\Models\\Permission {#3417 …37}\n            86 => App\\Models\\Permission {#3416 …37}\n            87 => App\\Models\\Permission {#3415 …37}\n            88 => App\\Models\\Permission {#3414 …37}\n            89 => App\\Models\\Permission {#3413 …37}\n            90 => App\\Models\\Permission {#3412 …37}\n            91 => App\\Models\\Permission {#3411 …37}\n            92 => App\\Models\\Permission {#3410 …37}\n            93 => App\\Models\\Permission {#3409 …37}\n            94 => App\\Models\\Permission {#3408 …37}\n            95 => App\\Models\\Permission {#3407 …37}\n            96 => App\\Models\\Permission {#3406 …37}\n            97 => App\\Models\\Permission {#3405 …37}\n            98 => App\\Models\\Permission {#3404 …37}\n            99 => App\\Models\\Permission {#3403 …37}\n            100 => App\\Models\\Permission {#3402 …37}\n            101 => App\\Models\\Permission {#3401 …37}\n            102 => App\\Models\\Permission {#3400 …37}\n            103 => App\\Models\\Permission {#3399 …37}\n            104 => App\\Models\\Permission {#3398 …37}\n            105 => App\\Models\\Permission {#3397 …37}\n            106 => App\\Models\\Permission {#3396 …37}\n            107 => App\\Models\\Permission {#3395 …37}\n            108 => App\\Models\\Permission {#3394 …37}\n            109 => App\\Models\\Permission {#3393 …37}\n            110 => App\\Models\\Permission {#3392 …37}\n            111 => App\\Models\\Permission {#3391 …37}\n            112 => App\\Models\\Permission {#3390 …37}\n            113 => App\\Models\\Permission {#3389 …37}\n            114 => App\\Models\\Permission {#3388 …37}\n            115 => App\\Models\\Permission {#3387 …37}\n            116 => App\\Models\\Permission {#3386 …37}\n            117 => App\\Models\\Permission {#3385 …37}\n            118 => App\\Models\\Permission {#3384 …37}\n            119 => App\\Models\\Permission {#3383 …37}\n            120 => App\\Models\\Permission {#3382 …37}\n            121 => App\\Models\\Permission {#3381 …37}\n            122 => App\\Models\\Permission {#3380 …37}\n            123 => App\\Models\\Permission {#3379 …37}\n            124 => App\\Models\\Permission {#3378 …37}\n            125 => App\\Models\\Permission {#3377 …37}\n            126 => App\\Models\\Permission {#3376 …37}\n            127 => App\\Models\\Permission {#3375 …37}\n            128 => App\\Models\\Permission {#3374 …37}\n            129 => App\\Models\\Permission {#3373 …37}\n            130 => App\\Models\\Permission {#3372 …37}\n            131 => App\\Models\\Permission {#3371 …37}\n            132 => App\\Models\\Permission {#3370 …37}\n            133 => App\\Models\\Permission {#3369 …37}\n            134 => App\\Models\\Permission {#3368 …37}\n            135 => App\\Models\\Permission {#3367 …37}\n            136 => App\\Models\\Permission {#3366 …37}\n            137 => App\\Models\\Permission {#3365 …37}\n            138 => App\\Models\\Permission {#3364 …37}\n            139 => App\\Models\\Permission {#3363 …37}\n            140 => App\\Models\\Permission {#3362 …37}\n            141 => App\\Models\\Permission {#3361 …37}\n            142 => App\\Models\\Permission {#3360 …37}\n            143 => App\\Models\\Permission {#3359 …37}\n            144 => App\\Models\\Permission {#3358 …37}\n            145 => App\\Models\\Permission {#3357 …37}\n            146 => App\\Models\\Permission {#3356 …37}\n            147 => App\\Models\\Permission {#3355 …37}\n            148 => App\\Models\\Permission {#3354 …37}\n            149 => App\\Models\\Permission {#3353 …37}\n            150 => App\\Models\\Permission {#3352 …37}\n            151 => App\\Models\\Permission {#3351 …37}\n            152 => App\\Models\\Permission {#3350 …37}\n            153 => App\\Models\\Permission {#3349 …37}\n            154 => App\\Models\\Permission {#3348 …37}\n            155 => App\\Models\\Permission {#3347 …37}\n            156 => App\\Models\\Permission {#3346 …37}\n            157 => App\\Models\\Permission {#3345 …37}\n            158 => App\\Models\\Permission {#3344 …37}\n            159 => App\\Models\\Permission {#3343 …37}\n            160 => App\\Models\\Permission {#3342 …37}\n            161 => App\\Models\\Permission {#3341 …37}\n            162 => App\\Models\\Permission {#3340 …37}\n            163 => App\\Models\\Permission {#3339 …37}\n            164 => App\\Models\\Permission {#3338 …37}\n            165 => App\\Models\\Permission {#3337 …37}\n            166 => App\\Models\\Permission {#3336 …37}\n            167 => App\\Models\\Permission {#3335 …37}\n            168 => App\\Models\\Permission {#3334 …37}\n            169 => App\\Models\\Permission {#3333 …37}\n            170 => App\\Models\\Permission {#3332 …37}\n            171 => App\\Models\\Permission {#3331 …37}\n            172 => App\\Models\\Permission {#3330 …37}\n            173 => App\\Models\\Permission {#3329 …37}\n            174 => App\\Models\\Permission {#3328 …37}\n            175 => App\\Models\\Permission {#3327 …37}\n            176 => App\\Models\\Permission {#3326 …37}\n            177 => App\\Models\\Permission {#3325 …37}\n            178 => App\\Models\\Permission {#3324 …37}\n            179 => App\\Models\\Permission {#3323 …37}\n            180 => App\\Models\\Permission {#3322 …37}\n            181 => App\\Models\\Permission {#3321 …37}\n            182 => App\\Models\\Permission {#3320 …37}\n            183 => App\\Models\\Permission {#3319 …37}\n            184 => App\\Models\\Permission {#3318 …37}\n            185 => App\\Models\\Permission {#3317 …37}\n            186 => App\\Models\\Permission {#3316 …37}\n            187 => App\\Models\\Permission {#3315 …37}\n            188 => App\\Models\\Permission {#3314 …37}\n            189 => App\\Models\\Permission {#3313 …37}\n            190 => App\\Models\\Permission {#3312 …37}\n            191 => App\\Models\\Permission {#3311 …37}\n            192 => App\\Models\\Permission {#3310 …37}\n            193 => App\\Models\\Permission {#3309 …37}\n            194 => App\\Models\\Permission {#3308 …37}\n            195 => App\\Models\\Permission {#3307 …37}\n            196 => App\\Models\\Permission {#3306 …37}\n            197 => App\\Models\\Permission {#3305 …37}\n            198 => App\\Models\\Permission {#3304 …37}\n            199 => App\\Models\\Permission {#3303 …37}\n            200 => App\\Models\\Permission {#3302 …37}\n            201 => App\\Models\\Permission {#3301 …37}\n            202 => App\\Models\\Permission {#3300 …37}\n            203 => App\\Models\\Permission {#3299 …37}\n            204 => App\\Models\\Permission {#3298 …37}\n            205 => App\\Models\\Permission {#3297 …37}\n            206 => App\\Models\\Permission {#3296 …37}\n            207 => App\\Models\\Permission {#3295 …37}\n            208 => App\\Models\\Permission {#3294 …37}\n            209 => App\\Models\\Permission {#3293 …37}\n            210 => App\\Models\\Permission {#3292 …37}\n            211 => App\\Models\\Permission {#3291 …37}\n            212 => App\\Models\\Permission {#3290 …37}\n            213 => App\\Models\\Permission {#3289 …37}\n            214 => App\\Models\\Permission {#3288 …37}\n            215 => App\\Models\\Permission {#3287 …37}\n            216 => App\\Models\\Permission {#3286 …37}\n            217 => App\\Models\\Permission {#3285 …37}\n            218 => App\\Models\\Permission {#3284 …37}\n            219 => App\\Models\\Permission {#3283 …37}\n            220 => App\\Models\\Permission {#3282 …37}\n            221 => App\\Models\\Permission {#3044 …37}\n            222 => App\\Models\\Permission {#3043 …37}\n            223 => App\\Models\\Permission {#3042 …37}\n            224 => App\\Models\\Permission {#3040 …37}\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: \"App\\Models\\Permission\"\n      -wildcardClass: \"\"\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"dDbaj3NzED4N4GiEvBES\"\n]", "filament.livewire.global-search #ZYbNLXb6knbvd0pKlJzv": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"ZYbNLXb6knbvd0pKlJzv\"\n]", "app.filament.widgets.notification-components.notification-bell #gb2hZxR78opjXlTnFVHW": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 3\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"gb2hZxR78opjXlTnFVHW\"\n]", "filament.livewire.notifications #vHHz2WJVlEDF3eMJa8eH": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4085\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"vHHz2WJVlEDF3eMJa8eH\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 32, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2099837228 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099837228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527817, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1776048017 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776048017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328345, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1893290995 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893290995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378964, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-351109610 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351109610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046717, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-617388720 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617388720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047227, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1578181597 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578181597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088517, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-738213719 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738213719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088762, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-505047046 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505047046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.089867, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128714550 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128714550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09032, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1983656120 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983656120\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090937, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1001944118 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001944118\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091381, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-654535342 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654535342\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09199, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-453374 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09276, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-554897283 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554897283\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093186, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177548017 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177548017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093738, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1851824211 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851824211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09453, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-509323386 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509323386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095352, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1073841593 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073841593\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095799, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-931825448 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931825448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096297, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1964666771 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964666771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096659, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-372835126 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372835126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096903, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-986642753 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986642753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097729, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1623069778 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623069778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098544, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478729551 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478729551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099129, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1474311253 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474311253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100157, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-594442577 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594442577\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101776, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1117237695 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117237695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102349, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1685641059 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685641059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102807, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-586137167 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586137167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103066, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1863818250 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863818250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105242, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-794441965 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794441965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105814, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1269718789 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269718789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110887, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/16/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole", "uri": "GET admin/shield/roles/{record}/edit", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/shield/roles", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "4.46s", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/admin/shield/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRKblJidnNFNXNkTUlmbVBHbWh2UFE9PSIsInZhbHVlIjoia2dTczdVK1E0M0RVMkJvZU5MVnYrNUozMlI5NDh3Z3RtTzd6Um1SVTFRajhIeDBrT1hBejVyZTE3YktXMFIwd3l6SFFyMVZjd1lYTEZRNUVhYmhIbmREejdxVVMyS1RRaWd0U2MxZGM1TkhNb2VMNUhlZnk3ckdTNXdKZlQ4eU4iLCJtYWMiOiIxYzA0NTQzNTM4ZDQyZGE2M2IxODRlMTY1YTJjM2IwNWEyMGI0MmYyZDA1ZWY1ZmE2NzY4OThlYmE5MzdjZjM3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IitaVWhVaXM3b1h4OEdXMjlBQSs5eHc9PSIsInZhbHVlIjoicldDSXFLK1hkZ3NOYXBRdE0xazEvdHhzaytCbGttTENFM0V1VnlhZ01GRUNSMkRxWENFc1diYlJ5dmxVRFpMcXJKZi9NaWRUa2Zla3FVTHgxeVF3VnhSb3VvME9hWjFBbDVOMzdPWHhYc2x6cytKU3B3Q0hmenh4K0NxTEJOazkiLCJtYWMiOiJhMGQwZWJlNzM3OTlkYzE3NTQwMjllMDBiM2Y3ZTViYzM3OWM2ZTNiYzQwZDkyYjQ0NzMzMWI5NTJjNDVlNjI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-23476788 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23476788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-647014685 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:25:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkUxR1htY1daZHBkMC9xN0VINTJSbGc9PSIsInZhbHVlIjoiaGRhMWRNY3YzdUk5L1h6dGl5QnVjNXJFVHJJR3hDcUJ5UFN5UDJmbFc1VzNyaGo4RzdxSG5qZ3VNV0w4SENjeUhJQ0tjUkNPdFl5by9YY1pHc3BIQ3JHNS9MVmpDZkxtN1VYNE1oUTVoYngzNUJkNTZJVE9nbHNaRnJHNzFxYUciLCJtYWMiOiI4OGMzMWRlMjY3ZGIzMzg1MGYxMzMyMGE2ZTA0M2M3OWQzNGJmYjY0YzNmYzdjMDExNzYwNWU2NWVjYWY5YzIxIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 14:25:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6ImNSdUNVZ0xQZUhuQlJWR0llV093aWc9PSIsInZhbHVlIjoiMXRUNEZuVHpPV3Q1cVdoM05UWG4xdThiL3graHQ3YW1pZTZadUZWNjBzU244TXhRTlRTS0RKUDlPZDBwQ3pXZUMycDh6UUJsa2llWFNaRGpodU1aUHc5SUJTcEprM3FleC9KSzBtenR1dnFVZVdCMkN3RnFkbmw3dGdwaUI4NS8iLCJtYWMiOiIyNGYwZTcwZDkwMDQ3MTFhNjg5M2UzMzg3YjBmYTBmODJhZDczYzM3ZTEyMzJlMjY1YmJlN2NjNmI5MDEzMjY2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 14:25:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkUxR1htY1daZHBkMC9xN0VINTJSbGc9PSIsInZhbHVlIjoiaGRhMWRNY3YzdUk5L1h6dGl5QnVjNXJFVHJJR3hDcUJ5UFN5UDJmbFc1VzNyaGo4RzdxSG5qZ3VNV0w4SENjeUhJQ0tjUkNPdFl5by9YY1pHc3BIQ3JHNS9MVmpDZkxtN1VYNE1oUTVoYngzNUJkNTZJVE9nbHNaRnJHNzFxYUciLCJtYWMiOiI4OGMzMWRlMjY3ZGIzMzg1MGYxMzMyMGE2ZTA0M2M3OWQzNGJmYjY0YzNmYzdjMDExNzYwNWU2NWVjYWY5YzIxIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 14:25:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6ImNSdUNVZ0xQZUhuQlJWR0llV093aWc9PSIsInZhbHVlIjoiMXRUNEZuVHpPV3Q1cVdoM05UWG4xdThiL3graHQ3YW1pZTZadUZWNjBzU244TXhRTlRTS0RKUDlPZDBwQ3pXZUMycDh6UUJsa2llWFNaRGpodU1aUHc5SUJTcEprM3FleC9KSzBtenR1dnFVZVdCMkN3RnFkbmw3dGdwaUI4NS8iLCJtYWMiOiIyNGYwZTcwZDkwMDQ3MTFhNjg5M2UzMzg3YjBmYTBmODJhZDczYzM3ZTEyMzJlMjY1YmJlN2NjNmI5MDEzMjY2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 14:25:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647014685\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-191923033 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/16/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191923033\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/16/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole"}, "badge": null}}