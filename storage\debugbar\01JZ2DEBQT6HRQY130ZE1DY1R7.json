{"__meta": {"id": "01JZ2DEBQT6HRQY130ZE1DY1R7", "datetime": "2025-07-01 07:15:26", "utime": **********.074547, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.325507, "end": **********.074561, "duration": 0.7490541934967041, "duration_str": "749ms", "measures": [{"label": "Booting", "start": **********.325507, "relative_start": 0, "end": **********.773695, "relative_end": **********.773695, "duration": 0.*****************, "duration_str": "448ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.773714, "relative_start": 0.*****************, "end": **********.074562, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.974221, "relative_start": 0.****************, "end": **********.976373, "relative_end": **********.976373, "duration": 0.002151966094970703, "duration_str": "2.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.072176, "relative_start": 0.****************, "end": **********.072963, "relative_end": **********.072963, "duration": 0.0007870197296142578, "duration_str": "787μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019540000000000002, "accumulated_duration_str": "19.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.980801, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.327}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.990305, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.327, "width_percent": 2.968}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.993356, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.295, "width_percent": 2.508}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9957821, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.802, "width_percent": 1.689}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9968412, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.491, "width_percent": 1.433}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0038838, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.924, "width_percent": 3.071}, {"sql": "select * from `payments` where year(`paid_date`) = 2025 and `status` in ('completed', 'paid') and `paid_date` is not null", "type": "query", "params": [], "bindings": [2025, "completed", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.0054278, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.995, "width_percent": 12.948}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.017326, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.943, "width_percent": 2.405}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.018348, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.348, "width_percent": 1.433}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.019712, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.781, "width_percent": 1.842}, {"sql": "select * from `roles` where `name` = 'bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.021852, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.623, "width_percent": 2.405}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (15))", "type": "query", "params": [], "bindings": ["App\\Models\\User", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.0252109, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.029, "width_percent": 3.838}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4, 6, 19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.026736, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.867, "width_percent": 1.945}, {"sql": "select * from `projects` where `projects`.`user_id` in (4, 6, 19) and `won_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.028501, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.812, "width_percent": 2.764}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.034399, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.575, "width_percent": 2.405}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.035434, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.981, "width_percent": 1.586}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '02'", "type": "query", "params": [], "bindings": ["paid", 2025, "02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.036913, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.567, "width_percent": 2.968}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '03'", "type": "query", "params": [], "bindings": ["paid", 2025, "03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.038141, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.535, "width_percent": 2.661}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '04'", "type": "query", "params": [], "bindings": ["paid", 2025, "04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.039257, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.197, "width_percent": 2.508}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '05'", "type": "query", "params": [], "bindings": ["paid", 2025, "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.040373, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.704, "width_percent": 2.661}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '06'", "type": "query", "params": [], "bindings": ["paid", 2025, "06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.041624, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.365, "width_percent": 2.61}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '07'", "type": "query", "params": [], "bindings": ["paid", 2025, "07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.0429642, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.975, "width_percent": 2.303}, {"sql": "select * from `projects` where `status` in ('planning', 'in_progress')", "type": "query", "params": [], "bindings": ["planning", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}], "start": **********.044311, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:252", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=252", "ajax": false, "filename": "RevenueForecastChart.php", "line": "252"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.278, "width_percent": 1.894}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.049829, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.172, "width_percent": 2.405}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0508392, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.577, "width_percent": 1.484}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '02' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["02", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.0523238, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.061, "width_percent": 2.149}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '02' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["02", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.054172, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.211, "width_percent": 3.378}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '03' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["03", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.055516, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.589, "width_percent": 2.098}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '03' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["03", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.057046, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.687, "width_percent": 3.941}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '04' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["04", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.058521, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.627, "width_percent": 1.842}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '04' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["04", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.0596159, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.47, "width_percent": 2.968}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '05' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["05", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.060833, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.438, "width_percent": 1.586}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '05' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["05", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.061842, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.025, "width_percent": 2.917}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '06' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.063041, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.942, "width_percent": 1.791}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '06' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["06", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.064104, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.733, "width_percent": 2.815}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '07' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.0652049, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.548, "width_percent": 1.894}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '07' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["07", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.066307, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.441, "width_percent": 2.559}]}, "models": {"data": {"App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.monthly-revenue-chart #H5LuzqbnOj8mVf4Gczud": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"current_year\"\n    \"dataChecksum\" => \"a369fb06786dd190c5cf8d6381ab8edb\"\n  ]\n  \"name\" => \"app.filament.widgets.monthly-revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MonthlyRevenueChart\"\n  \"id\" => \"H5LuzqbnOj8mVf4Gczud\"\n]", "app.filament.widgets.bde-performance-chart #Zwb47TGbRbQyeU17pejh": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"this_month\"\n    \"dataChecksum\" => \"705f9f6a260c944b564b927a292dad89\"\n  ]\n  \"name\" => \"app.filament.widgets.bde-performance-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\BdePerformanceChart\"\n  \"id\" => \"Zwb47TGbRbQyeU17pejh\"\n]", "app.filament.widgets.revenue-forecast-chart #TKkrj63yNM7hqYBNtOtR": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"6_months\"\n    \"dataChecksum\" => \"57140bad3c40b1517f411fe8421f272c\"\n  ]\n  \"name\" => \"app.filament.widgets.revenue-forecast-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueForecastChart\"\n  \"id\" => \"TKkrj63yNM7hqYBNtOtR\"\n]", "app.filament.widgets.milestone-due-vs-received-chart #vY2FSsoNnhJqTNkljRWg": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"last_6_months\"\n    \"dataChecksum\" => \"7d4b19abd5f34afd8ee78597507c8c12\"\n  ]\n  \"name\" => \"app.filament.widgets.milestone-due-vs-received-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MilestoneDueVsReceivedChart\"\n  \"id\" => \"vY2FSsoNnhJqTNkljRWg\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\MonthlyRevenueChart@updateChartData<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "751ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-345868302 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-345868302\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1130718647 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"357 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;current_year&quot;,&quot;dataChecksum&quot;:&quot;a369fb06786dd190c5cf8d6381ab8edb&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;H5LuzqbnOj8mVf4Gczud&quot;,&quot;name&quot;:&quot;app.filament.widgets.monthly-revenue-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;8e604ac32d9cd385ae11cc14c4e1cab00a6a8c222cd6e69358da3d7ceb0c9b9f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"355 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;this_month&quot;,&quot;dataChecksum&quot;:&quot;705f9f6a260c944b564b927a292dad89&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;Zwb47TGbRbQyeU17pejh&quot;,&quot;name&quot;:&quot;app.filament.widgets.bde-performance-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;0e991335fa6b53db29ef7707802dff8c1564b74635140b062d696682b69726f4&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;6_months&quot;,&quot;dataChecksum&quot;:&quot;57140bad3c40b1517f411fe8421f272c&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;TKkrj63yNM7hqYBNtOtR&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-forecast-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;122c5197540aadc412b33ead95a2583df33a2434a5d2826de9093864b4405703&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"368 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;last_6_months&quot;,&quot;dataChecksum&quot;:&quot;7d4b19abd5f34afd8ee78597507c8c12&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;vY2FSsoNnhJqTNkljRWg&quot;,&quot;name&quot;:&quot;app.filament.widgets.milestone-due-vs-received-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7125a5a37079ee8d25422b7b953fe835619f1e3a3e2c449a9404ef2569043bc9&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130718647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-382244251 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2042</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndUTm9JSHYzcjRlVGs0ZTJUdmlTZFE9PSIsInZhbHVlIjoiNHVacFJWeW5mcm1TemRnYTZjd3c1MU9EYUxLMk1Fc2xaR0tmeU1ITHhjYUQ5NFNmeVBoTlhReGxxZGhQazlnc201SDNKYlhRRFREVFdrTWs0czB0VDFoeXlFN1dxRmFaNmJ1REZtVFhnWm50RWFBU1pVakJEVXVJeDVoTGVKU0ciLCJtYWMiOiJiODMyZWQ4YzYzNDg4Y2NmZjRjMGU1NDZjYzg5NTdmNjIxYzMxZDU4MzcyZWNiYjhkNjVhOTdjMWYzYmM1ZDA0IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImZFTldZdjcxVSt6VWM4dzFxaVVFRkE9PSIsInZhbHVlIjoibEFid0x3aks2NHNJSHJPWm15VEdUOXAwcWFic2NadmZnbE1kUlBkQ1NaMkY5OVNRUkp4YzZUSzBFSVpXcWtZck90VnJJVWJUMHI2Wk5xYlhTZm1xWDZHMElCakdxalEvY2VqOWsvUlVJbEZSbVdpMUJ2RXpNNHNDSlBIQ1laWm0iLCJtYWMiOiIwOTFiYzBmYzkwY2ZjMDIyYWUyMjhjNjAwZjg1ZDVmMmI5OTA2ZTJlN2Y4MGMxZWY0MmM2OTEyZjc2Nzk4ZDkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382244251\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1031302388 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031302388\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1806538007 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 07:15:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806538007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1631614924 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631614924\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}