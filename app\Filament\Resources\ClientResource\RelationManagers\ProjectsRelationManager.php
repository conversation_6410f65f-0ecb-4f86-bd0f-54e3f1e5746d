<?php

namespace App\Filament\Resources\ClientResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;
use Closure;

class ProjectsRelationManager extends RelationManager
{
    protected static string $relationship = 'projects';

    public $tempGeneratedMilestones = null;
    public $tempGeneratedPayments = null;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('client_id')
                    ->default(fn (RelationManager $livewire) => $livewire->ownerRecord->id),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('project_type_id')
                            ->relationship('projectType', 'name')
                            ->label('Title')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state) {
                                    $projectType = \App\Models\ProjectType::find($state);
                                    $set('title', $projectType ? $projectType->name : 'New Project');
                                    if ($projectType && $projectType->name !== 'Product') {
                                        $set('product_id', null);
                                    }
                                }
                            }),
                        Forms\Components\DatePicker::make('won_date')
                            ->required()
                            ->dehydrated()
                            ->live(onBlur: true)
                            ->rules([
                                function (Get $get) {
                                    return function (string $attribute, $value, Closure $fail) use ($get) {
                                        $startDate = $get('start_date');
                                        if ($startDate && $value) {
                                            $wonDateCarbon = \Carbon\Carbon::parse($value);
                                            $startDateCarbon = \Carbon\Carbon::parse($startDate);
                                            if ($wonDateCarbon->gt($startDateCarbon)) {
                                                $fail('The won date must be equal to or less than the start date (' . $startDateCarbon->format('Y-m-d') . ').');
                                            }
                                        }
                                    };
                                },
                            ]),
                    ]),

                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'title')
                    ->label('Product')
                    ->searchable()
                    ->preload()
                    ->visible(function (Get $get) {
                        $projectTypeId = $get('project_type_id');
                        if (!$projectTypeId) return false;
                        $projectType = \App\Models\ProjectType::find($projectTypeId);
                        return $projectType && $projectType->name === 'Product';
                    })
                    ->live()
                    ->afterStateUpdated(function (Set $set, Get $get, $state) {
                        if ($state) {
                            $product = \App\Models\Product::find($state);
                            if ($product) {
                                $set('title', $product->title);
                                $set('total_payment', $product->amount);
                                $set('currency', $product->currency);
                                if ($get('start_date') && $get('duration') && $get('duration_unit')) {
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            }
                        }
                    }),

                Forms\Components\Grid::make(5)
                    ->schema([
                        Forms\Components\TextInput::make('duration')
                            ->required()
                            ->numeric()
                            ->minValue(1)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state && is_numeric($state) && $state > 0) { // Only calculate if duration is valid
                                    $this->calculateEndDate($set, $get);
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            }),
                        Forms\Components\Select::make('duration_unit')
                            ->options([
                                'hours' => 'Hours',
                                'days' => 'Days',
                                'weeks' => 'Weeks',
                                'months' => 'Months',
                                'years' => 'Years',
                            ])
                            ->required()
                            ->dehydrated()
                            ->live()
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state) { // Only calculate if duration_unit is selected
                                    $this->calculateEndDate($set, $get);
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            }),
                        Forms\Components\Select::make('payment_cycle')
                            ->options([
                                'weekly' => 'Weekly',
                                'biweekly' => 'Bi-weekly',
                                'monthly' => 'Monthly',
                                'quarterly' => 'Quarterly',
                                'yearly' => 'Yearly',
                                'milestone' => 'Milestone-based',
                                'upfront' => 'Upfront',
                            ])
                            ->required()
                            ->dehydrated()
                            ->live()
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state) { // Only calculate if payment_cycle is selected
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->required()
                            ->dehydrated()
                            ->live()
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state) { // Only calculate if start_date has a value
                                    $this->calculateEndDate($set, $get);
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            })
                            ->rules([
                                function (Get $get) {
                                    return function (string $attribute, $value, Closure $fail) use ($get) {
                                        $wonDate = $get('won_date');
                                        if ($wonDate && $value) {
                                            $wonDateCarbon = \Carbon\Carbon::parse($wonDate);
                                            $startDateCarbon = \Carbon\Carbon::parse($value);
                                            if ($startDateCarbon->lt($wonDateCarbon)) {
                                                $fail('The start date must be equal to or greater than the won date (' . $wonDateCarbon->format('Y-m-d') . ').');
                                            }
                                        }
                                    };
                                },
                            ]),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('End Date')
                            ->helperText('Auto-calculated based on start date and duration')
                            ->live()
                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                // Only auto-calculate if the field is empty or being cleared
                                if (empty($state)) {
                                    $this->calculateEndDate($set, $get);
                                }
                            }),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        // Forms\Components\Select::make('user_id')
                        // ->relationship('user', 'name')
                        // ->label('BDE')
                        // ->required()
                        // ->default(auth()->id())
                        // ->disabled(fn() => !auth()->user()->hasRole('super_admin'))
                        // ->visible(fn() => auth()->user()->hasRole('super_admin'))
                        // ->live(onBlur: true),
                        Forms\Components\Hidden::make('user_id')
                            ->default(auth()->id())
                            ->dehydrated(),
                        auth()->user()->hasRole('super_admin')
                        ? Forms\Components\Select::make('bde_select')
                            ->relationship('user', 'name')
                            ->label('BDE')
                            ->default(auth()->id())
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $set('user_id', $state);
                            })
                        : Forms\Components\TextInput::make('bde_display')
                            ->label('BDE')
                            ->default(function ($operation, $record) {
                                // For create operation
                                if ($operation === 'create') {
                                    return auth()->user()->name;
                                }
                                // For edit operation - load the relationship properly
                                if ($record && $record->user_id) {
                                    return $record->user->name ?? 'Not assigned';
                                }
                                return auth()->user()->name;
                            })
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, $record) {
                                // Ensure the name is loaded when the form hydrates
                                if ($record && $record->user_id) {
                                    $component->state($record->user->name);
                                }
                            }),
                        // Forms\Components\Select::make('pricing_model_id')
                        //         ->relationship('pricingModel', 'name')
                        //         ->required()
                        //         ->live(onBlur: true)
                        //         ->afterStateUpdated(function (Set $set, $state, Get $get) {
                        //             if ($state) {
                        //                 $pricingModel = \App\Models\PricingModel::find($state);
                        //                 if ($pricingModel) {
                        //                     // Auto-set currency only if not manually changed
                        //                     $currentCurrency = $get('currency');
                        //                     $autoCurrency = null;

                        //                     if (str_contains(strtoupper($pricingModel->name), 'USD')) {
                        //                         $autoCurrency = 'USD';
                        //                     } elseif (str_contains(strtoupper($pricingModel->name), 'INR')) {
                        //                         $autoCurrency = 'INR';
                        //                     }

                        //                     // Only auto-update if currency wasn't manually set
                        //                     if ($autoCurrency && $currentCurrency !== $autoCurrency) {
                        //                         $set('currency', $autoCurrency);
                        //                     }

                        //                     // Handle milestones/payments logic
                        //                     if (str_contains($pricingModel->name, 'Fixed')) {
                        //                         $set('_generated_milestones', '');
                        //                         $set('_generated_payments', '');
                        //                     } else {
                        //                         $this->generateMilestonesAndPayments($set, $get);
                        //                     }
                        //                 }
                        //             }
                        //         }),
                        // Forms\Components\Select::make('currency')
                        //     ->options([
                        //         'INR' => 'Indian Rupee (₹)',
                        //         'USD' => 'US Dollar ($)',
                        //         'EUR' => 'Euro (€)',
                        //         'GBP' => 'British Pound (£)',
                        //     ])
                        //     ->default('INR')
                        //     ->required()
                        //     ->afterStateUpdated(function (Set $set, $state) {
                        //         $set('currency', $state);
                        //     }),
                        Forms\Components\Select::make('pricing_model_id')
                            ->relationship('pricingModel', 'name')
                            ->required()
                            ->live() // ⚠️ Changed from `live(onBlur: true)` to `live()` for instant reaction
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if (!$state) return;

                                $pricingModel = \App\Models\PricingModel::find($state);

                                if ($pricingModel) {
                                    // Force currency update (even if manually changed)
                                    if (str_contains(strtoupper($pricingModel->name), 'USD')) {
                                        $set('currency', 'USD'); // Immediate update
                                    }
                                    elseif (str_contains(strtoupper($pricingModel->name), 'INR')) {
                                        $set('currency', 'INR'); // Immediate update
                                    }

                                    // Rest of your logic (milestones, etc.)
                                    if (str_contains($pricingModel->name, 'Fixed')) {
                                        $set('_generated_milestones', '');
                                        $set('_generated_payments', '');
                                    } else {
                                        $this->generateMilestonesAndPayments($set, $get);
                                    }
                                }
                            }),
                        Forms\Components\Select::make('currency')
                        ->options([
                            'INR' => 'Indian Rupee (₹)',
                            'USD' => 'US Dollar ($)',
                            'EUR' => 'Euro (€)',
                            'GBP' => 'British Pound (£)',
                        ])
                        ->default('INR')
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Set $set, $state) {
                            $set('currency', $state);
                        }),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('total_payment')
                            ->required()
                            ->numeric()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->generateMilestonesAndPayments($set, $get);
                            }),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'completed' => 'Completed',
                                'on_hold' => 'On Hold',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('active')
                            ->required(),
                    ]),

                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull(),

                Forms\Components\Hidden::make('_generated_milestones'),
                Forms\Components\Hidden::make('_generated_payments'),
                Forms\Components\Hidden::make('title')
                    ->default(function (Get $get) {
                        $projectTypeId = $get('project_type_id');
                        if ($projectTypeId) {
                            $projectType = \App\Models\ProjectType::find($projectTypeId);
                            return $projectType ? $projectType->name : 'New Project';
                        }
                        return 'New Project';
                    })
                    ->live(),
            ]);
    }

    // Removed afterCreate() method to prevent duplicate payment creation
    // Payments are now created only in the CreateAction->after() callback

    private function createMilestonesAndPayments(array $data, $project, string $action = 'create')
    {
        // Prevent duplicate creation - check if milestones/payments already exist
        if ($action === 'create') {
            $existingMilestones = $project->milestones()->count();
            $existingPayments = $project->payments()->count();

            if ($existingMilestones > 0 || $existingPayments > 0) {
                // Already has milestones/payments, skip creation to prevent duplicates
                return;
            }
        }

        // Create milestones first, then link payments to them
        $createdMilestones = [];
        if (isset($data['_generated_milestones']) && !empty($data['_generated_milestones'])) {
            $milestones = json_decode($data['_generated_milestones'], true);
            if (is_array($milestones)) {
                foreach ($milestones as $index => $milestoneData) {
                    $milestoneData['project_id'] = $project->id;
                    $milestone = \App\Models\Milestone::create($milestoneData);
                    $createdMilestones[$index] = $milestone;
                }
            }
        }

        // Create payments and link them to corresponding milestones
        if (isset($data['_generated_payments']) && !empty($data['_generated_payments'])) {
            $payments = json_decode($data['_generated_payments'], true);
            if (is_array($payments)) {
                foreach ($payments as $index => $paymentData) {
                    $paymentData['project_id'] = $project->id;

                    // Link payment to corresponding milestone if it exists
                    if (isset($createdMilestones[$index])) {
                        $paymentData['milestone_id'] = $createdMilestones[$index]->id;
                    }

                    \App\Models\Payment::create($paymentData);
                }
            }
        }
    }

    // private function calculateEndDate(Set $set, Get $get)
    // {
    //     $startDate = $get('start_date');
    //     $duration = $get('duration');
    //     $durationUnit = $get('duration_unit');
    //     $paymentCycle = $get('payment_cycle');

    //     if (!$startDate || !$duration || !$durationUnit) {
    //         return;
    //     }

    //     try {
    //         $startCarbon = \Carbon\Carbon::parse($startDate);
    //         $durationValue = (int) $duration;

    //         if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
    //             $workHoursPerWeek = 40;
    //             $weeks = ceil($durationValue / $workHoursPerWeek);
    //             $endDate = $startCarbon->copy()->addWeeks($weeks);
    //         }
    //         elseif ($durationUnit === 'days' && $paymentCycle === 'weekly') {
    //             $weeks = ceil($durationValue / 7);
    //             $endDate = $startCarbon->copy()->addWeeks($weeks);
    //         }
    //         else {
    //             $endDate = match($durationUnit) {
    //                 'hours' => $startCarbon->copy()->addHours($durationValue),
    //                 'days' => $startCarbon->copy()->addDays($durationValue),
    //                 'weeks' => $startCarbon->copy()->addWeeks($durationValue),
    //                 'months' => $startCarbon->copy()->addMonths($durationValue - 1)->endOfMonth(),
    //                 'years' => $startCarbon->copy()->addYears($durationValue),
    //                 default => $startCarbon->copy()->addMonths($durationValue),
    //             };
    //         }

    //         $set('end_date', $endDate->format('Y-m-d'));
    //     } catch (\Exception $e) {
    //         // Handle errors silently
    //     }
    // }
    private function calculateEndDate(Set $set, Get $get)
    {
        $startDate = $get('start_date');
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');

        if (!$startDate || !$duration || !$durationUnit) {
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration;

            if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
                $workHoursPerWeek = 40;
                $weeks = ceil($durationValue / $workHoursPerWeek);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            elseif ($durationUnit === 'days' && $paymentCycle === 'weekly') {
                $weeks = ceil($durationValue / 7);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            else {
                $endDate = match($durationUnit) {
                    'hours' => $startCarbon->copy()->addHours($durationValue),
                    'days' => $startCarbon->copy()->addDays($durationValue),
                    'weeks' => $startCarbon->copy()->addWeeks($durationValue),
                    'months' => $startCarbon->copy()->addMonths($durationValue)->subDay(),
                    'years' => $startCarbon->copy()->addYears($durationValue)->subDay(),
                    default => $startCarbon->copy()->addMonths($durationValue),
                };
            }

            $set('end_date', $endDate->format('Y-m-d'));
        } catch (\Exception $e) {
            // Handle errors silently
        }
    }
    private function generateMilestonesAndPayments(Set $set, Get $get)
    {
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');
        $startDate = $get('start_date');
        $totalPayment = $get('total_payment');

        if (!$duration || !$durationUnit || !$paymentCycle || !$startDate || !$totalPayment) {
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration;
            $totalPaymentValue = (float) $totalPayment;
            $milestones = [];
            $payments = [];

            $numberOfCycles = $this->calculateNumberOfCycles($durationValue, $durationUnit, $paymentCycle);
            if ($numberOfCycles <= 0) return;

            if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
                $standardHoursPerWeek = 40;
                $fullWeeks = floor($durationValue / $standardHoursPerWeek);
                $remainingHours = $durationValue % $standardHoursPerWeek;
                $totalCycles = $fullWeeks + ($remainingHours > 0 ? 1 : 0);

                $adjustedAmounts = $this->distributeAmountEvenly($totalPaymentValue, $totalCycles);
                $adjustedPercentages = $this->distributePercentageEvenly($totalCycles);

                for ($i = 1; $i <= $fullWeeks; $i++) {
                    $dueDate = $this->calculateDueDate($startCarbon, $i, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($i, $paymentCycle),
                        'description' => "Auto-generated milestone {$i} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$i-1],
                        'hours' => $standardHoursPerWeek,
                        'amount' => $adjustedAmounts[$i-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$i-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }

                if ($remainingHours > 0) {
                    $cycleNumber = $fullWeeks + 1;
                    $dueDate = $this->calculateDueDate($startCarbon, $cycleNumber, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($cycleNumber, $paymentCycle),
                        'description' => "Auto-generated milestone {$cycleNumber} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$cycleNumber-1],
                        'hours' => $remainingHours,
                        'amount' => $adjustedAmounts[$cycleNumber-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$cycleNumber-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }
            } else {
                $adjustedAmounts = $this->distributeAmountEvenly($totalPaymentValue, $numberOfCycles);
                $adjustedPercentages = $this->distributePercentageEvenly($numberOfCycles);
                $hoursPerCycle = ($durationUnit === 'hours') ? round($durationValue / $numberOfCycles) : null;

                for ($i = 1; $i <= $numberOfCycles; $i++) {
                    $dueDate = $this->calculateDueDate($startCarbon, $i, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($i, $paymentCycle),
                        'description' => "Auto-generated milestone {$i} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$i-1],
                        'hours' => $hoursPerCycle,
                        'amount' => $adjustedAmounts[$i-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$i-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }
            }

            $set('_generated_milestones', json_encode($milestones));
            $set('_generated_payments', json_encode($payments));
        } catch (\Exception $e) {
            Log::error("Error generating milestones: " . $e->getMessage());
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
        }
    }

    private function distributeAmountEvenly(float $totalAmount, int $numberOfCycles): array
    {
        if ($numberOfCycles <= 0) return [];

        $amounts = [];
        $baseAmount = $totalAmount / $numberOfCycles;
        $sum = 0.0;

        // Round all amounts except the last one
        for ($i = 0; $i < $numberOfCycles - 1; $i++) {
            $rounded = round($baseAmount, 2);
            $amounts[] = $rounded;
            $sum += $rounded;
        }

        // The last amount gets the remainder to ensure the total is correct
        $lastAmount = round($totalAmount - $sum, 2);
        $amounts[] = $lastAmount;

        return $amounts;
    }

    private function distributePercentageEvenly(int $numberOfCycles): array
    {
        if ($numberOfCycles <= 0) return [];

        $percentages = [];
        $basePercentage = 100 / $numberOfCycles;
        $sum = 0.0;

        // Round all percentages except the last one
        for ($i = 0; $i < $numberOfCycles - 1; $i++) {
            $rounded = round($basePercentage, 2);
            $percentages[] = $rounded;
            $sum += $rounded;
        }

        // The last percentage gets the remainder to ensure the total is 100%
        $lastPercentage = round(100 - $sum, 2);
        $percentages[] = $lastPercentage;

        return $percentages;
    }

    private function calculateNumberOfCycles($duration, $durationUnit, $paymentCycle)
    {
        switch ($paymentCycle) {
            case 'weekly':
                if ($durationUnit === 'hours') {
                    $fullWeeks = floor($duration / 40);
                    $remainingHours = $duration % 40;
                    return $remainingHours > 0 ? $fullWeeks + 1 : $fullWeeks;
                }
                if ($durationUnit === 'weeks') return $duration;
                if ($durationUnit === 'days') return ceil($duration / 7);
                if ($durationUnit === 'months') return $duration * 4;
                return max(1, ceil($duration / 7));
            case 'biweekly':
                if ($durationUnit === 'weeks') return ceil($duration / 2);
                if ($durationUnit === 'months') return $duration * 2;
                return max(1, ceil($duration / 14));
            case 'monthly':
                if ($durationUnit === 'months') return $duration;
                if ($durationUnit === 'years') return $duration * 12;
                return max(1, ceil($duration / 30));
            case 'quarterly':
                if ($durationUnit === 'months') return ceil($duration / 3);
                if ($durationUnit === 'years') return $duration * 4;
                return max(1, ceil($duration / 90));
            case 'yearly':
                if ($durationUnit === 'years') return $duration;
                if ($durationUnit === 'months') return ceil($duration / 12);
                return max(1, ceil($duration / 365));
            case 'milestone':
                if ($durationUnit === 'months') return $duration;
                if ($durationUnit === 'weeks') return $duration;
                if ($durationUnit === 'years') return $duration * 12;
                return max(1, ceil($duration / 4));
            case 'upfront':
                return 1;
            default:
                return 1;
        }
    }

    // private function calculateDueDate($startDate, $cycleNumber, $paymentCycle, $duration, $durationUnit)
    // {
    //     $cycleNum = (int) $cycleNumber;
    //     $durationVal = (int) $duration;

    //     switch ($paymentCycle) {
    //         case 'weekly': return $startDate->copy()->addWeeks($cycleNum);
    //         case 'biweekly': return $startDate->copy()->addWeeks($cycleNum * 2);
    //         case 'monthly':
    //             $dueDate = $startDate->copy()->addMonths($cycleNum - 1);
    //             return $dueDate->endOfMonth();
    //         case 'quarterly': return $startDate->copy()->addMonths($cycleNum * 3);
    //         case 'yearly': return $startDate->copy()->addYears($cycleNum);
    //         case 'milestone':
    //             if ($durationUnit === 'months') {
    //                 $dueDate = $startDate->copy()->addMonths($cycleNum - 1);
    //                 return $dueDate->endOfMonth();
    //             }
    //             return $startDate->copy()->addWeeks($cycleNum);
    //         case 'upfront': return $startDate->copy();
    //         default: return $startDate->copy()->addMonths($cycleNum);
    //     }
    // }
    private function calculateDueDate($startDate, $cycleNumber, $paymentCycle, $duration, $durationUnit)
    {
        $cycleNum = (int) $cycleNumber;
        $startCarbon = \Carbon\Carbon::parse($startDate);

        switch ($paymentCycle) {
            // Weekly payments (every 7 days)
            case 'weekly':
                return $startCarbon->copy()->addWeeks($cycleNum);

            // Bi-weekly payments (every 14 days)
            case 'biweekly':
                return $startCarbon->copy()->addWeeks($cycleNum * 2);

            // Monthly payments (same day each month)
            case 'monthly':
                return $startCarbon->copy()->addMonths($cycleNum);

            // Quarterly payments (every 3 months)
            case 'quarterly':
                return $startCarbon->copy()->addMonths($cycleNum * 3);

            // Yearly payments (same date each year)
            case 'yearly':
                return $startCarbon->copy()->addYears($cycleNum);

            // Milestone-based (depends on duration unit)
            case 'milestone':
                switch ($durationUnit) {
                    case 'hours':
                    case 'days':
                        return $startCarbon->copy()->addDays($cycleNum * ceil($duration / $this->calculateNumberOfCycles($duration, $durationUnit, $paymentCycle)));
                    case 'weeks':
                        return $startCarbon->copy()->addWeeks($cycleNum);
                    case 'months':
                        return $startCarbon->copy()->addMonths($cycleNum);
                    case 'years':
                        return $startCarbon->copy()->addYears($cycleNum);
                    default:
                        return $startCarbon->copy()->addDays($cycleNum);
                }

            // Upfront payment (due immediately)
            case 'upfront':
                return $startCarbon->copy();

            // Default case (fallback to weekly)
            default:
                return $startCarbon->copy()->addWeeks($cycleNum);
        }
    }

    private function generateMilestoneTitle($cycleNumber, $paymentCycle)
    {
        switch ($paymentCycle) {
            case 'weekly': return "Week {$cycleNumber} Milestone";
            case 'biweekly': return "Bi-week {$cycleNumber} Milestone";
            case 'monthly': return "Month {$cycleNumber} Milestone";
            case 'quarterly': return "Quarter {$cycleNumber} Milestone";
            case 'yearly': return "Year {$cycleNumber} Milestone";
            case 'milestone': return "Milestone {$cycleNumber}";
            case 'upfront': return "Upfront Payment";
            default: return "Milestone {$cycleNumber}";
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.title')
                    ->label('Product')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('pricingModel.name')
                    ->label('Pricing Model')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('BDE')
                    ->sortable()
                    ->visible(fn(): bool => auth()->user()->hasRole('super_admin')),
                Tables\Columns\TextColumn::make('total_payment')
                    ->money(fn ($record) => $record->currency ?? 'INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'on_hold' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('project_type_id')
                    ->label('Project Type')
                    ->relationship('projectType', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('pricing_model_id')
                    ->label('Pricing Model')
                    ->relationship('pricingModel', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'completed' => 'Completed',
                        'on_hold' => 'On Hold',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Create Project')
                    ->mutateFormDataUsing(function (array $data, RelationManager $livewire): array {
                        $data['client_id'] = $livewire->ownerRecord->id;
                        $livewire->tempGeneratedMilestones = $data['_generated_milestones'] ?? null;
                        $livewire->tempGeneratedPayments = $data['_generated_payments'] ?? null;
                        unset($data['_generated_milestones'], $data['_generated_payments']);
                        return $data;
                    })
                    ->after(function (array $data, $record, RelationManager $livewire) {
                        if ($livewire->tempGeneratedMilestones) {
                            $data['_generated_milestones'] = $livewire->tempGeneratedMilestones;
                        }
                        if ($livewire->tempGeneratedPayments) {
                            $data['_generated_payments'] = $livewire->tempGeneratedPayments;
                        }
                        $livewire->createMilestonesAndPayments($data, $record, 'create');
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view_milestones')
                    ->label('')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn ($record) => "Milestones for {$record->title}")
                    ->modalWidth('7xl')
                    ->modalCancelAction(null)
                    ->modalSubmitAction(false)
                    ->modalContent(fn ($record) => view('filament.components.milestones-filament-table', [
                        'milestones' => $record->milestones()
                            ->where('is_merged', false)
                            ->with('payments')
                            ->get(),
                        'currency' => $record->currency ?? 'INR',
                    ])),
                Tables\Actions\EditAction::make()
                    ->label('')
                    ->mutateFormDataUsing(function (array $data, RelationManager $livewire): array {
                        $data['client_id'] = $livewire->ownerRecord->id;
                        $livewire->tempGeneratedMilestones = $data['_generated_milestones'] ?? null;
                        $livewire->tempGeneratedPayments = $data['_generated_payments'] ?? null;
                        unset($data['_generated_milestones'], $data['_generated_payments']);
                        return $data;
                    })
                    ->after(function (array $data, $record, RelationManager $livewire) {
                        if ($livewire->tempGeneratedMilestones) {
                            $data['_generated_milestones'] = $livewire->tempGeneratedMilestones;
                        }
                        if ($livewire->tempGeneratedPayments) {
                            $data['_generated_payments'] = $livewire->tempGeneratedPayments;
                        }
                        if (isset($data['_generated_milestones']) && !empty($data['_generated_milestones'])) {
                            $record->milestones()->delete();
                            $record->payments()->delete();
                            $livewire->createMilestonesAndPayments($data, $record, 'edit');
                        }
                    }),
                Tables\Actions\DeleteAction::make()->label(''),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
