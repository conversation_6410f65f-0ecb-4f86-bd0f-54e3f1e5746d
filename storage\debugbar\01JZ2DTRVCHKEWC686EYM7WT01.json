{"__meta": {"id": "01JZ2DTRVCHKEWC686EYM7WT01", "datetime": "2025-07-01 07:22:12", "utime": **********.718228, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 21, "messages": [{"message": "[07:22:12] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/dashboard\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.269894, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] User widget configs {\n    \"user_id\": 1,\n    \"userConfigs\": {\n        \"1\": {\n            \"id\": 17,\n            \"user_id\": 1,\n            \"widget_id\": 1,\n            \"position\": 1,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-10T06:02:57.000000Z\"\n        },\n        \"2\": {\n            \"id\": 18,\n            \"user_id\": 1,\n            \"widget_id\": 2,\n            \"position\": 2,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:14:00.000000Z\"\n        },\n        \"3\": {\n            \"id\": 19,\n            \"user_id\": 1,\n            \"widget_id\": 3,\n            \"position\": 3,\n            \"width\": 6,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:14:00.000000Z\"\n        },\n        \"4\": {\n            \"id\": 20,\n            \"user_id\": 1,\n            \"widget_id\": 4,\n            \"position\": 4,\n            \"width\": 3,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": false,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:14:00.000000Z\"\n        },\n        \"5\": {\n            \"id\": 21,\n            \"user_id\": 1,\n            \"widget_id\": 5,\n            \"position\": 5,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:14:00.000000Z\"\n        },\n        \"6\": {\n            \"id\": 22,\n            \"user_id\": 1,\n            \"widget_id\": 6,\n            \"position\": 6,\n            \"width\": 12,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:07:54.000000Z\"\n        },\n        \"7\": {\n            \"id\": 23,\n            \"user_id\": 1,\n            \"widget_id\": 7,\n            \"position\": 7,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-09T16:08:18.000000Z\"\n        },\n        \"8\": {\n            \"id\": 24,\n            \"user_id\": 1,\n            \"widget_id\": 8,\n            \"position\": 8,\n            \"width\": 12,\n            \"height\": 1,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-09T16:07:54.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"9\": {\n            \"id\": 25,\n            \"user_id\": 1,\n            \"widget_id\": 9,\n            \"position\": 9,\n            \"width\": 12,\n            \"height\": 2,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"10\": {\n            \"id\": 26,\n            \"user_id\": 1,\n            \"widget_id\": 10,\n            \"position\": 10,\n            \"width\": 12,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"11\": {\n            \"id\": 27,\n            \"user_id\": 1,\n            \"widget_id\": 11,\n            \"position\": 11,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"12\": {\n            \"id\": 28,\n            \"user_id\": 1,\n            \"widget_id\": 12,\n            \"position\": 12,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"13\": {\n            \"id\": 29,\n            \"user_id\": 1,\n            \"widget_id\": 13,\n            \"position\": 13,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"14\": {\n            \"id\": 30,\n            \"user_id\": 1,\n            \"widget_id\": 14,\n            \"position\": 14,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"15\": {\n            \"id\": 31,\n            \"user_id\": 1,\n            \"widget_id\": 15,\n            \"position\": 15,\n            \"width\": 12,\n            \"height\": 4,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"16\": {\n            \"id\": 32,\n            \"user_id\": 1,\n            \"widget_id\": 16,\n            \"position\": 16,\n            \"width\": 12,\n            \"height\": 4,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"17\": {\n            \"id\": 33,\n            \"user_id\": 1,\n            \"widget_id\": 17,\n            \"position\": 17,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        },\n        \"18\": {\n            \"id\": 34,\n            \"user_id\": 1,\n            \"widget_id\": 18,\n            \"position\": 18,\n            \"width\": 6,\n            \"height\": 3,\n            \"settings\": null,\n            \"is_enabled\": true,\n            \"created_at\": \"2025-06-23T07:21:40.000000Z\",\n            \"updated_at\": \"2025-06-23T07:21:40.000000Z\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.300105, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 1,\n    \"name\": \"Total Projects\",\n    \"component\": \"TotalProjectsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 1,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.300867, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 2,\n    \"name\": \"Total Clients\",\n    \"component\": \"TotalClientsWidget\",\n    \"width\": 3,\n    \"height\": 1,\n    \"position\": 2,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.301462, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 3,\n    \"name\": \"Pending Payments\",\n    \"component\": \"PendingPaymentsWidget\",\n    \"width\": 6,\n    \"height\": 1,\n    \"position\": 3,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.302021, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 5,\n    \"name\": \"Recent Projects\",\n    \"component\": \"RecentProjectsWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 5,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.302574, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 6,\n    \"name\": \"Upcoming Payments\",\n    \"component\": \"UpcomingPaymentsWidget\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 6,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.303076, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 7,\n    \"name\": \"Project Status\",\n    \"component\": \"ProjectStatusWidget\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 7,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.303538, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 8,\n    \"name\": \"Monthly Revenue\",\n    \"component\": \"MonthlyRevenueWidget\",\n    \"width\": 12,\n    \"height\": 1,\n    \"position\": 8,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.304028, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 9,\n    \"name\": \"Business Stats Overview\",\n    \"component\": \"BusinessStatsOverview\",\n    \"width\": 12,\n    \"height\": 2,\n    \"position\": 9,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.304513, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 10,\n    \"name\": \"Revenue Performance Chart\",\n    \"component\": \"RevenuePerformanceChart\",\n    \"width\": 12,\n    \"height\": 3,\n    \"position\": 10,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.305031, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 11,\n    \"name\": \"Project Status Distribution\",\n    \"component\": \"ProjectStatusChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 11,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.305788, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 12,\n    \"name\": \"BDE Performance Leaderboard\",\n    \"component\": \"BdePerformanceChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 12,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.30635, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 13,\n    \"name\": \"Top Clients by Revenue\",\n    \"component\": \"ClientRevenueChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 13,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.307408, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 14,\n    \"name\": \"Payment Status Overview\",\n    \"component\": \"PaymentStatusChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 14,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.307807, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 15,\n    \"name\": \"Project Timeline & Milestones\",\n    \"component\": \"ProjectTimelineChart\",\n    \"width\": 12,\n    \"height\": 4,\n    \"position\": 15,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.3082, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 16,\n    \"name\": \"Revenue Forecast & Trends\",\n    \"component\": \"RevenueForecastChart\",\n    \"width\": 12,\n    \"height\": 4,\n    \"position\": 16,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.308453, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 17,\n    \"name\": \"Service Performance Analysis\",\n    \"component\": \"ProjectTypePerformanceChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 17,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.308669, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Widget prepared {\n    \"id\": 18,\n    \"name\": \"Milestone Due vs Received\",\n    \"component\": \"MilestoneDueVsReceivedChart\",\n    \"width\": 6,\n    \"height\": 3,\n    \"position\": 18,\n    \"settings\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.308861, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.info: [DASHBOARD] Final widgets for view [\n    {\n        \"id\": 1,\n        \"name\": \"Total Projects\",\n        \"component\": \"TotalProjectsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 1,\n        \"settings\": null\n    },\n    {\n        \"id\": 2,\n        \"name\": \"Total Clients\",\n        \"component\": \"TotalClientsWidget\",\n        \"width\": 3,\n        \"height\": 1,\n        \"position\": 2,\n        \"settings\": null\n    },\n    {\n        \"id\": 3,\n        \"name\": \"Pending Payments\",\n        \"component\": \"PendingPaymentsWidget\",\n        \"width\": 6,\n        \"height\": 1,\n        \"position\": 3,\n        \"settings\": null\n    },\n    {\n        \"id\": 5,\n        \"name\": \"Recent Projects\",\n        \"component\": \"RecentProjectsWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 5,\n        \"settings\": null\n    },\n    {\n        \"id\": 6,\n        \"name\": \"Upcoming Payments\",\n        \"component\": \"UpcomingPaymentsWidget\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 6,\n        \"settings\": null\n    },\n    {\n        \"id\": 7,\n        \"name\": \"Project Status\",\n        \"component\": \"ProjectStatusWidget\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 7,\n        \"settings\": null\n    },\n    {\n        \"id\": 8,\n        \"name\": \"Monthly Revenue\",\n        \"component\": \"MonthlyRevenueWidget\",\n        \"width\": 12,\n        \"height\": 1,\n        \"position\": 8,\n        \"settings\": null\n    },\n    {\n        \"id\": 9,\n        \"name\": \"Business Stats Overview\",\n        \"component\": \"BusinessStatsOverview\",\n        \"width\": 12,\n        \"height\": 2,\n        \"position\": 9,\n        \"settings\": null\n    },\n    {\n        \"id\": 10,\n        \"name\": \"Revenue Performance Chart\",\n        \"component\": \"RevenuePerformanceChart\",\n        \"width\": 12,\n        \"height\": 3,\n        \"position\": 10,\n        \"settings\": null\n    },\n    {\n        \"id\": 11,\n        \"name\": \"Project Status Distribution\",\n        \"component\": \"ProjectStatusChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 11,\n        \"settings\": null\n    },\n    {\n        \"id\": 12,\n        \"name\": \"BDE Performance Leaderboard\",\n        \"component\": \"BdePerformanceChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 12,\n        \"settings\": null\n    },\n    {\n        \"id\": 13,\n        \"name\": \"Top Clients by Revenue\",\n        \"component\": \"ClientRevenueChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 13,\n        \"settings\": null\n    },\n    {\n        \"id\": 14,\n        \"name\": \"Payment Status Overview\",\n        \"component\": \"PaymentStatusChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 14,\n        \"settings\": null\n    },\n    {\n        \"id\": 15,\n        \"name\": \"Project Timeline & Milestones\",\n        \"component\": \"ProjectTimelineChart\",\n        \"width\": 12,\n        \"height\": 4,\n        \"position\": 15,\n        \"settings\": null\n    },\n    {\n        \"id\": 16,\n        \"name\": \"Revenue Forecast & Trends\",\n        \"component\": \"RevenueForecastChart\",\n        \"width\": 12,\n        \"height\": 4,\n        \"position\": 16,\n        \"settings\": null\n    },\n    {\n        \"id\": 17,\n        \"name\": \"Service Performance Analysis\",\n        \"component\": \"ProjectTypePerformanceChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 17,\n        \"settings\": null\n    },\n    {\n        \"id\": 18,\n        \"name\": \"Milestone Due vs Received\",\n        \"component\": \"MilestoneDueVsReceivedChart\",\n        \"width\": 6,\n        \"height\": 3,\n        \"position\": 18,\n        \"settings\": null\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.309089, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:12] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/dashboard\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.710548, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751354530.259811, "end": **********.718293, "duration": 2.458482027053833, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1751354530.259811, "relative_start": 0, "end": **********.724536, "relative_end": **********.724536, "duration": 1.****************, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.724551, "relative_start": 1.****************, "end": **********.718296, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "994ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.209797, "relative_start": 1.****************, "end": **********.212179, "relative_end": **********.212179, "duration": 0.002382040023803711, "duration_str": "2.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.dashboard", "start": **********.326327, "relative_start": 2.***************, "end": **********.326327, "relative_end": **********.326327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2f44b54be1e6b3a1bd77567b74ada6e", "start": **********.333856, "relative_start": 2.***************, "end": **********.333856, "relative_end": **********.333856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::543962272ccef1b0087ff8de93e25a12", "start": **********.337787, "relative_start": 2.0779759883880615, "end": **********.337787, "relative_end": **********.337787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dbc0ef3ed3193e87849b3ea3fba36a2f", "start": **********.340698, "relative_start": 2.0808870792388916, "end": **********.340698, "relative_end": **********.340698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b528d29c191f34648130e512c6c14c42", "start": **********.346917, "relative_start": 2.0871059894561768, "end": **********.346917, "relative_end": **********.346917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d691f9b9b8ea42c9565d0eae88a8a7a", "start": **********.35284, "relative_start": 2.093029022216797, "end": **********.35284, "relative_end": **********.35284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6793bd8c17ae551c989e7d8702e297bd", "start": **********.369544, "relative_start": 2.1097331047058105, "end": **********.369544, "relative_end": **********.369544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::669f45ea592e06d462edef0dc7991631", "start": **********.371968, "relative_start": 2.112157106399536, "end": **********.371968, "relative_end": **********.371968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920886b33da20750c767a455729f43a3", "start": **********.375028, "relative_start": 2.1152169704437256, "end": **********.375028, "relative_end": **********.375028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::580d32f5fef9ea18b4f700add0d02b13", "start": **********.376625, "relative_start": 2.116814136505127, "end": **********.376625, "relative_end": **********.376625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.514435, "relative_start": 2.254624128341675, "end": **********.514435, "relative_end": **********.514435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.576356, "relative_start": 2.316545009613037, "end": **********.576356, "relative_end": **********.576356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.583503, "relative_start": 2.3236920833587646, "end": **********.583503, "relative_end": **********.583503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.585205, "relative_start": 2.3253941535949707, "end": **********.585205, "relative_end": **********.585205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.589856, "relative_start": 2.330044984817505, "end": **********.589856, "relative_end": **********.589856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.591418, "relative_start": 2.3316071033477783, "end": **********.591418, "relative_end": **********.591418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.598136, "relative_start": 2.338325023651123, "end": **********.598136, "relative_end": **********.598136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.598842, "relative_start": 2.3390309810638428, "end": **********.598842, "relative_end": **********.598842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.602084, "relative_start": 2.342272996902466, "end": **********.602084, "relative_end": **********.602084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.602386, "relative_start": 2.3425750732421875, "end": **********.602386, "relative_end": **********.602386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.604335, "relative_start": 2.3445241451263428, "end": **********.604335, "relative_end": **********.604335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.604874, "relative_start": 2.3450629711151123, "end": **********.604874, "relative_end": **********.604874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.607989, "relative_start": 2.3481781482696533, "end": **********.607989, "relative_end": **********.607989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.608577, "relative_start": 2.3487660884857178, "end": **********.608577, "relative_end": **********.608577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.690022, "relative_start": 2.430211067199707, "end": **********.690022, "relative_end": **********.690022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.692384, "relative_start": 2.432573080062866, "end": **********.692384, "relative_end": **********.692384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.702257, "relative_start": 2.442445993423462, "end": **********.702257, "relative_end": **********.702257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.704576, "relative_start": 2.444765090942383, "end": **********.704576, "relative_end": **********.704576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.706157, "relative_start": 2.4463460445404053, "end": **********.706157, "relative_end": **********.706157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.71004, "relative_start": 2.4502291679382324, "end": **********.710163, "relative_end": **********.710163, "duration": 0.00012302398681640625, "duration_str": "123μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.713839, "relative_start": 2.4540281295776367, "end": **********.713961, "relative_end": **********.713961, "duration": 0.00012183189392089844, "duration_str": "122μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50754632, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 29, "nb_templates": 29, "templates": [{"name": "filament.pages.dashboard", "param_count": null, "params": [], "start": **********.326285, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.phpfilament.pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "__components::f2f44b54be1e6b3a1bd77567b74ada6e", "param_count": null, "params": [], "start": **********.333804, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/f2f44b54be1e6b3a1bd77567b74ada6e.blade.php__components::f2f44b54be1e6b3a1bd77567b74ada6e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Ff2f44b54be1e6b3a1bd77567b74ada6e.blade.php&line=1", "ajax": false, "filename": "f2f44b54be1e6b3a1bd77567b74ada6e.blade.php", "line": "?"}}, {"name": "__components::543962272ccef1b0087ff8de93e25a12", "param_count": null, "params": [], "start": **********.337751, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/543962272ccef1b0087ff8de93e25a12.blade.php__components::543962272ccef1b0087ff8de93e25a12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F543962272ccef1b0087ff8de93e25a12.blade.php&line=1", "ajax": false, "filename": "543962272ccef1b0087ff8de93e25a12.blade.php", "line": "?"}}, {"name": "__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "param_count": null, "params": [], "start": **********.340681, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fdbc0ef3ed3193e87849b3ea3fba36a2f.blade.php&line=1", "ajax": false, "filename": "dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php", "line": "?"}}, {"name": "__components::b528d29c191f34648130e512c6c14c42", "param_count": null, "params": [], "start": **********.346884, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b528d29c191f34648130e512c6c14c42.blade.php__components::b528d29c191f34648130e512c6c14c42", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb528d29c191f34648130e512c6c14c42.blade.php&line=1", "ajax": false, "filename": "b528d29c191f34648130e512c6c14c42.blade.php", "line": "?"}}, {"name": "__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "param_count": null, "params": [], "start": **********.352806, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php__components::1d691f9b9b8ea42c9565d0eae88a8a7a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php&line=1", "ajax": false, "filename": "1d691f9b9b8ea42c9565d0eae88a8a7a.blade.php", "line": "?"}}, {"name": "__components::6793bd8c17ae551c989e7d8702e297bd", "param_count": null, "params": [], "start": **********.369526, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/6793bd8c17ae551c989e7d8702e297bd.blade.php__components::6793bd8c17ae551c989e7d8702e297bd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F6793bd8c17ae551c989e7d8702e297bd.blade.php&line=1", "ajax": false, "filename": "6793bd8c17ae551c989e7d8702e297bd.blade.php", "line": "?"}}, {"name": "__components::669f45ea592e06d462edef0dc7991631", "param_count": null, "params": [], "start": **********.371951, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/669f45ea592e06d462edef0dc7991631.blade.php__components::669f45ea592e06d462edef0dc7991631", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F669f45ea592e06d462edef0dc7991631.blade.php&line=1", "ajax": false, "filename": "669f45ea592e06d462edef0dc7991631.blade.php", "line": "?"}}, {"name": "__components::920886b33da20750c767a455729f43a3", "param_count": null, "params": [], "start": **********.375008, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/920886b33da20750c767a455729f43a3.blade.php__components::920886b33da20750c767a455729f43a3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F920886b33da20750c767a455729f43a3.blade.php&line=1", "ajax": false, "filename": "920886b33da20750c767a455729f43a3.blade.php", "line": "?"}}, {"name": "__components::580d32f5fef9ea18b4f700add0d02b13", "param_count": null, "params": [], "start": **********.37659, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/580d32f5fef9ea18b4f700add0d02b13.blade.php__components::580d32f5fef9ea18b4f700add0d02b13", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F580d32f5fef9ea18b4f700add0d02b13.blade.php&line=1", "ajax": false, "filename": "580d32f5fef9ea18b4f700add0d02b13.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.514379, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.576335, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.58347, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.585165, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.589838, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.591386, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.598104, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.598812, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.602069, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.602373, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.604304, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.604855, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.607958, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.608547, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.690005, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.692347, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.70222, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.704543, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.706142, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 59, "nb_statements": 59, "nb_visible_statements": 59, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.039670000000000004, "accumulated_duration_str": "39.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.226032, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 1.714}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.234095, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.714, "width_percent": 1.79}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.240769, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.504, "width_percent": 1.512}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.247202, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.016, "width_percent": 1.286}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.2496018, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.302, "width_percent": 1.034}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.253777, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.336, "width_percent": 1.487}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.25637, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.823, "width_percent": 2.017}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.258229, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.839, "width_percent": 1.412}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.260204, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.251, "width_percent": 1.185}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.261705, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.436, "width_percent": 1.311}, {"sql": "select * from `dashboard_widgets` where `is_available` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.281035, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:80", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=80", "ajax": false, "filename": "Dashboard.php", "line": "80"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.747, "width_percent": 4.033}, {"sql": "select * from `dashboard_configs` where `user_id` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 85}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.284865, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Dashboard.php:85", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=85", "ajax": false, "filename": "Dashboard.php", "line": "85"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.78, "width_percent": 3.63}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Pages/Concerns/CanAuthorizeAccess.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\Concerns\\CanAuthorizeAccess.php", "line": 9}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.313014, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.41, "width_percent": 1.462}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 173}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 267}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3434339, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:173", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=173", "ajax": false, "filename": "Dashboard.php", "line": "173"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.872, "width_percent": 1.487}, {"sql": "select count(*) as aggregate from `clients` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 184}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 315}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3492548, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:184", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=184", "ajax": false, "filename": "Dashboard.php", "line": "184"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.359, "width_percent": 1.966}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'pending' and `due_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["pending", "2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 196}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 363}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.354552, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=196", "ajax": false, "filename": "Dashboard.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.325, "width_percent": 1.815}, {"sql": "select * from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 210}, {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 447}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.357028, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:210", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=210", "ajax": false, "filename": "Dashboard.php", "line": "210"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.14, "width_percent": 1.613}, {"sql": "select * from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 210}, {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 522}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.359425, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:210", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=210", "ajax": false, "filename": "Dashboard.php", "line": "210"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.754, "width_percent": 1.109}, {"sql": "select * from `payments` where `status` = 'pending' and `due_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["pending", "2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 225}, {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 561}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.360783, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:225", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=225", "ajax": false, "filename": "Dashboard.php", "line": "225"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.863, "width_percent": 1.185}, {"sql": "select * from `payments` where `status` = 'pending' and `due_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["pending", "2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 225}, {"index": 16, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 639}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.362039, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:225", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=225", "ajax": false, "filename": "Dashboard.php", "line": "225"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.048, "width_percent": 1.21}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `status` = 'active'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 238}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 655}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3634062, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:238", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=238", "ajax": false, "filename": "Dashboard.php", "line": "238"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.258, "width_percent": 0.681}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `status` = 'completed'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 239}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 655}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.364328, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:239", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 239}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=239", "ajax": false, "filename": "Dashboard.php", "line": "239"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.938, "width_percent": 0.555}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `status` = 'on_hold'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", "on_hold"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 240}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 655}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.365175, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:240", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 240}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=240", "ajax": false, "filename": "Dashboard.php", "line": "240"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.493, "width_percent": 0.681}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `status` = 'cancelled'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", "cancelled"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 241}, {"index": 17, "namespace": "view", "name": "filament.pages.dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/dashboard.blade.php", "line": 655}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.366899, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Dashboard.php:241", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Dashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Dashboard.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FDashboard.php&line=241", "ajax": false, "filename": "Dashboard.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.173, "width_percent": 1.588}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.3783798, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.762, "width_percent": 1.512}, {"sql": "select * from `payments` where year(`paid_date`) = 2025 and `status` in ('completed', 'paid') and `paid_date` is not null", "type": "query", "params": [], "bindings": [2025, "completed", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.380193, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.274, "width_percent": 1.437}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.394973, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.711, "width_percent": 1.714}, {"sql": "select * from `roles` where `name` = 'bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.3995981, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.425, "width_percent": 1.714}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (15))", "type": "query", "params": [], "bindings": ["App\\Models\\User", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.40296, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.139, "width_percent": 2.193}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4, 6, 19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.405998, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.332, "width_percent": 1.588}, {"sql": "select * from `projects` where `projects`.`user_id` in (4, 6, 19) and `won_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.407896, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.92, "width_percent": 1.412}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '02'", "type": "query", "params": [], "bindings": ["paid", 2025, "02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.42143, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.332, "width_percent": 3.428}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '03'", "type": "query", "params": [], "bindings": ["paid", 2025, "03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.424103, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.76, "width_percent": 1.563}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '04'", "type": "query", "params": [], "bindings": ["paid", 2025, "04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.4264302, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.323, "width_percent": 1.563}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '05'", "type": "query", "params": [], "bindings": ["paid", 2025, "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.428651, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.886, "width_percent": 1.311}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '06'", "type": "query", "params": [], "bindings": ["paid", 2025, "06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.43073, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.197, "width_percent": 1.185}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '07'", "type": "query", "params": [], "bindings": ["paid", 2025, "07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.432279, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.382, "width_percent": 1.286}, {"sql": "select * from `projects` where `status` in ('planning', 'in_progress')", "type": "query", "params": [], "bindings": ["planning", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}], "start": **********.433747, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:252", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=252", "ajax": false, "filename": "RevenueForecastChart.php", "line": "252"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.667, "width_percent": 0.882}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '02' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["02", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.447162, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.55, "width_percent": 1.765}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '02' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["02", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.449607, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.314, "width_percent": 1.311}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '03' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["03", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.450951, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.625, "width_percent": 0.756}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '03' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["03", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.452163, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.381, "width_percent": 1.21}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '04' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["04", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.4541311, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.591, "width_percent": 1.286}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '04' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["04", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.4564111, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.877, "width_percent": 3.353}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '05' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["05", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.458909, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.229, "width_percent": 1.613}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '05' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["05", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.460565, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.843, "width_percent": 1.79}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '06' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.462785, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.632, "width_percent": 1.235}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '06' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["06", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.464387, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.868, "width_percent": 1.991}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '07' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.466735, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.859, "width_percent": 1.462}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '07' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["07", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 43}], "start": **********.469293, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.321, "width_percent": 2.017}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.54828, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.338, "width_percent": 1.21}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.579611, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.548, "width_percent": 4.663}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.5860932, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.211, "width_percent": 4.285}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.595598, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.497, "width_percent": 1.84}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.600001, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.337, "width_percent": 1.361}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.602951, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.698, "width_percent": 1.109}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.6057942, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.807, "width_percent": 1.613}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.6098208, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.42, "width_percent": 1.916}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzc6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9kYXNoYm9hcmQiO31zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkbmZYa2NFSjVXRlF0STI1aWMwalVMZTZzVXQzZkFNVEhSeXVqeHhCUmguRzdkZ0xFSWpxbFciO30=', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzc6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9kYXNoYm9hcmQiO31zOjUwOiJsb2dpbl93ZWJfM2RjN2E5MTNlZjVmZDRiODkwZWNhYmUzNDg3MDg1NTczZTE2Y2Y4MiI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkbmZYa2NFSjVXRlF0STI1aWMwalVMZTZzVXQzZkFNVEhSeXVqeHhCUmguRzdkZ0xFSWpxbFciO30=", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.711613, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.336, "width_percent": 1.664}]}, "models": {"data": {"App\\Models\\DashboardWidget": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardWidget.php&line=1", "ajax": false, "filename": "DashboardWidget.php", "line": "?"}}, "App\\Models\\DashboardConfig": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FDashboardConfig.php&line=1", "ajax": false, "filename": "DashboardConfig.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 52, "is_counter": true}, "livewire": {"data": {"app.filament.pages.dashboard #iUMJJea0N4mcOFIiLcP7": "array:4 [\n  \"data\" => array:16 [\n    \"widgets\" => array:17 [\n      0 => array:7 [\n        \"id\" => 1\n        \"name\" => \"Total Projects\"\n        \"component\" => \"TotalProjectsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 1\n        \"settings\" => null\n      ]\n      1 => array:7 [\n        \"id\" => 2\n        \"name\" => \"Total Clients\"\n        \"component\" => \"TotalClientsWidget\"\n        \"width\" => 3\n        \"height\" => 1\n        \"position\" => 2\n        \"settings\" => null\n      ]\n      2 => array:7 [\n        \"id\" => 3\n        \"name\" => \"Pending Payments\"\n        \"component\" => \"PendingPaymentsWidget\"\n        \"width\" => 6\n        \"height\" => 1\n        \"position\" => 3\n        \"settings\" => null\n      ]\n      3 => array:7 [\n        \"id\" => 5\n        \"name\" => \"Recent Projects\"\n        \"component\" => \"RecentProjectsWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 5\n        \"settings\" => null\n      ]\n      4 => array:7 [\n        \"id\" => 6\n        \"name\" => \"Upcoming Payments\"\n        \"component\" => \"UpcomingPaymentsWidget\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 6\n        \"settings\" => null\n      ]\n      5 => array:7 [\n        \"id\" => 7\n        \"name\" => \"Project Status\"\n        \"component\" => \"ProjectStatusWidget\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 7\n        \"settings\" => null\n      ]\n      6 => array:7 [\n        \"id\" => 8\n        \"name\" => \"Monthly Revenue\"\n        \"component\" => \"MonthlyRevenueWidget\"\n        \"width\" => 12\n        \"height\" => 1\n        \"position\" => 8\n        \"settings\" => null\n      ]\n      7 => array:7 [\n        \"id\" => 9\n        \"name\" => \"Business Stats Overview\"\n        \"component\" => \"BusinessStatsOverview\"\n        \"width\" => 12\n        \"height\" => 2\n        \"position\" => 9\n        \"settings\" => null\n      ]\n      8 => array:7 [\n        \"id\" => 10\n        \"name\" => \"Revenue Performance Chart\"\n        \"component\" => \"RevenuePerformanceChart\"\n        \"width\" => 12\n        \"height\" => 3\n        \"position\" => 10\n        \"settings\" => null\n      ]\n      9 => array:7 [\n        \"id\" => 11\n        \"name\" => \"Project Status Distribution\"\n        \"component\" => \"ProjectStatusChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 11\n        \"settings\" => null\n      ]\n      10 => array:7 [\n        \"id\" => 12\n        \"name\" => \"BDE Performance Leaderboard\"\n        \"component\" => \"BdePerformanceChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 12\n        \"settings\" => null\n      ]\n      11 => array:7 [\n        \"id\" => 13\n        \"name\" => \"Top Clients by Revenue\"\n        \"component\" => \"ClientRevenueChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 13\n        \"settings\" => null\n      ]\n      12 => array:7 [\n        \"id\" => 14\n        \"name\" => \"Payment Status Overview\"\n        \"component\" => \"PaymentStatusChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 14\n        \"settings\" => null\n      ]\n      13 => array:7 [\n        \"id\" => 15\n        \"name\" => \"Project Timeline & Milestones\"\n        \"component\" => \"ProjectTimelineChart\"\n        \"width\" => 12\n        \"height\" => 4\n        \"position\" => 15\n        \"settings\" => null\n      ]\n      14 => array:7 [\n        \"id\" => 16\n        \"name\" => \"Revenue Forecast & Trends\"\n        \"component\" => \"RevenueForecastChart\"\n        \"width\" => 12\n        \"height\" => 4\n        \"position\" => 16\n        \"settings\" => null\n      ]\n      15 => array:7 [\n        \"id\" => 17\n        \"name\" => \"Service Performance Analysis\"\n        \"component\" => \"ProjectTypePerformanceChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 17\n        \"settings\" => null\n      ]\n      16 => array:7 [\n        \"id\" => 18\n        \"name\" => \"Milestone Due vs Received\"\n        \"component\" => \"MilestoneDueVsReceivedChart\"\n        \"width\" => 6\n        \"height\" => 3\n        \"position\" => 18\n        \"settings\" => null\n      ]\n    ]\n    \"dateFilter\" => \"month\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\Dashboard\"\n  \"id\" => \"iUMJJea0N4mcOFIiLcP7\"\n]", "app.filament.widgets.monthly-revenue-chart #1aedX8D73IunutJzVi40": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"current_year\"\n    \"dataChecksum\" => \"a369fb06786dd190c5cf8d6381ab8edb\"\n  ]\n  \"name\" => \"app.filament.widgets.monthly-revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MonthlyRevenueChart\"\n  \"id\" => \"1aedX8D73IunutJzVi40\"\n]", "app.filament.widgets.bde-performance-chart #I8fKN8DmQjNhT4ShoB40": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"this_month\"\n    \"dataChecksum\" => \"705f9f6a260c944b564b927a292dad89\"\n  ]\n  \"name\" => \"app.filament.widgets.bde-performance-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\BdePerformanceChart\"\n  \"id\" => \"I8fKN8DmQjNhT4ShoB40\"\n]", "app.filament.widgets.revenue-forecast-chart #65MqBiIWsiYn2iDqjaLo": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"6_months\"\n    \"dataChecksum\" => \"57140bad3c40b1517f411fe8421f272c\"\n  ]\n  \"name\" => \"app.filament.widgets.revenue-forecast-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueForecastChart\"\n  \"id\" => \"65MqBiIWsiYn2iDqjaLo\"\n]", "app.filament.widgets.milestone-due-vs-received-chart #iQdYkIUb3Whaj3zK2D4q": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"last_6_months\"\n    \"dataChecksum\" => \"7d4b19abd5f34afd8ee78597507c8c12\"\n  ]\n  \"name\" => \"app.filament.widgets.milestone-due-vs-received-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MilestoneDueVsReceivedChart\"\n  \"id\" => \"iQdYkIUb3Whaj3zK2D4q\"\n]", "filament.livewire.global-search #CgEhgGuWJ2Vn9MfqfCk4": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"CgEhgGuWJ2Vn9MfqfCk4\"\n]", "app.filament.widgets.notification-components.notification-bell #hM5X36ZKGviPkJdGd4Mo": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 177\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"hM5X36ZKGviPkJdGd4Mo\"\n]", "filament.livewire.notifications #ju5IFE9nA2azZ06Xn0sM": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2897\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"ju5IFE9nA2azZ06Xn0sM\"\n]"}, "count": 8}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 25, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128799464 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128799464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.518807, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2063718224 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063718224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51942, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-35796729 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35796729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.521533, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1748172840 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748172840\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.524128, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1999089815 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999089815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.525431, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-896703592 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896703592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527705, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1855818099 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855818099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529036, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1077087920 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077087920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530691, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2015235918 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015235918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.531571, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1251147624 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251147624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532533, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1762813172 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762813172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.533848, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-301946295 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301946295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535291, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1459480222 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459480222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536056, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1977442714 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977442714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537191, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-284971643 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284971643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539229, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-659672723 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659672723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540355, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1528541085 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528541085\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541379, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309545903 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309545903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542176, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-906266892 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906266892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543384, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-924625523 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924625523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544063, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1694273885 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694273885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544691, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-72298470 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72298470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.546253, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-655690127 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655690127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551546, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1965215857 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965215857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553228, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-202598281 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202598281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566391, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard", "uri": "GET admin/dashboard", "controller": "App\\Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.47s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im8ybjdtSktxVktYaXlFOG9nVnBpTXc9PSIsInZhbHVlIjoiQ2hlTVhXS0cwWnkyUFpnMUpVTWt3MzBMMFZSWnFpeEZ6OVRZSU5nRHI3b2RjTWtJVXVvMytEODJaWTdhZUdYWWpsbisxSVBmVENpV1FzQ043d3RlSW5tWTF6VDNlOFFyMVo2b2ozcnQxLzA5UjNyMG5PT3JwSHlHN1cxZndXRVIiLCJtYWMiOiJkODY3M2EzNWM4YWYzZDNlMDJiMWFiYjE3YTZiMGQ3ODg4MTUwZTA3YTEyZDg0MWRjODEzOGI5OTI5YzZmZmQ2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IlhKTmVzMXBhazFNMk5GY2g3SzBzSVE9PSIsInZhbHVlIjoiZ1NGNWFBTHo3TkpGWGNuSjVLS09mYjI5K2VIS0o1QlpSQ1NRNjRmR1U3RENGNVk5NjZ3MUg4Nk1rYVZra1BmWnI2cThaUUh5bjgxMVpxVmJKMEpRbEN1LzZlNi9TY1JBWTQ4cWVzTld4cG1iNXNLWHJTTzA4N20vZkRTWmF4UCsiLCJtYWMiOiIxNjE4MTI5ZjE3ZmUxZmYxMGRjZTdkZTFiMDU0ZTliOTg5ODAzMjQ0NGM5MThjNzc1ZDg3NTNiOWRlYTNmOTQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1327587472 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327587472\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1850404607 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 07:22:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZaYmZFbmFtMU5YY0tiUVpoMjc0b1E9PSIsInZhbHVlIjoiNkhaVnpRVmVtemc4bDV3NkJqTmxrL2RVa1liMlpNTGhEZGVGT1pWbWQ5WGxiYncxWENkYU9JcXREQUx1bEdBUXB3bmpuOEQ1K29yOWRMMXZNaXZWVXd1ZkRoS0tkaWZDSzZheWdtTEZKQjZVU0RJenB0djlVZDBaemc4aFRGcDEiLCJtYWMiOiIyZjIwNDdjNjllODFlZTczMGE3N2U2YTBlMjYzYzNiNTcyNTVlZGQ5ZmVhYjU0Mzg4NTdkYjgyMDgyMGNjYjU3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:22:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6InFjMDArQ1EwNVZpbGJHOTJzeVVPQmc9PSIsInZhbHVlIjoiODhpRXNRMCt3ZEdiNGljd1dXeXN0N3dXczlDVmdwME40V2o5ZjBLN1hyYzRNbWc5RmgrczExdjNhb2F2dWplbEZMN3p5YzUzd1ZXQzgrUi91Z0MwWndFSlFtR2xhZEZzL2ZDY0ROWlg3S1psdkZwRThGelJJZVk3WEZxU0JRUVYiLCJtYWMiOiI1MGZhZjQ0MmIyYmVjZjJiZWJmNjUzNmNiODIwMTIzZGNmYTA2ZDBjZjUzYThkMmZmZDY0ZDkwMDk3YjM3ZGM1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:22:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZaYmZFbmFtMU5YY0tiUVpoMjc0b1E9PSIsInZhbHVlIjoiNkhaVnpRVmVtemc4bDV3NkJqTmxrL2RVa1liMlpNTGhEZGVGT1pWbWQ5WGxiYncxWENkYU9JcXREQUx1bEdBUXB3bmpuOEQ1K29yOWRMMXZNaXZWVXd1ZkRoS0tkaWZDSzZheWdtTEZKQjZVU0RJenB0djlVZDBaemc4aFRGcDEiLCJtYWMiOiIyZjIwNDdjNjllODFlZTczMGE3N2U2YTBlMjYzYzNiNTcyNTVlZGQ5ZmVhYjU0Mzg4NTdkYjgyMDgyMGNjYjU3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:22:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6InFjMDArQ1EwNVZpbGJHOTJzeVVPQmc9PSIsInZhbHVlIjoiODhpRXNRMCt3ZEdiNGljd1dXeXN0N3dXczlDVmdwME40V2o5ZjBLN1hyYzRNbWc5RmgrczExdjNhb2F2dWplbEZMN3p5YzUzd1ZXQzgrUi91Z0MwWndFSlFtR2xhZEZzL2ZDY0ROWlg3S1psdkZwRThGelJJZVk3WEZxU0JRUVYiLCJtYWMiOiI1MGZhZjQ0MmIyYmVjZjJiZWJmNjUzNmNiODIwMTIzZGNmYTA2ZDBjZjUzYThkMmZmZDY0ZDkwMDk3YjM3ZGM1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:22:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850404607\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-435789638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435789638\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/dashboard", "action_name": "filament.admin.pages.dashboard", "controller_action": "App\\Filament\\Pages\\Dashboard"}, "badge": null}}