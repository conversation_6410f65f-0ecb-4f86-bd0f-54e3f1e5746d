{"__meta": {"id": "01JZ2P527DMM1DG3ZYABMMS301", "datetime": "2025-07-01 09:47:38", "utime": **********.60574, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.105521, "end": **********.605756, "duration": 1.5002350807189941, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": **********.105521, "relative_start": 0, "end": **********.530816, "relative_end": **********.530816, "duration": 0.****************, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.530824, "relative_start": 0.*****************, "end": **********.605758, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.895688, "relative_start": 0.****************, "end": **********.898516, "relative_end": **********.898516, "duration": 0.002827882766723633, "duration_str": "2.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.16786, "relative_start": 1.****************, "end": **********.16786, "relative_end": **********.16786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.175625, "relative_start": 1.****************, "end": **********.175625, "relative_end": **********.175625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.178697, "relative_start": 1.0731761455535889, "end": **********.178697, "relative_end": **********.178697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.182004, "relative_start": 1.0764830112457275, "end": **********.182004, "relative_end": **********.182004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.208087, "relative_start": 1.1025660037994385, "end": **********.208087, "relative_end": **********.208087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.212249, "relative_start": 1.1067280769348145, "end": **********.212249, "relative_end": **********.212249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.21728, "relative_start": 1.1117589473724365, "end": **********.21728, "relative_end": **********.21728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.229049, "relative_start": 1.123528003692627, "end": **********.229049, "relative_end": **********.229049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.233451, "relative_start": 1.127929925918579, "end": **********.233451, "relative_end": **********.233451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.238267, "relative_start": 1.1327459812164307, "end": **********.238267, "relative_end": **********.238267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.246788, "relative_start": 1.1412670612335205, "end": **********.246788, "relative_end": **********.246788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.249984, "relative_start": 1.144463062286377, "end": **********.249984, "relative_end": **********.249984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.254992, "relative_start": 1.1494710445404053, "end": **********.254992, "relative_end": **********.254992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.263577, "relative_start": 1.1580560207366943, "end": **********.263577, "relative_end": **********.263577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.266141, "relative_start": 1.1606199741363525, "end": **********.266141, "relative_end": **********.266141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.272036, "relative_start": 1.1665151119232178, "end": **********.272036, "relative_end": **********.272036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.280011, "relative_start": 1.174489974975586, "end": **********.280011, "relative_end": **********.280011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.282209, "relative_start": 1.1766879558563232, "end": **********.282209, "relative_end": **********.282209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.289361, "relative_start": 1.183840036392212, "end": **********.289361, "relative_end": **********.289361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.299759, "relative_start": 1.1942379474639893, "end": **********.299759, "relative_end": **********.299759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.304824, "relative_start": 1.199303150177002, "end": **********.304824, "relative_end": **********.304824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.310148, "relative_start": 1.2046270370483398, "end": **********.310148, "relative_end": **********.310148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.321849, "relative_start": 1.2163281440734863, "end": **********.321849, "relative_end": **********.321849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.324854, "relative_start": 1.2193329334259033, "end": **********.324854, "relative_end": **********.324854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.330276, "relative_start": 1.224755048751831, "end": **********.330276, "relative_end": **********.330276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.346344, "relative_start": 1.2408230304718018, "end": **********.346344, "relative_end": **********.346344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.351765, "relative_start": 1.246243953704834, "end": **********.351765, "relative_end": **********.351765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.357834, "relative_start": 1.2523131370544434, "end": **********.357834, "relative_end": **********.357834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.369901, "relative_start": 1.2643799781799316, "end": **********.369901, "relative_end": **********.369901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.373533, "relative_start": 1.2680120468139648, "end": **********.373533, "relative_end": **********.373533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.379874, "relative_start": 1.27435302734375, "end": **********.379874, "relative_end": **********.379874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.396193, "relative_start": 1.2906720638275146, "end": **********.396193, "relative_end": **********.396193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.400832, "relative_start": 1.2953109741210938, "end": **********.400832, "relative_end": **********.400832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.406107, "relative_start": 1.3005859851837158, "end": **********.406107, "relative_end": **********.406107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.415465, "relative_start": 1.3099441528320312, "end": **********.415465, "relative_end": **********.415465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.420425, "relative_start": 1.314903974533081, "end": **********.420425, "relative_end": **********.420425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.425637, "relative_start": 1.3201160430908203, "end": **********.425637, "relative_end": **********.425637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.437291, "relative_start": 1.3317699432373047, "end": **********.437291, "relative_end": **********.437291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.440688, "relative_start": 1.3351669311523438, "end": **********.440688, "relative_end": **********.440688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.44579, "relative_start": 1.3402690887451172, "end": **********.44579, "relative_end": **********.44579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.457893, "relative_start": 1.3523719310760498, "end": **********.457893, "relative_end": **********.457893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.460857, "relative_start": 1.3553359508514404, "end": **********.460857, "relative_end": **********.460857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.466024, "relative_start": 1.3605029582977295, "end": **********.466024, "relative_end": **********.466024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.47702, "relative_start": 1.3714990615844727, "end": **********.47702, "relative_end": **********.47702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.483731, "relative_start": 1.3782100677490234, "end": **********.483731, "relative_end": **********.483731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.490646, "relative_start": 1.385124921798706, "end": **********.490646, "relative_end": **********.490646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.502252, "relative_start": 1.3967311382293701, "end": **********.502252, "relative_end": **********.502252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.505135, "relative_start": 1.3996140956878662, "end": **********.505135, "relative_end": **********.505135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.509682, "relative_start": 1.404160976409912, "end": **********.509682, "relative_end": **********.509682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.522638, "relative_start": 1.4171171188354492, "end": **********.522638, "relative_end": **********.522638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.526605, "relative_start": 1.421083927154541, "end": **********.526605, "relative_end": **********.526605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.531879, "relative_start": 1.4263579845428467, "end": **********.531879, "relative_end": **********.531879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.548425, "relative_start": 1.442903995513916, "end": **********.548425, "relative_end": **********.548425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.557092, "relative_start": 1.451570987701416, "end": **********.557092, "relative_end": **********.557092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.562011, "relative_start": 1.4564900398254395, "end": **********.562011, "relative_end": **********.562011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.570632, "relative_start": 1.4651110172271729, "end": **********.570632, "relative_end": **********.570632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.576876, "relative_start": 1.4713549613952637, "end": **********.576876, "relative_end": **********.576876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.589916, "relative_start": 1.4843950271606445, "end": **********.589916, "relative_end": **********.589916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.600533, "relative_start": 1.4950120449066162, "end": **********.60331, "relative_end": **********.60331, "duration": 0.002777099609375, "duration_str": "2.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56635656, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 58, "nb_templates": 58, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.16778, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.178679, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.181985, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.208068, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.217262, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.570612, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 49, "nb_statements": 49, "nb_visible_statements": 49, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.031920000000000004, "accumulated_duration_str": "31.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.907477, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.162}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.9244149, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.162, "width_percent": 2.256}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.929639, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.417, "width_percent": 1.942}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.933805, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.36, "width_percent": 1.88}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.936419, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.239, "width_percent": 1.817}, {"sql": "select * from `roles` where `roles`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.948132, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.056, "width_percent": 11.028}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.964311, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.084, "width_percent": 6.203}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.0363681, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.287, "width_percent": 9.242}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'bde_team' and `roles`.`id` <> '15'", "type": "query", "params": [], "bindings": ["bde_team", "15"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.084225, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.529, "width_percent": 6.297}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/RefreshesPermissionCache.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\RefreshesPermissionCache.php", "line": 12}], "start": **********.092308, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.826, "width_percent": 1.754}, {"sql": "select * from `permissions` where (`name` = 'view_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.093617, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.58, "width_percent": 2.224}, {"sql": "select * from `permissions` where (`name` = 'view_any_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.095046, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.805, "width_percent": 1.128}, {"sql": "select * from `permissions` where (`name` = 'view_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.096028, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.932, "width_percent": 1.034}, {"sql": "select * from `permissions` where (`name` = 'view_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.096903, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.966, "width_percent": 1.003}, {"sql": "select * from `permissions` where (`name` = 'create_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.097842, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.969, "width_percent": 1.096}, {"sql": "select * from `permissions` where (`name` = 'update_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.098845, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.065, "width_percent": 1.096}, {"sql": "select * from `permissions` where (`name` = 'delete_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.100198, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.162, "width_percent": 1.786}, {"sql": "select * from `permissions` where (`name` = 'view_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.101795, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.947, "width_percent": 1.754}, {"sql": "select * from `permissions` where (`name` = 'view_any_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.103305, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.702, "width_percent": 1.66}, {"sql": "select * from `permissions` where (`name` = 'view_incentive::rule' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_incentive::rule", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.104493, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.362, "width_percent": 1.159}, {"sql": "select * from `permissions` where (`name` = 'view_any_incentive::rule' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_incentive::rule", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.105462, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.521, "width_percent": 1.065}, {"sql": "select * from `permissions` where (`name` = 'view_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.106651, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.586, "width_percent": 0.971}, {"sql": "select * from `permissions` where (`name` = 'view_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.107715, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.558, "width_percent": 0.815}, {"sql": "select * from `permissions` where (`name` = 'create_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.108524, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.372, "width_percent": 1.065}, {"sql": "select * from `permissions` where (`name` = 'update_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.109408, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.437, "width_percent": 1.159}, {"sql": "select * from `permissions` where (`name` = 'delete_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.110354, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.596, "width_percent": 0.971}, {"sql": "select * from `permissions` where (`name` = 'view_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.111468, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.568, "width_percent": 1.128}, {"sql": "select * from `permissions` where (`name` = 'view_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1124399, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.695, "width_percent": 1.128}, {"sql": "select * from `permissions` where (`name` = 'create_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.113694, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.823, "width_percent": 1.535}, {"sql": "select * from `permissions` where (`name` = 'update_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.115014, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.358, "width_percent": 1.19}, {"sql": "select * from `permissions` where (`name` = 'delete_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.116455, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.549, "width_percent": 1.629}, {"sql": "select * from `permissions` where (`name` = 'view_pricing::model' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_pricing::model", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.11792, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.178, "width_percent": 1.88}, {"sql": "select * from `permissions` where (`name` = 'view_any_pricing::model' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_pricing::model", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.119393, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.058, "width_percent": 1.504}, {"sql": "select * from `permissions` where (`name` = 'view_product' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_product", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.120529, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.561, "width_percent": 1.003}, {"sql": "select * from `permissions` where (`name` = 'view_any_product' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_product", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1214042, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.564, "width_percent": 0.909}, {"sql": "select * from `permissions` where (`name` = 'view_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.122186, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.472, "width_percent": 0.783}, {"sql": "select * from `permissions` where (`name` = 'view_any_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.122929, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.256, "width_percent": 1.003}, {"sql": "select * from `permissions` where (`name` = 'create_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.123794, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.258, "width_percent": 1.034}, {"sql": "select * from `permissions` where (`name` = 'update_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1246722, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.292, "width_percent": 0.971}, {"sql": "select * from `permissions` where (`name` = 'delete_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.12554, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.263, "width_percent": 1.065}, {"sql": "select * from `permissions` where (`name` = 'view_project::status::log' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project::status::log", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.12667, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.328, "width_percent": 0.815}, {"sql": "select * from `permissions` where (`name` = 'view_any_project::status::log' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project::status::log", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1276512, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.143, "width_percent": 0.877}, {"sql": "select * from `permissions` where (`name` = 'view_project::type' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project::type", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.128719, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.02, "width_percent": 1.535}, {"sql": "select * from `permissions` where (`name` = 'view_any_project::type' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project::type", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1297681, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.555, "width_percent": 1.034}, {"sql": "select * from `permissions` where (`name` = 'page_BdeDashboard' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_BdeDashboard", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.13069, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.589, "width_percent": 1.003}, {"sql": "select * from `permissions` where (`name` = 'page_MyProfilePage' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_MyProfilePage", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.131521, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.591, "width_percent": 0.94}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 15", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.132458, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:453", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=453", "ajax": false, "filename": "HasPermissions.php", "line": "453"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.531, "width_percent": 7.644}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 15), (2, 15), (13, 15), (14, 15), (15, 15), (16, 15), (21, 15), (25, 15), (26, 15), (37, 15), (38, 15), (49, 15), (50, 15), (51, 15), (52, 15), (57, 15), (85, 15), (86, 15), (87, 15), (88, 15), (93, 15), (218, 15), (230, 15), (226, 15), (231, 15), (97, 15), (98, 15), (99, 15), (100, 15), (105, 15), (222, 15), (232, 15), (109, 15), (110, 15), (163, 15), (167, 15)", "type": "query", "params": [], "bindings": [1, 15, 2, 15, 13, 15, 14, 15, 15, 15, 16, 15, 21, 15, 25, 15, 26, 15, 37, 15, 38, 15, 49, 15, 50, 15, 51, 15, 52, 15, 57, 15, 85, 15, 86, 15, 87, 15, 88, 15, 93, 15, 218, 15, 230, 15, 226, 15, 231, 15, 97, 15, 98, 15, 99, 15, 100, 15, 105, 15, 222, 15, 232, 15, 109, 15, 110, 15, 163, 15, 167, 15], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}], "start": **********.137195, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:406", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=406", "ajax": false, "filename": "HasPermissions.php", "line": "406"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.175, "width_percent": 3.791}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 554}], "start": **********.139039, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.966, "width_percent": 1.034}]}, "models": {"data": {"App\\Models\\Permission": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 39, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #gcNWqKGmv3egTTB2d8H6": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => Illuminate\\Support\\Collection {#3692\n      #items: array:36 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"view_client\"\n        3 => \"view_any_client\"\n        4 => \"create_client\"\n        5 => \"update_client\"\n        6 => \"delete_client\"\n        7 => \"view_incentive\"\n        8 => \"view_any_incentive\"\n        9 => \"view_incentive::rule\"\n        10 => \"view_any_incentive::rule\"\n        11 => \"view_milestone\"\n        12 => \"view_any_milestone\"\n        13 => \"create_milestone\"\n        14 => \"update_milestone\"\n        15 => \"delete_milestone\"\n        16 => \"view_payment\"\n        17 => \"view_any_payment\"\n        18 => \"create_payment\"\n        19 => \"update_payment\"\n        20 => \"delete_payment\"\n        21 => \"view_pricing::model\"\n        22 => \"view_any_pricing::model\"\n        23 => \"view_product\"\n        24 => \"view_any_product\"\n        25 => \"view_project\"\n        26 => \"view_any_project\"\n        27 => \"create_project\"\n        28 => \"update_project\"\n        29 => \"delete_project\"\n        30 => \"view_project::status::log\"\n        31 => \"view_any_project::status::log\"\n        32 => \"view_project::type\"\n        33 => \"view_any_project::type\"\n        34 => \"page_BdeDashboard\"\n        35 => \"page_MyProfilePage\"\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"data\" => array:26 [\n      \"id\" => 15\n      \"name\" => \"bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:2 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n      ]\n      \"client\" => array:5 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n      ]\n      \"incentive\" => array:2 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n      ]\n      \"incentive::rule\" => array:2 [\n        0 => \"view_incentive::rule\"\n        1 => \"view_any_incentive::rule\"\n      ]\n      \"milestone\" => array:5 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n      ]\n      \"notification::event\" => []\n      \"notification::role::preference\" => []\n      \"payment\" => array:5 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n      ]\n      \"pricing::model\" => array:2 [\n        0 => \"view_pricing::model\"\n        1 => \"view_any_pricing::model\"\n      ]\n      \"product\" => array:2 [\n        0 => \"view_product\"\n        1 => \"view_any_product\"\n      ]\n      \"project\" => array:5 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n      ]\n      \"project::status::log\" => array:2 [\n        0 => \"view_project::status::log\"\n        1 => \"view_any_project::status::log\"\n      ]\n      \"project::type\" => array:2 [\n        0 => \"view_project::type\"\n        1 => \"view_any_project::type\"\n      ]\n      \"role\" => []\n      \"role::notification::settings\" => []\n      \"user\" => []\n      \"pages_tab\" => array:2 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => []\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#3896\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #original: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"gcNWqKGmv3egTTB2d8H6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1362751198 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362751198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971849, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-718924432 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718924432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046728, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1288720027 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288720027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581905, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-956819063 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956819063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.582332, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@save<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Resources/Pages/EditRecord.php:134-177</a>", "middleware": "web", "duration": "1.5s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-902212575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-902212575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-128679142 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"2668 characters\">{&quot;data&quot;:{&quot;permissions&quot;:null,&quot;data&quot;:[{&quot;id&quot;:15,&quot;name&quot;:&quot;bde_team&quot;,&quot;guard_name&quot;:&quot;web&quot;,&quot;created_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;select_all&quot;:false,&quot;app::notification&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[[&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive&quot;:[[&quot;view_incentive&quot;,&quot;view_any_incentive&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive::rule&quot;:[[&quot;view_incentive::rule&quot;,&quot;view_any_incentive::rule&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;milestone&quot;:[[&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::event&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::role::preference&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;payment&quot;:[[&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;pricing::model&quot;:[[&quot;view_pricing::model&quot;,&quot;view_any_pricing::model&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;product&quot;:[[&quot;view_product&quot;,&quot;view_any_product&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project&quot;:[[&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::status::log&quot;:[[&quot;view_project::status::log&quot;,&quot;view_any_project::status::log&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::type&quot;:[[&quot;view_project::type&quot;,&quot;view_any_project::type&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;role&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;role::notification::settings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;user&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;pages_tab&quot;:[[&quot;page_BdeDashboard&quot;,&quot;page_DashboardSettings&quot;,&quot;page_ManageSetting&quot;,&quot;page_Themes&quot;,&quot;page_MyProfilePage&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;widgets_tab&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;custom_permissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;team_id&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/localhost:8000\\/admin\\/shield\\/roles&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Role&quot;,&quot;key&quot;:15,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;gcNWqKGmv3egTTB2d8H6&quot;,&quot;name&quot;:&quot;app.filament.resources.role-resource.pages.edit-role&quot;,&quot;path&quot;:&quot;admin\\/shield\\/roles\\/15\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;17363ae0257cf124a273d63db16d48bc82474ad87ee366fd85e73e4ba090ca5a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.pages_tab.1</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_MyProfilePage</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128679142\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-54637521 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3297</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklKNDk5dWhQa09FRjlUT3plYlhxOWc9PSIsInZhbHVlIjoid3gvQk1yVTdHM0JnRUI1b2JzMmVybGRZN0tTdVFTdXR4V3FwaUNJcjhyMVpiTldPY2szVVgzSDJ3T1c2SnJkVDNPSFVHV0JGNkZuTittYlZIeUl1eGdyZkVUKzExOVBTQlRCeWdjTndKRW5FejZuSHJ2eFRhZDZ6TnN2TUErZ0EiLCJtYWMiOiIyMmNkZTRhNDIzMDgxMmZhN2RmMDY0NjhjYWQxNTM1NmYwMjUzYjhjZWY5OTlmMTY3YmViODdhYzFlMjZjN2U2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IjRYeWZJQjFQTHlOQVZYMndTNjZLVHc9PSIsInZhbHVlIjoiYmw1SlFlaVhBOVI0UHFuZWVoWERMNkpveTkzQi9EcTZtQ3hscUtKdjgzRm82bXRXNndWRTBnZFM2bjJGcWpCd3hyTW1lOCtGQlRreEVhYnBmTXRvYXVKL29SbmdnU2RqN01OVXN1Zmx4T1BXeEp4ZksvcHlkRWptcmszam5GMysiLCJtYWMiOiIxYTliNTFiMjBmMjFiMzE0NzMyODhiYWYwMzQ3OTBjMzczOGNjOTAxNTE3OWU0OGNmZTE3Mjk2OGU2ZjdkOTRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54637521\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1226309015 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226309015\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1983536564 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:47:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983536564\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-105065170 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f491a7c-caef-476c-8126-7db2c92f2042</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105065170\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}