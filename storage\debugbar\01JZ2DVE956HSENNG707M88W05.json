{"__meta": {"id": "01JZ2DVE956HSENNG707M88W05", "datetime": "2025-07-01 07:22:34", "utime": **********.663075, "method": "GET", "uri": "/admin/home", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[07:22:34] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/home\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.341479, "xdebug_link": null, "collector": "log"}, {"message": "[07:22:34] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 4,\n    \"roles\": [\n        \"bde_team\"\n    ],\n    \"current_path\": \"admin\\/home\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.653431, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751354552.19217, "end": **********.6631, "duration": 2.4709300994873047, "duration_str": "2.47s", "measures": [{"label": "Booting", "start": 1751354552.19217, "relative_start": 0, "end": **********.412402, "relative_end": **********.412402, "duration": 1.****************, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.412432, "relative_start": 1.***************, "end": **********.663103, "relative_end": 3.0994415283203125e-06, "duration": 1.***************, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.290683, "relative_start": 2.***************, "end": **********.293609, "relative_end": **********.293609, "duration": 0.002925872802734375, "duration_str": "2.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.pages.bde-dashboard", "start": **********.348212, "relative_start": 2.****************, "end": **********.348212, "relative_end": **********.348212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dbc0ef3ed3193e87849b3ea3fba36a2f", "start": **********.358602, "relative_start": 2.****************, "end": **********.358602, "relative_end": **********.358602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b528d29c191f34648130e512c6c14c42", "start": **********.364063, "relative_start": 2.1718931198120117, "end": **********.364063, "relative_end": **********.364063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e6e211aedcebaa57390bdc37f34b88dc", "start": **********.36928, "relative_start": 2.177110195159912, "end": **********.36928, "relative_end": **********.36928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.390888, "relative_start": 2.1987180709838867, "end": **********.390888, "relative_end": **********.390888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.53089, "relative_start": 2.3387200832366943, "end": **********.53089, "relative_end": **********.53089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.537496, "relative_start": 2.3453261852264404, "end": **********.537496, "relative_end": **********.537496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.541009, "relative_start": 2.348839044570923, "end": **********.541009, "relative_end": **********.541009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.546427, "relative_start": 2.354257106781006, "end": **********.546427, "relative_end": **********.546427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.547433, "relative_start": 2.3552629947662354, "end": **********.547433, "relative_end": **********.547433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.552841, "relative_start": 2.360671043395996, "end": **********.552841, "relative_end": **********.552841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.553579, "relative_start": 2.3614091873168945, "end": **********.553579, "relative_end": **********.553579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.558813, "relative_start": 2.366643190383911, "end": **********.558813, "relative_end": **********.558813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.559124, "relative_start": 2.3669540882110596, "end": **********.559124, "relative_end": **********.559124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.562507, "relative_start": 2.3703370094299316, "end": **********.562507, "relative_end": **********.562507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.563138, "relative_start": 2.3709681034088135, "end": **********.563138, "relative_end": **********.563138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.565438, "relative_start": 2.3732681274414062, "end": **********.565438, "relative_end": **********.565438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.565706, "relative_start": 2.3735361099243164, "end": **********.565706, "relative_end": **********.565706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.638586, "relative_start": 2.446416139602661, "end": **********.638586, "relative_end": **********.638586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.639647, "relative_start": 2.447477102279663, "end": **********.639647, "relative_end": **********.639647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.645719, "relative_start": 2.4535491466522217, "end": **********.645719, "relative_end": **********.645719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.646616, "relative_start": 2.4544460773468018, "end": **********.646616, "relative_end": **********.646616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.647427, "relative_start": 2.4552571773529053, "end": **********.647427, "relative_end": **********.647427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.652485, "relative_start": 2.460314989089966, "end": **********.652751, "relative_end": **********.652751, "duration": 0.00026607513427734375, "duration_str": "266μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.6574, "relative_start": 2.4652299880981445, "end": **********.659222, "relative_end": **********.659222, "duration": 0.0018219947814941406, "duration_str": "1.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50868880, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "filament.pages.bde-dashboard", "param_count": null, "params": [], "start": **********.348184, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.phpfilament.pages.bde-dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fpages%2Fbde-dashboard.blade.php&line=1", "ajax": false, "filename": "bde-dashboard.blade.php", "line": "?"}}, {"name": "__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "param_count": null, "params": [], "start": **********.358585, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php__components::dbc0ef3ed3193e87849b3ea3fba36a2f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fdbc0ef3ed3193e87849b3ea3fba36a2f.blade.php&line=1", "ajax": false, "filename": "dbc0ef3ed3193e87849b3ea3fba36a2f.blade.php", "line": "?"}}, {"name": "__components::b528d29c191f34648130e512c6c14c42", "param_count": null, "params": [], "start": **********.364031, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b528d29c191f34648130e512c6c14c42.blade.php__components::b528d29c191f34648130e512c6c14c42", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb528d29c191f34648130e512c6c14c42.blade.php&line=1", "ajax": false, "filename": "b528d29c191f34648130e512c6c14c42.blade.php", "line": "?"}}, {"name": "__components::e6e211aedcebaa57390bdc37f34b88dc", "param_count": null, "params": [], "start": **********.369249, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/e6e211aedcebaa57390bdc37f34b88dc.blade.php__components::e6e211aedcebaa57390bdc37f34b88dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fe6e211aedcebaa57390bdc37f34b88dc.blade.php&line=1", "ajax": false, "filename": "e6e211aedcebaa57390bdc37f34b88dc.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.390865, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.530874, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.537444, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.540978, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.5464, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.547419, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.552799, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.553543, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.558796, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.559109, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.562477, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.563109, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.565424, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.565692, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.638545, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.639621, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.645704, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.646603, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.647414, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020919999999999998, "accumulated_duration_str": "20.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f' limit 1", "type": "query", "params": [], "bindings": ["mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.301281, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.25}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.30639, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.25, "width_percent": 3.489}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.313332, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.74, "width_percent": 3.872}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.32258, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.612, "width_percent": 3.107}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.324404, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.719, "width_percent": 1.816}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.329314, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.535, "width_percent": 2.39}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.331367, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.925, "width_percent": 1.625}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.332672, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.551, "width_percent": 2.247}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.335205, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.797, "width_percent": 2.581}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.3377888, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.379, "width_percent": 2.82}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 307}, {"index": 24, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 123}, {"index": 25, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 19}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.353158, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.199, "width_percent": 3.824}, {"sql": "select count(*) as aggregate from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `user_id` = 4", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 137}, {"index": 17, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 86}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.360498, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BdeDashboard.php:137", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FBdeDashboard.php&line=137", "ajax": false, "filename": "BdeDashboard.php", "line": "137"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.023, "width_percent": 3.155}, {"sql": "select count(*) as aggregate from `clients` where exists (select * from `projects` where `clients`.`id` = `projects`.`client_id` and `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `user_id` = 4)", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 150}, {"index": 17, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 133}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.366836, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BdeDashboard.php:150", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FBdeDashboard.php&line=150", "ajax": false, "filename": "BdeDashboard.php", "line": "150"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.178, "width_percent": 3.824}, {"sql": "select sum(`amount`) as aggregate from `incentives` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `user_id` = 4", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 178}, {"index": 17, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.371008, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BdeDashboard.php:178", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 178}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FBdeDashboard.php&line=178", "ajax": false, "filename": "BdeDashboard.php", "line": "178"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.002, "width_percent": 3.442}, {"sql": "select * from `projects` where `created_at` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `user_id` = 4 order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 195}, {"index": 16, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 203}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.3730829, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BdeDashboard.php:195", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 195}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FBdeDashboard.php&line=195", "ajax": false, "filename": "BdeDashboard.php", "line": "195"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.444, "width_percent": 3.776}, {"sql": "select * from `milestones` where `due_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59' and `status` != 'completed' and exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = 4) order by `due_date` asc limit 5", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59", "completed", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 215}, {"index": 16, "namespace": "view", "name": "filament.pages.bde-dashboard", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/pages/bde-dashboard.blade.php", "line": 256}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.376122, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BdeDashboard.php:215", "source": {"index": 15, "namespace": null, "name": "app/Filament/Pages/BdeDashboard.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\BdeDashboard.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FBdeDashboard.php&line=215", "ajax": false, "filename": "BdeDashboard.php", "line": "215"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.22, "width_percent": 2.868}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.394664, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.088, "width_percent": 3.155}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.402515, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.243, "width_percent": 3.728}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 4 and `read_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.532181, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.971, "width_percent": 11.998}, {"sql": "select * from `app_notifications` where `user_id` = 4 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.542628, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.969, "width_percent": 5.641}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.550387, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.61, "width_percent": 3.537}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.554936, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.147, "width_percent": 12.572}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.560188, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.719, "width_percent": 3.155}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.564043, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.874, "width_percent": 2.486}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.566306, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.359, "width_percent": 2.199}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiN2hKV0k0N3RDWVdTQWtUYVJmeVh4UnRSNVc0U01pRWs1b05iWWNJQyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzI6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9ob21lIjt9czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6NDtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG8uazNTQkQxUXR6NTZoSzJVL3NDUy5LOUg3OHpxR3QzUDd5RDBhZjFqT2p5SkdTa2Q5TkxXIjt9', `last_activity` = **********, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiN2hKV0k0N3RDWVdTQWtUYVJmeVh4UnRSNVc0U01pRWs1b05iWWNJQyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzI6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9ob21lIjt9czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6NDtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG8uazNTQkQxUXR6NTZoSzJVL3NDUy5LOUg3OHpxR3QzUDd5RDBhZjFqT2p5SkdTa2Q5TkxXIjt9", **********, 4, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.655274, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.558, "width_percent": 3.442}]}, "models": {"data": {"App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": {"app.filament.pages.bde-dashboard #TKdGOs296VxBfzjYUWqP": "array:4 [\n  \"data\" => array:16 [\n    \"dateFilter\" => \"month\"\n    \"bdeFilter\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.bde-dashboard\"\n  \"component\" => \"App\\Filament\\Pages\\BdeDashboard\"\n  \"id\" => \"TKdGOs296VxBfzjYUWqP\"\n]", "filament.livewire.global-search #84SQspSFtGPRpKLFgryt": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"84SQspSFtGPRpKLFgryt\"\n]", "app.filament.widgets.notification-components.notification-bell #SkrYXdcBv8oJYzaMCXtK": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 17\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"SkrYXdcBv8oJYzaMCXtK\"\n]", "filament.livewire.notifications #5VLj2RVdfKdGvpGAgHD0": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2953\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"5VLj2RVdfKdGvpGAgHD0\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 43, "messages": [{"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081975599 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081975599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.406047, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1347475440 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347475440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.407701, "xdebug_link": null}, {"message": "[\n  ability => view_any_app::notification,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1726010802 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">view_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726010802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412359, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1135844250 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135844250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.41261, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1680316059 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680316059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413397, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009905552 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009905552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415674, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1067635608 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067635608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415832, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1369311823 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369311823\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417848, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-5447830 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5447830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420785, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2050711936 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050711936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.421115, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive::rule,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1050891653 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive::rule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_incentive::rule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050891653\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424181, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1634945851 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634945851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424346, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-134358987 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134358987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425249, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-203820553 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203820553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.427944, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1761230759 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761230759\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.4283, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::event,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1771558910 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">view_any_notification::event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771558910\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.430582, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741348753 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741348753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.430744, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::role::preference,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1777207684 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::role::preference </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"39 characters\">view_any_notification::role::preference</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777207684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.432795, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2052258323 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052258323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.432943, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-932295801 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932295801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433643, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-818429082 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818429082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437966, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1973461401 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973461401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.438421, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-405706269 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405706269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.441762, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1458872745 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458872745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.441919, "xdebug_link": null}, {"message": "[\n  ability => view_any_product,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1756583075 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756583075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448552, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-169156373 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169156373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448903, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-932185534 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932185534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.450551, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-780895687 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780895687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.453896, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-267766224 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267766224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.454163, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::status::log,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-315357100 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::status::log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">view_any_project::status::log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315357100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.457685, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-55734994 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55734994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.45823, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::type,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1767581595 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::type </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_project::type</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767581595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.465202, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1655805815 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655805815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.465725, "xdebug_link": null}, {"message": "[\n  ability => view_any_role::notification::settings,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1043201803 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role::notification::settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view_any_role::notification::settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043201803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.473859, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1754469290 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754469290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.474056, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-560647941 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560647941\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.477274, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1771565829 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771565829\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.477629, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1171093736 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171093736\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.482106, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-941198133 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941198133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.482435, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819979267 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819979267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.490952, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 4,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1275663897 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275663897\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.491527, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-686621016 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686621016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.512688, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1577725760 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577725760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.513163, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/home", "action_name": "filament.admin.pages.home", "controller_action": "App\\Filament\\Pages\\BdeDashboard", "uri": "GET admin/home", "controller": "App\\Filament\\Pages\\BdeDashboard@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.48s", "peak_memory": "62MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ii9PRmk3ejlQTzRHTStZOFlpQkRZUkE9PSIsInZhbHVlIjoibGd3UVVZM1UwTzJMcFdIaDdzTkJwZTduMHZlK3V2c2FpbVNRMkczeCtxM1dtRDVFcXFmMUhhN2gyL1kvQVcybGhiTmRaWlR0TlpBU0lqZjNBSjR6cUJLRStNM25BRzNQSGFQMDRpaFZZQzY5VUxQSUxEOFZoeldIa29aS2lZZEV3UlFPWlA2NmhJMTE1dTBVVld5RXFxeGQ5bmJFQldKNkRQRDU4UDJYM1p5YVRzWHNnWXdLUjZFbHZxNVhybHNvZFJBaXUwSE4rVXhPNFB3ckx1RVgrRUp3aFdpeStSS3FvbC9IK3VYWS82RT0iLCJtYWMiOiJiZjAwZGE5NjhmYjJmNTVhYjgyNTNhOWM3YTJjNmI4MmNmODMwMGU2ZWI0NTJhZjA0NGNiNmYzMzVhMzI0ZjBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlkwQ0hoU09mNWl6SlVmeHlVRDNmTVE9PSIsInZhbHVlIjoiK1dueUNlZ3BhOVJQZXI5dWs0cDlFQUJKdWFnUEs4Q1A1ZTN0STRUNnhlWmg0QVN0cGNNcGtRNTlRMlZPR2FTeFNmNk5nWTBKSUU0czhXQnczL2k4RlBnMlpQN3JJY2Vhd29qRFkzNEFaTkJEaTNzOFNOcjM5NjJic1JKQTZFOEciLCJtYWMiOiJkMWQ4OGM3NWI0MTcxMjU1ZDZiNzQxNDQ3M2RhOGZhYzM3Zjc4YmJkODllODQ0ODZkYjJmNzAzNTQ3MDc5ZWMwIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImNBcEpvOUtPVGQzVVhtY1E1UklsV3c9PSIsInZhbHVlIjoiNW96R1JYWVNGcjRZY0pnMjhOTEN6b3I1SkpNbEpWSUMyN3VoZDZKUHlCRmhLOWg0KzVNSFU0WXVEaDdGTlVrQTZGV21sVEFwOUpEem80cmNyeW9SeU5TWGxPaVB4d2xXcjREQmdXRG1rWmZVYW1mOTAvbkd5NDdOeGk3RXNmNHgiLCJtYWMiOiI2MDE1Y2EwMjQzN2Y5MDIxNDBkYjc5ZjhhYTNhOTNkNmU4OWQzMDY1MmRiN2UyMmI2NTUzMTgwMDhiNjRhOTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-999299600 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|3V9UEaUGqSwxxRR9V5ZDPTi63jHRtaJZ5kgsZ0VbL3DXQjB9OGul8EU2LisB|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7hJWI47tCYWSAkTaRfyXxRtR5W4SMiEk5oNbYcIC</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999299600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 07:22:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IndLcUJEcytNUHJFN1lCVnp5cjRlQVE9PSIsInZhbHVlIjoicVNmWDhNQml2K2JmalV6SjBhUDFkMlZsNGhIVlU0Sml0SXJ3VjUrRW02UUNqNmNHQnoxQ3RGRXBqTnZyblBWYWpMOWovSEE3ekxRZ1N4cHM5TDNtZmdodDRNYjliQ3FSWkoxa1JNZW1zYitGUGU4VGlDb3o4UlNwZ0xQdk9icS8iLCJtYWMiOiJmMzk1OGM2NmJjNDdjZjMyZmIzNWZjOWNmMWNhM2JmMjE5OTRlNzgwYmIxNzAzY2Y3YTVjZWE1Y2M3M2ViZTAwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:22:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6ImRBOC9OejJTOVlVaDRyQTZpcGVRcUE9PSIsInZhbHVlIjoiZ25ieDVXMklXek9CNEFLeDFVQWZlOUVSR1I1ajFhbXBEYVUzb2h1WlVmZGtKMEFxd3J4RFg4U3g1VEZOKzl3a1Exanl6eENWVzdZNDlrZjBET1UwaXE5d1RCd0wzYXVZQkRySytSd1hKbTJRNnR5UVFobk1ROXlKckJwNlFsVGgiLCJtYWMiOiI4Y2Y2NGY1YWEyMWU2ODU2ODFhZTk4MjQ3ZmExMmRiOTc4MTk4MGNjMjk4MWFiNDVjYjdjN2E0NWFiZGVlZDgyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 09:22:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IndLcUJEcytNUHJFN1lCVnp5cjRlQVE9PSIsInZhbHVlIjoicVNmWDhNQml2K2JmalV6SjBhUDFkMlZsNGhIVlU0Sml0SXJ3VjUrRW02UUNqNmNHQnoxQ3RGRXBqTnZyblBWYWpMOWovSEE3ekxRZ1N4cHM5TDNtZmdodDRNYjliQ3FSWkoxa1JNZW1zYitGUGU4VGlDb3o4UlNwZ0xQdk9icS8iLCJtYWMiOiJmMzk1OGM2NmJjNDdjZjMyZmIzNWZjOWNmMWNhM2JmMjE5OTRlNzgwYmIxNzAzY2Y3YTVjZWE1Y2M3M2ViZTAwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:22:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6ImRBOC9OejJTOVlVaDRyQTZpcGVRcUE9PSIsInZhbHVlIjoiZ25ieDVXMklXek9CNEFLeDFVQWZlOUVSR1I1ajFhbXBEYVUzb2h1WlVmZGtKMEFxd3J4RFg4U3g1VEZOKzl3a1Exanl6eENWVzdZNDlrZjBET1UwaXE5d1RCd0wzYXVZQkRySytSd1hKbTJRNnR5UVFobk1ROXlKckJwNlFsVGgiLCJtYWMiOiI4Y2Y2NGY1YWEyMWU2ODU2ODFhZTk4MjQ3ZmExMmRiOTc4MTk4MGNjMjk4MWFiNDVjYjdjN2E0NWFiZGVlZDgyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 09:22:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2036845392 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7hJWI47tCYWSAkTaRfyXxRtR5W4SMiEk5oNbYcIC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036845392\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/home", "action_name": "filament.admin.pages.home", "controller_action": "App\\Filament\\Pages\\BdeDashboard"}, "badge": null}}