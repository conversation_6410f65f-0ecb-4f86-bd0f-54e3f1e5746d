{"__meta": {"id": "01JZ4F08Z1BAK5DB3JPCBSK6JH", "datetime": "2025-07-02 02:21:10", "utime": **********.49787, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[02:21:07] LOG.error: Undefined variable $get {\n    \"userId\": 19,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.760876, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751422866.738823, "end": **********.497893, "duration": 3.7590701580047607, "duration_str": "3.76s", "measures": [{"label": "Booting", "start": 1751422866.738823, "relative_start": 0, "end": **********.234537, "relative_end": **********.234537, "duration": 0.****************, "duration_str": "496ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.234549, "relative_start": 0.****************, "end": **********.497896, "relative_end": 2.86102294921875e-06, "duration": 3.****************, "duration_str": "3.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.622568, "relative_start": 0.****************, "end": **********.625008, "relative_end": **********.625008, "duration": 0.002440214157104492, "duration_str": "2.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.05031, "relative_start": 1.****************, "end": **********.05031, "relative_end": **********.05031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.052445, "relative_start": 1.***************, "end": **********.052445, "relative_end": **********.052445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.053167, "relative_start": 1.3143441677093506, "end": **********.053167, "relative_end": **********.053167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.764393, "relative_start": 2.0255701541900635, "end": **********.764393, "relative_end": **********.764393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.765245, "relative_start": 2.0264220237731934, "end": **********.765245, "relative_end": **********.765245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.765816, "relative_start": 2.0269930362701416, "end": **********.765816, "relative_end": **********.765816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.766133, "relative_start": 2.0273101329803467, "end": **********.766133, "relative_end": **********.766133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.766531, "relative_start": 2.027708053588867, "end": **********.766531, "relative_end": **********.766531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.767179, "relative_start": 2.0283560752868652, "end": **********.767179, "relative_end": **********.767179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.767963, "relative_start": 2.029139995574951, "end": **********.767963, "relative_end": **********.767963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.76956, "relative_start": 2.0307371616363525, "end": **********.76956, "relative_end": **********.76956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.379753, "relative_start": 2.64093017578125, "end": **********.379753, "relative_end": **********.379753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.990983, "relative_start": 3.25216007232666, "end": **********.990983, "relative_end": **********.990983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.994849, "relative_start": 3.256026029586792, "end": **********.994849, "relative_end": **********.994849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.997413, "relative_start": 3.25858998298645, "end": **********.997413, "relative_end": **********.997413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.997621, "relative_start": 3.258798122406006, "end": **********.997621, "relative_end": **********.997621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.030015, "relative_start": 3.291192054748535, "end": **********.030015, "relative_end": **********.030015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.087551, "relative_start": 3.3487281799316406, "end": **********.087551, "relative_end": **********.087551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.088177, "relative_start": 3.3493540287017822, "end": **********.088177, "relative_end": **********.088177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.474893, "relative_start": 3.736070156097412, "end": **********.474893, "relative_end": **********.474893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.475669, "relative_start": 3.7368459701538086, "end": **********.475669, "relative_end": **********.475669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.476031, "relative_start": 3.737208127975464, "end": **********.476031, "relative_end": **********.476031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 56785192, "peak_usage_str": "54MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "ErrorException", "message": "Undefined variable $get", "code": 0, "file": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "line": 282, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1520060017 data-indent-pad=\"  \"><span class=sf-dump-note>array:75</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>256</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">handleError</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Undefined variable $get</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"101 characters\">D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>282</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>282</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Bootstrap\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Undefined variable $get</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"101 characters\">D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>282</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/support/src/Concerns/EvaluatesClosures.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">App\\Filament\\Resources\\ClientResource\\RelationManagers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"78 characters\">App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">[object Filament\\Forms\\Set]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/forms/src/Components/Concerns/HasState.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>111</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">evaluate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Filament\\Support\\Components\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/forms/src/Components/Concerns/HasState.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">callAfterStateUpdatedHook</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Filament\\Forms\\Components\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">vendor/filament/forms/src/Concerns/HasState.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">callAfterStateUpdated</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Filament\\Forms\\Components\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">vendor/filament/forms/src/Concerns/HasState.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">callAfterStateUpdated</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Filament\\Forms\\ComponentContainer</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/filament/forms/src/Concerns/InteractsWithForms.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>310</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">callAfterStateUpdated</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Filament\\Forms\\ComponentContainer</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">updatedInteractsWithForms</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Filament\\Resources\\RelationManagers\\RelationManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Container/Util.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>96</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unwrapIfClosure</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Container\\Util</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">callBoundMethod</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a><samp data-depth=5 id=sf-dump-1520060017-ref23959 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">__id</span>: \"<span class=sf-dump-str title=\"20 characters\">1NqHdTFDIHtzfFjaVG35</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">__name</span>: \"<span class=sf-dump-str title=\"82 characters\">app.filament.resources.client-resource.relation-managers.projects-relation-manager</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeCollection</span></span> {<a class=sf-dump-ref>#3763</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2435</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2434</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2436</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2438</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2439</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n              </samp>}\n              <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2440</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n              </samp>}\n              <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2441</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22437 title=\"7 occurrences\">#2437</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n              </samp>}\n              <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Locked\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Locked</span></span> {<a class=sf-dump-ref>#2442</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"11 characters\">ownerRecord</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"11 characters\">ownerRecord</span>\"\n              </samp>}\n              <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Locked\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Locked</span></span> {<a class=sf-dump-ref>#2343</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"9 characters\">pageClass</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"9 characters\">pageClass</span>\"\n              </samp>}\n              <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2342</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>10</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2340</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"15 characters\">actionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>11</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2339</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"18 characters\">defaultTableAction</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"18 characters\">defaultTableAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"11 characters\">tableAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>12</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2338</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"27 characters\">defaultTableActionArguments</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"27 characters\">defaultTableActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"20 characters\">tableActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>13</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2337</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"24 characters\">defaultTableActionRecord</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22341 title=\"7 occurrences\">#2341</a> &#8230;1}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"24 characters\">defaultTableActionRecord</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"17 characters\">tableActionRecord</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">rulesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messagesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">validationAttributesFromOutside</span>: []\n          +<span class=sf-dump-public title=\"Public property\">ownerRecord</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Client\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Client</span></span> {<a class=sf-dump-ref>#3935</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">clients</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>26</span>\n              \"<span class=sf-dump-key>company_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>contact_person</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:10:48</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:10:48</span>\"\n              \"<span class=sf-dump-key>company_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\"> Global FinServe LLP</span>\"\n              \"<span class=sf-dump-key>tax_id</span>\" => \"<span class=sf-dump-str title=\"14 characters\">07GFPL9988B2Z3</span>\"\n              \"<span class=sf-dump-key>registered_address</span>\" => \"<span class=sf-dump-str title=\"30 characters\">45, Connaught Place, New Delhi</span>\"\n              \"<span class=sf-dump-key>official_email</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>company_number</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+91-9654321987</span>\"\n              \"<span class=sf-dump-key>personnel_details</span>\" => \"<span class=sf-dump-str title=\"213 characters\">[{&quot;name&quot;: &quot;Sneha Sharma&quot;, &quot;skype&quot;: null, &quot;department&quot;: &quot;Finance&quot;, &quot;designation&quot;: &quot;Finance Head&quot;, &quot;mobile_number&quot;: &quot;8899771122&quot;, &quot;official_email&quot;: &quot;<EMAIL>&quot;, &quot;whatsapp_number&quot;: &quot;8899771122&quot;}]</span>\"\n              \"<span class=sf-dump-key>social_media_access</span>\" => \"<span class=sf-dump-str title=\"495 characters\">[{&quot;password&quot;: null, &quot;platform&quot;: &quot;instagram&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;youtube&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;twitter&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;website_gsa&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;website_ga&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;facebook&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;gmb&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;linkedin&quot;, &quot;username&quot;: null}]</span>\"\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>19</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>26</span>\n              \"<span class=sf-dump-key>company_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>contact_person</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:10:48</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:10:48</span>\"\n              \"<span class=sf-dump-key>company_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\"> Global FinServe LLP</span>\"\n              \"<span class=sf-dump-key>tax_id</span>\" => \"<span class=sf-dump-str title=\"14 characters\">07GFPL9988B2Z3</span>\"\n              \"<span class=sf-dump-key>registered_address</span>\" => \"<span class=sf-dump-str title=\"30 characters\">45, Connaught Place, New Delhi</span>\"\n              \"<span class=sf-dump-key>official_email</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>company_number</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+91-9654321987</span>\"\n              \"<span class=sf-dump-key>personnel_details</span>\" => \"<span class=sf-dump-str title=\"213 characters\">[{&quot;name&quot;: &quot;Sneha Sharma&quot;, &quot;skype&quot;: null, &quot;department&quot;: &quot;Finance&quot;, &quot;designation&quot;: &quot;Finance Head&quot;, &quot;mobile_number&quot;: &quot;8899771122&quot;, &quot;official_email&quot;: &quot;<EMAIL>&quot;, &quot;whatsapp_number&quot;: &quot;8899771122&quot;}]</span>\"\n              \"<span class=sf-dump-key>social_media_access</span>\" => \"<span class=sf-dump-str title=\"495 characters\">[{&quot;password&quot;: null, &quot;platform&quot;: &quot;instagram&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;youtube&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;twitter&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;website_gsa&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;website_ga&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;facebook&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;gmb&quot;, &quot;username&quot;: null}, {&quot;password&quot;: null, &quot;platform&quot;: &quot;linkedin&quot;, &quot;username&quot;: null}]</span>\"\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>19</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n              \"<span class=sf-dump-key>personnel_details</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              \"<span class=sf-dump-key>social_media_access</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">company_name</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">company_email</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">contact_person</span>\"\n              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">tax_id</span>\"\n              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">registered_address</span>\"\n              <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"14 characters\">official_email</span>\"\n              <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">company_number</span>\"\n              <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">personnel_details</span>\"\n              <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"19 characters\">social_media_access</span>\"\n              <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">pageClass</span>: \"<span class=sf-dump-str title=\"54 characters\">App\\Filament\\Resources\\ClientResource\\Pages\\EditClient</span>\"\n          +<span class=sf-dump-public title=\"Public property\">mountedActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">defaultAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultActionArguments</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">hasActionsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">placeholderHeight</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">componentFileAttachments</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">cachedForms</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>mountedTableActionForm</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref>#2313</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"35 characters\">filament-forms::component-container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              +<span class=sf-dump-public title=\"Public property\">model</span>: \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">parentComponent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-num>2</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">components</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Hidden\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Hidden</span></span> {<a class=sf-dump-ref>#2376</a> &#8230;70}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Grid\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Grid</span></span> {<a class=sf-dump-ref>#2307</a> &#8230;59}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Select\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Select</span></span> {<a class=sf-dump-ref>#2277</a> &#8230;128}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Grid\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Grid</span></span> {<a class=sf-dump-ref>#3591</a> &#8230;59}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Grid\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Grid</span></span> {<a class=sf-dump-ref>#3629</a> &#8230;59}\n                <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Grid\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Grid</span></span> {<a class=sf-dump-ref>#3687</a> &#8230;59}\n                <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Textarea\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Textarea</span></span> {<a class=sf-dump-ref>#3700</a> &#8230;81}\n                <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Hidden\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Hidden</span></span> {<a class=sf-dump-ref>#3701</a> &#8230;70}\n                <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Hidden\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Hidden</span></span> {<a class=sf-dump-ref>#3702</a> &#8230;70}\n                <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Hidden\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Hidden</span></span> {<a class=sf-dump-ref>#3703</a> &#8230;70}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabels</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">operation</span>: \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"25 characters\">mountedTableActionsData.0</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: \"<span class=sf-dump-str title=\"25 characters\">mountedTableActionsData.0</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>toggleTableColumnForm</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref>#3699</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"35 characters\">filament-forms::component-container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              +<span class=sf-dump-public title=\"Public property\">model</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">parentComponent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">components</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Checkbox\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Checkbox</span></span> {<a class=sf-dump-ref>#3704</a> &#8230;72}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabels</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">operation</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"19 characters\">toggledTableColumns</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n              #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>tableFiltersForm</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref>#3706</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"35 characters\">filament-forms::component-container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              +<span class=sf-dump-public title=\"Public property\">model</span>: \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">parentComponent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">components</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Group\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Group</span></span> {<a class=sf-dump-ref>#3707</a> &#8230;59}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Group\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Group</span></span> {<a class=sf-dump-ref>#3727</a> &#8230;59}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Group\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Group</span></span> {<a class=sf-dump-ref>#3747</a> &#8230;59}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabels</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">operation</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"12 characters\">tableFilters</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n              #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">hasCachedForms</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">isCachingForms</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">currentlyValidatingForm</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasFormsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">oldFormState</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>26</span>\n                \"<span class=sf-dump-key>project_type_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n                \"<span class=sf-dump-key>won_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-25</span>\"\n                \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str>4</span>\"\n                \"<span class=sf-dump-key>duration_unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">months</span>\"\n                \"<span class=sf-dump-key>payment_cycle</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n                \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-01</span>\"\n                \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-10-31</span>\"\n                \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>bde_display</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Falak khan</span>\"\n                \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n                \"<span class=sf-dump-key>total_payment</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2500.00</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>_generated_milestones</span>\" => \"<span class=sf-dump-str title=\"653 characters\">[{&quot;title&quot;:&quot;Month 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 4&quot;,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 2 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 2 of 4&quot;,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 3 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 3 of 4&quot;,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 4 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 4 of 4&quot;,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;}]</span>\"\n                \"<span class=sf-dump-key>_generated_payments</span>\" => \"<span class=sf-dump-str title=\"233 characters\">[{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;status&quot;:&quot;pending&quot;}]</span>\"\n                \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"18 characters\">School Management1</span>\"\n              </samp>]\n            </samp>]\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsComponents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">hasInfolistsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedInfolists</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsComponent</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsInfolist</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">activeTab</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedTabs</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a><samp data-depth=6 id=sf-dump-1520060017-ref22326 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"22 characters\">filament-tables::index</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">table</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n            #<span class=sf-dump-protected title=\"Protected property\">isStriped</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isLoadingDeferred</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">defaultGroup</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">cachedGroups</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?array</span>\n            #<span class=sf-dump-protected title=\"Protected property\">groups</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">isGroupsOnly</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">areGroupingSettingsInDropdownOnDesktop</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">areGroupingSettingsHidden</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isGroupingDirectionSettingHidden</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modifyGroupRecordsTriggerActionUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">defaultPaginationPageOption</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isPaginated</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isPaginatedWhileReordering</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">paginationPageOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hasExtremePaginationLinks</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">pollingInterval</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isReorderable</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isReorderAuthorized</span>: <span class=sf-dump-note>Closure(): bool</span> {<a class=sf-dump-ref>#2331</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>returnType</span>: \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n78 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Concerns\\InteractsWithRelationshipTable.php\n101 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\filament\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Resources\\Concerns\\InteractsWithRelationshipTable.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">109 to 109</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">reorderColumn</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modifyReorderRecordsTriggerActionUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">reorderAnimationDuration</span>: <span class=sf-dump-num>300</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isSearchable</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">persistsSearchInSession</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">persistsColumnSearchesInSession</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">searchPlaceholder</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">searchDebounce</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isSearchOnBlur</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">defaultSort</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">defaultSortDirection</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">persistsSortInSession</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">defaultSortOptionLabel</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hasSummary</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">columnToggleFormColumns</span>: <span class=sf-dump-num>1</span>\n            #<span class=sf-dump-protected title=\"Protected property\">columnToggleFormMaxHeight</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">columnToggleFormWidth</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modifyToggleColumnsTriggerActionUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">actions</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22330 title=\"2 occurrences\">#2330</a><samp data-depth=8 id=sf-dump-1520060017-ref22330 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"29 characters\">filament-actions::link-action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"15 characters\">view_milestones</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Support\\Enums\\ActionSize\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Support\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ActionSize</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22382 title=\"3 occurrences\">#2382</a> &#8230;2}\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: \"<span class=sf-dump-str title=\"4 characters\">info</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"14 characters\">heroicon-o-eye</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2319</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2322</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-note>Closure($record)</span> {<a class=sf-dump-ref>#2427</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-note>Closure($record)</span> {<a class=sf-dump-ref>#2329</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: \"<span class=sf-dump-str title=\"3 characters\">7xl</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">canAccessSelectedRecords</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectRecordsAfterCompletion</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldFetchSelectedRecords</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\EditAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EditAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22419 title=\"2 occurrences\">#2419</a><samp data-depth=8 id=sf-dump-1520060017-ref22419 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"29 characters\">filament-actions::link-action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-note>Closure(RelationManager $livewire, Model $record): bool</span> {<a class=sf-dump-ref>#2421</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-note>Closure(): void</span> {<a class=sf-dump-ref>#2403</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"4 characters\">edit</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Support\\Enums\\ActionSize\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Support\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ActionSize</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22382 title=\"3 occurrences\">#2382</a> &#8230;2}\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"24 characters\">heroicon-m-pencil-square</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-note>Closure(?Form $form)</span> {<a class=sf-dump-ref>#2404</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2417</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2416</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: \"<span class=sf-dump-str title=\"12 characters\">Save changes</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-note>Closure(): string</span> {<a class=sf-dump-ref>#2411</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-note>Closure(Form $form): Form</span> {<a class=sf-dump-ref>#2422</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-note>Closure(array $data, RelationManager $livewire): array</span> {<a class=sf-dump-ref>#2420</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-note>Closure(array $data, $record, RelationManager $livewire)</span> {<a class=sf-dump-ref>#2402</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">canAccessSelectedRecords</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectRecordsAfterCompletion</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldFetchSelectedRecords</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateRecordDataUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">using</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\DeleteAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DeleteAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22397 title=\"2 occurrences\">#2397</a><samp data-depth=8 id=sf-dump-1520060017-ref22397 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"29 characters\">filament-actions::link-action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-note>Closure(RelationManager $livewire, Model $record): bool</span> {<a class=sf-dump-ref>#2400</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-note>Closure(Model $record): bool</span> {<a class=sf-dump-ref>#2387</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-note>Closure(): void</span> {<a class=sf-dump-ref>#2383</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Support\\Enums\\ActionSize\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Support\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ActionSize</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22382 title=\"3 occurrences\">#2382</a> &#8230;2}\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: \"<span class=sf-dump-str title=\"6 characters\">danger</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"16 characters\">heroicon-m-trash</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2401</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2396</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: \"<span class=sf-dump-str title=\"7 characters\">Deleted</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-note>Closure(MountableAction $action): Alignment</span> {<a class=sf-dump-ref>#2389</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-note>Closure(MountableAction $action): Alignment</span> {<a class=sf-dump-ref>#2388</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-note>Closure(MountableAction $action): ?string</span> {<a class=sf-dump-ref>#2385</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-note>Closure(): string</span> {<a class=sf-dump-ref>#2395</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-note>Closure(MountableAction $action): ?string</span> {<a class=sf-dump-ref>#2386</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-note>Closure(MountableAction $action): MaxWidth</span> {<a class=sf-dump-ref>#2384</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: \"<span class=sf-dump-str title=\"16 characters\">heroicon-o-trash</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">canAccessSelectedRecords</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectRecordsAfterCompletion</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldFetchSelectedRecords</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">using</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">flatActions</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\CreateAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CreateAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22391 title=\"2 occurrences\">#2391</a><samp data-depth=8 id=sf-dump-1520060017-ref22391 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"31 characters\">filament-actions::button-action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-note>Closure(RelationManager $livewire): bool</span> {<a class=sf-dump-ref>#2393</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-note>Closure(array $arguments, Form $form, HasTable $livewire): void</span> {<a class=sf-dump-ref>#2315</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"14 characters\">Create Project</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2390</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2368</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: \"<span class=sf-dump-str title=\"7 characters\">Created</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: <span class=sf-dump-note>Closure(): array</span> {<a class=sf-dump-ref>#2364</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: \"<span class=sf-dump-str title=\"6 characters\">Create</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-note>Closure(): string</span> {<a class=sf-dump-ref>#2365</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-note>Closure(Form $form): Form</span> {<a class=sf-dump-ref>#2399</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-note>Closure(array $data, RelationManager $livewire): array</span> {<a class=sf-dump-ref>#2367</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-note>Closure(array $data, $record, RelationManager $livewire)</span> {<a class=sf-dump-ref>#2392</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">canAccessSelectedRecords</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectRecordsAfterCompletion</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldFetchSelectedRecords</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canCreateAnother</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">using</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              \"<span class=sf-dump-key>view_milestones</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22330 title=\"2 occurrences\">#2330</a>}\n              \"<span class=sf-dump-key>edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\EditAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EditAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22419 title=\"2 occurrences\">#2419</a>}\n              \"<span class=sf-dump-key>delete</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\DeleteAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DeleteAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22397 title=\"2 occurrences\">#2397</a>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">actionsColumnLabel</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">actionsAlignment</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">actionsPosition</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">bulkActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\BulkActionGroup\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">BulkActionGroup</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22312 title=\"2 occurrences\">#2312</a><samp data-depth=8 id=sf-dump-1520060017-ref22312 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">group</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"30 characters\">filament-actions::button-group</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">group</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">actions</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                #<span class=sf-dump-protected title=\"Protected property\">flatActions</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: \"<span class=sf-dump-str title=\"2 characters\">sm</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDropdown</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dropdownPlacement</span>: \"<span class=sf-dump-str title=\"12 characters\">bottom-start</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">dropdownMaxHeight</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dropdownOffset</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dropdownWidth</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"12 characters\">Bulk actions</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: \"<span class=sf-dump-str title=\"4 characters\">gray</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"28 characters\">heroicon-m-ellipsis-vertical</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">flatBulkActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>delete</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\DeleteBulkAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DeleteBulkAction</span></span> {<a class=sf-dump-ref>#2375</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"31 characters\">filament-actions::button-action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\BulkActionGroup\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">BulkActionGroup</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22312 title=\"2 occurrences\">#2312</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-note>Closure(RelationManager $livewire): bool</span> {<a class=sf-dump-ref>#2377</a> &#8230;4}\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-note>Closure(HasTable $livewire): bool</span> {<a class=sf-dump-ref>#2316</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-note>Closure(): void</span> {<a class=sf-dump-ref>#2370</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"15 characters\">Delete selected</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: \"<span class=sf-dump-str title=\"6 characters\">danger</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"16 characters\">heroicon-m-trash</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2378</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2374</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: \"<span class=sf-dump-str title=\"7 characters\">Deleted</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-note>Closure(MountableAction $action): Alignment</span> {<a class=sf-dump-ref>#2372</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-note>Closure(MountableAction $action): Alignment</span> {<a class=sf-dump-ref>#2371</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-note>Closure(MountableAction $action): ?string</span> {<a class=sf-dump-ref>#2366</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-note>Closure(): string</span> {<a class=sf-dump-ref>#2373</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-note>Closure(MountableAction $action): ?string</span> {<a class=sf-dump-ref>#2369</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-note>Closure(MountableAction $action): MaxWidth</span> {<a class=sf-dump-ref>#2363</a> &#8230;6}\n                #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: \"<span class=sf-dump-str title=\"16 characters\">heroicon-o-trash</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Filament\\Tables\\Table</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectRecordsAfterCompletion</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldFetchSelectedRecords</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">records</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">using</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">checkIfRecordIsSelectableUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">selectsCurrentPageOnly</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">recordCheckboxPosition</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">isSelectable</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>title</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22335 title=\"2 occurrences\">#2335</a><samp data-depth=8 id=sf-dump-1520060017-ref22335 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"36 characters\">filament-tables::columns.text-column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">columnToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToCount</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToExistenceCheck</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">layout</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isClickDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hiddenFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">visibleFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isInline</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isGloballySearchable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isIndividuallySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchForcedCaseInsensitive</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSortable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">summarizers</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isToggleable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isToggledHiddenByDefault</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">isHeaderWrapped</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraCellAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">extraHeaderAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">loop</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">width</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">getStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">separator</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDistinctList</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">inverseRelationshipName</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">verticalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canWrap</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBadge</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBulleted</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isListWithLineBreaks</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">listLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isLimitedListExpandable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isCopyable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyableState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessage</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessageDuration</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formatStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">prefix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">suffix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">timezone</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHtml</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMarkdown</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDate</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDateTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMoney</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionAbove</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionBelow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">fontFamily</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">weight</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">lineClamp</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              \"<span class=sf-dump-key>product.title</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22432 title=\"2 occurrences\">#2432</a><samp data-depth=8 id=sf-dump-1520060017-ref22432 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"36 characters\">filament-tables::columns.text-column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">columnToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToCount</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToExistenceCheck</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">layout</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isClickDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hiddenFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">visibleFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isInline</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isGloballySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isIndividuallySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchForcedCaseInsensitive</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSortable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">summarizers</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isToggleable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isToggledHiddenByDefault</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">isHeaderWrapped</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraCellAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">extraHeaderAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"7 characters\">Product</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"13 characters\">product.title</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">loop</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">width</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">getStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">separator</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDistinctList</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">inverseRelationshipName</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">verticalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canWrap</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBadge</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBulleted</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isListWithLineBreaks</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">listLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isLimitedListExpandable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isCopyable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyableState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessage</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessageDuration</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formatStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">prefix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">suffix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">timezone</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHtml</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMarkdown</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDate</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDateTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMoney</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionAbove</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionBelow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">fontFamily</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">weight</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">lineClamp</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              \"<span class=sf-dump-key>pricingModel.name</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22431 title=\"2 occurrences\">#2431</a><samp data-depth=8 id=sf-dump-1520060017-ref22431 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"36 characters\">filament-tables::columns.text-column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">columnToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToCount</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToExistenceCheck</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">layout</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isClickDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hiddenFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">visibleFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isInline</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isGloballySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isIndividuallySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchForcedCaseInsensitive</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSortable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">summarizers</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isToggleable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isToggledHiddenByDefault</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">isHeaderWrapped</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraCellAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">extraHeaderAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"13 characters\">Pricing Model</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"17 characters\">pricingModel.name</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">loop</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">width</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">getStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">separator</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDistinctList</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">inverseRelationshipName</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">verticalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canWrap</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBadge</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBulleted</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isListWithLineBreaks</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">listLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isLimitedListExpandable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isCopyable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyableState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessage</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessageDuration</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formatStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">prefix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">suffix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">timezone</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHtml</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMarkdown</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDate</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDateTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMoney</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionAbove</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionBelow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">fontFamily</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">weight</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">lineClamp</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              \"<span class=sf-dump-key>user.name</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22430 title=\"2 occurrences\">#2430</a><samp data-depth=8 id=sf-dump-1520060017-ref22430 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"36 characters\">filament-tables::columns.text-column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">columnToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToCount</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToExistenceCheck</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToSum</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">layout</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isClickDisabled</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-note>Closure(): bool</span> {<a class=sf-dump-ref>#2333</a> &#8230;5}\n                #<span class=sf-dump-protected title=\"Protected property\">hiddenFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">visibleFrom</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isInline</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isGloballySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isIndividuallySearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">searchQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSearchForcedCaseInsensitive</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isSortable</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortColumns</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">sortQuery</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">summarizers</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">isToggleable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isToggledHiddenByDefault</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]\n                #<span class=sf-dump-protected title=\"Protected property\">isHeaderWrapped</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraCellAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">extraHeaderAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"3 characters\">BDE</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"9 characters\">user.name</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">loop</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">width</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">alignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">getStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">separator</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDistinctList</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">inverseRelationshipName</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">verticalAlignment</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">canWrap</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBadge</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isBulleted</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isListWithLineBreaks</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">listLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isLimitedListExpandable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isCopyable</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyableState</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessage</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">copyMessageDuration</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">formatStateUsing</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">characterLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimit</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">wordLimitEnd</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">prefix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">suffix</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">timezone</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isHtml</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMarkdown</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDate</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isDateTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isMoney</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">isTime</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionAbove</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">descriptionBelow</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">fontFamily</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">iconColor</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">weight</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">lineClamp</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              \"<span class=sf-dump-key>total_payment</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22428 title=\"2 occurrences\">#2428</a><samp data-depth=8 id=sf-dump-1520060017-ref22428 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"36 characters\">filament-tables::columns.text-column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">column</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">columnToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToAvg</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToCount</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipsToExistenceCheck</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMax</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">columnToMin</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationshipToMin</span>: <span class=sf-dump-const>null</span>\n                 &#8230;84\n              </samp>}\n              \"<span class=sf-dump-key>start_date</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22424 title=\"2 occurrences\">#2424</a> &#8230;97}\n              \"<span class=sf-dump-key>end_date</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22418 title=\"2 occurrences\">#2418</a> &#8230;97}\n              \"<span class=sf-dump-key>status</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22414 title=\"2 occurrences\">#2414</a> &#8230;97}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">columnsLayout</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22335 title=\"2 occurrences\">#2335</a>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22432 title=\"2 occurrences\">#2432</a>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22431 title=\"2 occurrences\">#2431</a>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22430 title=\"2 occurrences\">#2430</a>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22428 title=\"2 occurrences\">#2428</a> &#8230;84}\n              <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22424 title=\"2 occurrences\">#2424</a> &#8230;97}\n              <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22418 title=\"2 occurrences\">#2418</a> &#8230;97}\n              <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Columns\\TextColumn\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Columns</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextColumn</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22414 title=\"2 occurrences\">#2414</a> &#8230;97}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">collapsibleColumnsLayout</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hasColumnGroups</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hasColumnsLayout</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">contentFooter</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">contentGrid</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">emptyState</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">emptyStateDescription</span>: <span class=sf-dump-note>Closure(Table $table): ?string</span> {<a class=sf-dump-ref>#2324</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>returnType</span>: \"<span class=sf-dump-str title=\"7 characters\">?string</span>\"\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n21 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Table\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Table</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22326 title=\"11 occurrences\">#2326</a>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table.php\n55 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\tables\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Table.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 96</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">emptyStateHeading</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">emptyStateIcon</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">emptyStateActions</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">areFilterIndicatorsHidden</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">filters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>project_type_id</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Filters\\SelectFilter\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Filters</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SelectFilter</span></span> {<a class=sf-dump-ref>#2412</a> &#8230;36}\n              \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Filters\\SelectFilter\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Filters</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SelectFilter</span></span> {<a class=sf-dump-ref>#2407</a> &#8230;36}\n              \"<span class=sf-dump-key>status</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Filters\\SelectFilter\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Filters</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SelectFilter</span></span> {<a class=sf-dump-ref>#2394</a> &#8230;36}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">filtersFormSchema</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">filtersFormColumns</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">filtersFormMaxHeight</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">filtersFormWidth</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">filtersLayout</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modifyFiltersTriggerActionUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">persistsFiltersInSession</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">shouldDeselectAllRecordsWhenFiltered</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hasDeferredFilters</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modifyFiltersApplyActionUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">heading</span>: \"<span class=sf-dump-str title=\"8 characters\">Projects</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">header</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">description</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">headerActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Tables\\Actions\\CreateAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Tables\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CreateAction</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref22391 title=\"2 occurrences\">#2391</a>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">headerActionsPosition</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">query</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">inverseRelationship</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">getRelationshipUsing</span>: <span class=sf-dump-note>Closure(): Builder</span> {<a class=sf-dump-ref>#2325</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Relation|Illuminate\\Database\\Eloquent\\Builder\n84 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Database\\Eloquent\\Relations\\Relation|Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Builder</span></span>\"\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n78 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Concerns\\InteractsWithRelationshipTable.php\n101 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\filament\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Resources\\Concerns\\InteractsWithRelationshipTable.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">58 to 58</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">queryScopes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager::modifyQueryWithActiveTab(Builder $query): Builder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager::modifyQueryWithActiveTab(Builder $query): Builder</span></span> {<a class=sf-dump-ref>#2323</a> &#8230;4}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">queryStringIdentifier</span>: \"<span class=sf-dump-str title=\"23 characters\">projectsRelationManager</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">recordAction</span>: <span class=sf-dump-note>Closure(Model $record, Table $table): ?string</span> {<a class=sf-dump-ref>#2327</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>returnType</span>: \"<span class=sf-dump-str title=\"7 characters\">?string</span>\"\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n78 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Concerns\\InteractsWithRelationshipTable.php\n101 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\filament\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Resources\\Concerns\\InteractsWithRelationshipTable.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">61 to 83</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">recordClasses</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">allowsDuplicates</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">shouldOpenRecordUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">recordUrl</span>: <span class=sf-dump-note>Closure(Model $record, Table $table): ?string</span> {<a class=sf-dump-ref>#2328</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>returnType</span>: \"<span class=sf-dump-str title=\"7 characters\">?string</span>\"\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n78 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Concerns\\InteractsWithRelationshipTable.php\n101 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\filament\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Resources\\Concerns\\InteractsWithRelationshipTable.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"9 characters\">84 to 108</span>\"\n            </samp>}\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">hasTableModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">shouldMountInteractsWithTable</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">isTableLoaded</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">tableGrouping</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableGroupingDirection</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableRecordsPerPage</span>: <span class=sf-dump-num>10</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultTableRecordsPerPageSelectOption</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">isTableReordering</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">tableColumnSearches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">tableSearch</span>: \"\"\n          +<span class=sf-dump-public title=\"Public property\">tableSortColumn</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableSortDirection</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">toggledTableColumns</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>title</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionsData</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>26</span>\n              \"<span class=sf-dump-key>project_type_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n              \"<span class=sf-dump-key>won_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-25</span>\"\n              \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str>4</span>\"\n              \"<span class=sf-dump-key>duration_unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">months</span>\"\n              \"<span class=sf-dump-key>payment_cycle</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n              \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-01</span>\"\n              \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-10-31</span>\"\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>19</span>\n              \"<span class=sf-dump-key>bde_display</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Falak khan</span>\"\n              \"<span class=sf-dump-key>pricing_model_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n              \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n              \"<span class=sf-dump-key>total_payment</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2500.00</span>\"\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n              \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>_generated_milestones</span>\" => \"<span class=sf-dump-str title=\"653 characters\">[{&quot;title&quot;:&quot;Month 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 4&quot;,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 2 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 2 of 4&quot;,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 3 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 3 of 4&quot;,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;},{&quot;title&quot;:&quot;Month 4 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 4 of 4&quot;,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;percentage&quot;:25,&quot;hours&quot;:null,&quot;amount&quot;:625,&quot;status&quot;:&quot;pending&quot;}]</span>\"\n              \"<span class=sf-dump-key>_generated_payments</span>\" => \"<span class=sf-dump-str title=\"233 characters\">[{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-08-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;status&quot;:&quot;pending&quot;},{&quot;amount&quot;:625,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;status&quot;:&quot;pending&quot;}]</span>\"\n              \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"18 characters\">School Management1</span>\"\n            </samp>]\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionsArguments</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedMountedTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedMountedTableActionRecordKey</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableActionArguments</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">selectedTableRecords</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableBulkAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">mountedTableBulkActionData</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">cachedSelectedTableRecords</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Illuminate\\Database\\Eloquent\\Collection|Illuminate\\Support\\Collection</span>\n          +<span class=sf-dump-public title=\"Public property\">tableFilters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>project_type_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">tableDeferredFilters</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">allowsDuplicates</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedTableRecords</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">paginators</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>projectsRelationManagerPage</span>\" => <span class=sf-dump-num>1</span>\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">tempGeneratedMilestones</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tempGeneratedPayments</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"25 characters\">updatedInteractsWithForms</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vendor/livewire/livewire/src/Wrapped.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Resources\\ClientResource\\RelationManagers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProjectsRelationManager</span></span> {<a class=sf-dump-ref href=#sf-dump-1520060017-ref23959 title=\"24 occurrences\">#3959</a>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"25 characters\">updatedInteractsWithForms</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"85 characters\">vendor/livewire/livewire/src/Features/SupportLifecycleHooks/SupportLifecycleHooks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Livewire\\Wrapped</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">updatedInteractsWithForms</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"85 characters\">vendor/livewire/livewire/src/Features/SupportLifecycleHooks/SupportLifecycleHooks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>76</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">callTraitHook</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">updated</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">mountedTableActionsData.0.pricing_model_id</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"46 characters\">vendor/livewire/livewire/src/ComponentHook.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Livewire\\Features\\SupportLifecycleHooks\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">vendor/livewire/livewire/src/ComponentHookRegistry.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>115</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Livewire\\ComponentHook</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">vendor/livewire/livewire/src/EventBus.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>73</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Livewire\\ComponentHookRegistry</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>304</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Livewire\\EventBus</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>98</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">updateProperties</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"87 characters\">[object App\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.pricing_model_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:42</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tempGeneratedMilestones</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tempGeneratedPayments</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>ownerRecord</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n            \"<span class=sf-dump-key>key</span>\" => <span class=sf-dump-num>26</span>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">mdl</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>pageClass</span>\" => \"<span class=sf-dump-str title=\"54 characters\">App\\Filament\\Resources\\ClientResource\\Pages\\EditClient</span>\"\n        \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>activeTab</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>isTableLoaded</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>tableGrouping</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tableGroupingDirection</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>isTableReordering</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>tableColumnSearches</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>tableSearch</span>\" => \"\"\n        \"<span class=sf-dump-key>tableSortColumn</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>tableSortDirection</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>toggledTableColumns</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedTableActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:19</span> [ &#8230;19]\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedTableActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => []\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>defaultTableAction</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>defaultTableActionArguments</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>defaultTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>selectedTableRecords</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>mountedTableBulkAction</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>mountedTableBulkActionData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => []\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>project_type_id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n            \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n              <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n            </samp>]\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>tableDeferredFilters</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>projectsRelationManagerPage</span>\" => <span class=sf-dump-num>1</span>\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/livewire/livewire/src/LivewireManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>102</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:42</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>tempGeneratedMilestones</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tempGeneratedPayments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>ownerRecord</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n              \"<span class=sf-dump-key>key</span>\" => <span class=sf-dump-num>26</span>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">mdl</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>pageClass</span>\" => \"<span class=sf-dump-str title=\"54 characters\">App\\Filament\\Resources\\ClientResource\\Pages\\EditClient</span>\"\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>activeTab</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>isTableLoaded</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableGrouping</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableGroupingDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => <span class=sf-dump-num>10</span>\n          \"<span class=sf-dump-key>isTableReordering</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableColumnSearches</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableSearch</span>\" => \"\"\n          \"<span class=sf-dump-key>tableSortColumn</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSortDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>toggledTableColumns</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>product</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>selectedTableRecords</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableBulkAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedTableBulkActionData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>project_type_id</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n              \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n              \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableDeferredFilters</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>projectsRelationManagerPage</span>\" => <span class=sf-dump-num>1</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">1NqHdTFDIHtzfFjaVG35</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"82 characters\">app.filament.resources.client-resource.relation-managers.projects-relation-manager</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">admin/clients/26/edit</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">f1a262f276be53fb7a6f1eceee08ff503f4dc591cd0769644bec9539b6257154</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.pricing_model_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"73 characters\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>94</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Livewire\\LivewireManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:42</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>tempGeneratedMilestones</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tempGeneratedPayments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>ownerRecord</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n              \"<span class=sf-dump-key>key</span>\" => <span class=sf-dump-num>26</span>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">mdl</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>pageClass</span>\" => \"<span class=sf-dump-str title=\"54 characters\">App\\Filament\\Resources\\ClientResource\\Pages\\EditClient</span>\"\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>activeTab</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>isTableLoaded</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableGrouping</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableGroupingDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => <span class=sf-dump-num>10</span>\n          \"<span class=sf-dump-key>isTableReordering</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableColumnSearches</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableSearch</span>\" => \"\"\n          \"<span class=sf-dump-key>tableSortColumn</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSortDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>toggledTableColumns</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>product</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>selectedTableRecords</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableBulkAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedTableBulkActionData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>project_type_id</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n              \"<span class=sf-dump-key>pricing_model_id</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n              \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableDeferredFilters</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>projectsRelationManagerPage</span>\" => <span class=sf-dump-num>1</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">1NqHdTFDIHtzfFjaVG35</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"82 characters\">app.filament.resources.client-resource.relation-managers.projects-relation-manager</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">admin/clients/26/edit</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">f1a262f276be53fb7a6f1eceee08ff503f4dc591cd0769644bec9539b6257154</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.pricing_model_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Livewire\\Mechanisms\\HandleRequests\\HandleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"58 characters\">[object Livewire\\Mechanisms\\HandleRequests\\HandleRequests]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">D:\\wamp64\\www\\smms\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520060017\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                        $set('_generated_milestones', '');\n", "                                        $set('_generated_payments', '');\n", "                                    } else {\n", "                                        $this->generateMilestonesAndPayments($set, $get);\n", "                                    }\n", "                                }\n", "                            }),\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FProjectsRelationManager.php&line=282", "ajax": false, "filename": "ProjectsRelationManager.php", "line": "282"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.050285, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.052427, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.053145, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.76437, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.765205, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.7658, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.766099, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.766513, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.767161, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.767927, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.76954, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.379723, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.990959, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.994827, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.997393, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.997607, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.029993, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.087536, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.088156, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.474869, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.475652, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.476015, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00833, "accumulated_duration_str": "8.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'tdvfTu5BCoGvpVecBSOgOHtYDPPiDAUn1ODDQk1Z' limit 1", "type": "query", "params": [], "bindings": ["tdvfTu5BCoGvpVecBSOgOHtYDPPiDAUn1ODDQk1Z"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.629286, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 7.803}, {"sql": "select * from `users` where `id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.643482, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.803, "width_percent": 9.844}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6485798, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.647, "width_percent": 9.124}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.652306, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.771, "width_percent": 7.923}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.654252, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.694, "width_percent": 5.522}, {"sql": "select * from `clients` where `clients`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.659982, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.216, "width_percent": 29.292}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php", "line": 184}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/RelationManagers/RelationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\RelationManagers\\RelationManager.php", "line": 216}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}], "start": **********.739985, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.508, "width_percent": 12.605}, {"sql": "select * from `project_types` where `project_types`.`id` = '13' limit 1", "type": "query", "params": [], "bindings": ["13"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php", "line": 77}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.748508, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ProjectsRelationManager.php:77", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FProjectsRelationManager.php&line=77", "ajax": false, "filename": "ProjectsRelationManager.php", "line": "77"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.113, "width_percent": 10.324}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 101}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 30}], "start": **********.752491, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ProjectsRelationManager.php:266", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/ProjectsRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\ProjectsRelationManager.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FProjectsRelationManager.php&line=266", "ajax": false, "filename": "ProjectsRelationManager.php", "line": "266"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.437, "width_percent": 7.563}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Client": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProjectType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProjectType.php&line=1", "ajax": false, "filename": "ProjectType.php", "line": "?"}}, "App\\Models\\PricingModel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "3.8s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2019598952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019598952\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-63711272 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0lQQpeWXgeTGgD4uDL5Ty2hwf8WViCiz1zJO4QFY</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"3544 characters\">{&quot;data&quot;:{&quot;tempGeneratedMilestones&quot;:null,&quot;tempGeneratedPayments&quot;:null,&quot;ownerRecord&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Client&quot;,&quot;key&quot;:26,&quot;s&quot;:&quot;mdl&quot;}],&quot;pageClass&quot;:&quot;App\\\\Filament\\\\Resources\\\\ClientResource\\\\Pages\\\\EditClient&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeTab&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableRecordsPerPage&quot;:10,&quot;isTableReordering&quot;:false,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;toggledTableColumns&quot;:[{&quot;product&quot;:[{&quot;title&quot;:false},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;create&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;client_id&quot;:26,&quot;project_type_id&quot;:&quot;13&quot;,&quot;won_date&quot;:&quot;2025-06-25&quot;,&quot;product_id&quot;:&quot;1&quot;,&quot;duration&quot;:&quot;4&quot;,&quot;duration_unit&quot;:&quot;months&quot;,&quot;payment_cycle&quot;:&quot;monthly&quot;,&quot;start_date&quot;:&quot;2025-07-01&quot;,&quot;end_date&quot;:&quot;2025-10-31&quot;,&quot;user_id&quot;:19,&quot;bde_display&quot;:&quot;Falak khan&quot;,&quot;pricing_model_id&quot;:null,&quot;currency&quot;:&quot;INR&quot;,&quot;total_payment&quot;:&quot;2500.00&quot;,&quot;status&quot;:&quot;active&quot;,&quot;description&quot;:null,&quot;_generated_milestones&quot;:&quot;[{\\&quot;title\\&quot;:\\&quot;Month 1 Milestone\\&quot;,\\&quot;description\\&quot;:\\&quot;Auto-generated milestone 1 of 4\\&quot;,\\&quot;due_date\\&quot;:\\&quot;2025-08-01\\&quot;,\\&quot;percentage\\&quot;:25,\\&quot;hours\\&quot;:null,\\&quot;amount\\&quot;:625,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;title\\&quot;:\\&quot;Month 2 Milestone\\&quot;,\\&quot;description\\&quot;:\\&quot;Auto-generated milestone 2 of 4\\&quot;,\\&quot;due_date\\&quot;:\\&quot;2025-09-01\\&quot;,\\&quot;percentage\\&quot;:25,\\&quot;hours\\&quot;:null,\\&quot;amount\\&quot;:625,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;title\\&quot;:\\&quot;Month 3 Milestone\\&quot;,\\&quot;description\\&quot;:\\&quot;Auto-generated milestone 3 of 4\\&quot;,\\&quot;due_date\\&quot;:\\&quot;2025-10-01\\&quot;,\\&quot;percentage\\&quot;:25,\\&quot;hours\\&quot;:null,\\&quot;amount\\&quot;:625,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;title\\&quot;:\\&quot;Month 4 Milestone\\&quot;,\\&quot;description\\&quot;:\\&quot;Auto-generated milestone 4 of 4\\&quot;,\\&quot;due_date\\&quot;:\\&quot;2025-11-01\\&quot;,\\&quot;percentage\\&quot;:25,\\&quot;hours\\&quot;:null,\\&quot;amount\\&quot;:625,\\&quot;status\\&quot;:\\&quot;pending\\&quot;}]&quot;,&quot;_generated_payments&quot;:&quot;[{\\&quot;amount\\&quot;:625,\\&quot;due_date\\&quot;:\\&quot;2025-08-01\\&quot;,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;amount\\&quot;:625,\\&quot;due_date\\&quot;:\\&quot;2025-09-01\\&quot;,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;amount\\&quot;:625,\\&quot;due_date\\&quot;:\\&quot;2025-10-01\\&quot;,\\&quot;status\\&quot;:\\&quot;pending\\&quot;},{\\&quot;amount\\&quot;:625,\\&quot;due_date\\&quot;:\\&quot;2025-11-01\\&quot;,\\&quot;status\\&quot;:\\&quot;pending\\&quot;}]&quot;,&quot;title&quot;:&quot;School Management1&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableFilters&quot;:[{&quot;project_type_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;pricing_model_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;projectsRelationManagerPage&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;1NqHdTFDIHtzfFjaVG35&quot;,&quot;name&quot;:&quot;app.filament.resources.client-resource.relation-managers.projects-relation-manager&quot;,&quot;path&quot;:&quot;admin\\/clients\\/26\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f1a262f276be53fb7a6f1eceee08ff503f4dc591cd0769644bec9539b6257154&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.pricing_model_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63711272\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-700289692 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4295</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFCYTVXZHdYUjdjNE1uVkFnVjlqcGc9PSIsInZhbHVlIjoiTnVlWWdmVDZraFBFWHNuc0VBVFNhK3lvNGtpd2V0cENSOWJQZ3pNWlBVbFRyOU9aRS9vVTA3OFFKYnBBLzN3bDR1YlJUcG9RckIrSDFXa3RUeU91b25ZN0p2a0N5UWtZVUtTNnBGZ1FTSEludk5HWUtTUURncFFrdFVyZXBUUGZhaGNXbEphL0JXK3lESWY5VnFHVkt0V0MvZDk0c1h4MTVnMEF5OUtEVzFmUFA5Y1hvdU00eG84SFRLZDd1MzRidnR0RFVSQkdSTVdmZEpMZis5TVJYNkhOamM2UnJCQldpa2tmeVBXbzNxOD0iLCJtYWMiOiI5NDY3NWVkYzY3M2IwYmRlNjA4MmY1YjY2YWIxZDExMTg0NzY0Y2ExNTBhNmEwYTgxNDdjY2MwOGI0NjJhMjg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9TTXNESEwzZ1MvVkkzMVMrZG0zeWc9PSIsInZhbHVlIjoiVzFVUVFidmJSYkIrL2FxYzNKb3BxME9jN2I4K3pKZTRqSXEvbXdkSGdWZjFPY3dwbUFYTjhZNkRrblJFKzZ1cVRmSXR3aDZldWZNei8xRzVJNloyRHI5WjczcVZXZWdhTkl4VFZLajIvdFdic2owMnZ2Nk9PQ1lIcndnbGIvT1giLCJtYWMiOiJkZTk2MTdkMmVkMzMxMjRlMDQ2YTM5ZmVlMWQ2ZTU2ODljODlhOWUwZDI2YzRkZDI4ZmQ4NjNiYmYwMjJlZmM2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InRnQXZ1Y09ERnUyV2FHZHFaNnU3Q1E9PSIsInZhbHVlIjoiSGFKa2F2MUxJamprcE5ObThYVGlaV3hmYXlKN0pwVldQQ0VjaU1pcDNsQUx2eGhRcFk2K1dBZnVlcCtQNGttbFRrSTZqRUtTbkUyQWNBd0lGTDhKRUl4dDA2VHd0UVVzdHYxaEVlSTRud2Yrc3ZabkFZNVIxa0EvVnZWbzJFRTgiLCJtYWMiOiIyMTg0MjZmNjk5N2M1NWEyOGRiMzQ4YzZjMmIwZTUwYWJlODUwMDQ0NzE5NGZiNDNhZWU5NDFhNDM2MzAyNzhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700289692\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-928321473 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|Gkx81hLUil4MDhe2uFzUzCfsIebikPpqVYwj58Dl98Kha2jZhNtTChXt3mHw|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0lQQpeWXgeTGgD4uDL5Ty2hwf8WViCiz1zJO4QFY</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tdvfTu5BCoGvpVecBSOgOHtYDPPiDAUn1ODDQk1Z</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928321473\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1513529979 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 02:21:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513529979\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-633335540 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0lQQpeWXgeTGgD4uDL5Ty2hwf8WViCiz1zJO4QFY</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633335540\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": "500 Internal Server Error"}}