<?php

namespace App\Http\Responses;

use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;
use Illuminate\Http\RedirectResponse;
use Livewire\Features\SupportRedirects\Redirector;

class LoginResponse implements LoginResponseContract
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        // Get the authenticated user
        $user = auth()->user();
        
        // Role-based redirect logic
        if ($user && $user->hasRole('super_admin')) {
            return redirect()->intended('/admin/dashboard');
        }
        
        if ($user && $user->hasAnyRole(['jr_bde_team', 'bde_team'])) {
            return redirect()->intended('/admin/home');
        }
        
        // Default redirect
        return redirect()->intended(filament()->getHomeUrl());
    }
}
