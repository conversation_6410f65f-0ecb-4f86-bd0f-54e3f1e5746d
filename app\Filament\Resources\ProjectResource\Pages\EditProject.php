<?php

namespace App\Filament\Resources\ProjectResource\Pages;

use App\Filament\Resources\ProjectResource;
use App\Filament\Resources\ProjectResource\RelationManagers\MilestonesRelationManager;
use App\Filament\Resources\ProjectResource\RelationManagers\IncentivesRelationManager;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;

class EditProject extends EditRecord
{
    protected static string $resource = ProjectResource::class;

    protected static string $view = 'filament.resources.project-resource.pages.edit-project';

    protected function getHeaderActions(): array
    {
        return [
            // Delete action removed for better UI consistency with card design
        ];
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }

    public function getContentTabLabel(): ?string
    {
        return 'Project Details';
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Section::make('Project Details')
                            ->schema([
                                Forms\Components\Select::make('client_id')
                                    ->relationship('client', 'company_name')
                                    ->label('Client')
                                    ->searchable()
                                    ->preload()
                                    ->required(),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Select::make('project_type_id')
                                            ->relationship('projectType', 'name')
                                            ->label('Title')
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                if ($state) {
                                                    $projectType = \App\Models\ProjectType::find($state);
                                                    $set('title', $projectType ? $projectType->name : 'New Project');
                                                    if ($projectType && $projectType->name !== 'Product') {
                                                        $set('product_id', null);
                                                    }
                                                }
                                            }),
                                        Forms\Components\DatePicker::make('won_date')
                                            ->required()
                                            ->dehydrated()
                                            ->live(onBlur: true)
                                            ->rules([
                                                function (Get $get) {
                                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                        $startDate = $get('start_date');
                                                        if ($startDate && $value) {
                                                            $wonDateCarbon = \Carbon\Carbon::parse($value);
                                                            $startDateCarbon = \Carbon\Carbon::parse($startDate);
                                                            if ($wonDateCarbon->gt($startDateCarbon)) {
                                                                $fail('The won date must be equal to or less than the start date (' . $startDateCarbon->format('Y-m-d') . ').');
                                                            }
                                                        }
                                                    };
                                                },
                                            ]),
                                    ]),

                                Forms\Components\Select::make('product_id')
                                    ->relationship('product', 'title')
                                    ->label('Product')
                                    ->searchable()
                                    ->preload()
                                    ->visible(function (Get $get) {
                                        $projectTypeId = $get('project_type_id');
                                        if (!$projectTypeId) return false;
                                        $projectType = \App\Models\ProjectType::find($projectTypeId);
                                        return $projectType && $projectType->name === 'Product';
                                    })
                                    ->live()
                                    ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                        if ($state) {
                                            $product = \App\Models\Product::find($state);
                                            if ($product) {
                                                $set('title', $product->title);
                                                $set('total_payment', $product->amount);
                                                $set('currency', $product->currency);
                                                if ($get('start_date') && $get('duration') && $get('duration_unit')) {
                                                    $this->generateMilestonesAndPayments($set, $get);
                                                }
                                            }
                                        }
                                    }),

                                Forms\Components\Grid::make(5)
                                    ->schema([
                                        Forms\Components\TextInput::make('duration')
                                            ->required()
                                            ->numeric()
                                            ->minValue(1)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                $this->calculateEndDate($set, $get);
                                                $this->generateMilestonesAndPayments($set, $get);
                                            }),
                                        Forms\Components\Select::make('duration_unit')
                                            ->options([
                                                'hours' => 'Hours',
                                                'days' => 'Days',
                                                'weeks' => 'Weeks',
                                                'months' => 'Months',
                                                'years' => 'Years',
                                            ])
                                            ->required()
                                            ->dehydrated()
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                $this->calculateEndDate($set, $get);
                                                $this->generateMilestonesAndPayments($set, $get);
                                            }),
                                        Forms\Components\Select::make('payment_cycle')
                                            ->options([
                                                'weekly' => 'Weekly',
                                                'biweekly' => 'Bi-weekly',
                                                'monthly' => 'Monthly',
                                                'quarterly' => 'Quarterly',
                                                'yearly' => 'Yearly',
                                                'milestone' => 'Milestone-based',
                                                'upfront' => 'Upfront',
                                            ])
                                            ->required()
                                            ->dehydrated()
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                $this->generateMilestonesAndPayments($set, $get);
                                            }),
                                        Forms\Components\DatePicker::make('start_date')
                                            ->required()
                                            ->dehydrated()
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                $this->calculateEndDate($set, $get);
                                                $this->generateMilestonesAndPayments($set, $get);
                                            })
                                            ->rules([
                                                function (Get $get) {
                                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                        $wonDate = $get('won_date');
                                                        if ($wonDate && $value) {
                                                            $wonDateCarbon = \Carbon\Carbon::parse($wonDate);
                                                            $startDateCarbon = \Carbon\Carbon::parse($value);
                                                            if ($startDateCarbon->lt($wonDateCarbon)) {
                                                                $fail('The start date must be equal to or greater than the won date (' . $wonDateCarbon->format('Y-m-d') . ').');
                                                            }
                                                        }
                                                    };
                                                },
                                            ]),
                                        Forms\Components\DatePicker::make('end_date')
                                            ->label('End Date')
                                            ->helperText('Auto-calculated based on start date and duration')
                                            ->live()
                                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                                // Only auto-calculate if the field is empty or being cleared
                                                if (empty($state)) {
                                                    $this->calculateEndDate($set, $get);
                                                }
                                            }),
                                    ]),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Hidden::make('user_id')
                                            ->default(auth()->id())
                                            ->dehydrated(),
                                        auth()->user()->hasRole('super_admin')
                                        ? Forms\Components\Select::make('bde_select')
                                            ->relationship('user', 'name')
                                            ->label('BDE')
                                            ->default(auth()->id())
                                            ->live()
                                            ->afterStateUpdated(function ($state, Set $set) {
                                                $set('user_id', $state);
                                            })
                                        : Forms\Components\TextInput::make('bde_display')
                                            ->label('BDE')
                                            ->default(function ($operation, $record) {
                                                // For create operation
                                                if ($operation === 'create') {
                                                    return auth()->user()->name;
                                                }
                                                // For edit operation - load the relationship properly
                                                if ($record && $record->user_id) {
                                                    return $record->user->name ?? 'Not assigned';
                                                }
                                                return auth()->user()->name;
                                            })
                                            ->disabled()
                                            ->dehydrated(false)
                                            ->afterStateHydrated(function (Forms\Components\TextInput $component, $record) {
                                                // Ensure the name is loaded when the form hydrates
                                                if ($record && $record->user_id) {
                                                    $component->state($record->user->name);
                                                }
                                            }),
                                        Forms\Components\Select::make('pricing_model_id')
                                            ->relationship('pricingModel', 'name')
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                                if (!$state) return;

                                                $pricingModel = \App\Models\PricingModel::find($state);

                                                if ($pricingModel) {
                                                    // Force currency update (even if manually changed)
                                                    if (str_contains(strtoupper($pricingModel->name), 'USD')) {
                                                        $set('currency', 'USD');
                                                    }
                                                    elseif (str_contains(strtoupper($pricingModel->name), 'INR')) {
                                                        $set('currency', 'INR');
                                                    }

                                                    // Rest of your logic (milestones, etc.)
                                                    if (str_contains($pricingModel->name, 'Fixed')) {
                                                        $set('_generated_milestones', '');
                                                        $set('_generated_payments', '');
                                                    } else {
                                                        $this->generateMilestonesAndPayments($set, $get);
                                                    }
                                                }
                                            }),
                                        Forms\Components\Select::make('currency')
                                        ->options([
                                            'INR' => 'Indian Rupee (₹)',
                                            'USD' => 'US Dollar ($)',
                                            'EUR' => 'Euro (€)',
                                            'GBP' => 'British Pound (£)',
                                        ])
                                        ->default('INR')
                                        ->required()
                                        ->live()
                                        ->afterStateUpdated(function (Set $set, $state) {
                                            $set('currency', $state);
                                        }),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('total_payment')
                                            ->required()
                                            ->numeric()
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                $this->generateMilestonesAndPayments($set, $get);
                                            }),
                                        Forms\Components\Select::make('status')
                                            ->options([
                                                'active' => 'Active',
                                                'completed' => 'Completed',
                                                'on_hold' => 'On Hold',
                                                'cancelled' => 'Cancelled',
                                            ])
                                            ->default('active')
                                            ->required(),
                                    ]),

                                Forms\Components\Textarea::make('description')
                                    ->maxLength(65535)
                                    ->columnSpanFull(),

                                Forms\Components\Hidden::make('_generated_milestones'),
                                Forms\Components\Hidden::make('_generated_payments'),
                                Forms\Components\Hidden::make('title')
                                    ->default(function (Get $get) {
                                        $projectTypeId = $get('project_type_id');
                                        if ($projectTypeId) {
                                            $projectType = \App\Models\ProjectType::find($projectTypeId);
                                            return $projectType ? $projectType->name : 'New Project';
                                        }
                                        return 'New Project';
                                    }),
                            ])
                    ])
            ]);
    }

    private function calculateEndDate(Set $set, Get $get)
    {
        $startDate = $get('start_date');
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');

        if (!$startDate || !$duration || !$durationUnit) {
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration;

            if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
                $workHoursPerWeek = 40;
                $weeks = ceil($durationValue / $workHoursPerWeek);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            elseif ($durationUnit === 'days' && $paymentCycle === 'weekly') {
                $weeks = ceil($durationValue / 7);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            else {
                $endDate = match($durationUnit) {
                    'hours' => $startCarbon->copy()->addHours($durationValue),
                    'days' => $startCarbon->copy()->addDays($durationValue),
                    'weeks' => $startCarbon->copy()->addWeeks($durationValue),
                    'months' => $startCarbon->copy()->addMonths($durationValue)->subDay(),
                    'years' => $startCarbon->copy()->addYears($durationValue)->subDay(),
                    default => $startCarbon->copy()->addMonths($durationValue),
                };
            }

            $set('end_date', $endDate->format('Y-m-d'));
        } catch (\Exception $e) {
            // Handle errors silently
        }
    }

    private function generateMilestonesAndPayments(Set $set, Get $get)
    {
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');
        $startDate = $get('start_date');
        $totalPayment = $get('total_payment');

        if (!$duration || !$durationUnit || !$paymentCycle || !$startDate || !$totalPayment) {
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration;
            $totalPaymentValue = (float) $totalPayment;
            $milestones = [];
            $payments = [];

            $numberOfCycles = $this->calculateNumberOfCycles($durationValue, $durationUnit, $paymentCycle);
            if ($numberOfCycles <= 0) return;

            if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
                $standardHoursPerWeek = 40;
                $fullWeeks = floor($durationValue / $standardHoursPerWeek);
                $remainingHours = $durationValue % $standardHoursPerWeek;
                $totalCycles = $fullWeeks + ($remainingHours > 0 ? 1 : 0);

                $adjustedAmounts = $this->distributeAmountEvenly($totalPaymentValue, $totalCycles);
                $adjustedPercentages = $this->distributePercentageEvenly($totalCycles);

                for ($i = 1; $i <= $fullWeeks; $i++) {
                    $dueDate = $this->calculateDueDate($startCarbon, $i, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($i, $paymentCycle),
                        'description' => "Auto-generated milestone {$i} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$i-1],
                        'hours' => $standardHoursPerWeek,
                        'amount' => $adjustedAmounts[$i-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$i-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }

                if ($remainingHours > 0) {
                    $cycleNumber = $fullWeeks + 1;
                    $dueDate = $this->calculateDueDate($startCarbon, $cycleNumber, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($cycleNumber, $paymentCycle),
                        'description' => "Auto-generated milestone {$cycleNumber} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$cycleNumber-1],
                        'hours' => $remainingHours,
                        'amount' => $adjustedAmounts[$cycleNumber-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$cycleNumber-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }
            } else {
                $adjustedAmounts = $this->distributeAmountEvenly($totalPaymentValue, $numberOfCycles);
                $adjustedPercentages = $this->distributePercentageEvenly($numberOfCycles);
                $hoursPerCycle = ($durationUnit === 'hours') ? round($durationValue / $numberOfCycles) : null;

                for ($i = 1; $i <= $numberOfCycles; $i++) {
                    $dueDate = $this->calculateDueDate($startCarbon, $i, $paymentCycle, $durationValue, $durationUnit);

                    $milestones[] = [
                        'title' => $this->generateMilestoneTitle($i, $paymentCycle),
                        'description' => "Auto-generated milestone {$i} of {$numberOfCycles}",
                        'due_date' => $dueDate->format('Y-m-d'),
                        'percentage' => $adjustedPercentages[$i-1],
                        'hours' => $hoursPerCycle,
                        'amount' => $adjustedAmounts[$i-1],
                        'status' => 'pending',
                    ];

                    $payments[] = [
                        'amount' => $adjustedAmounts[$i-1],
                        'due_date' => $dueDate->format('Y-m-d'),
                        'status' => 'pending',
                    ];
                }
            }

            $set('_generated_milestones', json_encode($milestones));
            $set('_generated_payments', json_encode($payments));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error generating milestones: " . $e->getMessage());
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
        }
    }

    private function calculateNumberOfCycles(int $duration, string $durationUnit, string $paymentCycle): int
    {
        return match([$durationUnit, $paymentCycle]) {
            ['hours', 'weekly'] => max(1, ceil($duration / 40)),
            ['days', 'weekly'] => max(1, ceil($duration / 7)),
            ['weeks', 'weekly'] => max(1, $duration),
            ['months', 'weekly'] => max(1, $duration * 4),
            ['years', 'weekly'] => max(1, $duration * 52),
            ['hours', 'biweekly'] => max(1, ceil($duration / 80)),
            ['days', 'biweekly'] => max(1, ceil($duration / 14)),
            ['weeks', 'biweekly'] => max(1, ceil($duration / 2)),
            ['months', 'biweekly'] => max(1, $duration * 2),
            ['years', 'biweekly'] => max(1, $duration * 26),
            ['hours', 'monthly'] => max(1, ceil($duration / 160)),
            ['days', 'monthly'] => max(1, ceil($duration / 30)),
            ['weeks', 'monthly'] => max(1, ceil($duration / 4)),
            ['months', 'monthly'] => max(1, $duration),
            ['years', 'monthly'] => max(1, $duration * 12),
            ['hours', 'quarterly'] => max(1, ceil($duration / 480)),
            ['days', 'quarterly'] => max(1, ceil($duration / 90)),
            ['weeks', 'quarterly'] => max(1, ceil($duration / 12)),
            ['months', 'quarterly'] => max(1, ceil($duration / 3)),
            ['years', 'quarterly'] => max(1, $duration * 4),
            ['hours', 'yearly'] => max(1, ceil($duration / 1920)),
            ['days', 'yearly'] => max(1, ceil($duration / 365)),
            ['weeks', 'yearly'] => max(1, ceil($duration / 52)),
            ['months', 'yearly'] => max(1, ceil($duration / 12)),
            ['years', 'yearly'] => max(1, $duration),
            default => 1,
        };
    }

    private function distributeAmountEvenly(float $totalAmount, int $numberOfCycles): array
    {
        if ($numberOfCycles <= 0) return [];

        $amounts = [];
        $baseAmount = $totalAmount / $numberOfCycles;
        $sum = 0.0;

        for ($i = 0; $i < $numberOfCycles - 1; $i++) {
            $amounts[] = round($baseAmount, 2);
            $sum += $amounts[$i];
        }

        $amounts[] = round($totalAmount - $sum, 2);
        return $amounts;
    }

    private function distributePercentageEvenly(int $numberOfCycles): array
    {
        if ($numberOfCycles <= 0) return [];

        $percentages = [];
        $basePercentage = 100 / $numberOfCycles;
        $sum = 0.0;

        for ($i = 0; $i < $numberOfCycles - 1; $i++) {
            $percentages[] = round($basePercentage, 2);
            $sum += $percentages[$i];
        }

        $percentages[] = round(100 - $sum, 2);
        return $percentages;
    }

    private function calculateDueDate(\Carbon\Carbon $startDate, int $cycleNumber, string $paymentCycle, int $duration, string $durationUnit): \Carbon\Carbon
    {
        return match($paymentCycle) {
            'weekly' => $startDate->copy()->addWeeks($cycleNumber),
            'biweekly' => $startDate->copy()->addWeeks($cycleNumber * 2),
            'monthly' => $startDate->copy()->addMonths($cycleNumber),
            'quarterly' => $startDate->copy()->addMonths($cycleNumber * 3),
            'yearly' => $startDate->copy()->addYears($cycleNumber),
            default => $startDate->copy()->addWeeks($cycleNumber),
        };
    }

    private function generateMilestoneTitle(int $cycleNumber, string $paymentCycle): string
    {
        return match($paymentCycle) {
            'weekly' => "Week {$cycleNumber}",
            'biweekly' => "Bi-week {$cycleNumber}",
            'monthly' => "Month {$cycleNumber}",
            'quarterly' => "Quarter {$cycleNumber}",
            'yearly' => "Year {$cycleNumber}",
            default => "Milestone {$cycleNumber}",
        };
    }

    protected function afterSave(): void
    {
        $data = $this->form->getState();
        $record = $this->record;

        // Only regenerate milestones/payments if the generated data exists
        if (isset($data['_generated_milestones']) && !empty($data['_generated_milestones'])) {
            // Delete existing milestones and payments
            $record->milestones()->delete();
            $record->payments()->delete();

            // Create new milestones and payments
            $this->createMilestonesAndPayments($data, $record, 'edit');
        }
    }

    private function createMilestonesAndPayments(array $data, $project, string $action = 'edit')
    {
        // Create milestones first, then link payments to them
        $createdMilestones = [];
        if (isset($data['_generated_milestones']) && !empty($data['_generated_milestones'])) {
            $milestones = json_decode($data['_generated_milestones'], true);
            if (is_array($milestones)) {
                foreach ($milestones as $index => $milestoneData) {
                    $milestoneData['project_id'] = $project->id;
                    $milestone = \App\Models\Milestone::create($milestoneData);
                    $createdMilestones[$index] = $milestone;
                }
            }
        }

        // Create payments and link them to corresponding milestones
        if (isset($data['_generated_payments']) && !empty($data['_generated_payments'])) {
            $payments = json_decode($data['_generated_payments'], true);
            if (is_array($payments)) {
                foreach ($payments as $index => $paymentData) {
                    $paymentData['project_id'] = $project->id;

                    // Link payment to corresponding milestone if it exists
                    if (isset($createdMilestones[$index])) {
                        $paymentData['milestone_id'] = $createdMilestones[$index]->id;
                    }

                    \App\Models\Payment::create($paymentData);
                }
            }
        }
    }

    public function getRelationManagers(): array
    {
        return [
            MilestonesRelationManager::class,
            IncentivesRelationManager::class,
        ];
    }
}
