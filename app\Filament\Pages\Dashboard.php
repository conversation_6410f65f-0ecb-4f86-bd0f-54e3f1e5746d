<?php

// This is a comment added as per user request

namespace App\Filament\Pages;

use App\Models\DashboardConfig;
use App\Models\DashboardWidget;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Traits\HasPageShield;
use Livewire\Component;
use Carbon\Carbon;

class Dashboard extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?int $navigationSort = -2;

    protected static string $view = 'filament.pages.dashboard';

    public static function canAccess(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Only super_admin can access the regular dashboard
        if (auth()->user()->hasRole('super_admin')) {
            return true;
        }

        // All other users should be redirected to BDE dashboard
        return false;
    }

    public static function shouldRegisterNavigation(): bool
    {
        // Hide navigation for BDE users
        return !(auth()->check() && auth()->user()->hasAnyRole(['bde', 'bde_team']));
    }

    public array $widgets = [];

    // Date filter property
    public string $dateFilter = 'month';

    // Make this the default page
    public static function getSlug(): string
    {
        return 'dashboard';
    }

    public function resetDashboard()
    {
        $userId = Filament::auth()->id();

        // Delete all dashboard configurations for the current user
        DashboardConfig::where('user_id', $userId)->delete();

        // Show a notification
        Notification::make()
            ->title('Dashboard reset successfully')
            ->success()
            ->send();

        // Reload the page
        $this->redirect(route('filament.admin.pages.dashboard'));
    }

    public function mount(): void
    {
        $userId = Filament::auth()->id();
        $allWidgets = DashboardWidget::where('is_available', true)->get();

        // Get user's widget configurations
        $userConfigs = DashboardConfig::where('user_id', $userId)
            ->orderBy('position')
            ->get()
            ->keyBy('widget_id');

        \Log::info('[DASHBOARD] User widget configs', [
            'user_id' => $userId,
            'userConfigs' => $userConfigs->toArray(),
        ]);

        // Prepare widgets data for the view
        $this->widgets = [];

        foreach ($allWidgets as $widget) {
            // Skip if user has explicitly disabled this widget
            if (isset($userConfigs[$widget->id]) && !$userConfigs[$widget->id]->is_enabled) {
                continue;
            }

            // Get user config or use defaults
            $config = $userConfigs[$widget->id] ?? null;

            $widgetArr = [
                'id' => $widget->id,
                'name' => $widget->name,
                'component' => $widget->component,
                'width' => $config ? $config->width : $widget->default_width,
                'height' => $config ? $config->height : $widget->default_height,
                'position' => $config ? $config->position : $widget->id,
                'settings' => $config ? $config->settings : $widget->default_settings,
            ];
            \Log::info('[DASHBOARD] Widget prepared', $widgetArr);
            $this->widgets[] = $widgetArr;
        }

        // Sort widgets by position
        usort($this->widgets, function ($a, $b) {
            return $a['position'] <=> $b['position'];
        });

        \Log::info('[DASHBOARD] Final widgets for view', $this->widgets);
    }

    /**
     * Get date range based on filter
     */
    public function getDateRange(): array
    {
        $now = Carbon::now();

        return match($this->dateFilter) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay()
            ],
            'yesterday' => [
                'start' => $now->copy()->subDay()->startOfDay(),
                'end' => $now->copy()->subDay()->endOfDay()
            ],
            'tomorrow' => [
                'start' => $now->copy()->addDay()->startOfDay(),
                'end' => $now->copy()->addDay()->endOfDay()
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek()
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth()
            ],
            'this_year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear()
            ],
            default => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay()
            ]
        };
    }

    /**
     * Get filtered project count
     */
    public function getFilteredProjectCount(): int
    {
        $dateRange = $this->getDateRange();

        return \App\Models\Project::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->count();
    }

    /**
     * Get filtered client count
     */
    public function getFilteredClientCount(): int
    {
        $dateRange = $this->getDateRange();

        return \App\Models\Client::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->count();
    }

    /**
     * Get filtered pending payments
     */
    public function getFilteredPendingPayments(): float
    {
        $dateRange = $this->getDateRange();

        // Get payments with their project currency
        $payments = \App\Models\Payment::where('status', 'pending')
            ->whereBetween('due_date', [$dateRange['start'], $dateRange['end']])
            ->with('project:id,currency')
            ->get();

        // For now, sum all amounts (mixed currencies)
        // TODO: Implement currency conversion to base currency
        return $payments->sum('amount');
    }

    /**
     * Get filtered recent projects
     */
    public function getFilteredRecentProjects()
    {
        $dateRange = $this->getDateRange();

        return \App\Models\Project::with(['client', 'user'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->latest()
            ->take(5)
            ->get();
    }

    /**
     * Get filtered upcoming payments
     */
    public function getFilteredUpcomingPayments()
    {
        $dateRange = $this->getDateRange();

        return \App\Models\Payment::with('project')
            ->where('status', 'pending')
            ->whereBetween('due_date', [$dateRange['start'], $dateRange['end']])
            ->orderBy('due_date')
            ->take(5)
            ->get();
    }

    /**
     * Get filtered project status counts
     */
    public function getFilteredProjectStatusCounts(): array
    {
        $dateRange = $this->getDateRange();

        $baseQuery = \App\Models\Project::whereBetween('created_at', [$dateRange['start'], $dateRange['end']]);

        return [
            'active' => $baseQuery->clone()->where('status', 'active')->count(),
            'completed' => $baseQuery->clone()->where('status', 'completed')->count(),
            'on_hold' => $baseQuery->clone()->where('status', 'on_hold')->count(),
            'cancelled' => $baseQuery->clone()->where('status', 'cancelled')->count(),
        ];
    }

    /**
     * Update date filter
     */
    public function updateDateFilter($filter)
    {
        $this->dateFilter = $filter;
        // The view will automatically re-render with new data
    }
}
