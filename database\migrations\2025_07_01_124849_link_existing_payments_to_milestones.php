<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Link existing payments to their corresponding milestones
        $this->linkPaymentsToMilestones();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove milestone_id links from payments
        DB::table('payments')->update(['milestone_id' => null]);
    }

    private function linkPaymentsToMilestones(): void
    {
        // Get all projects that have both milestones and payments
        $projects = DB::table('projects')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('milestones')
                    ->whereColumn('milestones.project_id', 'projects.id');
            })
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('payments')
                    ->whereColumn('payments.project_id', 'projects.id')
                    ->whereNull('payments.milestone_id');
            })
            ->get();

        foreach ($projects as $project) {
            // Get milestones and payments for this project, ordered by due_date
            $milestones = DB::table('milestones')
                ->where('project_id', $project->id)
                ->orderBy('due_date')
                ->get();

            $payments = DB::table('payments')
                ->where('project_id', $project->id)
                ->whereNull('milestone_id')
                ->orderBy('due_date')
                ->get();

            // Link payments to milestones based on matching due_date and amount
            foreach ($payments as $payment) {
                $matchingMilestone = $milestones->first(function ($milestone) use ($payment) {
                    return $milestone->due_date === $payment->due_date
                        && abs($milestone->amount - $payment->amount) < 0.01; // Allow for small floating point differences
                });

                if ($matchingMilestone) {
                    DB::table('payments')
                        ->where('id', $payment->id)
                        ->update(['milestone_id' => $matchingMilestone->id]);

                    echo "Linked payment {$payment->id} to milestone {$matchingMilestone->id} for project {$project->id}\n";
                }
            }
        }
    }
};
