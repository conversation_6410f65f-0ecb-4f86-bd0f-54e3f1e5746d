<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class RedirectByRole
{
    public function handle($request, Closure $next)
    {
        Log::debug('RedirectByRole: Middleware entered', [
            'path' => $request->path(),
            'authenticated' => auth()->check() ? 'yes' : 'no'
        ]);

        $response = $next($request);

        if (auth()->check()) {
            $user = auth()->user();
            $roles = $user->getRoleNames()->toArray();

            Log::debug('RedirectByRole: User check', [
                'user_id' => $user->id,
                'roles' => $roles,
                'current_path' => $request->path()
            ]);

            // Handle POST-login redirects
            if ($request->is('admin') || $request->is('admin/login')) {
                if ($user->hasRole('super_admin')) {
                    Log::info('Redirecting super_admin to /admin/dashboard');
                    return redirect('/admin/dashboard');
                }

                if ($user->hasAnyRole(['jr_bde_team', 'bde_team'])) {
                    Log::info('Redirecting BDE to /admin/home');
                    return redirect('/admin/home');
                }
            }

            // Allow super_admin to access both dashboards
            // No redirection needed
        }

        return $response;
    }
}
