<?php

namespace App\Filament\Forms;

use Filament\Forms;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Closure;

class ProjectFormComponents
{
    public static function getProjectFormSchema(bool $isRelationManager = false): array
    {
        $schema = [
            // Title and Won Date in single row (first row)
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\Select::make('project_type_id')
                        ->relationship('projectType', 'name')
                        ->label('Title')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            if ($state) {
                                $projectType = \App\Models\ProjectType::find($state);
                                $set('title', $projectType ? $projectType->name : 'New Project');

                                // Clear product selection when project type changes
                                if ($projectType && $projectType->name !== 'Product') {
                                    $set('product_id', null);
                                } else if ($projectType && $projectType->name === 'Product') {
                                    // Auto-select "Product" pricing model when Product project type is selected
                                    $productPricingModel = \App\Models\PricingModel::where('name', 'Product')->first();
                                    if ($productPricingModel) {
                                        $set('pricing_model_id', $productPricingModel->id);
                                    }
                                }
                            }
                        }),
                    Forms\Components\DatePicker::make('won_date')
                        ->required()
                        ->default(now())
                        ->label('Won Date'),
                ]),

            // Product Selection (only visible when project type is "Product")
            Forms\Components\Select::make('product_id')
                ->relationship('product', 'title')
                ->label('Product')
                ->searchable()
                ->preload()
                ->visible(function (Get $get) {
                    $projectTypeId = $get('project_type_id');
                    if (!$projectTypeId) return false;

                    $projectType = \App\Models\ProjectType::find($projectTypeId);
                    return $projectType && $projectType->name === 'Product';
                })
                ->required(function (Get $get) {
                    $projectTypeId = $get('project_type_id');
                    if (!$projectTypeId) return false;

                    $projectType = \App\Models\ProjectType::find($projectTypeId);
                    return $projectType && $projectType->name === 'Product';
                })
                ->live()
                ->afterStateUpdated(function (Set $set, Get $get, $state) {
                    // Update project title and other fields based on selected product
                    if ($state) {
                        $product = \App\Models\Product::find($state);
                        if ($product) {
                            $set('title', $product->title);
                            $set('total_payment', $product->amount);
                            $set('currency', $product->currency);
                            // Auto-select "Product" pricing model
                            $productPricingModel = \App\Models\PricingModel::where('name', 'Product')->first();
                            if ($productPricingModel) {
                                $set('pricing_model_id', $productPricingModel->id);
                            }

                            // Generate milestone and payment data for Product
                            self::generateProductMilestoneAndPayment($set, $get);
                        }
                    }
                }),

            // BDE and Pricing Model in single row (second row)
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\Select::make('user_id')
                        ->relationship('user', 'name', function (Builder $query) {
                            return $query->whereHas('roles', function (Builder $query) {
                                $query->whereIn('name', ['bde', 'bde_team']);
                            });
                        })
                        ->label('BDE')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            // Auto-update incentive method when BDE changes
                            $set('incentive_method', null);

                            // Auto-select incentive method if pricing model is already selected
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId && $state) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                if ($pricingModel) {
                                    $matchingMethod = $pricingModel->name;
                                    $set('incentive_method', $matchingMethod);
                                }
                            }
                        }),
                    Forms\Components\Select::make('pricing_model_id')
                        ->relationship('pricingModel', 'name')
                        ->label('Pricing Model')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            // Auto-update currency and incentive method when pricing model changes
                            if ($state) {
                                $pricingModel = \App\Models\PricingModel::find($state);
                                if ($pricingModel) {
                                    // Auto-update currency
                                    if (str_contains($pricingModel->name, 'USD')) {
                                        $set('currency', 'USD');
                                    } elseif (str_contains($pricingModel->name, 'INR')) {
                                        $set('currency', 'INR');
                                    }

                                    // Auto-update incentive method
                                    $userId = $get('user_id');
                                    if ($userId) {
                                        $matchingMethod = $pricingModel->name;
                                        $set('incentive_method', $matchingMethod);
                                    }
                                }
                            }
                        }),
                ]),

            // Currency and Total Payment in single row (third row)
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\Select::make('currency')
                        ->options([
                            'USD' => 'USD',
                            'INR' => 'INR',
                        ])
                        ->required()
                        ->default('USD')
                        ->label('Currency'),
                    Forms\Components\TextInput::make('total_payment')
                        ->numeric()
                        ->required()
                        ->label('Total Payment')
                        ->prefix(fn (Get $get): string => $get('currency') ?? 'USD'),
                ]),

            // Duration, Duration Unit, Payment Cycle, Start Date, End Date (for Product pricing model, show all fields like other models)
            Forms\Components\Grid::make(5)
                ->schema([
                    Forms\Components\TextInput::make('duration')
                        ->required(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->visible(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->numeric()
                        ->minValue(1)
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            self::calculateEndDate($set, $get);
                            self::generateMilestonesAndPayments($set, $get);
                        }),
                    Forms\Components\Select::make('duration_unit')
                        ->options([
                            'hours' => 'Hours',
                            'days' => 'Days',
                            'weeks' => 'Weeks',
                            'months' => 'Months',
                            'years' => 'Years',
                        ])
                        ->required(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->visible(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            self::calculateEndDate($set, $get);
                            self::generateMilestonesAndPayments($set, $get);
                        }),
                    Forms\Components\Select::make('payment_cycle')
                        ->options([
                            'weekly' => 'Weekly',
                            'biweekly' => 'Bi-weekly',
                            'monthly' => 'Monthly',
                            'quarterly' => 'Quarterly',
                            'yearly' => 'Yearly',
                            'milestone' => 'Milestone-based',
                            'upfront' => 'Upfront',
                        ])
                        ->required(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->visible(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->live()
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            self::generateMilestonesAndPayments($set, $get);
                        }),
                    Forms\Components\DatePicker::make('start_date')
                        ->required(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->visible(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->live()
                        ->label('Start Date')
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            self::calculateEndDate($set, $get);
                            self::generateMilestonesAndPayments($set, $get);
                        }),
                    Forms\Components\DatePicker::make('end_date')
                        ->label('End Date')
                        ->helperText('Auto-calculated based on start date and duration, but can be manually edited')
                        ->visible(function (Get $get) {
                            $pricingModelId = $get('pricing_model_id');
                            if ($pricingModelId) {
                                $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                                return $pricingModel && $pricingModel->name === 'Product';
                            }
                            return true;
                        })
                        ->live(),
                ])
                ->visible(function (Get $get) {
                    $pricingModelId = $get('pricing_model_id');
                    if ($pricingModelId) {
                        $pricingModel = \App\Models\PricingModel::find($pricingModelId);
                        return $pricingModel && $pricingModel->name === 'Product';
                    }
                    return true;
                }),

            // Status field
            Forms\Components\Select::make('status')
                ->options([
                    'active' => 'Active',
                    'completed' => 'Completed',
                    'on_hold' => 'On Hold',
                    'cancelled' => 'Cancelled',
                ])
                ->required()
                ->default('active')
                ->label('Status'),

            // Hidden title field (auto-populated)
            Forms\Components\Hidden::make('title')
                ->default(function (Get $get) {
                    $projectTypeId = $get('project_type_id');
                    if ($projectTypeId) {
                        $projectType = \App\Models\ProjectType::find($projectTypeId);
                        return $projectType ? $projectType->name : 'New Project';
                    }
                    return 'New Project';
                }),

            // Hidden fields to store generated data
            Forms\Components\Hidden::make('_generated_milestones'),
            Forms\Components\Hidden::make('_generated_payments'),
        ];

        // Add client_id field for relation manager
        if ($isRelationManager) {
            array_unshift($schema,
                Forms\Components\Hidden::make('client_id')
                    ->default(fn ($livewire) => $livewire->ownerRecord->id)
            );
        } else {
            // Add client selection for standalone form
            array_unshift($schema,
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'company_name', function (Builder $query) {
                        // Non-super admin users should only see their own clients
                        if (auth()->check() && !auth()->user()->hasRole('super_admin')) {
                            $query->where('created_by', auth()->id());
                        }
                        return $query;
                    })
                    ->label('Client')
                    ->searchable()
                    ->preload()
                    ->required()
            );
        }

        return $schema;
    }

    public static function getProjectTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('title')
                ->searchable()
                ->sortable(),
            Tables\Columns\TextColumn::make('client.company_name')
                ->label('Client')
                ->searchable()
                ->sortable()
                ->toggleable(),
            Tables\Columns\TextColumn::make('user.name')
                ->label('BDE')
                ->searchable()
                ->sortable(),
            Tables\Columns\TextColumn::make('pricingModel.name')
                ->label('Pricing Model')
                ->searchable()
                ->sortable(),
            Tables\Columns\TextColumn::make('total_payment')
                ->label('Total Payment')
                ->money(fn ($record) => $record->currency ?? 'USD')
                ->sortable(),
            Tables\Columns\TextColumn::make('status')
                ->badge()
                ->color(fn (string $state): string => match ($state) {
                    'active' => 'success',
                    'completed' => 'info',
                    'on_hold' => 'warning',
                    'cancelled' => 'danger',
                })
                ->sortable(),
            Tables\Columns\TextColumn::make('won_date')
                ->date()
                ->sortable(),
        ];
    }

    /**
     * Calculate end date based on start date, duration and duration unit
     */
    public static function calculateEndDate(Set $set, Get $get)
    {
        $startDate = $get('start_date');
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');

        if (!$startDate || !$duration || !$durationUnit) {
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration; // Convert to integer

            switch ($durationUnit) {
                case 'hours':
                    $endDate = $startCarbon->copy()->addHours($durationValue);
                    break;
                case 'days':
                    $endDate = $startCarbon->copy()->addDays($durationValue);
                    break;
                case 'weeks':
                    $endDate = $startCarbon->copy()->addWeeks($durationValue);
                    break;
                case 'months':
                    $endDate = $startCarbon->copy()->addMonths($durationValue - 1)->endOfMonth();
                    break;
                case 'years':
                    $endDate = $startCarbon->copy()->addYears($durationValue);
                    break;
                default:
                    return;
            }

            $set('end_date', $endDate->format('Y-m-d'));
        } catch (\Exception $e) {
            // Handle date parsing errors silently
        }
    }

    /**
     * Generate milestones and payments based on duration, payment cycle, and total payment
     */
    public static function generateMilestonesAndPayments(Set $set, Get $get)
    {
        $pricingModelId = $get('pricing_model_id');

        // Check if pricing model is Fixed-USD or Fixed-INR - don't generate milestones for these
        if ($pricingModelId) {
            $pricingModel = \App\Models\PricingModel::find($pricingModelId);
            if ($pricingModel && in_array($pricingModel->name, ['Fixed-USD', 'Fixed-INR'])) {
                // Clear any existing generated data for fixed pricing models
                $set('_generated_milestones', '');
                $set('_generated_payments', '');
                return;
            }
        }

        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');
        $startDate = $get('start_date');
        $totalPayment = $get('total_payment');

        // Only generate if all required fields are present and valid
        if (!$duration || !$durationUnit || !$paymentCycle || !$startDate || !$totalPayment) {
            // Clear any existing generated data
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        // Validate numeric values to prevent infinite loops
        if (!is_numeric($duration) || !is_numeric($totalPayment) || $duration <= 0 || $totalPayment <= 0) {
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration; // Convert to integer
            $totalPaymentValue = (float) $totalPayment; // Convert to float
            $milestones = [];
            $payments = [];

            // Calculate number of payment cycles
            $numberOfCycles = self::calculateNumberOfCycles($durationValue, $durationUnit, $paymentCycle);

            if ($numberOfCycles <= 0) {
                return;
            }

            // Prevent generating too many cycles (max 100 to prevent timeouts)
            if ($numberOfCycles > 100) {
                $numberOfCycles = 100;
            }

            // Calculate amount per cycle
            $amountPerCycle = $totalPaymentValue / $numberOfCycles;
            $percentagePerCycle = 100 / $numberOfCycles;

            // Generate milestones and payments
            for ($i = 1; $i <= $numberOfCycles; $i++) {
                $dueDate = self::calculateDueDate($startCarbon, $i, $paymentCycle, $durationValue, $durationUnit);

                $milestones[] = [
                    'title' => self::generateMilestoneTitle($i, $paymentCycle),
                    'description' => "Auto-generated milestone {$i} of {$numberOfCycles}",
                    'due_date' => $dueDate->format('Y-m-d'),
                    'percentage' => round($percentagePerCycle, 2),
                    'amount' => round($amountPerCycle, 2),
                    'status' => 'pending',
                ];

                $payments[] = [
                    'amount' => round($amountPerCycle, 2),
                    'due_date' => $dueDate->format('Y-m-d'),
                    'status' => 'pending',
                ];
            }

            // Store the generated data in hidden fields
            $set('_generated_milestones', json_encode($milestones));
            $set('_generated_payments', json_encode($payments));

        } catch (\Exception $e) {
            // Handle errors silently
        }
    }

    /**
     * Calculate number of payment cycles based on duration and payment cycle
     */
    private static function calculateNumberOfCycles($duration, $durationUnit, $paymentCycle)
    {
        switch ($paymentCycle) {
            case 'hourly':
                $totalHours = self::convertToHours($duration, $durationUnit);
                return $totalHours;
            case 'daily':
                $totalHours = self::convertToHours($duration, $durationUnit);
                return ceil($totalHours / 24);
            case 'weekly':
                if ($durationUnit === 'weeks') {
                    return $duration;
                } elseif ($durationUnit === 'months') {
                    return $duration * 4; // Approximate 4 weeks per month
                } else {
                    $totalHours = self::convertToHours($duration, $durationUnit);
                    return ceil($totalHours / (24 * 7));
                }
            case 'biweekly':
                if ($durationUnit === 'weeks') {
                    return ceil($duration / 2);
                } elseif ($durationUnit === 'months') {
                    return $duration * 2; // Approximate 2 biweekly periods per month
                } else {
                    $totalHours = self::convertToHours($duration, $durationUnit);
                    return ceil($totalHours / (24 * 14));
                }
            case 'monthly':
                if ($durationUnit === 'months') {
                    return $duration; // One payment per month
                } elseif ($durationUnit === 'years') {
                    return $duration * 12; // 12 months per year
                } else {
                    $totalHours = self::convertToHours($duration, $durationUnit);
                    return ceil($totalHours / (24 * 30)); // Approximate
                }
            case 'quarterly':
                if ($durationUnit === 'months') {
                    return ceil($duration / 3); // 3 months per quarter
                } elseif ($durationUnit === 'years') {
                    return $duration * 4; // 4 quarters per year
                } else {
                    $totalHours = self::convertToHours($duration, $durationUnit);
                    return ceil($totalHours / (24 * 90)); // Approximate
                }
            case 'yearly':
                if ($durationUnit === 'years') {
                    return $duration;
                } elseif ($durationUnit === 'months') {
                    return ceil($duration / 12); // 12 months per year
                } else {
                    $totalHours = self::convertToHours($duration, $durationUnit);
                    return ceil($totalHours / (24 * 365)); // Approximate
                }
            case 'milestone':
                // For milestone-based, create milestones based on duration
                if ($durationUnit === 'months') {
                    return $duration; // One milestone per month
                } elseif ($durationUnit === 'weeks') {
                    return $duration; // One milestone per week
                } elseif ($durationUnit === 'years') {
                    return $duration * 12; // One milestone per month for years
                } else {
                    return max(1, ceil($duration / 4)); // Default to quarters
                }
            case 'upfront':
                return 1;
            default:
                return 1;
        }
    }

    /**
     * Convert duration to hours
     */
    private static function convertToHours($duration, $durationUnit)
    {
        switch ($durationUnit) {
            case 'hours':
                return $duration;
            case 'days':
                return $duration * 24;
            case 'weeks':
                return $duration * 24 * 7;
            case 'months':
                return $duration * 24 * 30; // Approximate
            case 'years':
                return $duration * 24 * 365; // Approximate
            default:
                return $duration;
        }
    }

    /**
     * Calculate due date for a specific cycle
     */
    private static function calculateDueDate($startDate, $cycleNumber, $paymentCycle, $duration, $durationUnit)
    {
        $cycleNum = (int) $cycleNumber;
        $durationVal = (int) $duration;

        switch ($paymentCycle) {
            case 'hourly':
                return $startDate->copy()->addHours($cycleNum);
            case 'daily':
                return $startDate->copy()->addDays($cycleNum);
            case 'weekly':
                return $startDate->copy()->addWeeks($cycleNum);
            case 'biweekly':
                return $startDate->copy()->addWeeks($cycleNum * 2);
            case 'monthly':
                // For monthly payments, set due date to end of each month
                $dueDate = $startDate->copy()->addMonths($cycleNum - 1);
                return $dueDate->endOfMonth();
            case 'quarterly':
                return $startDate->copy()->addMonths($cycleNum * 3);
            case 'yearly':
                return $startDate->copy()->addYears($cycleNum);
            case 'milestone':
                // Distribute milestones evenly across the project duration
                if ($durationUnit === 'months') {
                    // For monthly milestones, set due date to end of each month
                    $dueDate = $startDate->copy()->addMonths($cycleNum - 1);
                    return $dueDate->endOfMonth();
                } elseif ($durationUnit === 'weeks') {
                    return $startDate->copy()->addWeeks($cycleNum);
                } else {
                    $totalCycles = self::calculateNumberOfCycles($durationVal, $durationUnit, $paymentCycle);
                    $intervalDays = ceil(self::convertToHours($durationVal, $durationUnit) / 24 / $totalCycles);
                    return $startDate->copy()->addDays($intervalDays * $cycleNum);
                }
            case 'upfront':
                return $startDate->copy();
            default:
                return $startDate->copy()->addMonths($cycleNum);
        }
    }

    /**
     * Generate milestone title based on cycle and payment type
     */
    private static function generateMilestoneTitle($cycleNumber, $paymentCycle)
    {
        switch ($paymentCycle) {
            case 'hourly':
                return "Hour {$cycleNumber} Milestone";
            case 'daily':
                return "Day {$cycleNumber} Milestone";
            case 'weekly':
                return "Week {$cycleNumber} Milestone";
            case 'biweekly':
                return "Bi-week {$cycleNumber} Milestone";
            case 'monthly':
                return "Month {$cycleNumber} Milestone";
            case 'quarterly':
                return "Quarter {$cycleNumber} Milestone";
            case 'yearly':
                return "Year {$cycleNumber} Milestone";
            case 'milestone':
                return "Milestone {$cycleNumber}";
            case 'upfront':
                return "Upfront Payment";
            default:
                return "Milestone {$cycleNumber}";
        }
    }

    /**
     * Generate milestone and payment for Product projects
     */
    public static function generateProductMilestoneAndPayment(Set $set, Get $get)
    {
        $productId = $get('product_id');
        $deliveryDate = $get('delivery_date');
        $totalPayment = $get('total_payment');
        $currency = $get('currency');

        // Only generate if all required fields are present
        if (!$productId || !$deliveryDate || !$totalPayment) {
            // Clear any existing generated data
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        try {
            // Get product details
            $product = \App\Models\Product::find($productId);
            if (!$product) {
                return;
            }

            // Create single milestone for product delivery
            $milestones = [[
                'title' => 'Product Delivery - ' . $product->title,
                'description' => 'Product delivery milestone for ' . $product->title,
                'due_date' => $deliveryDate,
                'percentage' => 100, // Single milestone = 100%
                'amount' => (float) $totalPayment,
                'status' => 'pending',
                'currency' => $currency ?? 'INR',
            ]];

            // Create single payment for the milestone
            $payments = [[
                'amount' => (float) $totalPayment,
                'due_date' => $deliveryDate,
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'currency' => $currency ?? 'INR',
            ]];

            // Store the generated data in hidden fields (same pattern as regular projects)
            $set('_generated_milestones', json_encode($milestones));
            $set('_generated_payments', json_encode($payments));

        } catch (\Exception $e) {
            // Handle errors silently
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
        }
    }
}
