[2025-07-02 05:55:50] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"project_created","user_ids":[19],"data":{"project_title":"Website Development","project_id":165,"client_name":" Global FinServe LLP","total_payment":"5000.00","start_date":"2025-07-01 00:00:00","end_date":"2025-07-15 00:00:00"}} 
[2025-07-02 05:55:51] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"project_created","user_ids":[1],"data":{"project_title":"Website Development","project_id":165,"client_name":" Global FinServe LLP","total_payment":"5000.00","start_date":"2025-07-01 00:00:00","end_date":"2025-07-15 00:00:00"}} 
[2025-07-02 05:56:17] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 05:56:21] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 05:56:59] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 05:57:00] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 05:58:14] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/home","authenticated":"yes"} 
[2025-07-02 05:58:15] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/home"} 
[2025-07-02 05:58:23] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 05:58:23] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/app-notifications"} 
[2025-07-02 05:58:40] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 05:58:40] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/app-notifications"} 
[2025-07-02 05:58:51] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/clients","authenticated":"yes"} 
[2025-07-02 05:58:52] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/clients"} 
[2025-07-02 05:59:12] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/role-notification-settings","authenticated":"yes"} 
[2025-07-02 05:59:12] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/role-notification-settings"} 
[2025-07-02 05:59:18] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/role-notification-settings/create","authenticated":"yes"} 
[2025-07-02 05:59:20] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/role-notification-settings/create"} 
[2025-07-02 05:59:56] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/role-notification-settings","authenticated":"yes"} 
[2025-07-02 05:59:56] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/role-notification-settings"} 
[2025-07-02 06:00:03] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/clients","authenticated":"yes"} 
[2025-07-02 06:00:03] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/clients"} 
[2025-07-02 06:00:17] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects","authenticated":"yes"} 
[2025-07-02 06:00:17] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects"} 
[2025-07-02 06:00:22] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 06:00:23] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_completed","user_ids":[19],"data":{"milestone_id":117,"milestone_title":"Month 1","project_title":"Digital Marketing","status":"completed"}} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment updated {"payment_id":420,"status":"paid","original_status":"pending","is_dirty_status":true,"milestone_id":117,"project_id":165} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment marked as paid, creating incentive {"payment_id":420,"new_status":"paid","old_status":"pending"} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Searching for incentive rules {"project_id":165,"user_id":19,"user_role_ids":[16],"pricing_model_name":"Contract/Digital Marketing","product_id":null} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Excluding product-specific rules for non-Product pricing model {"project_id":165,"pricing_model_name":"Contract/Digital Marketing"} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Found incentive rules {"project_id":165,"rules_count":1,"rules":[{"id":104,"pricing_model":"Contract/Digital Marketing","role_id":16,"role_name":"jr_bde_team","product_id":null,"product_name":null,"currency":"INR","amount":null}]} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Starting payment incentive calculation {"payment_id":420,"project_id":165,"pricing_model":"Contract/Digital Marketing","milestone_id":117,"milestone_percentage":"8.33"} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Contract/Digital Marketing incentive calculation {"payment_id":420,"milestone_id":117,"milestone_title":"Month 1","milestone_individual_percentage":"8.33","cumulative_percentage":8.33,"incentive_duration_percentage":"25.00","total_project_months":12,"incentive_period_months":3.0,"contract_length":"25.00","incentive_percentage":"5.00"} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Incentive calculated for payment within period {"payment_id":420,"payment_amount":"1000.00","incentive_percentage":"5.00","calculated_incentive":50.0} 
[2025-07-02 06:01:12] local.INFO: [INCENTIVE_OBSERVER] Incentive created {"incentive_id":92,"user_id":19,"status":"pending"} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"incentive_submitted","user_ids":[1],"data":{"incentive_id":92,"user_name":"Falak khan","project_title":"Digital Marketing","amount":"50.00","calculation_date":"2025-07-02 00:00:00"}} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment incentive created successfully {"payment_id":420,"incentive_id":92,"amount":50.0,"rule_id":104} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_received","user_ids":[19],"data":{"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 1","milestone_id":117,"project_id":165,"client_name":" Global FinServe LLP"}} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_received","user_ids":[1],"data":{"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 1","milestone_id":117,"project_id":165,"client_name":" Global FinServe LLP"}} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_completed","user_ids":[19],"data":{"milestone_id":118,"milestone_title":"Month 2","project_title":"Digital Marketing","status":"completed"}} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment updated {"payment_id":421,"status":"paid","original_status":"pending","is_dirty_status":true,"milestone_id":118,"project_id":165} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment marked as paid, creating incentive {"payment_id":421,"new_status":"paid","old_status":"pending"} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Searching for incentive rules {"project_id":165,"user_id":19,"user_role_ids":[16],"pricing_model_name":"Contract/Digital Marketing","product_id":null} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Excluding product-specific rules for non-Product pricing model {"project_id":165,"pricing_model_name":"Contract/Digital Marketing"} 
[2025-07-02 06:01:12] local.INFO: [PROJECT] Found incentive rules {"project_id":165,"rules_count":1,"rules":[{"id":104,"pricing_model":"Contract/Digital Marketing","role_id":16,"role_name":"jr_bde_team","product_id":null,"product_name":null,"currency":"INR","amount":null}]} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Starting payment incentive calculation {"payment_id":421,"project_id":165,"pricing_model":"Contract/Digital Marketing","milestone_id":118,"milestone_percentage":"8.33"} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Contract/Digital Marketing incentive calculation {"payment_id":421,"milestone_id":118,"milestone_title":"Month 2","milestone_individual_percentage":"8.33","cumulative_percentage":16.66,"incentive_duration_percentage":"25.00","total_project_months":12,"incentive_period_months":3.0,"contract_length":"25.00","incentive_percentage":"5.00"} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Incentive calculated for payment within period {"payment_id":421,"payment_amount":"1000.00","incentive_percentage":"5.00","calculated_incentive":50.0} 
[2025-07-02 06:01:12] local.INFO: [INCENTIVE_OBSERVER] Incentive created {"incentive_id":93,"user_id":19,"status":"pending"} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"incentive_submitted","user_ids":[1],"data":{"incentive_id":93,"user_name":"Falak khan","project_title":"Digital Marketing","amount":"50.00","calculation_date":"2025-07-02 00:00:00"}} 
[2025-07-02 06:01:12] local.INFO: [PAYMENT_OBSERVER] Payment incentive created successfully {"payment_id":421,"incentive_id":93,"amount":50.0,"rule_id":104} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_received","user_ids":[19],"data":{"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 2","milestone_id":118,"project_id":165,"client_name":" Global FinServe LLP"}} 
[2025-07-02 06:01:12] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_received","user_ids":[1],"data":{"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 2","milestone_id":118,"project_id":165,"client_name":" Global FinServe LLP"}} 
[2025-07-02 06:01:17] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 06:01:17] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 06:01:41] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/clients","authenticated":"yes"} 
[2025-07-02 06:01:42] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/clients"} 
[2025-07-02 06:02:05] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:02:06] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:02:45] local.INFO: [INCENTIVE_OBSERVER] Incentive status changed {"incentive_id":93,"user_id":19,"old_status":"pending","new_status":"paid"} 
[2025-07-02 06:02:45] local.INFO: [INCENTIVE_OBSERVER] Sending notification to user {"user_id":19,"status":"paid"} 
[2025-07-02 06:02:45] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"incentive_status_changed","user_ids":[19],"data":{"incentive_id":93,"user_name":"Falak khan","project_title":"Digital Marketing","amount":"50.00","status":"paid"}} 
[2025-07-02 06:04:45] local.INFO: [INCENTIVE_OBSERVER] Incentive status changed {"incentive_id":92,"user_id":19,"old_status":"pending","new_status":"paid"} 
[2025-07-02 06:04:45] local.INFO: [INCENTIVE_OBSERVER] Sending notification to user {"user_id":19,"status":"paid"} 
[2025-07-02 06:04:45] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"incentive_status_changed","user_ids":[19],"data":{"incentive_id":92,"user_name":"Falak khan","project_title":"Digital Marketing","amount":"50.00","status":"paid"}} 
[2025-07-02 06:05:04] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:05:05] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:05:45] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 06:05:46] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 06:05:58] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:05:59] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:06:15] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/clients","authenticated":"yes"} 
[2025-07-02 06:06:16] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/clients"} 
[2025-07-02 06:06:20] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/clients/26/edit","authenticated":"yes"} 
[2025-07-02 06:06:22] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/clients/26/edit"} 
[2025-07-02 06:06:49] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"project_updated","user_ids":[19],"data":{"project_title":"Digital Marketing","project_id":165,"client_name":" Global FinServe LLP","total_payment":"12000.00","start_date":"2025-07-01 00:00:00","end_date":"2026-06-30 00:00:00"}} 
[2025-07-02 06:06:49] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"project_updated","user_ids":[1],"data":{"project_title":"Digital Marketing","project_id":165,"client_name":" Global FinServe LLP","total_payment":"12000.00","start_date":"2025-07-01 00:00:00","end_date":"2026-06-30 00:00:00"}} 
[2025-07-02 06:07:04] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:07:04] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:07:41] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/home","authenticated":"yes"} 
[2025-07-02 06:07:41] local.DEBUG: RedirectByRole: User check {"user_id":19,"roles":["jr_bde_team"],"current_path":"admin/home"} 
[2025-07-02 06:10:14] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-02 06:10:14] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/payments"} 
[2025-07-02 06:11:01] local.INFO: [PAYMENT_OBSERVER] Payment updated {"payment_id":422,"status":"pending","original_status":"pending","is_dirty_status":false,"milestone_id":119,"project_id":165} 
[2025-07-02 06:11:43] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-02 06:11:44] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/payments"} 
[2025-07-02 06:11:53] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:11:53] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:14:06] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects","authenticated":"yes"} 
[2025-07-02 06:14:06] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects"} 
[2025-07-02 06:14:11] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 06:14:12] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 06:15:09] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[1],"data":{"milestone_id":119,"project_id":165,"milestone_title":"Month 3","project_title":"Digital Marketing","due_date":"2025-06-30","days_overdue":2.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:15:09] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[19],"data":{"milestone_id":119,"project_id":165,"milestone_title":"Month 3","project_title":"Digital Marketing","due_date":"2025-06-30","days_overdue":2.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:15:16] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/165/edit","authenticated":"yes"} 
[2025-07-02 06:15:17] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/165/edit"} 
[2025-07-02 06:15:27] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:15:28] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:19:04] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:19:05] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:19:37] local.ERROR: Command "payment:due-notifications" is not defined.

Did you mean one of these?
    make:notification
    payments:send-due-date-notifications
    payments:send-due-notifications {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"payment:due-notifications\" is not defined.

Did you mean one of these?
    make:notification
    payments:send-due-date-notifications
    payments:send-due-notifications at D:\\wamp64\\www\\smms\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 D:\\wamp64\\www\\smms\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('payment:due-not...')
#1 D:\\wamp64\\www\\smms\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\wamp64\\www\\smms\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Starting payment due date check {"timestamp":"2025-07-02 06:19:57"} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Checking due payments {"current_date":"2025-07-02","check_until_date":"2025-07-05"} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Found due payments {"count":1,"payments":{"Illuminate\\Support\\Collection":[{"id":422,"amount":"1000.00","due_date":"2025-07-04","project_title":"Digital Marketing","milestone_title":"Month 3"}]}} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Processing due payment {"payment_id":422,"project_title":"Digital Marketing","milestone_title":"Month 3","due_date":"2025-07-04","days_until_due":2.0,"is_overdue":false,"amount":"1000.00","bde_user_id":19,"client_name":" Global FinServe LLP"} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Sending notification to BDE {"payment_id":422,"bde_user_id":19,"bde_name":"Falak khan"} 
[2025-07-02 06:19:57] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_due","user_ids":[19],"data":{"payment_id":422,"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 3","milestone_id":119,"project_id":165,"client_name":" Global FinServe LLP","due_date":"2025-07-04","days_until_due":2.0,"is_overdue":false,"status_text":"due in 2 days"}} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Sending notification to super admin {"payment_id":422,"admin_id":1,"admin_name":"Admin User"} 
[2025-07-02 06:19:57] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_due","user_ids":[1],"data":{"payment_id":422,"payment_amount":"1000.00","project_title":"Digital Marketing","milestone_title":"Month 3","milestone_id":119,"project_id":165,"client_name":" Global FinServe LLP","due_date":"2025-07-04","days_until_due":2.0,"is_overdue":false,"status_text":"due in 2 days"}} 
[2025-07-02 06:19:57] local.INFO: [PAYMENT_DUE] Payment due date check completed  
[2025-07-02 06:21:00] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:21:02] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:23:47] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/milestones","authenticated":"yes"} 
[2025-07-02 06:23:48] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/milestones"} 
[2025-07-02 06:25:13] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[1],"data":{"milestone_id":119,"project_id":165,"milestone_title":"Month 3","project_title":"Digital Marketing","due_date":"2025-06-30","days_overdue":2.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:25:13] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[19],"data":{"milestone_id":119,"project_id":165,"milestone_title":"Month 3","project_title":"Digital Marketing","due_date":"2025-06-30","days_overdue":2.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:25:13] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[1],"data":{"milestone_id":120,"project_id":165,"milestone_title":"Month 4","project_title":"Digital Marketing","due_date":"2025-06-29","days_overdue":3.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:25:13] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"milestone_overdue","user_ids":[19],"data":{"milestone_id":120,"project_id":165,"milestone_title":"Month 4","project_title":"Digital Marketing","due_date":"2025-06-29","days_overdue":3.0,"bde_name":"Falak khan"}} 
[2025-07-02 06:25:21] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:25:22] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:25:58] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-02 06:25:58] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/payments"} 
[2025-07-02 06:26:33] local.INFO: [PAYMENT_OBSERVER] Payment updated {"payment_id":422,"status":"pending","original_status":"pending","is_dirty_status":false,"milestone_id":119,"project_id":165} 
[2025-07-02 06:26:58] local.INFO: [NOTIFICATION_SERVICE] Sending notification {"event":"payment_due","user_ids":[19],"data":{"payment_id":422,"payment_amount":"1000.00","project_title":"Digital Marketing","due_date":"2025-06-27 00:00:00"}} 
[2025-07-02 06:27:06] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-02 06:27:07] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/payments"} 
[2025-07-02 06:27:24] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:27:25] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:28:10] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:28:11] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-02 06:29:23] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-02 06:29:24] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
