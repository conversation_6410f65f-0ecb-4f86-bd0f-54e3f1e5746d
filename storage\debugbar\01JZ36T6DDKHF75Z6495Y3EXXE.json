{"__meta": {"id": "01JZ36T6DDKHF75Z6495Y3EXXE", "datetime": "2025-07-01 14:38:48", "utime": **********.237822, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.505468, "end": **********.237834, "duration": 0.7323660850524902, "duration_str": "732ms", "measures": [{"label": "Booting", "start": **********.505468, "relative_start": 0, "end": **********.873531, "relative_end": **********.873531, "duration": 0.*****************, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.873546, "relative_start": 0.*****************, "end": **********.237835, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.11438, "relative_start": 0.****************, "end": **********.116839, "relative_end": **********.116839, "duration": 0.0024590492248535156, "duration_str": "2.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.235847, "relative_start": 0.****************, "end": **********.236462, "relative_end": **********.236462, "duration": 0.0006151199340820312, "duration_str": "615μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 39, "nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.022529999999999994, "accumulated_duration_str": "22.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv' limit 1", "type": "query", "params": [], "bindings": ["6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.123553, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.796}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.137079, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.796, "width_percent": 2.796}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.140646, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.593, "width_percent": 3.063}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.143861, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.655, "width_percent": 2.264}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.145232, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.919, "width_percent": 1.332}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1522732, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.25, "width_percent": 2.13}, {"sql": "select * from `payments` where year(`paid_date`) = 2025 and `status` in ('completed', 'paid') and `paid_date` is not null", "type": "query", "params": [], "bindings": [2025, "completed", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.1535869, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.381, "width_percent": 5.016}, {"sql": "select `id`, `currency` from `projects` where `projects`.`id` in (160, 161, 162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.1573558, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.396, "width_percent": 2.708}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.173211, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.104, "width_percent": 1.953}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.174355, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.057, "width_percent": 1.065}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1760461, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.122, "width_percent": 2.885}, {"sql": "select * from `roles` where `name` = 'bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.1789472, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.007, "width_percent": 1.997}, {"sql": "select * from `roles` where `name` = 'jr_bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["jr_bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.180002, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.004, "width_percent": 2.175}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (15, 16))", "type": "query", "params": [], "bindings": ["App\\Models\\User", 15, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.184051, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.179, "width_percent": 13.404}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4, 6, 19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.188199, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.584, "width_percent": 1.687}, {"sql": "select * from `projects` where `projects`.`user_id` in (4, 6, 19) and `won_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.190208, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.27, "width_percent": 3.285}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1981049, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.555, "width_percent": 1.909}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1990612, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.463, "width_percent": 1.332}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '02'", "type": "query", "params": [], "bindings": ["paid", 2025, "02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.200741, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.795, "width_percent": 2.13}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '03'", "type": "query", "params": [], "bindings": ["paid", 2025, "03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.2018359, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.925, "width_percent": 1.731}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '04'", "type": "query", "params": [], "bindings": ["paid", 2025, "04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.203295, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.656, "width_percent": 2.929}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '05'", "type": "query", "params": [], "bindings": ["paid", 2025, "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.2047338, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.586, "width_percent": 2.53}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '06'", "type": "query", "params": [], "bindings": ["paid", 2025, "06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.206456, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.116, "width_percent": 3.329}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '07'", "type": "query", "params": [], "bindings": ["paid", 2025, "07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.2078578, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.445, "width_percent": 1.82}, {"sql": "select * from `projects` where `status` in ('planning', 'in_progress')", "type": "query", "params": [], "bindings": ["planning", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}], "start": **********.209315, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:252", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=252", "ajax": false, "filename": "RevenueForecastChart.php", "line": "252"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.265, "width_percent": 1.332}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.215911, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.596, "width_percent": 3.506}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.217723, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.103, "width_percent": 1.553}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '02' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["02", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.21914, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.656, "width_percent": 1.553}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '02' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["02", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.220135, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.209, "width_percent": 2.219}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '03' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["03", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.221142, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.429, "width_percent": 0.976}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '03' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["03", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.221973, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.405, "width_percent": 3.773}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '04' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["04", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.22351, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.178, "width_percent": 1.687}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '04' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["04", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.22507, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.865, "width_percent": 2.13}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '05' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["05", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.226079, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.995, "width_percent": 1.065}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '05' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["05", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.22686, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.06, "width_percent": 1.642}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '06' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.227687, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.703, "width_percent": 0.932}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '06' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["06", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.22842, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.635, "width_percent": 2.042}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '07' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.229405, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.676, "width_percent": 1.909}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '07' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["07", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.230563, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.585, "width_percent": 5.415}]}, "models": {"data": {"App\\Models\\Payment": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.monthly-revenue-chart #dCKoVLTyyfxpJ7yLUSrd": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"current_year\"\n    \"dataChecksum\" => \"cd90f05fa1811a21a22ad08c895b2b70\"\n  ]\n  \"name\" => \"app.filament.widgets.monthly-revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MonthlyRevenueChart\"\n  \"id\" => \"dCKoVLTyyfxpJ7yLUSrd\"\n]", "app.filament.widgets.bde-performance-chart #Ei2EQTssRLW0xXd3Y3UL": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"this_month\"\n    \"dataChecksum\" => \"705f9f6a260c944b564b927a292dad89\"\n  ]\n  \"name\" => \"app.filament.widgets.bde-performance-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\BdePerformanceChart\"\n  \"id\" => \"Ei2EQTssRLW0xXd3Y3UL\"\n]", "app.filament.widgets.revenue-forecast-chart #eT64of418ZkdohjkUG9a": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"6_months\"\n    \"dataChecksum\" => \"0b4287a0535fd4b3b3dc29e9fa3d8c06\"\n  ]\n  \"name\" => \"app.filament.widgets.revenue-forecast-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueForecastChart\"\n  \"id\" => \"eT64of418ZkdohjkUG9a\"\n]", "app.filament.widgets.milestone-due-vs-received-chart #rII0eXH03YA4nbb34v3b": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"last_6_months\"\n    \"dataChecksum\" => \"6edec273f189350582f767f28c874ffb\"\n  ]\n  \"name\" => \"app.filament.widgets.milestone-due-vs-received-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MilestoneDueVsReceivedChart\"\n  \"id\" => \"rII0eXH03YA4nbb34v3b\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\MonthlyRevenueChart@updateChartData<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "735ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1910719982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1910719982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-867941139 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"357 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;current_year&quot;,&quot;dataChecksum&quot;:&quot;cd90f05fa1811a21a22ad08c895b2b70&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;dCKoVLTyyfxpJ7yLUSrd&quot;,&quot;name&quot;:&quot;app.filament.widgets.monthly-revenue-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5ca0561ff7e19ef83c1611da127c08de4f62b23631a2c7477348e0d42f566b55&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"355 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;this_month&quot;,&quot;dataChecksum&quot;:&quot;705f9f6a260c944b564b927a292dad89&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;Ei2EQTssRLW0xXd3Y3UL&quot;,&quot;name&quot;:&quot;app.filament.widgets.bde-performance-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;50b2baa4deedf28a0522b15fcc2fa0ee071a089eb4e7727daef86b934d6cb1fe&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;6_months&quot;,&quot;dataChecksum&quot;:&quot;0b4287a0535fd4b3b3dc29e9fa3d8c06&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;eT64of418ZkdohjkUG9a&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-forecast-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;12d9206e54e9075db52d33571a26c69f691239ec03a3b4de78305b89089fc183&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"368 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;last_6_months&quot;,&quot;dataChecksum&quot;:&quot;6edec273f189350582f767f28c874ffb&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;rII0eXH03YA4nbb34v3b&quot;,&quot;name&quot;:&quot;app.filament.widgets.milestone-due-vs-received-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f6227b9be3f1a4aff0bae62470adc23495e22757e3812e82dd0784a47675cdfd&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867941139\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1620319737 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2246</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjRDZXJsRHN1Ui9CUGlYZnhvT2pzS1E9PSIsInZhbHVlIjoiQk41QzFEOUh2Z1ZZN09sR0QzZHhhMWlKS2JERXkyT3E1SjZ5UDRnK205bXplVXZ6anZFOU9PYW9TV0lQV25aVXIwSUwzR2pMd1VHZHlkOVFPdDZkK2QveW9EcmRyMVlMcEMvNVhQU0x1MlJEeDdrMytyUmJycVdFZDZoS2NiM2xpbGVUZUZOYk1HM3J0cHI4Q3o4aVcwZ3ZtcENQRUdEWkdRcHlMbXNsUzlYYk1HVGdHK2pGYlRTRk5hL1A3Z2h1WjhidWpvamRNRXEvVFhRN25RWXZTVlRsai9LYTFRRCt0V0pWVjZocW45TT0iLCJtYWMiOiIzYjcxNzQ0ZmFkMGNjMGQ1YzYwM2I0NjczNGI5NjRmYWYyNDg5NmI3ZDRjNGJlOGFhZWFmZjlhZmQzY2RjZWU3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkM1bUEyWnFJSnRHeUh6VGxLNW43bmc9PSIsInZhbHVlIjoiQ2p0L0MzcksxaFFCZ0V2Sk1qN05lTEFVcjl6R1hIb3NOM3lsYmZFOFkvVE5jZzE3ajM5cEwrVjdjcjV2Tjd6RW9QRVVRUEJYUnh2UlI4bS9UR1lBdjRqWU9Ub244NFVFenJaZzkxZHdUaXZDQXdHTGRDa3dwQTI4dVlWa1JPeUoiLCJtYWMiOiIzZDVlYTMyMmIwZTU0OThiMjVkZDE5YmNmZDQ1MWYwZjdjODJjMDQ5OWEyNTY2Mjk3NGJmMzQ1MTMxMDRmODAwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxMNXBQTmJld0FJWGdsWWNFc2RTd0E9PSIsInZhbHVlIjoiMmFWeTVGTUxFMU1nOTNuZUZ1UDg0aUpUci9kemVyUDB5K3c2cFRWUjdDQkNRNitOejl0WjlzSWZDTEdsODNtdVdWaE4wV25WdDBGdVhXbkUwSU5BSU1oR3RTTFpieTJudXZZOE9FWDN6c2lucjNWVkFZcTdXaXg1TG5NWFk1d2UiLCJtYWMiOiJmODlkN2EzNzc4M2JiY2FiNTNjNmE5MjlmZmE5ZmJhZGMwNmJjZmNiZDRlNmRhYzdmNTRiOWY4YWU3YzQ5YTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620319737\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1886618132 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886618132\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-382674033 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:38:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382674033\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1930607086 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930607086\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}