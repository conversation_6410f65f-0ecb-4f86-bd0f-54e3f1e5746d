{"__meta": {"id": "01JZ2YFY3WHN6WMY8Z8YTPSRJY", "datetime": "2025-07-01 12:13:23", "utime": **********.452903, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********01.886275, "end": **********.452917, "duration": 1.5666420459747314, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": ********01.886275, "relative_start": 0, "end": **********.670131, "relative_end": **********.670131, "duration": 0.****************, "duration_str": "784ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.670147, "relative_start": 0.****************, "end": **********.452919, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "783ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.939978, "relative_start": 1.****************, "end": **********.941938, "relative_end": **********.941938, "duration": 0.0019600391387939453, "duration_str": "1.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.383512, "relative_start": 1.***************, "end": **********.383512, "relative_end": **********.383512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.421766, "relative_start": 1.****************, "end": **********.421766, "relative_end": **********.421766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.429616, "relative_start": 1.5433409214019775, "end": **********.429616, "relative_end": **********.429616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.450619, "relative_start": 1.5643439292907715, "end": **********.451384, "relative_end": **********.451384, "duration": 0.0007650852203369141, "duration_str": "765μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 60303680, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.383489, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.421757, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.429603, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}]}, "queries": {"count": 298, "nb_statements": 298, "nb_visible_statements": 298, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13515, "accumulated_duration_str": "135ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 198 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `sessions` where `id` = 'OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33' limit 1", "type": "query", "params": [], "bindings": ["OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.945634, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 0.422}, {"sql": "select * from `users` where `id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.957239, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 0.422, "width_percent": 0.414}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.9603028, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 0.836, "width_percent": 0.296}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9632032, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.132, "width_percent": 0.2}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.964408, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.332, "width_percent": 0.155}, {"sql": "select * from `clients` where `clients`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.969012, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.487, "width_percent": 0.429}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.025002, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.916, "width_percent": 0.518}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.0265489, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.434, "width_percent": 0.422}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100) and `milestones`.`id` = '100' limit 1", "type": "query", "params": [], "bindings": [26, 100, "100"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.028964, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.856, "width_percent": 0.363}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.0302742, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.219, "width_percent": 0.215}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 569}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 264}], "start": **********.034176, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:569", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=569", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "569"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.433, "width_percent": 0.511}, {"sql": "select * from `payments` where `milestone_id` = 100 limit 1", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.03833, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.944, "width_percent": 1.524}, {"sql": "select * from `payments` where `milestone_id` = 102 limit 1", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.0414329, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.468, "width_percent": 0.289}, {"sql": "select * from `payments` where `milestone_id` = 103 limit 1", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.042759, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.757, "width_percent": 0.237}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.0457559, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.993, "width_percent": 0.422}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.046982, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.415, "width_percent": 0.422}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.0494668, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.837, "width_percent": 0.54}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.051316, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.377, "width_percent": 0.311}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.052629, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.688, "width_percent": 0.333}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.053722, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.021, "width_percent": 0.259}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.054647, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.28, "width_percent": 0.185}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.055482, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.465, "width_percent": 0.163}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.056306, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.627, "width_percent": 0.259}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.057436, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.886, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.058675, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.138, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.059855, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.367, "width_percent": 0.215}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.06107, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.582, "width_percent": 0.281}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.062365, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.863, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.067523, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.092, "width_percent": 0.422}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.069194, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.514, "width_percent": 0.222}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.071458, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.736, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.0723472, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.951, "width_percent": 0.2}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.073264, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.151, "width_percent": 0.296}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.074269, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.447, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 89}], "start": **********.0752091, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.698, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.076116, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.927, "width_percent": 0.237}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.07695, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.164, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.079149, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.49, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.080574, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.852, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.083091, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.097, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.084494, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.43, "width_percent": 0.333}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.086078, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.762, "width_percent": 0.37}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.087454, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.132, "width_percent": 0.259}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.089302, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.391, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.090412, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.606, "width_percent": 0.192}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.092619, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.798, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.093836, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.072, "width_percent": 0.222}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.0950398, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.294, "width_percent": 0.289}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.096311, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.583, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.097722, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.827, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.09887, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.041, "width_percent": 0.377}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.1004171, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.419, "width_percent": 0.363}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.101979, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.781, "width_percent": 0.407}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.103187, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.188, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 58}], "start": **********.10417, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.44, "width_percent": 0.229}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.105274, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.669, "width_percent": 0.259}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.106152, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.928, "width_percent": 0.222}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.107101, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.15, "width_percent": 0.192}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.1079688, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.343, "width_percent": 0.185}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.108845, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.528, "width_percent": 0.244}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.110208, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.772, "width_percent": 0.37}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.1117659, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.142, "width_percent": 0.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.113178, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.445, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.114381, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.674, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.115566, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.882, "width_percent": 0.333}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.117226, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.215, "width_percent": 0.585}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.1192539, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.799, "width_percent": 0.451}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.12133, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.25, "width_percent": 0.34}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.12283, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.591, "width_percent": 0.289}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.124306, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.879, "width_percent": 0.281}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.125891, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.161, "width_percent": 0.407}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.127408, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.568, "width_percent": 0.429}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 27}], "start": **********.129111, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.997, "width_percent": 0.459}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.1308599, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.455, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.132353, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.862, "width_percent": 0.622}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.134504, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.484, "width_percent": 0.385}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.1361232, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.869, "width_percent": 0.348}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.137463, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.216, "width_percent": 0.237}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.1385489, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.453, "width_percent": 0.414}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.140192, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.868, "width_percent": 0.348}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.141461, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.215, "width_percent": 0.318}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.142896, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.533, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.144178, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.785, "width_percent": 0.303}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.1457179, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.088, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 103 and `is_merged` = 1", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.1472912, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.495, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.149697, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.821, "width_percent": 0.377}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.15133, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.198, "width_percent": 0.348}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.1528149, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.546, "width_percent": 0.311}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.15453, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.857, "width_percent": 0.54}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.1563458, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.397, "width_percent": 0.422}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 153}], "start": **********.1580172, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.819, "width_percent": 0.466}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.159846, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.285, "width_percent": 0.474}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.161587, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.758, "width_percent": 0.37}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.163167, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.128, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.164674, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.461, "width_percent": 0.289}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.166418, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.75, "width_percent": 0.407}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.1682112, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.157, "width_percent": 0.592}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.1702209, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.749, "width_percent": 0.444}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.1719062, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.193, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.173375, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.518, "width_percent": 0.318}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.17478, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.836, "width_percent": 0.266}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.17561, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.103, "width_percent": 0.333}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.176319, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.436, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.189922, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.702, "width_percent": 0.474}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.19103, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.176, "width_percent": 0.289}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1917949, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.464, "width_percent": 0.178}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.192449, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.642, "width_percent": 0.385}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193198, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.027, "width_percent": 0.266}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193811, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.293, "width_percent": 0.303}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194525, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.596, "width_percent": 0.311}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.19516, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.907, "width_percent": 0.259}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.195868, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.166, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.196566, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.41, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197135, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.625, "width_percent": 0.192}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197794, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.817, "width_percent": 0.281}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.198423, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.098, "width_percent": 0.296}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1993072, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.394, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.200182, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.727, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.200948, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.994, "width_percent": 0.289}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.20183, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.282, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2026958, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.689, "width_percent": 0.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.204423, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.993, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205226, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.266, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2062368, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.511, "width_percent": 0.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.207039, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.814, "width_percent": 0.244}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2077882, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 41.058, "width_percent": 0.326}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.208514, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 41.384, "width_percent": 0.259}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.209218, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 41.643, "width_percent": 0.237}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2099051, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 41.879, "width_percent": 0.237}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.210671, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.116, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.211443, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.471, "width_percent": 0.318}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.21226, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.789, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213006, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.056, "width_percent": 0.222}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.21365, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.278, "width_percent": 0.34}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.214422, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.618, "width_percent": 0.348}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215877, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.966, "width_percent": 0.414}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.216952, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.38, "width_percent": 0.34}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.218026, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.721, "width_percent": 0.429}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.218972, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.15, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.219857, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.512, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220631, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.764, "width_percent": 0.259}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221463, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.023, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2222738, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.4, "width_percent": 0.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.222974, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.704, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223668, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.911, "width_percent": 0.259}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.224552, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.17, "width_percent": 0.422}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2254891, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.592, "width_percent": 0.355}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.226212, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.947, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2273922, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.272, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2282422, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.598, "width_percent": 0.237}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229016, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.835, "width_percent": 0.37}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2298372, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.205, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2306871, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.538, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2313242, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.767, "width_percent": 0.207}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.232041, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.974, "width_percent": 0.437}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2329502, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 50.411, "width_percent": 0.466}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.233989, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 50.877, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2347949, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 51.158, "width_percent": 0.281}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2356482, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 51.439, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.236388, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 51.794, "width_percent": 0.281}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.237068, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.075, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238647, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.357, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2394161, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.63, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240431, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.86, "width_percent": 0.229}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2411, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 53.089, "width_percent": 0.229}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.241896, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 53.319, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2428112, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 53.725, "width_percent": 0.34}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2436829, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 54.066, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.244458, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 54.34, "width_percent": 0.259}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.245284, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 54.599, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246089, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 54.976, "width_percent": 0.377}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2470071, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 55.353, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247729, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 55.597, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.248716, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 55.849, "width_percent": 0.348}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249517, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 56.197, "width_percent": 0.525}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.251074, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 56.722, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.251963, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 57.055, "width_percent": 0.274}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.252879, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 57.329, "width_percent": 0.429}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2538002, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 57.758, "width_percent": 0.34}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.254678, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 58.098, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255411, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 58.343, "width_percent": 0.281}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2562392, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 58.624, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256998, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 58.979, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2577012, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 59.245, "width_percent": 0.237}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.258384, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 59.482, "width_percent": 0.192}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.259032, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 59.674, "width_percent": 0.326}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.259772, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 60, "width_percent": 0.289}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260388, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 60.289, "width_percent": 0.281}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.260967, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 60.57, "width_percent": 0.289}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.261662, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 60.858, "width_percent": 0.296}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.262694, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 61.154, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.263483, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 61.428, "width_percent": 0.237}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.264327, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 61.665, "width_percent": 0.363}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2651522, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 62.027, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.266186, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 62.353, "width_percent": 0.444}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.26727, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 62.797, "width_percent": 0.259}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.268146, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 63.056, "width_percent": 0.414}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.269104, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 63.47, "width_percent": 0.377}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.270018, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 63.848, "width_percent": 0.259}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.270874, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 64.107, "width_percent": 0.289}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.271786, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 64.395, "width_percent": 0.348}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2725601, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 64.743, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.274348, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 65.076, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.275303, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 65.438, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2764041, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 65.705, "width_percent": 0.237}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.277088, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 65.942, "width_percent": 0.244}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.277876, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.186, "width_percent": 0.414}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.278752, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.6, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279529, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.926, "width_percent": 0.237}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.280228, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 67.162, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.281009, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 67.414, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.281759, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 67.769, "width_percent": 0.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.282681, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 68.073, "width_percent": 0.414}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.283709, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 68.487, "width_percent": 0.348}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2847679, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 68.835, "width_percent": 0.437}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.285816, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.271, "width_percent": 0.37}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287073, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.641, "width_percent": 0.318}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287935, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.959, "width_percent": 0.296}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.288847, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 70.255, "width_percent": 0.407}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.289775, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 70.662, "width_percent": 0.429}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.290788, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.091, "width_percent": 0.266}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.291527, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.358, "width_percent": 0.222}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.292232, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.58, "width_percent": 0.296}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.292887, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.876, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293626, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.157, "width_percent": 0.237}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294322, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.394, "width_percent": 0.185}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.294972, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.579, "width_percent": 0.303}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.295637, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.882, "width_percent": 0.281}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.296216, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 73.163, "width_percent": 0.274}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.296772, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 73.437, "width_percent": 0.318}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.297495, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 73.755, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.298519, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 74.036, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.299371, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 74.317, "width_percent": 0.333}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.300337, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 74.65, "width_percent": 0.466}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.30134, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 75.117, "width_percent": 0.37}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3022861, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 75.486, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.303056, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 75.812, "width_percent": 0.237}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.303738, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.049, "width_percent": 0.34}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3044882, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.389, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3052921, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.715, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.30601, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.959, "width_percent": 0.215}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.306629, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.174, "width_percent": 0.274}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3072, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.447, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3081012, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.699, "width_percent": 0.222}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3086689, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.921, "width_percent": 0.192}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.309202, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 78.113, "width_percent": 0.178}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.309792, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 78.291, "width_percent": 0.4}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.310561, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 78.69, "width_percent": 0.311}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.311388, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.001, "width_percent": 0.296}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.312077, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.297, "width_percent": 0.274}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3126888, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.571, "width_percent": 0.296}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3135269, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.867, "width_percent": 0.296}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.314401, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 80.163, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.315063, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 80.437, "width_percent": 0.207}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.315729, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 80.644, "width_percent": 0.511}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.316808, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 81.154, "width_percent": 0.407}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.317784, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 81.561, "width_percent": 0.274}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.318581, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 81.835, "width_percent": 0.326}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.319441, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 82.161, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3202798, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 82.412, "width_percent": 0.444}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3212209, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 82.856, "width_percent": 0.311}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3232942, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.167, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.324162, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.529, "width_percent": 0.207}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.324915, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.737, "width_percent": 0.377}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.325794, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 84.114, "width_percent": 0.34}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3288782, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 84.454, "width_percent": 1.236}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3332121, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 85.69, "width_percent": 0.636}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.335386, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 86.326, "width_percent": 0.74}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.336746, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 87.066, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.337666, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 87.429, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.338485, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 87.71, "width_percent": 0.289}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3393831, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 87.999, "width_percent": 0.355}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3401349, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 88.354, "width_percent": 0.296}, {"sql": "select * from `milestones` where `project_id` = ? and `id` not in (?, ?, ?) and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3414629, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 88.65, "width_percent": 0.451}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.342349, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 89.101, "width_percent": 0.34}, {"sql": "select * from `milestones` where `id` in (?) and `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3443482, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 89.441, "width_percent": 0.459}, {"sql": "update `milestones` set `merged_with_milestone_id` = ?, `is_merged` = ?, `original_amount` = ?, `original_percentage` = ?, `original_due_date` = ?, `milestones`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3459551, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 89.9, "width_percent": 1.768}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.349309, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 91.669, "width_percent": 0.562}, {"sql": "update `milestones` set `due_date` = ?, `percentage` = ?, `amount` = ?, `original_due_date` = ?, `milestones`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3510602, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 92.231, "width_percent": 1.384}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.354298, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 93.615, "width_percent": 0.355}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.355243, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 93.97, "width_percent": 0.289}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.356061, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.258, "width_percent": 0.252}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.35675, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.51, "width_percent": 0.244}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.357455, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.754, "width_percent": 0.237}, {"sql": "select * from `projects` where `client_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3724961, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.991, "width_percent": 0.474}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37345, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.464, "width_percent": 0.392}, {"sql": "select * from `projects` where `client_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.374531, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.856, "width_percent": 0.333}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.375262, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.189, "width_percent": 0.392}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = ? and `milestones`.`id` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.376699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.582, "width_percent": 0.429}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = ? and `milestones`.`id` in (?) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3774621, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.011, "width_percent": 0.363}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3782048, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.373, "width_percent": 0.192}, {"sql": "select * from `projects` where `client_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408277, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.566, "width_percent": 0.385}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409028, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.95, "width_percent": 0.266}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4131749, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.217, "width_percent": 0.311}, {"sql": "select * from `cache` where `key` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4154148, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.528, "width_percent": 0.437}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (19) and `model_has_permissions`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.41891, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.964, "width_percent": 0.303}, {"sql": "select * from `projects` where `client_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426784, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.267, "width_percent": 0.414}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427574, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.682, "width_percent": 0.318}]}, "models": {"data": {"App\\Models\\PricingModel": {"value": 129, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Project": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Payment": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Client": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 177, "is_counter": true}, "livewire": {"data": {"app.filament.resources.client-resource.relation-managers.milestones-relation-manager #E5dXhl92yLIuZPBN0Zu9": "array:4 [\n  \"data\" => array:40 [\n    \"ownerRecord\" => App\\Models\\Client {#3935\n      #connection: \"mysql\"\n      #table: \"clients\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"<PERSON><PERSON><PERSON>\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #original: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"id\" => \"integer\"\n        \"personnel_details\" => \"array\"\n        \"social_media_access\" => \"array\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"company_name\"\n        1 => \"company_email\"\n        2 => \"phone\"\n        3 => \"address\"\n        4 => \"contact_person\"\n        5 => \"status\"\n        6 => \"tax_id\"\n        7 => \"registered_address\"\n        8 => \"official_email\"\n        9 => \"company_number\"\n        10 => \"personnel_details\"\n        11 => \"social_media_access\"\n        12 => \"created_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"pageClass\" => \"App\\Filament\\Resources\\ClientResource\\Pages\\EditClient\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeTab\" => null\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => array:2 [\n      0 => []\n      1 => []\n    ]\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => []\n    \"defaultTableActionArguments\" => []\n    \"defaultTableActionRecord\" => []\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => null\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"milestonesRelationManagerPage\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.client-resource.relation-managers.milestones-relation-manager\"\n  \"component\" => \"App\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager\"\n  \"id\" => \"E5dXhl92yLIuZPBN0Zu9\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-921053977 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921053977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420058, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2120574176 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120574176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420493, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-18305233 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18305233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.429129, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-815749621 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815749621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.429258, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager@callMountedTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:70-161</a>", "middleware": "web", "duration": "1.58s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1181005478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181005478\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-714873119 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"4381 characters\">{&quot;data&quot;:{&quot;ownerRecord&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Client&quot;,&quot;key&quot;:26,&quot;s&quot;:&quot;mdl&quot;}],&quot;pageClass&quot;:&quot;App\\\\Filament\\\\Resources\\\\ClientResource\\\\Pages\\\\EditClient&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeTab&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableRecordsPerPage&quot;:10,&quot;isTableReordering&quot;:false,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;editAll&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;milestones&quot;:[{&quot;06e23314-7cda-4a11-b1ea-54159ea84c72&quot;:[{&quot;id&quot;:100,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-15&quot;,&quot;percentage&quot;:&quot;50.00&quot;,&quot;hours&quot;:&quot;0.00&quot;,&quot;amount&quot;:&quot;4000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:&quot;2000.00&quot;,&quot;original_percentage&quot;:&quot;25.00&quot;,&quot;original_due_date&quot;:&quot;2025-07-08T00:00:00.000000Z&quot;,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:12:35.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-08&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;4000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;66392d30-fc05-4138-9974-1ce3cbc78e8d&quot;:[{&quot;id&quot;:102,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 3 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 3 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-22&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-22&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;67e835da-c133-4d5a-ba02-6e02563fea63&quot;:[{&quot;id&quot;:103,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 4 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 4 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-29&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-29&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:&quot;100&quot;,&quot;defaultTableAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionRecord&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableFilters&quot;:null,&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;milestonesRelationManagerPage&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;E5dXhl92yLIuZPBN0Zu9&quot;,&quot;name&quot;:&quot;app.filament.resources.client-resource.relation-managers.milestones-relation-manager&quot;,&quot;path&quot;:&quot;admin\\/clients\\/26\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;91ebe54e9d7b5087007d08794725eba517b00fd7d02bade054527521737bcb9d&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.06e23314-7cda-4a11-b1ea-54159ea84c72.unmerge_milestone_ids.0</span>\" => \"<span class=sf-dump-str title=\"3 characters\">101</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"22 characters\">callMountedTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714873119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-53942895 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5190</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjBjNUpza0JkbElON1VybGp1UENRT1E9PSIsInZhbHVlIjoiTlk4ZnZiQndFRFJWV21udDhRVkRJSHg1Z09WNG9TaDlvL3NlTWZQK3cwNWxwWHNMWXUvOFhBS3ZSaFdTNFVRbkdVSU1NWUxpd2VVaTZLdDJiaGJzVTE2dE9hOHNBY1BVcURRa05LcjRodEFyeXNkT0swaGZyR0pZVUZ0TGgxZTd1MHZ1VnVqVU01VWlxL01DSE5YYjRYcHVrR2NQTmhrUXNVQ1UvSGdSdnFrMG1Fa08ydGNRNFlFcjEvSzE3UTQ2N2VqL1FQT2JZSUM4V3pJeTdsSEs2TXF0RXFyRnJEcVZEdmkxeS8raE1qZz0iLCJtYWMiOiIxZWI0YTEyZDFmMmIxN2VkZjgxMTlkNjhhNWZkZmQ1N2JmYWFlZDdhZDJlM2YzZDg3MjkxOTI2YTE3ZDNjNDU2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilp0UnJoUVNVS0t1VkhxYW5TOUZadWc9PSIsInZhbHVlIjoiVys4cDJCaGtBWmVZczVBNTdzTThRaCtQd2IxTDNEQnZkRG0yYmI5TUoyMmk1Rk11aDBHajJ6RzlkNC9FRUNPNUpHVHZkbDNQeU5TcGVZQ2hYZmFmbVdZNDVqd2tnMStDTVRtbnBvbHpleWgyb1ZsWnk3Z0xUUHM3ZFQxOUQzZWoiLCJtYWMiOiI3ZDRjNDcwMjZjMjZhODNiYmIyNmQzYmRhYWJiZWYzZTcwNTFkZGZkYjk1MmRjYWQ0YjRhODBlNDdkNDE1ZGQ2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IlpSOEJiUTlINGMxUEllVGhTTk1iQWc9PSIsInZhbHVlIjoiUjM1SFVHbitVaGIzdnBBWDdIQUdLbzhHa1l3M1ZQRkUrZXVmYXJSTE5wSkNNYXVhMGs5c29PRjNrRmhZaHg4VzFBK0lxYTlVSkROMzZ2bCtzYUFCM2pyREhVcmQ3bTJ4ZWVONWhGQ2xjdlZDcW5nRHB2TDlVd1VJS0FPMUIyNEUiLCJtYWMiOiJkOTIzZjQxYmI0NDU2ODAwN2Y1MWY1NTRhNDQwZmI2MWIxMWI4YTMzNzNlODBlMzU2MmRlZmEwZWQyYzE0ZTFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53942895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2095233474 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|ceSSE69Z6lQRjclxArTEUqvoloq2EMgMZKE49WMA10W3Vcuc7S97Dvhk23GU|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095233474\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1466376198 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:13:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466376198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1481446627 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481446627\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}