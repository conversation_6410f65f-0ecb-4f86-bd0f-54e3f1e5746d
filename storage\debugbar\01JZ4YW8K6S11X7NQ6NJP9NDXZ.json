{"__meta": {"id": "01JZ4YW8K6S11X7NQ6NJP9NDXZ", "datetime": "2025-07-02 06:58:36", "utime": **********.263675, "method": "GET", "uri": "/admin/app-notifications", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[06:58:34] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/app-notifications\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.085772, "xdebug_link": null, "collector": "log"}, {"message": "[06:58:36] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/app-notifications\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.258723, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.397591, "end": **********.263694, "duration": 2.866102933883667, "duration_str": "2.87s", "measures": [{"label": "Booting", "start": **********.397591, "relative_start": 0, "end": **********.801829, "relative_end": **********.801829, "duration": 0.****************, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.801839, "relative_start": 0.****************, "end": **********.263696, "relative_end": 1.9073486328125e-06, "duration": 2.****************, "duration_str": "2.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.054638, "relative_start": 0.****************, "end": **********.0577, "relative_end": **********.0577, "duration": 0.003062009811401367, "duration_str": "3.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.155635, "relative_start": 0.****************, "end": **********.155635, "relative_end": **********.155635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.162411, "relative_start": 0.***************, "end": **********.162411, "relative_end": **********.162411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.166637, "relative_start": 0.7690458297729492, "end": **********.166637, "relative_end": **********.166637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.169993, "relative_start": 0.7724018096923828, "end": **********.169993, "relative_end": **********.169993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.178269, "relative_start": 0.7806777954101562, "end": **********.178269, "relative_end": **********.178269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.194378, "relative_start": 0.7967867851257324, "end": **********.194378, "relative_end": **********.194378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.202046, "relative_start": 0.8044548034667969, "end": **********.202046, "relative_end": **********.202046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.208154, "relative_start": 0.8105628490447998, "end": **********.208154, "relative_end": **********.208154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.254112, "relative_start": 0.8565208911895752, "end": **********.254112, "relative_end": **********.254112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.262042, "relative_start": 0.8644509315490723, "end": **********.262042, "relative_end": **********.262042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.266037, "relative_start": 0.868445873260498, "end": **********.266037, "relative_end": **********.266037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.301818, "relative_start": 0.9042267799377441, "end": **********.301818, "relative_end": **********.301818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.311752, "relative_start": 0.914160966873169, "end": **********.311752, "relative_end": **********.311752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.316835, "relative_start": 0.9192438125610352, "end": **********.316835, "relative_end": **********.316835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.362236, "relative_start": 0.9646449089050293, "end": **********.362236, "relative_end": **********.362236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.368069, "relative_start": 0.970477819442749, "end": **********.368069, "relative_end": **********.368069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.374973, "relative_start": 0.9773819446563721, "end": **********.374973, "relative_end": **********.374973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.415303, "relative_start": 1.017711877822876, "end": **********.415303, "relative_end": **********.415303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.420314, "relative_start": 1.0227229595184326, "end": **********.420314, "relative_end": **********.420314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.42558, "relative_start": 1.0279889106750488, "end": **********.42558, "relative_end": **********.42558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.462327, "relative_start": 1.0647358894348145, "end": **********.462327, "relative_end": **********.462327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.467351, "relative_start": 1.0697598457336426, "end": **********.467351, "relative_end": **********.467351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.471999, "relative_start": 1.0744078159332275, "end": **********.471999, "relative_end": **********.471999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.511507, "relative_start": 1.1139159202575684, "end": **********.511507, "relative_end": **********.511507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.516731, "relative_start": 1.1191399097442627, "end": **********.516731, "relative_end": **********.516731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.521079, "relative_start": 1.123487949371338, "end": **********.521079, "relative_end": **********.521079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.560168, "relative_start": 1.1625769138336182, "end": **********.560168, "relative_end": **********.560168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.564888, "relative_start": 1.1672968864440918, "end": **********.564888, "relative_end": **********.564888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.568212, "relative_start": 1.1706209182739258, "end": **********.568212, "relative_end": **********.568212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.596936, "relative_start": 1.1993448734283447, "end": **********.596936, "relative_end": **********.596936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.601298, "relative_start": 1.203706979751587, "end": **********.601298, "relative_end": **********.601298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.605093, "relative_start": 1.2075018882751465, "end": **********.605093, "relative_end": **********.605093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.641402, "relative_start": 1.2438108921051025, "end": **********.641402, "relative_end": **********.641402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.646573, "relative_start": 1.2489819526672363, "end": **********.646573, "relative_end": **********.646573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.650277, "relative_start": 1.252685785293579, "end": **********.650277, "relative_end": **********.650277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.683904, "relative_start": 1.2863128185272217, "end": **********.683904, "relative_end": **********.683904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.688646, "relative_start": 1.2910549640655518, "end": **********.688646, "relative_end": **********.688646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.69349, "relative_start": 1.2958989143371582, "end": **********.69349, "relative_end": **********.69349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.727973, "relative_start": 1.3303818702697754, "end": **********.727973, "relative_end": **********.727973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.732042, "relative_start": 1.3344509601593018, "end": **********.732042, "relative_end": **********.732042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.73491, "relative_start": 1.3373188972473145, "end": **********.73491, "relative_end": **********.73491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.762632, "relative_start": 1.3650407791137695, "end": **********.762632, "relative_end": **********.762632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.766341, "relative_start": 1.3687498569488525, "end": **********.766341, "relative_end": **********.766341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.770073, "relative_start": 1.3724818229675293, "end": **********.770073, "relative_end": **********.770073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.80286, "relative_start": 1.405268907546997, "end": **********.80286, "relative_end": **********.80286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.806485, "relative_start": 1.4088938236236572, "end": **********.806485, "relative_end": **********.806485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.810587, "relative_start": 1.4129958152770996, "end": **********.810587, "relative_end": **********.810587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.837595, "relative_start": 1.4400038719177246, "end": **********.837595, "relative_end": **********.837595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.841697, "relative_start": 1.444105863571167, "end": **********.841697, "relative_end": **********.841697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.844784, "relative_start": 1.447192907333374, "end": **********.844784, "relative_end": **********.844784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.872899, "relative_start": 1.4753079414367676, "end": **********.872899, "relative_end": **********.872899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.877827, "relative_start": 1.4802358150482178, "end": **********.877827, "relative_end": **********.877827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.881014, "relative_start": 1.4834229946136475, "end": **********.881014, "relative_end": **********.881014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.910991, "relative_start": 1.5133998394012451, "end": **********.910991, "relative_end": **********.910991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.91599, "relative_start": 1.5183990001678467, "end": **********.91599, "relative_end": **********.91599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.920875, "relative_start": 1.5232839584350586, "end": **********.920875, "relative_end": **********.920875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.954676, "relative_start": 1.5570847988128662, "end": **********.954676, "relative_end": **********.954676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.960967, "relative_start": 1.5633759498596191, "end": **********.960967, "relative_end": **********.960967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.965305, "relative_start": 1.567713975906372, "end": **********.965305, "relative_end": **********.965305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.01494, "relative_start": 1.6173489093780518, "end": 1751439515.01494, "relative_end": 1751439515.01494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.022609, "relative_start": 1.6250178813934326, "end": 1751439515.022609, "relative_end": 1751439515.022609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.028811, "relative_start": 1.6312198638916016, "end": 1751439515.028811, "relative_end": 1751439515.028811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.06414, "relative_start": 1.6665489673614502, "end": 1751439515.06414, "relative_end": 1751439515.06414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.069564, "relative_start": 1.6719729900360107, "end": 1751439515.069564, "relative_end": 1751439515.069564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.073227, "relative_start": 1.675635814666748, "end": 1751439515.073227, "relative_end": 1751439515.073227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.106142, "relative_start": 1.7085509300231934, "end": 1751439515.106142, "relative_end": 1751439515.106142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.111661, "relative_start": 1.7140698432922363, "end": 1751439515.111661, "relative_end": 1751439515.111661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.115422, "relative_start": 1.7178308963775635, "end": 1751439515.115422, "relative_end": 1751439515.115422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.154956, "relative_start": 1.7573649883270264, "end": 1751439515.154956, "relative_end": 1751439515.154956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.160566, "relative_start": 1.7629749774932861, "end": 1751439515.160566, "relative_end": 1751439515.160566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.164327, "relative_start": 1.7667357921600342, "end": 1751439515.164327, "relative_end": 1751439515.164327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.198873, "relative_start": 1.8012819290161133, "end": 1751439515.198873, "relative_end": 1751439515.198873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.203497, "relative_start": 1.805905818939209, "end": 1751439515.203497, "relative_end": 1751439515.203497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.207793, "relative_start": 1.81020188331604, "end": 1751439515.207793, "relative_end": 1751439515.207793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.241736, "relative_start": 1.8441448211669922, "end": 1751439515.241736, "relative_end": 1751439515.241736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.246953, "relative_start": 1.8493618965148926, "end": 1751439515.246953, "relative_end": 1751439515.246953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.250798, "relative_start": 1.8532068729400635, "end": 1751439515.250798, "relative_end": 1751439515.250798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.285615, "relative_start": 1.888023853302002, "end": 1751439515.285615, "relative_end": 1751439515.285615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.291387, "relative_start": 1.8937959671020508, "end": 1751439515.291387, "relative_end": 1751439515.291387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.2956, "relative_start": 1.8980088233947754, "end": 1751439515.2956, "relative_end": 1751439515.2956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.334682, "relative_start": 1.9370908737182617, "end": 1751439515.334682, "relative_end": 1751439515.334682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.350604, "relative_start": 1.9530129432678223, "end": 1751439515.350604, "relative_end": 1751439515.350604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.363937, "relative_start": 1.9663457870483398, "end": 1751439515.363937, "relative_end": 1751439515.363937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.470745, "relative_start": 2.0731539726257324, "end": 1751439515.470745, "relative_end": 1751439515.470745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.521235, "relative_start": 2.1236438751220703, "end": 1751439515.521235, "relative_end": 1751439515.521235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.528586, "relative_start": 2.1309947967529297, "end": 1751439515.528586, "relative_end": 1751439515.528586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.66019, "relative_start": 2.262598991394043, "end": 1751439515.66019, "relative_end": 1751439515.66019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.666036, "relative_start": 2.268444776535034, "end": 1751439515.666036, "relative_end": 1751439515.666036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.670452, "relative_start": 2.2728610038757324, "end": 1751439515.670452, "relative_end": 1751439515.670452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.758883, "relative_start": 2.3612918853759766, "end": 1751439515.758883, "relative_end": 1751439515.758883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.764649, "relative_start": 2.3670578002929688, "end": 1751439515.764649, "relative_end": 1751439515.764649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.768586, "relative_start": 2.370994806289673, "end": 1751439515.768586, "relative_end": 1751439515.768586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.799998, "relative_start": 2.402406930923462, "end": 1751439515.799998, "relative_end": 1751439515.799998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.804599, "relative_start": 2.407007932662964, "end": 1751439515.804599, "relative_end": 1751439515.804599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.808922, "relative_start": 2.4113309383392334, "end": 1751439515.808922, "relative_end": 1751439515.808922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.838654, "relative_start": 2.4410629272460938, "end": 1751439515.838654, "relative_end": 1751439515.838654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.84316, "relative_start": 2.445568799972534, "end": 1751439515.84316, "relative_end": 1751439515.84316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.84685, "relative_start": 2.449258804321289, "end": 1751439515.84685, "relative_end": 1751439515.84685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.87984, "relative_start": 2.4822487831115723, "end": 1751439515.87984, "relative_end": 1751439515.87984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.885126, "relative_start": 2.487534999847412, "end": 1751439515.885126, "relative_end": 1751439515.885126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.888839, "relative_start": 2.4912478923797607, "end": 1751439515.888839, "relative_end": 1751439515.888839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.922405, "relative_start": 2.5248138904571533, "end": 1751439515.922405, "relative_end": 1751439515.922405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.927771, "relative_start": 2.530179977416992, "end": 1751439515.927771, "relative_end": 1751439515.927771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.931347, "relative_start": 2.5337557792663574, "end": 1751439515.931347, "relative_end": 1751439515.931347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.961364, "relative_start": 2.5637729167938232, "end": 1751439515.961364, "relative_end": 1751439515.961364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.965468, "relative_start": 2.5678768157958984, "end": 1751439515.965468, "relative_end": 1751439515.965468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.969491, "relative_start": 2.571899890899658, "end": 1751439515.969491, "relative_end": 1751439515.969491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751439515.998999, "relative_start": 2.601408004760742, "end": 1751439515.998999, "relative_end": 1751439515.998999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.004311, "relative_start": 2.606719970703125, "end": **********.004311, "relative_end": **********.004311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.008187, "relative_start": 2.610595941543579, "end": **********.008187, "relative_end": **********.008187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.031659, "relative_start": 2.6340677738189697, "end": **********.031659, "relative_end": **********.031659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.079152, "relative_start": 2.68156099319458, "end": **********.079152, "relative_end": **********.079152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.145442, "relative_start": 2.7478508949279785, "end": **********.145442, "relative_end": **********.145442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.151215, "relative_start": 2.7536239624023438, "end": **********.151215, "relative_end": **********.151215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.152895, "relative_start": 2.7553038597106934, "end": **********.152895, "relative_end": **********.152895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.157665, "relative_start": 2.7600739002227783, "end": **********.157665, "relative_end": **********.157665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.158993, "relative_start": 2.761401891708374, "end": **********.158993, "relative_end": **********.158993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.161118, "relative_start": 2.7635269165039062, "end": **********.161118, "relative_end": **********.161118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.161519, "relative_start": 2.763927936553955, "end": **********.161519, "relative_end": **********.161519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.163811, "relative_start": 2.7662198543548584, "end": **********.163811, "relative_end": **********.163811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.164159, "relative_start": 2.7665679454803467, "end": **********.164159, "relative_end": **********.164159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.166369, "relative_start": 2.768777847290039, "end": **********.166369, "relative_end": **********.166369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.166681, "relative_start": 2.769089937210083, "end": **********.166681, "relative_end": **********.166681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.168473, "relative_start": 2.7708818912506104, "end": **********.168473, "relative_end": **********.168473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.168687, "relative_start": 2.7710959911346436, "end": **********.168687, "relative_end": **********.168687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.23606, "relative_start": 2.8384687900543213, "end": **********.23606, "relative_end": **********.23606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.237672, "relative_start": 2.840080976486206, "end": **********.237672, "relative_end": **********.237672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.248118, "relative_start": 2.850526809692383, "end": **********.248118, "relative_end": **********.248118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.251222, "relative_start": 2.853630781173706, "end": **********.251222, "relative_end": **********.251222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.252258, "relative_start": 2.8546669483184814, "end": **********.252258, "relative_end": **********.252258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.258314, "relative_start": 2.860722780227661, "end": **********.25841, "relative_end": **********.25841, "duration": 9.608268737792969e-05, "duration_str": "96μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.2612, "relative_start": 2.8636088371276855, "end": **********.261267, "relative_end": **********.261267, "duration": 6.699562072753906e-05, "duration_str": "67μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59391096, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 130, "nb_templates": 130, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.155615, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "3x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.162398, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.178253, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "3x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.194363, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "102x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.254097, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 102, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.031648, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.079122, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.145429, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.151189, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.152877, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.157649, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.158981, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.236048, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.237659, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.2481, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.251207, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.252247, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01818, "accumulated_duration_str": "18.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.065252, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 11.166}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.070435, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.166, "width_percent": 2.86}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.072843, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.026, "width_percent": 1.705}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.076097, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.732, "width_percent": 1.87}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.07709, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.602, "width_percent": 0.99}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0801651, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.592, "width_percent": 1.705}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.081192, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.297, "width_percent": 1.155}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0819511, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.452, "width_percent": 0.77}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.082606, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.222, "width_percent": 1.045}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.083273, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.267, "width_percent": 0.99}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.093173, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.257, "width_percent": 7.096}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.143568, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.353, "width_percent": 3.135}, {"sql": "select * from `app_notifications` where `user_id` = 1 order by `app_notifications`.`id` asc limit 34 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.14596, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.488, "width_percent": 4.51}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.1476462, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.999, "width_percent": 1.54}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.148607, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.539, "width_percent": 1.54}, {"sql": "select * from `notification_events` where `notification_events`.`id` in (1, 2, 5, 6, 11, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.149556, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.079, "width_percent": 3.74}, {"sql": "select `notification_events`.`module`, `notification_events`.`id` from `notification_events` order by `notification_events`.`module` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.191766, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.82, "width_percent": 3.52}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.114964, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.34, "width_percent": 3.575}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.147025, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.915, "width_percent": 15.017}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.154042, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.932, "width_percent": 7.701}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.159838, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.633, "width_percent": 3.355}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.161967, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.988, "width_percent": 6.766}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.16475, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.754, "width_percent": 5.501}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.167427, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.254, "width_percent": 2.695}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.169266, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.949, "width_percent": 2.695}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.2597928, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.645, "width_percent": 3.355}]}, "models": {"data": {"App\\Models\\AppNotification": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 54, "is_counter": true}, "livewire": {"data": {"app.filament.resources.app-notification-resource.pages.list-app-notifications #YyUtKquF7iv2y6xJegvK": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"read\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.app-notification-resource.pages.list-app-notifications\"\n  \"component\" => \"App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications\"\n  \"id\" => \"YyUtKquF7iv2y6xJegvK\"\n]", "filament.livewire.global-search #jM2QVAJKGCnNDMSgKfJ4": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"jM2QVAJKGCnNDMSgKfJ4\"\n]", "app.filament.widgets.notification-components.notification-bell #4ENCTvHxmg42q8sjjMWo": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 34\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"4ENCTvHxmg42q8sjjMWo\"\n]", "filament.livewire.notifications #PvncQQg0ks2Eyfv7sMKi": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3545\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"PvncQQg0ks2Eyfv7sMKi\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 235, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-287782428 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287782428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097059, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2103753168 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103753168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098195, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1121006637 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1121006637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103092, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1825877847 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825877847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125114, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1999868884 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999868884\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223128, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1340370224 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340370224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225145, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1046357592 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046357592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24824, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1858784419 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858784419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.249716, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-147817781 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147817781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.251933, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1912289028 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912289028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26167, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-601005843 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601005843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271586, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-257491626 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257491626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274429, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752737157 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752737157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.293512, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-397813404 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397813404\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296655, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2083527813 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083527813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299628, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-28870655 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28870655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311361, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-251789789 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251789789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.32301, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600697587 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600697587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.327876, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-910630660 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910630660\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.351377, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-505370407 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505370407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354663, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-756233503 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756233503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.359716, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-541703193 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541703193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.367158, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1223412563 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223412563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381851, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1300579110 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300579110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384563, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1560096423 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560096423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.409476, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1939228374 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939228374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.411075, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1234974924 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234974924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.413206, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-721565082 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-721565082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.419995, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114124175 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114124175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431383, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-865489304 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865489304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433913, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1599884364 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599884364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.455349, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1723519638 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723519638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.457965, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2033567312 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033567312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.460297, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1771172350 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771172350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.467033, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-65458857 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65458857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.479156, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-157405856 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157405856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.481899, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1150960273 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150960273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.505016, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1145808616 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145808616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.507063, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-604906232 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604906232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.509637, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-202136922 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202136922\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.516342, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1019012974 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019012974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527004, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-347187731 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347187731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529565, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1435219055 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435219055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550622, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1228964571 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228964571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.552462, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1709070876 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709070876\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.555788, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-452539383 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452539383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564628, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1363935710 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363935710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.574538, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-387520106 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387520106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.576628, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2074085930 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074085930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592922, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2019091758 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019091758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593992, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-722566248 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722566248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595661, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505513556 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505513556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600878, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2038338107 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038338107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.612299, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-188197767 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188197767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614724, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1300955077 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300955077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635115, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1446911072 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446911072\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.636766, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1772842862 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772842862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.639147, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1000045116 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000045116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646227, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1964793917 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964793917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655102, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1486157424 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1486157424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.658176, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-895816351 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895816351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.678082, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1624494526 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624494526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.679727, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1038781505 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038781505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.682043, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864422782 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864422782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.68835, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1824929668 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824929668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.698762, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-238153417 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238153417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.701502, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1296072785 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296072785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721081, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-856430299 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856430299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.723051, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1378659989 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378659989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726077, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1309124376 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309124376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.731784, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-944223509 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944223509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738782, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1692362321 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692362321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.741527, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1131130590 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131130590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75829, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1178840256 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178840256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759632, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1838301418 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838301418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761322, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1687434238 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687434238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76603, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-37700922 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37700922\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776586, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-534561210 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534561210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778218, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-802404683 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802404683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797496, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177897123 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177897123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.799271, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1185973591 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185973591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.801029, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1336479005 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336479005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.806269, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-331866502 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331866502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.814511, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-9163909 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9163909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.816244, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1826251641 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826251641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.833396, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1068831361 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068831361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.834599, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1042217503 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042217503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.836243, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1409361918 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409361918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.841428, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-536056860 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536056860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.848508, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298777273 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298777273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.850737, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-39129766 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39129766\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868453, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-799973443 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799973443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869653, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-602644290 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602644290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871471, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426423841 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426423841\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877588, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1139610933 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139610933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886116, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1533146560 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533146560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888754, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949594195 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949594195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.907365, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2113405179 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2113405179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.908314, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-532079485 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532079485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.90979, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-402381754 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402381754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915691, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-351445450 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351445450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.926928, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-425335689 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425335689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.929255, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-168594827 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168594827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94905, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-390830080 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390830080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.950499, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-569871241 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569871241\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.952887, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505140308 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505140308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.960561, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-708791189 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708791189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972745, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-97177468 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97177468\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.976234, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-709992933 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709992933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.004726, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-186433172 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186433172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.006381, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1636725807 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636725807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.012467, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-697967655 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697967655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.021878, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-618967402 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618967402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.033753, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-527860910 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527860910\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.036503, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-613341362 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613341362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.058424, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-814225273 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814225273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.060003, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1692000622 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692000622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.062522, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1752892441 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752892441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.069256, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-364921522 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364921522\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.07891, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-322708989 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322708989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.080917, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169771065 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169771065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.100618, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-597359934 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597359934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.102077, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1843329624 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843329624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.104296, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-623349287 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623349287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.111353, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-483976301 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483976301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.120394, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-348620004 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348620004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.122792, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1812230250 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812230250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.145188, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1677115016 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677115016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.149543, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1518173262 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518173262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.153042, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-461215045 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461215045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.160222, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-866013146 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866013146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.169598, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1133151461 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133151461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.171893, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555323892 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555323892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.193904, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-16959822 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16959822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.195389, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1252254533 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252254533\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.19731, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2047419134 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047419134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.203185, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1963209044 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963209044\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.212839, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852878254 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852878254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.215334, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-276550152 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276550152\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.234697, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1327634051 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327634051\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.236472, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1170304492 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170304492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.239025, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-950618674 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950618674\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.246603, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1432612854 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432612854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.256513, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-47807025 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47807025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.25979, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365451556 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365451556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.279364, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-900876747 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900876747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.281181, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-130614204 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130614204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.283618, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-696355692 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696355692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.290851, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1455170801 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455170801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.303361, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-909213390 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909213390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.305946, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1509716772 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509716772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.326081, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1675435143 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675435143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.327673, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-232207548 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232207548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.329979, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-800311450 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800311450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.349664, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1392282965 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392282965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.374615, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1666881600 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666881600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.380473, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233034881 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233034881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.462474, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1632004003 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632004003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.464289, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1396110680 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396110680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.468389, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2045045339 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045045339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.520739, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2135018995 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135018995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.535577, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1825831630 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825831630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.538244, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1550705231 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550705231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.647125, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1609262336 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609262336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.650623, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-702527678 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702527678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.655913, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1554225785 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554225785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.665646, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2121363621 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121363621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.699252, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-485337075 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485337075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.702139, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-565486087 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565486087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.746331, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-107868178 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107868178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.747985, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1306231567 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306231567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.755051, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-894672235 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894672235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.764194, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1181258017 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181258017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.773707, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-134139182 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134139182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.775939, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1796533251 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796533251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.794048, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179447135 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179447135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.795672, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1318823035 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318823035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.798104, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-742502437 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742502437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.804269, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1960437564 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960437564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.812877, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-733412230 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733412230\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.81519, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-66449071 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66449071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.83286, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1036695551 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036695551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.834401, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1914307476 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914307476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.836956, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1846090165 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846090165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.842885, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2060015973 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060015973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.851998, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1483670726 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483670726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.854347, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281212890 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281212890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.872464, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1109584298 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109584298\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.875383, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1568366794 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568366794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.877949, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679330928 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679330928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.884778, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1825082236 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825082236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.894801, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-840160337 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840160337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.897305, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-893361606 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893361606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.916642, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532217930 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532217930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.918248, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985486999 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985486999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.920614, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-751985792 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751985792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.927464, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-9108345 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9108345\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.936054, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1602356599 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602356599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.937589, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1041513781 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041513781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.955293, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-946127039 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946127039\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.957324, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1591992337 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591992337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.960037, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2017765715 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017765715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.965145, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-828333662 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828333662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.974437, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-275984545 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275984545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.976775, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1399371529 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399371529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.99408, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005265222 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005265222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.99514, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1210669386 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210669386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751439515.997248, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-601642590 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601642590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.00403, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1755351622 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755351622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.086145, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-465188620 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465188620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.086556, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1174485478 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174485478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088551, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168008778 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168008778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08922, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-879317744 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879317744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090855, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1761880475 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761880475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091745, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1684321785 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684321785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092936, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2107259572 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107259572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09446, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-664781038 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664781038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095168, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-781308596 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781308596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096025, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-91572589 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91572589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097335, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1279399481 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279399481\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098297, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-745850356 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745850356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098932, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1256033985 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256033985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100179, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-854296579 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854296579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100866, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-499965657 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499965657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101286, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1447192689 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447192689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103089, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-448437233 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448437233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104371, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-458406742 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-458406742\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.105145, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930833029 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930833029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.106395, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1661806445 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661806445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108828, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-183497271 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183497271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.109931, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-860309702 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860309702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111273, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1667636084 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667636084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.112789, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1522223156 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522223156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119302, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-227302354 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227302354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121161, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1962380417 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962380417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135515, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications", "uri": "GET admin/app-notifications", "controller": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/app-notifications", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.87s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNvLzhrYVpTdFNlTlh5alI2MDlPOHc9PSIsInZhbHVlIjoiRHViRmU3S2M0RmV0TXc0cit1d09NN3VXbllKVmlLRnVLdjh0aXRUTURCaGtHUFRjKzFuSlFjL0c4blR2ZXpWd1o4anZOdURFQlVLVWEvTXBGeWxlUGdETTlpaHpxa2VZb0VydTlKd2lGbERnVjJBc0JJUWZuNEJkbFdwdzNtbkgiLCJtYWMiOiI3ODk5YTRkODZiYzFhMjA4NzhhNmNlYWJiYjA2NTQ1YTQ3OTc1NTFmZGM0N2I5N2Y4ZGY1YjY1OTk5MWFlOWU0IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InF2bS9VWWFqWFRtZ2dpMUpWbW9QVXc9PSIsInZhbHVlIjoiLzVkUEZNbWtvcHdpRWRBakM1OW9JUklpeWIvNm1IVElDMnQzeXFUOGZaVnp5TjNOSkxrMm4yeXRaVmpHbUE1d0ttVzlvRE1BTlJoMVIyZW8rVHhhdVVGT1EwWXNZa1ZMY1hWSXJJY1ROV01qWUo0UWxqSjVsUTlVUHZRTndzNnUiLCJtYWMiOiJiMWFmNjQ0NWRjYzNmZGM1OWNmZTg3ZDIzZDg0YjBjNTlhNzljYWRmNTkyMTkyMDdiZmYyZGZjOGU3YjQ3NjdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-252250940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252250940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-931824021 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 06:58:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjliVGJVTURSVVJtdDVkRVRsUDdVRHc9PSIsInZhbHVlIjoiMGxNeEtoMWRIM1lWamd1WFk2MFE0OW1wWHZlV0I5UTlXY0lrN2haU3ljR2YzOUpnd1puRnVMM0dUL3JTNm5mdHJRTUx4NTFQUkdHbVpteFZrTi9ZMzhsWmtJUHpKWHNNTDVSQlV4Y1lkcVNnMG1YN2c3VWxPOHNoUkE5NUZ0bEYiLCJtYWMiOiIzZjk0YjY2YTVmNTA3ODU4NWE5ZTNmYjY3YmFlNDVjOWNjZjJkZmYxMzNhNjQ5MzhjMTZkMTY4NTYwZWM1MmE2IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 08:58:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IlVaUm5CcXpGYVBUZWZsZmxFK29qRlE9PSIsInZhbHVlIjoiTjVGUTFXVGcydEhIQUdTcTcvdkd6RzdqNzZpSmh4a1dXTElzL0NNdE1nZ3preDAvWGlTSnVkbmRseklDTXp2ZlAyUk1WK2N6aWU5dmNDTjVsWHJKUlM0SWxtM3Bwam1iZ2RDK1ZMelArSTdJaVRqczcyeEpleUpkMEZLUEU2d0siLCJtYWMiOiIxNWE5NTBkOGJjMzE5MDRlN2ZkNGI4MGM5NzhkYWRkZDZiOTk1YTlhYjI4MGRjODA4Yjc3OWMzZjUwZjJhNThiIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 08:58:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjliVGJVTURSVVJtdDVkRVRsUDdVRHc9PSIsInZhbHVlIjoiMGxNeEtoMWRIM1lWamd1WFk2MFE0OW1wWHZlV0I5UTlXY0lrN2haU3ljR2YzOUpnd1puRnVMM0dUL3JTNm5mdHJRTUx4NTFQUkdHbVpteFZrTi9ZMzhsWmtJUHpKWHNNTDVSQlV4Y1lkcVNnMG1YN2c3VWxPOHNoUkE5NUZ0bEYiLCJtYWMiOiIzZjk0YjY2YTVmNTA3ODU4NWE5ZTNmYjY3YmFlNDVjOWNjZjJkZmYxMzNhNjQ5MzhjMTZkMTY4NTYwZWM1MmE2IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 08:58:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IlVaUm5CcXpGYVBUZWZsZmxFK29qRlE9PSIsInZhbHVlIjoiTjVGUTFXVGcydEhIQUdTcTcvdkd6RzdqNzZpSmh4a1dXTElzL0NNdE1nZ3preDAvWGlTSnVkbmRseklDTXp2ZlAyUk1WK2N6aWU5dmNDTjVsWHJKUlM0SWxtM3Bwam1iZ2RDK1ZMelArSTdJaVRqczcyeEpleUpkMEZLUEU2d0siLCJtYWMiOiIxNWE5NTBkOGJjMzE5MDRlN2ZkNGI4MGM5NzhkYWRkZDZiOTk1YTlhYjI4MGRjODA4Yjc3OWMzZjUwZjJhNThiIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 08:58:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931824021\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1414314653 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/app-notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414314653\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications"}, "badge": null}}