{"__meta": {"id": "01JZ4GXTDGF91MYKZG5NR7QBMH", "datetime": "2025-07-02 02:54:47", "utime": ********87.228333, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********84.982957, "end": ********87.228546, "duration": 2.245589017868042, "duration_str": "2.25s", "measures": [{"label": "Booting", "start": ********84.982957, "relative_start": 0, "end": **********.386479, "relative_end": **********.386479, "duration": 0.****************, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.386488, "relative_start": 0.****************, "end": ********87.228565, "relative_end": 1.9073486328125e-05, "duration": 1.****************, "duration_str": "1.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.641608, "relative_start": 0.****************, "end": **********.643937, "relative_end": **********.643937, "duration": 0.002329111099243164, "duration_str": "2.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.754295, "relative_start": 0.****************, "end": **********.754295, "relative_end": **********.754295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.762192, "relative_start": 0.****************, "end": **********.762192, "relative_end": **********.762192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.773129, "relative_start": 0.7901721000671387, "end": **********.773129, "relative_end": **********.773129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.788547, "relative_start": 0.8055901527404785, "end": **********.788547, "relative_end": **********.788547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.796828, "relative_start": 0.8138711452484131, "end": **********.796828, "relative_end": **********.796828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.804621, "relative_start": 0.8216640949249268, "end": **********.804621, "relative_end": **********.804621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.809744, "relative_start": 0.826786994934082, "end": **********.809744, "relative_end": **********.809744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.816099, "relative_start": 0.8331420421600342, "end": **********.816099, "relative_end": **********.816099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.87615, "relative_start": 0.8931930065155029, "end": **********.87615, "relative_end": **********.87615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.893999, "relative_start": 0.9110422134399414, "end": **********.893999, "relative_end": **********.893999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.942511, "relative_start": 0.9595541954040527, "end": **********.942511, "relative_end": **********.942511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.948995, "relative_start": 0.9660382270812988, "end": **********.948995, "relative_end": **********.948995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.985182, "relative_start": 1.0022251605987549, "end": **********.985182, "relative_end": **********.985182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.989087, "relative_start": 1.0061302185058594, "end": **********.989087, "relative_end": **********.989087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.018349, "relative_start": 1.0353920459747314, "end": ********86.018349, "relative_end": ********86.018349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.022185, "relative_start": 1.0392282009124756, "end": ********86.022185, "relative_end": ********86.022185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.051902, "relative_start": 1.0689451694488525, "end": ********86.051902, "relative_end": ********86.051902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.056782, "relative_start": 1.0738251209259033, "end": ********86.056782, "relative_end": ********86.056782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.122902, "relative_start": 1.1399450302124023, "end": ********86.122902, "relative_end": ********86.122902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.128357, "relative_start": 1.145400047302246, "end": ********86.128357, "relative_end": ********86.128357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.239477, "relative_start": 1.2565200328826904, "end": ********86.239477, "relative_end": ********86.239477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.244983, "relative_start": 1.262026071548462, "end": ********86.244983, "relative_end": ********86.244983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.288069, "relative_start": 1.3051121234893799, "end": ********86.288069, "relative_end": ********86.288069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.293845, "relative_start": 1.3108880519866943, "end": ********86.293845, "relative_end": ********86.293845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.367721, "relative_start": 1.3847641944885254, "end": ********86.367721, "relative_end": ********86.367721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.382604, "relative_start": 1.3996469974517822, "end": ********86.382604, "relative_end": ********86.382604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.479362, "relative_start": 1.4964051246643066, "end": ********86.479362, "relative_end": ********86.479362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.487933, "relative_start": 1.5049760341644287, "end": ********86.487933, "relative_end": ********86.487933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.57115, "relative_start": 1.5881931781768799, "end": ********86.57115, "relative_end": ********86.57115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.577031, "relative_start": 1.594074010848999, "end": ********86.577031, "relative_end": ********86.577031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.685246, "relative_start": 1.70228910446167, "end": ********86.685246, "relative_end": ********86.685246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.690543, "relative_start": 1.7075860500335693, "end": ********86.690543, "relative_end": ********86.690543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.795413, "relative_start": 1.8124561309814453, "end": ********86.795413, "relative_end": ********86.795413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.803145, "relative_start": 1.820188045501709, "end": ********86.803145, "relative_end": ********86.803145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.863001, "relative_start": 1.8800442218780518, "end": ********86.863001, "relative_end": ********86.863001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********86.885301, "relative_start": 1.9023442268371582, "end": ********86.885301, "relative_end": ********86.885301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********87.011617, "relative_start": 2.0286600589752197, "end": ********87.011617, "relative_end": ********87.011617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********87.030142, "relative_start": 2.047185182571411, "end": ********87.030142, "relative_end": ********87.030142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********87.129944, "relative_start": 2.146987199783325, "end": ********87.129944, "relative_end": ********87.129944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********87.166619, "relative_start": 2.183662176132202, "end": ********87.168973, "relative_end": ********87.168973, "duration": 0.0023539066314697266, "duration_str": "2.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 54229888, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 39, "nb_templates": 39, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.754276, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.762175, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.773114, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.788535, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.796811, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.804609, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.809725, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.816071, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.876134, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.893981, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.942476, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.948979, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.985169, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.989073, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.018336, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.022173, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.051885, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.056766, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.122877, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.128339, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.239459, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.244964, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.288052, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.293827, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.367676, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.382574, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.479344, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.487915, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.571132, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.577014, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.685229, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.690518, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.795387, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.803124, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.862983, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********86.885264, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********87.011588, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********87.030122, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********87.129902, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00683, "accumulated_duration_str": "6.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.649257, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 12.299}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.658861, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.299, "width_percent": 8.199}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.661729, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.498, "width_percent": 6.735}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.665144, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.233, "width_percent": 7.76}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.666721, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.993, "width_percent": 5.124}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.683973, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.117, "width_percent": 24.744}, {"sql": "select count(*) as aggregate from `notification_events`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.743623, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.861, "width_percent": 25.183}, {"sql": "select * from `notification_events` order by `notification_events`.`id` asc limit 15 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.746128, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.044, "width_percent": 9.956}]}, "models": {"data": {"App\\Models\\NotificationEvent": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.resources.notification-event-resource.pages.list-notification-events #vVKt3ZGmBguj7wrh2oIX": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_active\" => array:1 [\n        \"value\" => null\n      ]\n      \"is_hierarchical\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:1 [\n      \"created_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.notification-event-resource.pages.list-notification-events\"\n  \"component\" => \"App\\Filament\\Resources\\NotificationEventResource\\Pages\\ListNotificationEvents\"\n  \"id\" => \"vVKt3ZGmBguj7wrh2oIX\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 93, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1725573850 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725573850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.689187, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1909042882 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909042882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695828, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-402738012 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402738012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721196, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845750018 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845750018\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.840368, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1247088527 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247088527\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.842953, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1909430765 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909430765\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862678, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1482227817 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482227817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867786, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255738971 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255738971\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873694, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-180232259 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180232259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.893618, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1407581782 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407581782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.902999, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1171176126 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171176126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.905767, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-660211816 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660211816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.933682, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-310024968 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310024968\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.935514, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1239018399 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239018399\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938942, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005799817 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005799817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948611, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907053042 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907053042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954189, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1132652357 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132652357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956594, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1392575152 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392575152\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.979578, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-374136288 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374136288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.982194, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-456026510 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456026510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.983792, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1364126025 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364126025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.988811, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-728478275 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728478275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.99337, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1764120012 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764120012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.995802, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1092380247 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092380247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.012029, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-18958154 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18958154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.01524, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1160545691 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160545691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.017004, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1854048760 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854048760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.021943, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1934195229 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934195229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.027124, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1536331679 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536331679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.028875, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1094093987 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094093987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.042292, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-191880546 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191880546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.044713, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1232589799 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232589799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.04682, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1244918976 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244918976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.056461, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1240162469 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240162469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.085756, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-799869748 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799869748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.089198, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1693297607 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693297607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.106589, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1765310364 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765310364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.108233, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-509709161 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509709161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.11977, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1622585680 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622585680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.12799, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2139133318 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139133318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.186993, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-236214326 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236214326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.195948, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-215924720 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215924720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.225496, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-613858011 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613858011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.227098, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1024368439 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024368439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.228946, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1721933396 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721933396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.244623, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648690913 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648690913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.252554, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076375813 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076375813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.255051, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083234547 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083234547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.275958, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-40581403 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40581403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.283566, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1459339534 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459339534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.285877, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1888370562 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888370562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.293462, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-434395223 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434395223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.306181, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1577206322 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577206322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.308835, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-143747067 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143747067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.352815, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1762790682 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762790682\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.356381, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648463243 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648463243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.361018, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-547213953 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\NotificationEvent(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547213953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.379893, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-613531529 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613531529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.39564, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-743434417 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743434417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.403696, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-728257910 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728257910\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.473013, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1950426904 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950426904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.47483, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1498896964 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498896964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.477223, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1319314346 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319314346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.487536, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1779228464 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779228464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.494105, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-295712564 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295712564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.496796, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-144583456 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144583456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.563535, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1323613283 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323613283\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.56646, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-81898186 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81898186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.568934, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=11),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-534489439 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534489439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.57661, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-346391658 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346391658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.654405, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1086557494 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086557494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.657311, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741503605 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741503605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.676246, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-905385565 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905385565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.678147, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-230776280 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230776280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.683206, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-678035358 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678035358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.690148, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1497553322 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497553322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.696747, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1094542498 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094542498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.741465, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1069782511 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069782511\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.788908, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-688462105 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688462105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.790817, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-656363544 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656363544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.793234, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-871187075 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871187075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.802727, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-823323932 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823323932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.837239, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1043019274 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043019274\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.840133, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1785188417 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785188417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.85667, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1656579835 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656579835\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.858472, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-830940622 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830940622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.860799, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1041454009 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041454009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.883666, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1306385506 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306385506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.91682, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1409508038 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409508038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.927225, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-446109632 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446109632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.972716, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-8333281 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8333281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********86.984619, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-156817144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156817144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********87.000969, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\NotificationEvent(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\NotificationEvent)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1898890146 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\NotificationEvent(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\NotificationEvent(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[0 =&gt; Object(App\\Models\\NotificationEvent)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898890146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********87.029189, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "2.26s", "peak_memory": "60MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-795615588 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-795615588\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1296878950 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1815 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;module&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;is_active&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;is_hierarchical&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[{&quot;created_at&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;vVKt3ZGmBguj7wrh2oIX&quot;,&quot;name&quot;:&quot;app.filament.resources.notification-event-resource.pages.list-notification-events&quot;,&quot;path&quot;:&quot;admin\\/notification-events&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;335f0888751f102ccad8a02bfcad1820b3626876e636a4c31b82c52f814721fa&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296878950\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-202667859 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2169</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/admin/notification-events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijg1WGNhQmVhTmc3NlZONVVVUUltY0E9PSIsInZhbHVlIjoiZE4vZ1JrUVBjQnJJanFSam9WazF5U1Z3cTF0dURISEE3TGZVaTlvU29ZeGRFdVdybmNjcExpaXBydldHdS9XQnBieHhuTDZwNGtLdnhuUlNOOUJZRUV2c3hHc2k1TWZTcys0ZWZrZXBkTm9iam41b3lqTjdsSTlwM3dmdnhqSTMiLCJtYWMiOiIwZDAzZjYyYWVjMWQ1ODE0MzkyY2U2N2ZlYWY1NTQzN2JjZTRlODY2YWI2ZjY4ZGU3MjJhNDRlYWE0YmY4ZTFhIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Iis5TDdyVzIvamdGa2UwRXJBalFjSHc9PSIsInZhbHVlIjoiWFFhTTBMMjJ3TkZlcVlBbUE3TDBscnpWVWxoV0RpODlYQ1daQkd5bnRRZEoyRTA0U0RVNTllZkdCT01YN3pmc3kyOVhjMkhiQzd4QU1tV2I5RGMrTit5aTF4N3VQQ3pGODNnYUNYTk4rUWxWdzhWeUQvLzkyTjI0bDZIU0U1akciLCJtYWMiOiI3NjJiNDJhNjVjNDI2ZDUwYjg0NmRjM2U2MTZjN2UxNDIzMzhjODkxZDY0MzUyMmMyODZlNWMwNmY2Mjc0MWZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202667859\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1971489425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971489425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2010826784 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 02:54:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010826784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1517803742 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/admin/notification-events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517803742\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}