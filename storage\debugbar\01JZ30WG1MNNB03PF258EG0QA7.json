{"__meta": {"id": "01JZ30WG1MNNB03PF258EG0QA7", "datetime": "2025-07-01 12:55:12", "utime": **********.181804, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 31, "messages": [{"message": "[12:55:11] LOG.info: [NOTIFICATION_SERVICE] Sending notification {\n    \"event\": \"milestone_completed\",\n    \"user_ids\": [\n        19\n    ],\n    \"data\": {\n        \"milestone_id\": 107,\n        \"milestone_title\": \"Week 1 Milestone\",\n        \"project_title\": \"Mobile App\",\n        \"status\": \"completed\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.880979, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Payment updated {\n    \"payment_id\": 410,\n    \"status\": \"paid\",\n    \"original_status\": \"pending\",\n    \"is_dirty_status\": true,\n    \"milestone_id\": 107,\n    \"project_id\": 162\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.893942, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Payment marked as paid, creating incentive {\n    \"payment_id\": 410,\n    \"new_status\": \"paid\",\n    \"old_status\": \"pending\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.89423, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Using project-based incentive calculation for T&M-USD {\n    \"payment_id\": 410,\n    \"project_id\": 162,\n    \"pricing_model\": \"T&M-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.896441, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Project completion calculation {\n    \"project_id\": 162,\n    \"paid_milestones_count\": 1,\n    \"paid_milestone_ids\": [\n        107\n    ],\n    \"total_percentage\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.898519, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Project completion recalculated {\n    \"payment_id\": 410,\n    \"project_id\": 162,\n    \"total_completion_percentage\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.89871, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Searching for incentive rules {\n    \"project_id\": 162,\n    \"user_id\": 19,\n    \"user_role_ids\": [\n        16\n    ],\n    \"pricing_model_name\": \"T&M-USD\",\n    \"product_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.901417, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Excluding product-specific rules for non-Product pricing model {\n    \"project_id\": 162,\n    \"pricing_model_name\": \"T&M-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.902086, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Found incentive rules {\n    \"project_id\": 162,\n    \"rules_count\": 1,\n    \"rules\": [\n        {\n            \"id\": 108,\n            \"pricing_model\": \"T&M-USD\",\n            \"role_id\": 16,\n            \"role_name\": \"jr_bde_team\",\n            \"product_id\": null,\n            \"product_name\": null,\n            \"currency\": \"INR\",\n            \"amount\": null\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.909047, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Found applicable incentive rules {\n    \"payment_id\": 410,\n    \"project_id\": 162,\n    \"applicable_rules_count\": 1,\n    \"rules\": [\n        108\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.909222, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Creating incentive for rule {\n    \"payment_id\": 410,\n    \"project_id\": 162,\n    \"rule_id\": 108,\n    \"rule_duration_percentage\": \"0.00\",\n    \"current_completion\": 0,\n    \"milestone_id\": 107\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.911756, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Skipping T&M-USD incentive: not all milestone hours fully paid {\n    \"payment_id\": 410,\n    \"fully_paid_hours\": 40,\n    \"project_hours\": 70\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.914891, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [NOTIFICATION_SERVICE] Sending notification {\n    \"event\": \"milestone_completed\",\n    \"user_ids\": [\n        19\n    ],\n    \"data\": {\n        \"milestone_id\": 108,\n        \"milestone_title\": \"Week 2 Milestone\",\n        \"project_title\": \"Mobile App\",\n        \"status\": \"completed\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.92131, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Payment updated {\n    \"payment_id\": 411,\n    \"status\": \"paid\",\n    \"original_status\": \"pending\",\n    \"is_dirty_status\": true,\n    \"milestone_id\": 108,\n    \"project_id\": 162\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.926499, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Payment marked as paid, creating incentive {\n    \"payment_id\": 411,\n    \"new_status\": \"paid\",\n    \"old_status\": \"pending\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.926737, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Using project-based incentive calculation for T&M-USD {\n    \"payment_id\": 411,\n    \"project_id\": 162,\n    \"pricing_model\": \"T&M-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.92838, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Project completion calculation {\n    \"project_id\": 162,\n    \"paid_milestones_count\": 2,\n    \"paid_milestone_ids\": [\n        107,\n        108\n    ],\n    \"total_percentage\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.930099, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Project completion recalculated {\n    \"payment_id\": 411,\n    \"project_id\": 162,\n    \"total_completion_percentage\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.930249, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Searching for incentive rules {\n    \"project_id\": 162,\n    \"user_id\": 19,\n    \"user_role_ids\": [\n        16\n    ],\n    \"pricing_model_name\": \"T&M-USD\",\n    \"product_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.931511, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Excluding product-specific rules for non-Product pricing model {\n    \"project_id\": 162,\n    \"pricing_model_name\": \"T&M-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.931791, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PROJECT] Found incentive rules {\n    \"project_id\": 162,\n    \"rules_count\": 1,\n    \"rules\": [\n        {\n            \"id\": 108,\n            \"pricing_model\": \"T&M-USD\",\n            \"role_id\": 16,\n            \"role_name\": \"jr_bde_team\",\n            \"product_id\": null,\n            \"product_name\": null,\n            \"currency\": \"INR\",\n            \"amount\": null\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.93404, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Found applicable incentive rules {\n    \"payment_id\": 411,\n    \"project_id\": 162,\n    \"applicable_rules_count\": 1,\n    \"rules\": [\n        108\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.934198, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Creating incentive for rule {\n    \"payment_id\": 411,\n    \"project_id\": 162,\n    \"rule_id\": 108,\n    \"rule_duration_percentage\": \"0.00\",\n    \"current_completion\": 0,\n    \"milestone_id\": 108\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.935814, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Calculating incentive amount {\n    \"project_id\": 162,\n    \"pricing_model\": \"T&M-USD\",\n    \"project_type\": \"Mobile App\",\n    \"rule_id\": 108\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.939968, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] T&M-USD Hours Completion Check {\n    \"project_id\": 162,\n    \"project_hours\": 70,\n    \"fully_paid_hours\": 70,\n    \"hours_completion_percentage\": 100\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.94219, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] T&M-USD Incentive Calculation (New) {\n    \"project_id\": 162,\n    \"total_payment_usd\": \"5000.00\",\n    \"project_hours\": 70,\n    \"fully_paid_hours\": 70,\n    \"rule_percentage\": \"50.00\",\n    \"incentive_amount_inr\": 2500,\n    \"calculation_note\": \"T&M-USD: All hours paid, USD amount converted to INR using percentage field from rule\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.942371, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Session conversion data check {\n    \"payment_id\": 411,\n    \"session_key\": \"payment_conversion_411\",\n    \"session_data\": null,\n    \"payment_exchange_source\": null,\n    \"payment_exchange_rate\": null,\n    \"project_currency\": \"USD\",\n    \"incentive_currency\": \"INR\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.942546, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Currency conversion setup {\n    \"payment_id\": 411,\n    \"project_id\": 162,\n    \"project_currency\": \"USD\",\n    \"incentive_currency\": \"INR\",\n    \"conversion_data\": {\n        \"conversion_method\": \"api\",\n        \"manual_exchange_rate\": null\n    },\n    \"original_amount\": 2500,\n    \"has_project_config\": false,\n    \"project_config\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.943224, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [INCENTIVE_OBSERVER] Incentive created {\n    \"incentive_id\": 89,\n    \"user_id\": 19,\n    \"status\": \"pending\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.945344, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [NOTIFICATION_SERVICE] Sending notification {\n    \"event\": \"incentive_submitted\",\n    \"user_ids\": [\n        1\n    ],\n    \"data\": {\n        \"incentive_id\": 89,\n        \"user_name\": \"<PERSON><PERSON><PERSON> khan\",\n        \"project_title\": \"Mobile App\",\n        \"amount\": \"2500.00\",\n        \"calculation_date\": \"2025-07-01T00:00:00.000000Z\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.955606, "xdebug_link": null, "collector": "log"}, {"message": "[12:55:11] LOG.info: [PAYMENT_OBSERVER] Incentive created successfully {\n    \"payment_id\": 411,\n    \"incentive_id\": 89,\n    \"storage_amount\": 2500,\n    \"storage_currency\": \"INR\",\n    \"original_amount\": 2500,\n    \"original_currency\": \"USD\",\n    \"exchange_rate\": null,\n    \"conversion_method\": \"same_currency\",\n    \"rule_type\": \"\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.965765, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.007951, "end": **********.181874, "duration": 1.1739230155944824, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": **********.007951, "relative_start": 0, "end": **********.411189, "relative_end": **********.411189, "duration": 0.*****************, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.411201, "relative_start": 0.*****************, "end": **********.181877, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "771ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.65009, "relative_start": 0.****************, "end": **********.652, "relative_end": **********.652, "duration": 0.0019099712371826172, "duration_str": "1.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.98873, "relative_start": 0.****************, "end": **********.98873, "relative_end": **********.98873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.997186, "relative_start": 0.****************, "end": **********.997186, "relative_end": **********.997186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.009425, "relative_start": 1.0014739036560059, "end": **********.009425, "relative_end": **********.009425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.021444, "relative_start": 1.0134930610656738, "end": **********.021444, "relative_end": **********.021444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.033937, "relative_start": 1.0259859561920166, "end": **********.033937, "relative_end": **********.033937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.042778, "relative_start": 1.0348269939422607, "end": **********.042778, "relative_end": **********.042778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.047565, "relative_start": 1.039613962173462, "end": **********.047565, "relative_end": **********.047565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.050783, "relative_start": 1.0428318977355957, "end": **********.050783, "relative_end": **********.050783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.057473, "relative_start": 1.0495219230651855, "end": **********.057473, "relative_end": **********.057473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.061358, "relative_start": 1.0534069538116455, "end": **********.061358, "relative_end": **********.061358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.098656, "relative_start": 1.0907049179077148, "end": **********.098656, "relative_end": **********.098656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.105155, "relative_start": 1.0972039699554443, "end": **********.105155, "relative_end": **********.105155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.138969, "relative_start": 1.1310179233551025, "end": **********.138969, "relative_end": **********.138969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.147181, "relative_start": 1.1392300128936768, "end": **********.147181, "relative_end": **********.147181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.171395, "relative_start": 1.1634440422058105, "end": **********.171395, "relative_end": **********.171395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.178251, "relative_start": 1.170300006866455, "end": **********.179466, "relative_end": **********.179466, "duration": 0.0012149810791015625, "duration_str": "1.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59070944, "peak_usage_str": "56MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 15, "nb_templates": 15, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.988713, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.997172, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.009413, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.02143, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.033924, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.042767, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.047553, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.050771, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.057451, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.061347, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.098642, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.10514, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.138955, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.14716, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.171379, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 160, "nb_statements": 160, "nb_visible_statements": 160, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09921999999999999, "accumulated_duration_str": "99.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 60 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `sessions` where `id` = 'OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33' limit 1", "type": "query", "params": [], "bindings": ["OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.6557891, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 1.018}, {"sql": "select * from `users` where `id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.664891, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.018, "width_percent": 0.706}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.667873, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.723, "width_percent": 0.423}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.670364, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.147, "width_percent": 0.403}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.671622, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.55, "width_percent": 0.282}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.687139, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.832, "width_percent": 0.625}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.692229, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.457, "width_percent": 0.554}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (19) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.696042, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.011, "width_percent": 0.564}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = 19) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) and `milestones`.`id` = '107' limit 1", "type": "query", "params": [], "bindings": [19, "107"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.737203, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.576, "width_percent": 5.311}, {"sql": "select * from `projects` where `projects`.`id` in (162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.743522, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.887, "width_percent": 0.534}, {"sql": "select * from `clients` where `clients`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 29, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 30, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 31, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.744713, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.421, "width_percent": 0.454}, {"sql": "select * from `milestones` where `project_id` = 162 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [162], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 264}], "start": **********.7490451, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:330", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=330", "ajax": false, "filename": "MilestoneResource.php", "line": "330"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.875, "width_percent": 0.675}, {"sql": "select * from `payments` where `milestone_id` = 107 limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.754669, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.55, "width_percent": 1.592}, {"sql": "select * from `payments` where `milestone_id` = 108 limit 1", "type": "query", "params": [], "bindings": [108], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.757976, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.143, "width_percent": 0.554}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.765651, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.697, "width_percent": 0.534}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.7670972, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.231, "width_percent": 0.443}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.7685978, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.674, "width_percent": 0.403}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.769615, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.078, "width_percent": 0.373}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.77091, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.451, "width_percent": 0.423}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.771902, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.874, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.773214, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.237, "width_percent": 0.645}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.774853, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.882, "width_percent": 0.635}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.776495, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.517, "width_percent": 0.464}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.777533, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.98, "width_percent": 0.323}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.778484, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.303, "width_percent": 0.323}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.7793381, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.625, "width_percent": 0.302}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.781697, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.928, "width_percent": 0.403}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.7828898, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.331, "width_percent": 0.383}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 413}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.783887, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:413", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=413", "ajax": false, "filename": "MilestoneResource.php", "line": "413"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.714, "width_percent": 0.262}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.7855492, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.976, "width_percent": 0.494}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.790246, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.47, "width_percent": 0.766}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.792073, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.236, "width_percent": 0.595}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 413}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.7935312, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:413", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=413", "ajax": false, "filename": "MilestoneResource.php", "line": "413"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.83, "width_percent": 0.292}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.7957978, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.123, "width_percent": 0.262}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.798712, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.385, "width_percent": 0.272}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.799835, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.657, "width_percent": 0.242}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.8008978, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.899, "width_percent": 0.222}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.8019319, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.12, "width_percent": 0.383}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.802842, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.503, "width_percent": 0.383}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.803782, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.886, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.804884, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.249, "width_percent": 0.423}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.8058, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.672, "width_percent": 0.474}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.8073108, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.146, "width_percent": 0.585}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.809233, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.731, "width_percent": 0.484}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.8103552, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.214, "width_percent": 0.272}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.811168, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.487, "width_percent": 0.323}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.812619, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.809, "width_percent": 0.393}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.813645, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.202, "width_percent": 0.373}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.814549, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.575, "width_percent": 0.413}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.815623, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.988, "width_percent": 0.383}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.816541, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.371, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.817499, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.734, "width_percent": 0.544}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.829523, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.278, "width_percent": 0.544}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.8308651, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.823, "width_percent": 0.443}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.832009, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.266, "width_percent": 0.443}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.833162, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.71, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.834124, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.042, "width_percent": 0.302}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.835143, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.344, "width_percent": 0.292}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.836908, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.637, "width_percent": 0.363}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.838028, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 32, "width_percent": 0.333}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8389652, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.332, "width_percent": 0.464}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.840673, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.796, "width_percent": 0.645}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.841961, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.441, "width_percent": 0.403}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8430212, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.844, "width_percent": 0.292}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.845863, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.136, "width_percent": 0.524}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8472328, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.66, "width_percent": 0.433}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8483121, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.094, "width_percent": 0.323}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.84933, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.416, "width_percent": 0.312}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.850199, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.729, "width_percent": 0.282}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.851108, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.011, "width_percent": 0.292}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8531501, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.303, "width_percent": 0.302}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8540838, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.606, "width_percent": 0.262}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.854907, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.868, "width_percent": 0.292}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.8559048, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.16, "width_percent": 0.605}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 451}], "start": **********.857676, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.765, "width_percent": 0.514}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.858829, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.279, "width_percent": 0.383}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.860504, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.662, "width_percent": 0.343}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.861511, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.004, "width_percent": 0.323}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.862394, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.327, "width_percent": 0.312}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.8632572, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.639, "width_percent": 0.272}, {"sql": "select * from `milestones` where `milestones`.`id` = 107 limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 513}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 111}], "start": **********.864376, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:513", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 513}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=513", "ajax": false, "filename": "MilestoneResource.php", "line": "513"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.911, "width_percent": 0.484}, {"sql": "update `milestones` set `percentage` = null, `status` = 'completed', `milestones`.`updated_at` = '2025-07-01 12:55:11' where `id` = 107", "type": "query", "params": [], "bindings": [null, "completed", "2025-07-01 12:55:11", 107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}], "start": **********.8658688, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "Milestone.php:121", "source": {"index": 14, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=121", "ajax": false, "filename": "Milestone.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.395, "width_percent": 4.314}, {"sql": "select exists(select * from `payments` where `milestone_id` = 107) as `exists`", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 81}, {"index": 18, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.8709998, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Milestone.php:81", "source": {"index": 11, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=81", "ajax": false, "filename": "Milestone.php", "line": "81"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.709, "width_percent": 0.433}, {"sql": "select * from `payments` where `milestone_id` = 107 limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 23}, {"index": 23, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.872412, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "MilestoneObserver.php:23", "source": {"index": 16, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FMilestoneObserver.php&line=23", "ajax": false, "filename": "MilestoneObserver.php", "line": "23"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.142, "width_percent": 1.149}, {"sql": "select * from `projects` where `projects`.`id` = 162 limit 1", "type": "query", "params": [], "bindings": [162], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 34}, {"index": 28, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 30, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 32, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.87465, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestoneObserver.php:34", "source": {"index": 21, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FMilestoneObserver.php&line=34", "ajax": false, "filename": "MilestoneObserver.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.291, "width_percent": 0.544}, {"sql": "select * from `users` where `users`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 37}, {"index": 28, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 30, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 32, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.8757281, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneObserver.php:37", "source": {"index": 21, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FMilestoneObserver.php&line=37", "ajax": false, "filename": "MilestoneObserver.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.835, "width_percent": 0.514}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 37}, {"index": 33, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 35, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 36, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 37, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.876682, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestoneObserver.php:37", "source": {"index": 26, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FMilestoneObserver.php&line=37", "ajax": false, "filename": "MilestoneObserver.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.349, "width_percent": 0.393}, {"sql": "select * from `notification_events` where `name` = 'milestone_completed' limit 1", "type": "query", "params": [], "bindings": ["milestone_completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 26}, {"index": 17, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 45}, {"index": 24, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 26, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.877759, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "NotificationService.php:26", "source": {"index": 16, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FServices%2FNotificationService.php&line=26", "ajax": false, "filename": "NotificationService.php", "line": "26"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.742, "width_percent": 0.716}, {"sql": "select * from `notification_preferences` where `notification_preferences`.`user_id` = 19 and `notification_preferences`.`user_id` is not null and `notification_event_id` = 7 limit 1", "type": "query", "params": [], "bindings": [19, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 62}, {"index": 20, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 45}, {"index": 27, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 29, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.881906, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "NotificationService.php:62", "source": {"index": 19, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FServices%2FNotificationService.php&line=62", "ajax": false, "filename": "NotificationService.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.458, "width_percent": 2.358}, {"sql": "select `id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 19 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [19, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 81}, {"index": 18, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 45}, {"index": 25, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 28, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8848171, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "NotificationService.php:81", "source": {"index": 17, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FServices%2FNotificationService.php&line=81", "ajax": false, "filename": "NotificationService.php", "line": "81"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.816, "width_percent": 0.474}, {"sql": "select * from `role_notification_settings` where `role_id` in (16)", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 82}, {"index": 16, "namespace": null, "name": "app/Observers/MilestoneObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\MilestoneObserver.php", "line": 45}, {"index": 23, "namespace": null, "name": "app/Models/Milestone.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Milestone.php", "line": 121}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 515}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8862572, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "NotificationService.php:82", "source": {"index": 15, "namespace": null, "name": "app/Services/NotificationService.php", "file": "D:\\wamp64\\www\\smms\\app\\Services\\NotificationService.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FServices%2FNotificationService.php&line=82", "ajax": false, "filename": "NotificationService.php", "line": "82"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.29, "width_percent": 2.762}, {"sql": "select * from `payments` where (`milestone_id` = 107) limit 1", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 111}], "start": **********.890331, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:533", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=533", "ajax": false, "filename": "MilestoneResource.php", "line": "533"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.052, "width_percent": 0.816}, {"sql": "update `payments` set `paid_date` = '2025-07-08 00:00:00', `status` = 'paid', `payment_method` = 'bank_transfer', `payments`.`updated_at` = '2025-07-01 12:55:11' where `id` = 410", "type": "query", "params": [], "bindings": ["2025-07-08 00:00:00", "paid", "bank_transfer", "2025-07-01 12:55:11", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 111}], "start": **********.89261, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:533", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=533", "ajax": false, "filename": "MilestoneResource.php", "line": "533"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.868, "width_percent": 0.494}, {"sql": "select * from `projects` where `projects`.`id` = 162 limit 1", "type": "query", "params": [], "bindings": [162], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 103}, {"index": 34, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 35, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 36, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 37, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}], "start": **********.894442, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PaymentObserver.php:103", "source": {"index": 21, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FPaymentObserver.php&line=103", "ajax": false, "filename": "PaymentObserver.php", "line": "103"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.362, "width_percent": 0.464}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 104}, {"index": 35, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 36, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 37, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 38, "namespace": null, "name": "vendor/filament/tables/src/Actions/Action.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Action.php", "line": 168}], "start": **********.8954508, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PaymentObserver.php:104", "source": {"index": 22, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FPaymentObserver.php&line=104", "ajax": false, "filename": "PaymentObserver.php", "line": "104"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.825, "width_percent": 0.433}, {"sql": "select * from `milestones` where `milestones`.`project_id` = 162 and `milestones`.`project_id` is not null and exists (select * from `payments` where `milestones`.`id` = `payments`.`milestone_id` and `status` = 'paid')", "type": "query", "params": [], "bindings": [162, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 884}, {"index": 17, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 496}, {"index": 18, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 112}, {"index": 31, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 32, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.8968139, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "PaymentObserver.php:884", "source": {"index": 16, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 884}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FPaymentObserver.php&line=884", "ajax": false, "filename": "PaymentObserver.php", "line": "884"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.259, "width_percent": 1.099}, {"sql": "select `role_id` from `model_has_roles` where `model_type` = 'App\\\\Models\\\\User' and `model_id` = 19 limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 508}, {"index": 16, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 112}, {"index": 29, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 30, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 31, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}], "start": **********.899046, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "PaymentObserver.php:508", "source": {"index": 15, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 508}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FObservers%2FPaymentObserver.php&line=508", "ajax": false, "filename": "PaymentObserver.php", "line": "508"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.357, "width_percent": 0.917}, {"sql": "select `role_id` from `model_has_roles` where `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `model_has_roles`.`model_id` = 19", "type": "query", "params": [], "bindings": ["App\\Models\\User", 19], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 120}, {"index": 14, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 519}, {"index": 15, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 112}, {"index": 28, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 29, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.900359, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Project.php:120", "source": {"index": 13, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=120", "ajax": false, "filename": "Project.php", "line": "120"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.275, "width_percent": 0.383}, {"sql": "select * from `incentive_rules` where exists (select * from `pricing_models` where `incentive_rules`.`pricing_model_id` = `pricing_models`.`id` and `name` = 'T&M-USD') and `role_id` in (16) and `product_id` is null", "type": "query", "params": [], "bindings": ["T&M-USD", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 171}, {"index": 16, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 519}, {"index": 17, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 112}, {"index": 30, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 31, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.9022088, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "Project.php:171", "source": {"index": 15, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 171}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=171", "ajax": false, "filename": "Project.php", "line": "171"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.658, "width_percent": 2.711}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 171}, {"index": 21, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 519}, {"index": 22, "namespace": null, "name": "app/Observers/PaymentObserver.php", "file": "D:\\wamp64\\www\\smms\\app\\Observers\\PaymentObserver.php", "line": 112}, {"index": 35, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 533}, {"index": 36, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.9054909, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Project.php:171", "source": {"index": 20, "namespace": null, "name": "app/Models/Project.php", "file": "D:\\wamp64\\www\\smms\\app\\Models\\Project.php", "line": 171}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=171", "ajax": false, "filename": "Project.php", "line": "171"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.369, "width_percent": 0.363}, {"sql": "select * from `roles` where `roles`.`id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9070399, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 61.732, "width_percent": 0.585}, {"sql": "select * from `incentives` where `project_id` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9093769, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 62.316, "width_percent": 1.451}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911063, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 63.767, "width_percent": 0.383}, {"sql": "select * from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9119601, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 64.15, "width_percent": 0.484}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912689, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 64.634, "width_percent": 1.471}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.914344, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.106, "width_percent": 0.312}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.915241, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.418, "width_percent": 0.474}, {"sql": "update `milestones` set `percentage` = ?, `status` = ?, `milestones`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.916146, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 66.892, "width_percent": 1.592}, {"sql": "select exists(select * from `payments` where `milestone_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918006, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 68.484, "width_percent": 0.403}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918636, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 68.887, "width_percent": 0.353}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.919183, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.24, "width_percent": 0.323}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.919699, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.563, "width_percent": 0.323}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.920219, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 69.885, "width_percent": 0.333}, {"sql": "select * from `notification_events` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.92073, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 70.218, "width_percent": 0.302}, {"sql": "select * from `notification_preferences` where `notification_preferences`.`user_id` = ? and `notification_preferences`.`user_id` is not null and `notification_event_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9215121, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 70.52, "width_percent": 0.343}, {"sql": "select `id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = ? and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922148, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 70.863, "width_percent": 0.403}, {"sql": "select * from `role_notification_settings` where `role_id` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9229121, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.266, "width_percent": 0.655}, {"sql": "select * from `payments` where (`milestone_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.924137, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 71.921, "width_percent": 0.645}, {"sql": "update `payments` set `paid_date` = ?, `status` = ?, `payment_method` = ?, `payments`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925647, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.566, "width_percent": 0.383}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926931, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 72.949, "width_percent": 0.433}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927656, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 73.382, "width_percent": 0.353}, {"sql": "select * from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null and exists (select * from `payments` where `milestones`.`id` = `payments`.`milestone_id` and `status` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928688, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 73.735, "width_percent": 1.038}, {"sql": "select `role_id` from `model_has_roles` where `model_type` = ? and `model_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930349, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 74.773, "width_percent": 0.504}, {"sql": "select `role_id` from `model_has_roles` where `model_has_roles`.`model_type` = ? and `model_has_roles`.`model_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930977, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 75.277, "width_percent": 0.363}, {"sql": "select * from `incentive_rules` where exists (select * from `pricing_models` where `incentive_rules`.`pricing_model_id` = `pricing_models`.`id` and `name` = ?) and `role_id` in (?) and `product_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931879, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 75.64, "width_percent": 0.645}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932761, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.285, "width_percent": 0.403}, {"sql": "select * from `roles` where `roles`.`id` in (16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93336, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.688, "width_percent": 0.292}, {"sql": "select * from `incentives` where `project_id` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93433, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 76.98, "width_percent": 0.524}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935078, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.505, "width_percent": 0.433}, {"sql": "select * from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935968, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 77.938, "width_percent": 0.524}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936699, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 78.462, "width_percent": 0.333}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9372032, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 78.795, "width_percent": 0.292}, {"sql": "select * from `incentives` where `project_id` = ? and `user_id` = ? and `incentive_rule_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937663, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.087, "width_percent": 0.443}, {"sql": "select * from `project_types` where `project_types`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938789, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.53, "width_percent": 0.413}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940436, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 79.944, "width_percent": 0.786}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`milestone_id` = ? and `payments`.`milestone_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9415, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 80.73, "width_percent": 0.393}, {"sql": "select * from `cache` where `key` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942669, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 81.123, "width_percent": 0.353}, {"sql": "insert into `incentives` (`user_id`, `project_id`, `milestone_id`, `incentive_rule_id`, `amount`, `currency`, `original_amount`, `original_currency`, `exchange_rate`, `conversion_method`, `api_source`, `conversion_date`, `calculation_date`, `status`, `description`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943671, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 81.476, "width_percent": 1.461}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945511, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 82.937, "width_percent": 0.464}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946169, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.401, "width_percent": 0.343}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946681, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.743, "width_percent": 0.302}, {"sql": "select * from `notification_events` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947198, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 84.046, "width_percent": 0.343}, {"sql": "select `role_id` from `role_notification_settings` where JSON_EXTRACT(notification_settings, '$.\"14\"') IS NOT NULL", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9477072, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 84.388, "width_percent": 4.001}, {"sql": "select * from `roles` where `id` in (?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951858, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 88.389, "width_percent": 0.403}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = ? and `name` in (?))", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952693, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 88.793, "width_percent": 1.915}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954839, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 90.708, "width_percent": 0.393}, {"sql": "select `role_id` from `role_notification_settings` where JSON_EXTRACT(notification_settings, '$.\"14\"') IS NOT NULL", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9557998, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 91.101, "width_percent": 0.937}, {"sql": "select * from `roles` where `id` in (?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9570692, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 92.038, "width_percent": 0.665}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = ? and `name` in (?))", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958349, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 92.703, "width_percent": 0.726}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959434, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 93.429, "width_percent": 0.443}, {"sql": "select * from `notification_preferences` where `notification_preferences`.`user_id` = ? and `notification_preferences`.`user_id` is not null and `notification_event_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96018, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 93.872, "width_percent": 0.373}, {"sql": "select `id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = ? and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960882, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.245, "width_percent": 0.494}, {"sql": "select * from `role_notification_settings` where `role_id` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961631, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.739, "width_percent": 0.383}, {"sql": "insert into `app_notifications` (`user_id`, `notification_event_id`, `title`, `message`, `data`, `status`, `sent_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9642081, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.122, "width_percent": 1.189}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = ?) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97963, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.311, "width_percent": 0.847}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = ?) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980629, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.158, "width_percent": 0.776}, {"sql": "select * from `projects` where `projects`.`id` in (161, 162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9816809, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.934, "width_percent": 0.423}, {"sql": "select * from `clients` where `clients`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982327, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.357, "width_percent": 0.343}, {"sql": "select `projects`.`title`, `projects`.`id` from `projects` order by `projects`.`title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.031756, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.7, "width_percent": 0.554}, {"sql": "select `clients`.`company_name`, `clients`.`id` from `clients` order by `clients`.`company_name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.040571, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.254, "width_percent": 0.746}]}, "models": {"data": {"App\\Models\\PricingModel": {"value": 70, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Role": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Project": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Payment": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Client": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\IncentiveRule": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FIncentiveRule.php&line=1", "ajax": false, "filename": "IncentiveRule.php", "line": "?"}}, "App\\Models\\ProjectType": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProjectType.php&line=1", "ajax": false, "filename": "ProjectType.php", "line": "?"}}, "App\\Models\\RoleNotificationSettings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRoleNotificationSettings.php&line=1", "ajax": false, "filename": "RoleNotificationSettings.php", "line": "?"}}}, "count": 125, "is_counter": true}, "livewire": {"data": {"app.filament.resources.milestone-resource.pages.list-milestones #MLIi0mWY8iKtHLkzeifz": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:5 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"project_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"client\" => array:1 [\n        \"value\" => null\n      ]\n      \"due_date\" => array:2 [\n        \"due_from\" => null\n        \"due_until\" => null\n      ]\n      \"amount_range\" => array:2 [\n        \"amount_from\" => null\n        \"amount_to\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => array:1 [\n      0 => []\n    ]\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => []\n    \"defaultTableActionArguments\" => []\n    \"defaultTableActionRecord\" => []\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.milestone-resource.pages.list-milestones\"\n  \"component\" => \"App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones\"\n  \"id\" => \"MLIi0mWY8iKtHLkzeifz\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 18, "messages": [{"message": "[\n  ability => create_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-622559093 data-indent-pad=\"  \"><span class=sf-dump-note>create_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">create_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622559093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.698922, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1250705816 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250705816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.699501, "xdebug_link": null}, {"message": "[\n  ability => reorder_milestone,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1199176584 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">reorder_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199176584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.708397, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Milestone,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1995568068 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995568068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.708515, "xdebug_link": null}, {"message": "[\n  ability => delete_any_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-590552873 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">delete_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590552873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73373, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-71699998 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71699998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.733862, "xdebug_link": null}, {"message": "[\n  ability => update_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-455442246 data-indent-pad=\"  \"><span class=sf-dump-note>update_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">update_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455442246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077132, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1533104304 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533104304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077289, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-838351932 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838351932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096998, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-728305640 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728305640\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097146, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1451051574 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451051574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104781, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1275829715 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275829715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104895, "xdebug_link": null}, {"message": "[\n  ability => update_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1301400194 data-indent-pad=\"  \"><span class=sf-dump-note>update_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">update_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301400194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111766, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-641941181 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641941181\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111908, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1576625592 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576625592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13737, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-802066359 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802066359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137591, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1995501457 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995501457\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146507, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1831242156 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831242156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.14672, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones@callMountedTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:70-161</a>", "middleware": "web", "duration": "1.18s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-887903731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-887903731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"3307 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;project_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;due_date&quot;:[{&quot;due_from&quot;:null,&quot;due_until&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;amount_range&quot;:[{&quot;amount_from&quot;:null,&quot;amount_to&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;editAll&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;milestones&quot;:[{&quot;9ee8c2f1-704e-48b5-bd1e-bdb6431b46aa&quot;:[{&quot;id&quot;:107,&quot;project_id&quot;:162,&quot;title&quot;:&quot;Week 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 2&quot;,&quot;due_date&quot;:&quot;2025-07-08&quot;,&quot;percentage&quot;:&quot;50.00&quot;,&quot;hours&quot;:&quot;40.00&quot;,&quot;amount&quot;:&quot;2500.00&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:52:46.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:52:46.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-08&quot;,&quot;payment_paid_date&quot;:&quot;2025-07-08&quot;,&quot;payment_method&quot;:&quot;bank_transfer&quot;,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;paid&quot;,&quot;payment_amount&quot;:&quot;2500.00&quot;,&quot;payment_notes&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;20fb20a6-27a4-457d-8de2-8f6d7e6c6502&quot;:[{&quot;id&quot;:108,&quot;project_id&quot;:162,&quot;title&quot;:&quot;Week 2 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 2 of 2&quot;,&quot;due_date&quot;:&quot;2025-07-15&quot;,&quot;percentage&quot;:&quot;50.00&quot;,&quot;hours&quot;:&quot;30.00&quot;,&quot;amount&quot;:&quot;2500.00&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:52:46.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:52:46.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-15&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2500.00&quot;,&quot;payment_notes&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:&quot;107&quot;,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;MLIi0mWY8iKtHLkzeifz&quot;,&quot;name&quot;:&quot;app.filament.resources.milestone-resource.pages.list-milestones&quot;,&quot;path&quot;:&quot;admin\\/milestones&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;d7a758f0d30bf876eaf33ffda6fa3dd9265390efe179e1b3c41e3c7cd028a3a2&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.20fb20a6-27a4-457d-8de2-8f6d7e6c6502.payment_paid_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.20fb20a6-27a4-457d-8de2-8f6d7e6c6502.payment_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">bank_transfer</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.20fb20a6-27a4-457d-8de2-8f6d7e6c6502.payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"22 characters\">callMountedTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4198</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjBjNUpza0JkbElON1VybGp1UENRT1E9PSIsInZhbHVlIjoiTlk4ZnZiQndFRFJWV21udDhRVkRJSHg1Z09WNG9TaDlvL3NlTWZQK3cwNWxwWHNMWXUvOFhBS3ZSaFdTNFVRbkdVSU1NWUxpd2VVaTZLdDJiaGJzVTE2dE9hOHNBY1BVcURRa05LcjRodEFyeXNkT0swaGZyR0pZVUZ0TGgxZTd1MHZ1VnVqVU01VWlxL01DSE5YYjRYcHVrR2NQTmhrUXNVQ1UvSGdSdnFrMG1Fa08ydGNRNFlFcjEvSzE3UTQ2N2VqL1FQT2JZSUM4V3pJeTdsSEs2TXF0RXFyRnJEcVZEdmkxeS8raE1qZz0iLCJtYWMiOiIxZWI0YTEyZDFmMmIxN2VkZjgxMTlkNjhhNWZkZmQ1N2JmYWFlZDdhZDJlM2YzZDg3MjkxOTI2YTE3ZDNjNDU2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZUMTIzblkxN1ZFK2hzRlR6OFBrTEE9PSIsInZhbHVlIjoiWDgrQisyNFhHOXV3ZVNCdlE1OE5RWnZUWkpIaVpqcERRSkREMTlvVmlySHh0VzY3RHBmMmRUQlp5a05Ub3N4UVJmYk0vUnlIWnA5cXdDV25wTWpMalNERDdrV3B4MDJXbFFmeEcrUldBbVJITVV4VW16azBhaFkyTCtHc1dpc1IiLCJtYWMiOiJmY2U5NWU5NGViYjdmODA4YmIyMzk5ZWMzMjQ0ZTM4MWI3Njc0NDcyM2ZlNjI3MDU1Zjk0Y2Q3MDdkZDk5OGNjIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Im9mTzBTTW5mTThhVDBkbGxsSzBSdGc9PSIsInZhbHVlIjoiM1owQVFZakJ6dUwrRmJwQlEzU1FQcGxBS3JGNXZTZ25wMFZDNTRVck1WZjVmZFRJZFRmUWlwbm1sdGFBSmtSamlwV2FhK21VaHNEUEJqM09kZ09KNGVrVGpnRnJOd05DeUppckR2OVRLMzJ2UE44eDRUa0xLYW9CMXFpbFRmaTQiLCJtYWMiOiIyYzY0ZTM3ZjdkZDkyNjc1YTg4NjkwMWRjZWNiYjEwYjA0NmJmOTk2ZmY5OGZmYzZmODA5ZWQ2NGNkMGZlMjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1395627750 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|ceSSE69Z6lQRjclxArTEUqvoloq2EMgMZKE49WMA10W3Vcuc7S97Dvhk23GU|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395627750\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-868802987 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:55:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868802987\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1380105200 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380105200\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}