{"__meta": {"id": "01JZ370C8CDS7HBXCQBS4Y13TE", "datetime": "2025-07-01 14:42:10", "utime": **********.828677, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380929.989156, "end": **********.828692, "duration": 0.8395359516143799, "duration_str": "840ms", "measures": [{"label": "Booting", "start": 1751380929.989156, "relative_start": 0, "end": **********.444232, "relative_end": **********.444232, "duration": 0.****************, "duration_str": "455ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.444247, "relative_start": 0.****************, "end": **********.828694, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.715708, "relative_start": 0.****************, "end": **********.717752, "relative_end": **********.717752, "duration": 0.0020439624786376953, "duration_str": "2.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.826107, "relative_start": 0.****************, "end": **********.826862, "relative_end": **********.826862, "duration": 0.0007550716400146484, "duration_str": "755μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 39, "nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.018649999999999996, "accumulated_duration_str": "18.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv' limit 1", "type": "query", "params": [], "bindings": ["6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.72321, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.753}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.7335389, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.753, "width_percent": 3.056}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.737268, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.81, "width_percent": 2.681}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.739823, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.491, "width_percent": 1.823}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7409081, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.314, "width_percent": 1.555}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.747516, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.869, "width_percent": 2.735}, {"sql": "select * from `payments` where year(`paid_date`) = 2025 and `status` in ('completed', 'paid') and `paid_date` is not null", "type": "query", "params": [], "bindings": [2025, "completed", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.7491739, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.603, "width_percent": 3.217}, {"sql": "select `id`, `currency` from `projects` where `projects`.`id` in (160, 161, 162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.752419, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.82, "width_percent": 2.842}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.765821, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.662, "width_percent": 2.735}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7670848, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.397, "width_percent": 1.18}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7689772, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.576, "width_percent": 2.627}, {"sql": "select * from `roles` where `name` = 'bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.772151, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.204, "width_percent": 2.788}, {"sql": "select * from `roles` where `name` = 'jr_bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["jr_bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.7734869, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.992, "width_percent": 2.091}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (15, 16))", "type": "query", "params": [], "bindings": ["App\\Models\\User", 15, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.776531, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.083, "width_percent": 4.772}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4, 6, 19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.778263, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.855, "width_percent": 1.823}, {"sql": "select * from `projects` where `projects`.`user_id` in (4, 6, 19) and `won_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.779511, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.678, "width_percent": 2.52}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.785952, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.198, "width_percent": 3.7}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7876348, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.898, "width_percent": 2.038}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '02'", "type": "query", "params": [], "bindings": ["paid", 2025, "02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.789288, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.936, "width_percent": 2.842}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '03'", "type": "query", "params": [], "bindings": ["paid", 2025, "03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.790735, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.777, "width_percent": 1.984}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '04'", "type": "query", "params": [], "bindings": ["paid", 2025, "04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.791905, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.761, "width_percent": 1.877}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '05'", "type": "query", "params": [], "bindings": ["paid", 2025, "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.7929869, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.638, "width_percent": 1.984}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '06'", "type": "query", "params": [], "bindings": ["paid", 2025, "06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.794132, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.622, "width_percent": 2.198}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '07'", "type": "query", "params": [], "bindings": ["paid", 2025, "07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.795295, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.82, "width_percent": 2.145}, {"sql": "select * from `projects` where `status` in ('planning', 'in_progress')", "type": "query", "params": [], "bindings": ["planning", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}], "start": **********.796573, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:252", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=252", "ajax": false, "filename": "RevenueForecastChart.php", "line": "252"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.965, "width_percent": 1.716}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.803463, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.681, "width_percent": 2.52}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.804724, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.201, "width_percent": 1.609}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '02' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["02", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.80622, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.81, "width_percent": 2.842}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '02' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["02", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.807578, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.651, "width_percent": 3.324}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '03' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["03", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.808826, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.976, "width_percent": 1.823}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '03' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["03", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8098578, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.799, "width_percent": 3.164}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '04' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["04", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.811074, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.962, "width_percent": 2.145}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '04' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["04", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.812126, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.107, "width_percent": 3.271}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '05' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["05", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.813548, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.378, "width_percent": 1.877}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '05' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["05", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.814589, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.255, "width_percent": 3.378}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '06' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.816034, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.633, "width_percent": 1.609}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '06' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["06", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.81734, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.241, "width_percent": 3.271}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '07' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8186278, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.512, "width_percent": 3.217}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '07' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["07", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.81999, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.729, "width_percent": 3.271}]}, "models": {"data": {"App\\Models\\Payment": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.monthly-revenue-chart #l4QiAGq9U5hZsQnyOs3T": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"current_year\"\n    \"dataChecksum\" => \"cd90f05fa1811a21a22ad08c895b2b70\"\n  ]\n  \"name\" => \"app.filament.widgets.monthly-revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MonthlyRevenueChart\"\n  \"id\" => \"l4QiAGq9U5hZsQnyOs3T\"\n]", "app.filament.widgets.bde-performance-chart #y5Dk3F4LMWh86mUEoo9s": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"this_month\"\n    \"dataChecksum\" => \"705f9f6a260c944b564b927a292dad89\"\n  ]\n  \"name\" => \"app.filament.widgets.bde-performance-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\BdePerformanceChart\"\n  \"id\" => \"y5Dk3F4LMWh86mUEoo9s\"\n]", "app.filament.widgets.revenue-forecast-chart #GTnaFrPUylU7Y7C1ZWWz": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"6_months\"\n    \"dataChecksum\" => \"0b4287a0535fd4b3b3dc29e9fa3d8c06\"\n  ]\n  \"name\" => \"app.filament.widgets.revenue-forecast-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueForecastChart\"\n  \"id\" => \"GTnaFrPUylU7Y7C1ZWWz\"\n]", "app.filament.widgets.milestone-due-vs-received-chart #VDHmXCEI8wgSzPENY8zc": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"last_6_months\"\n    \"dataChecksum\" => \"6edec273f189350582f767f28c874ffb\"\n  ]\n  \"name\" => \"app.filament.widgets.milestone-due-vs-received-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MilestoneDueVsReceivedChart\"\n  \"id\" => \"VDHmXCEI8wgSzPENY8zc\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\MonthlyRevenueChart@updateChartData<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "843ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-632237567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-632237567\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1081631402 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"357 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;current_year&quot;,&quot;dataChecksum&quot;:&quot;cd90f05fa1811a21a22ad08c895b2b70&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;l4QiAGq9U5hZsQnyOs3T&quot;,&quot;name&quot;:&quot;app.filament.widgets.monthly-revenue-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;3630d55051f9780f66fe1543101b319cb942583e757c43ddfe6f579860a67be9&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"355 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;this_month&quot;,&quot;dataChecksum&quot;:&quot;705f9f6a260c944b564b927a292dad89&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;y5Dk3F4LMWh86mUEoo9s&quot;,&quot;name&quot;:&quot;app.filament.widgets.bde-performance-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c34297350c8ab8a906726eedb90882cf8f6e4e3dc9f6a05ac7681d6d4814df4c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;6_months&quot;,&quot;dataChecksum&quot;:&quot;0b4287a0535fd4b3b3dc29e9fa3d8c06&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;GTnaFrPUylU7Y7C1ZWWz&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-forecast-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2f6fb342c5b8d12dd747e43ce4019f34ac5807f0d020e2800c1dc938d1cfdf8f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"368 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;last_6_months&quot;,&quot;dataChecksum&quot;:&quot;6edec273f189350582f767f28c874ffb&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;VDHmXCEI8wgSzPENY8zc&quot;,&quot;name&quot;:&quot;app.filament.widgets.milestone-due-vs-received-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;bef99681dbb22bdf7066d93400d06c476c9a9f0f744879b94dfc5057175cec72&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081631402\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1032563594 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2042</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjRDZXJsRHN1Ui9CUGlYZnhvT2pzS1E9PSIsInZhbHVlIjoiQk41QzFEOUh2Z1ZZN09sR0QzZHhhMWlKS2JERXkyT3E1SjZ5UDRnK205bXplVXZ6anZFOU9PYW9TV0lQV25aVXIwSUwzR2pMd1VHZHlkOVFPdDZkK2QveW9EcmRyMVlMcEMvNVhQU0x1MlJEeDdrMytyUmJycVdFZDZoS2NiM2xpbGVUZUZOYk1HM3J0cHI4Q3o4aVcwZ3ZtcENQRUdEWkdRcHlMbXNsUzlYYk1HVGdHK2pGYlRTRk5hL1A3Z2h1WjhidWpvamRNRXEvVFhRN25RWXZTVlRsai9LYTFRRCt0V0pWVjZocW45TT0iLCJtYWMiOiIzYjcxNzQ0ZmFkMGNjMGQ1YzYwM2I0NjczNGI5NjRmYWYyNDg5NmI3ZDRjNGJlOGFhZWFmZjlhZmQzY2RjZWU3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IjdqU3J1Tk0rU1BvOUF5UDZla1lubGc9PSIsInZhbHVlIjoiSXJzUWllLzgzS0M1T1dUblk0YmRsZzFaV0tHMVJhcjZFV2Q5U2FVZ05heDhTRktyK1hFY1pDby9Vb0o2Q3lsL1pyQUdHcGtiTmNnYm9MMlp6TFcxNzFvRHlaYXpRdVZETXowSkF4M3RwTDNjN090OWt3VWt6cGV1MHJwaEZkVEciLCJtYWMiOiJjNzJmOGMxOWE4NmQzM2QzZGUyOTllNGQ4Yjc1MmZkMzI5NWJlMGY0YTAwMjNiYmJhMGE4OThlYmZjYjA5ZjVkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjVZTm92OWxONFZ2dDl0NWZvRkM0eXc9PSIsInZhbHVlIjoibjRqaEduV1hIeUkwQUFOWnlVTnhKSEtZTmtiT2RpczZjUFFYaFBIcFl2L0JmRkI3cjV6SmhNQSs5UUxEQ295SnZtanY5cHBOMzBaeGVkVHVyM29WMC9qWk03am4vcVV5WUhVY1VVUzBMT3dCdm9SanNhOTgzdUNISXl0QzhOKzUiLCJtYWMiOiI3ZjVkMmY5MmQ0MTYzOGZkNGI3Mjg0MGZlMDczMGQ5MDAxOWVjMzg1NDc3ZjEwZTYxNWUyMTIxMDEyYWM1ODMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032563594\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610599225 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610599225\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-232323130 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:42:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232323130\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1552160659 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552160659\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}