{"__meta": {"id": "01JZ28FAMKJCFY4MWDQSPCHNJW", "datetime": "2025-07-01 05:48:34", "utime": **********.838031, "method": "POST", "uri": "/admin/logout", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[05:48:34] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/logout\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.821055, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.096401, "end": **********.838048, "duration": 0.7416470050811768, "duration_str": "742ms", "measures": [{"label": "Booting", "start": **********.096401, "relative_start": 0, "end": **********.484475, "relative_end": **********.484475, "duration": 0.****************, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.484487, "relative_start": 0.*****************, "end": **********.83805, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.784443, "relative_start": 0.****************, "end": **********.786279, "relative_end": **********.786279, "duration": 0.0018360614776611328, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.828622, "relative_start": 0.****************, "end": **********.831308, "relative_end": **********.831308, "duration": 0.0026857852935791016, "duration_str": "2.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.8342, "relative_start": 0.****************, "end": **********.834281, "relative_end": **********.834281, "duration": 8.106231689453125e-05, "duration_str": "81μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009529999999999999, "accumulated_duration_str": "9.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'lQBWlFYAN2c61M6nxeoOBxxO63oTyWcSr6ics6k5' limit 1", "type": "query", "params": [], "bindings": ["lQBWlFYAN2c61M6nxeoOBxxO63oTyWcSr6ics6k5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.793513, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 21.406}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.8006659, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.406, "width_percent": 17.209}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.804907, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.615, "width_percent": 6.925}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.808211, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.54, "width_percent": 9.129}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.81034, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.669, "width_percent": 4.407}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.8137898, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.077, "width_percent": 4.092}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.814894, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.169, "width_percent": 3.987}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.816097, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.156, "width_percent": 3.463}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.8171709, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.619, "width_percent": 2.413}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.81798, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.033, "width_percent": 3.043}, {"sql": "update `users` set `remember_token` = 'sPa90Sya520RCO4UZaWteRKQYL8v2mRifn9zs3IuMQRWpPnaBt1XFp3NCRt3' where `id` = 1", "type": "query", "params": [], "bindings": ["sPa90Sya520RCO4UZaWteRKQYL8v2mRifn9zs3IuMQRWpPnaBt1XFp3NCRt3", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 100}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 700}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 630}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Http/Controllers/Auth/LogoutController.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Controllers\\Auth\\LogoutController.php", "line": 12}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.8225858, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:100", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=100", "ajax": false, "filename": "EloquentUserProvider.php", "line": "100"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.076, "width_percent": 6.401}, {"sql": "delete from `sessions` where `id` = 'lQBWlFYAN2c61M6nxeoOBxxO63oTyWcSr6ics6k5'", "type": "query", "params": [], "bindings": ["lQBWlFYAN2c61M6nxeoOBxxO63oTyWcSr6ics6k5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 583}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Http/Controllers/Auth/LogoutController.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Controllers\\Auth\\LogoutController.php", "line": 14}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.8268049, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.476, "width_percent": 4.197}, {"sql": "select * from `sessions` where `id` = 'BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO' limit 1", "type": "query", "params": [], "bindings": ["BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.831752, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.674, "width_percent": 5.352}, {"sql": "insert into `sessions` (`payload`, `last_activity`, `user_id`, `ip_address`, `user_agent`, `id`) values ('YToyOntzOjY6Il90b2tlbiI7czo0MDoiVk9uMEE5aVBxMWxIeVdnOGtuNmZMWWhmZGxrc1Q5b3ZlRE5UTXJhNSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********, null, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO')", "type": "query", "params": [], "bindings": ["YToyOntzOjY6Il90b2tlbiI7czo0MDoiVk9uMEE5aVBxMWxIeVdnOGtuNmZMWWhmZGxrc1Q5b3ZlRE5UTXJhNSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==", **********, null, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 157}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 141}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.833072, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:157", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=157", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "157"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.025, "width_percent": 3.778}, {"sql": "select * from `sessions` where `id` = 'BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO' limit 1", "type": "query", "params": [], "bindings": ["BB5nKg9liwNbrS4hvaKNEr0S5eaoJMTUbUGxpnBO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/php-debugbar/php-debugbar/src/DebugBar/DebugBar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php", "line": 446}], "start": **********.8368049, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.803, "width_percent": 4.197}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/admin/logout", "action_name": "filament.admin.auth.logout", "controller_action": "Filament\\Http\\Controllers\\Auth\\LogoutController", "uri": "POST admin/logout", "controller": "Filament\\Http\\Controllers\\Auth\\LogoutController", "prefix": "/admin", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor", "duration": "743ms", "peak_memory": "52MB", "response": "Redirect to http://localhost:8000/admin/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9FAt8Mi3h06m0zLMruEYqOw9I0nV2RQeRovYmdCC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1001678089 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6ImNmTVR3RklWSUgzWWFhM1VrcFZWelE9PSIsInZhbHVlIjoiZDB2eVF6ZjloVzNSQUtPWTZYOFFxd2xnYUk4TEVHSnorZ1hxOXVWWlljMEl6YmdaZUR2a2EvNXM3VHU4MmNkZWJ2NkVnQlAybUQ3RVI2MjNCMVVmbkpHcjhQYVcydVVvNFZoNXI4ZkJzV0s2cjBNeUlLK1hPQWVlOGpHNU1iSVFta3dVYzFFNlArWkVORklQNGFFdGplSzZ1YnFiWWZEOEw4U3dqOWY0aDdQUXRiVjdlOUxTdnRjcS9jVDZ4bE1MZU04ZWgwbW1iS21ibEFUNFJ5UHcyK285T3hLVWtlVlc2enNaRGtBSlBEaz0iLCJtYWMiOiJkNDVhOTFmYjdjNGM2MDYzZmIyNGJkNWUzMDQyODhiNzRhOGQ2MWY1NjllYmQ1YzliNDZmN2NjZWRiODY3YjM4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImFuWnp2NCtXMUVpN1BHSmQwTTFNV1E9PSIsInZhbHVlIjoibDI3ZzJST2FtS2dSN2lVa3VUU3g5cDBsWkQyaUc4OUU0QnNSMS94YXZ5anAxSlpmRE5jazZjbGVzL0VHZGFTMy8xZTczRytJVjh4Y3M5RHZ0TUJZUXpzTnNXQlYrbVQ3WnM5alpHRUZjeUdJVmJtaVBxd0hyRW9LbTZZNmorZTEiLCJtYWMiOiIyZWFkYmMwZmJmYzM1MGFmOGM1MjUzODk4N2JlZWM2ZmQwOWZlNzZlMWQ3N2Y3ZDE4NTBkMzFhZDhkMzI2YTdlIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkcrK1RPd1JlOUNDUE12YThtQlBUV2c9PSIsInZhbHVlIjoia3hFSDRnL1FMVmZ1WCtzQ2VoVTN5UnRmbTF6VGFLQmVrbmNVcVY2Q1R5dXNWOWgzVUpEd3AybCtZOWk0RmpNMmx3N3ZhejBraERqWDRGTjVFMGdLQng0RHJwYkgyWElHWEdVR2pPcW0rY3laM3YzaGsyZTI1ZTZ2djEweUhWR1YiLCJtYWMiOiJlMjk3NzBiYTE4YWJjYmM0OGUxZGI5ZjljY2YwYmExY2UwYmNlMWY2Y2I5NmY3NDVjMzM3YWQ5NDEwMjM1ZmVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001678089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|psNdoAELXD1VYordkzPebsz3tf9LAuPo8QopZLV3VIhwFmm7SGVkJUsifn8x|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9FAt8Mi3h06m0zLMruEYqOw9I0nV2RQeRovYmdCC</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lQBWlFYAN2c61M6nxeoOBxxO63oTyWcSr6ics6k5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1315350522 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 05:48:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVLNjlQaWVnNE5jN2J0L0VQVlNCYnc9PSIsInZhbHVlIjoiMVdxeTJLYkN2dlhVakViVGlibGlEaTlaYjFIMUM5ZDRmdWp4b3pPaUZ2Q0NtMnZydEp0TzNDdTdrdGF6WUJTdkQ0aG0yR3drSGZyV09xSWh0S2w3V3VZb1BERVVYOHVFZ0xhTkQ4QnJEU0M3VnlYZGg5cElGMWl5dDgybm5YK20iLCJtYWMiOiI4YjBiMDI0MmVjZTBiZDFkMzFiZjhjYWYzMDA4NWNiOWVkYTg4ZmE4NmJkYTBhNzZmZjMxY2I3MGMwODBkY2YwIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 07:48:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6ImJYOTkzMWVtSXAwTHArTjVEL3VSSUE9PSIsInZhbHVlIjoiVDBWNkdIUTVVMHZRMHRndHNDUk0xQVJMbHBtWm5ZZ1BDNUF0b2l6KzZQNzlrY0g0WFNUT2c0Nk9QczI1QUFEdERsTDl6K3FwbmljRWNpY0pMV08yLzNvQ0Y4VE9KTml0RkJqOElFV01JMCtBaTJSMzlvMlBDd0ZBdEw0c1kvUGsiLCJtYWMiOiI1YzMzYjQ1M2EwYjI5ZTZkMjNjMDFjZDk4NjRhMDU0ODFhYmM4NWE0MDM5NDkzN2Y0MDllOTFmN2EyMTFhMzU5IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 07:48:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"396 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlErYzlYc1ZqcmlNa3IxaGNrV25NRWc9PSIsInZhbHVlIjoiMUk2ZGxiRG84eUpKbDR5bHJ6ZGtjUWROOWRpM3BrcVZxditxeWJZYm9tVThDQzFrRCtCNThDNFNqMjBWUHlRTSIsIm1hYyI6IjViOGI2YmRhNTAyOTA3MmQ0ZWIzYzVmN2UwODgyNTE0OTdkYzBlZGY1NGZlNzg3NzY4OWRlNGVjMzljNTg4YTgiLCJ0YWciOiIifQ%3D%3D; expires=Thu, 02 Jul 2020 05:48:34 GMT; Max-Age=0; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVLNjlQaWVnNE5jN2J0L0VQVlNCYnc9PSIsInZhbHVlIjoiMVdxeTJLYkN2dlhVakViVGlibGlEaTlaYjFIMUM5ZDRmdWp4b3pPaUZ2Q0NtMnZydEp0TzNDdTdrdGF6WUJTdkQ0aG0yR3drSGZyV09xSWh0S2w3V3VZb1BERVVYOHVFZ0xhTkQ4QnJEU0M3VnlYZGg5cElGMWl5dDgybm5YK20iLCJtYWMiOiI4YjBiMDI0MmVjZTBiZDFkMzFiZjhjYWYzMDA4NWNiOWVkYTg4ZmE4NmJkYTBhNzZmZjMxY2I3MGMwODBkY2YwIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 07:48:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6ImJYOTkzMWVtSXAwTHArTjVEL3VSSUE9PSIsInZhbHVlIjoiVDBWNkdIUTVVMHZRMHRndHNDUk0xQVJMbHBtWm5ZZ1BDNUF0b2l6KzZQNzlrY0g0WFNUT2c0Nk9QczI1QUFEdERsTDl6K3FwbmljRWNpY0pMV08yLzNvQ0Y4VE9KTml0RkJqOElFV01JMCtBaTJSMzlvMlBDd0ZBdEw0c1kvUGsiLCJtYWMiOiI1YzMzYjQ1M2EwYjI5ZTZkMjNjMDFjZDk4NjRhMDU0ODFhYmM4NWE0MDM5NDkzN2Y0MDllOTFmN2EyMTFhMzU5IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 07:48:34 GMT; path=/; httponly</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"371 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlErYzlYc1ZqcmlNa3IxaGNrV25NRWc9PSIsInZhbHVlIjoiMUk2ZGxiRG84eUpKbDR5bHJ6ZGtjUWROOWRpM3BrcVZxditxeWJZYm9tVThDQzFrRCtCNThDNFNqMjBWUHlRTSIsIm1hYyI6IjViOGI2YmRhNTAyOTA3MmQ0ZWIzYzVmN2UwODgyNTE0OTdkYzBlZGY1NGZlNzg3NzY4OWRlNGVjMzljNTg4YTgiLCJ0YWciOiIifQ%3D%3D; expires=Thu, 02-Jul-2020 05:48:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315350522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-785502424 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VOn0A9iPq1lHyWg8kn6fLYhfdlksT9oveDNTMra5</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785502424\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/admin/logout", "action_name": "filament.admin.auth.logout", "controller_action": "Filament\\Http\\Controllers\\Auth\\LogoutController"}, "badge": "302 Found"}}