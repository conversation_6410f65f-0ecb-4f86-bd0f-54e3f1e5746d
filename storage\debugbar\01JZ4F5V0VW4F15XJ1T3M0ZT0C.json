{"__meta": {"id": "01JZ4F5V0VW4F15XJ1T3M0ZT0C", "datetime": "2025-07-02 02:24:12", "utime": **********.827802, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.169982, "end": **********.827813, "duration": 0.6578309535980225, "duration_str": "658ms", "measures": [{"label": "Booting", "start": **********.169982, "relative_start": 0, "end": **********.552353, "relative_end": **********.552353, "duration": 0.****************, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.552363, "relative_start": 0.*****************, "end": **********.827815, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.8099, "relative_start": 0.****************, "end": **********.815306, "relative_end": **********.815306, "duration": 0.005405902862548828, "duration_str": "5.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.82539, "relative_start": 0.****************, "end": **********.825997, "relative_end": **********.825997, "duration": 0.0006070137023925781, "duration_str": "607μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.826019, "relative_start": 0.****************, "end": **********.826044, "relative_end": **********.826044, "duration": 2.5033950805664062e-05, "duration_str": "25μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "658ms", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-538228847 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538228847\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1115759125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115759125\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1066036702 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFCYTVXZHdYUjdjNE1uVkFnVjlqcGc9PSIsInZhbHVlIjoiTnVlWWdmVDZraFBFWHNuc0VBVFNhK3lvNGtpd2V0cENSOWJQZ3pNWlBVbFRyOU9aRS9vVTA3OFFKYnBBLzN3bDR1YlJUcG9RckIrSDFXa3RUeU91b25ZN0p2a0N5UWtZVUtTNnBGZ1FTSEludk5HWUtTUURncFFrdFVyZXBUUGZhaGNXbEphL0JXK3lESWY5VnFHVkt0V0MvZDk0c1h4MTVnMEF5OUtEVzFmUFA5Y1hvdU00eG84SFRLZDd1MzRidnR0RFVSQkdSTVdmZEpMZis5TVJYNkhOamM2UnJCQldpa2tmeVBXbzNxOD0iLCJtYWMiOiI5NDY3NWVkYzY3M2IwYmRlNjA4MmY1YjY2YWIxZDExMTg0NzY0Y2ExNTBhNmEwYTgxNDdjY2MwOGI0NjJhMjg4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlF6TkFTbGVjTUhEQUtuK2UyeUFLc2c9PSIsInZhbHVlIjoiZ2t6VklmM3NJMEd0VkhBOEdUcnVLRS9pNWpvMTJ3SFNHOUt1K21tbTVkSDBQczcxL2wwTGRuNjlBZWVqRTBmdXdVdGxjMW9IUFZLUFFEYVZNM0N0MGM1ZGtkblZhN1pYWFlENDN1Z0hYZkdZOFNEdlJVR0NtaGZrV1pYOE05d0siLCJtYWMiOiJhODVmMTk5ZTZjMDBkZDQ3N2Y5ZDVjNTVjZThlZDhiNTE4ZjNjNDM4MWI2NDBjZDY5OTkzMGFhZTE3YTBkM2I3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkdsQy9lWWErSFFvVWxRV0FkRkEwcUE9PSIsInZhbHVlIjoiUGxXRFZqTjdKT3ZNZGhNN3pJQ1k5WmRtc3VFYVpWUnd0bld1THM4d0llY1ArQkV4MEhUSDY0aEdSZ1g0ZU40bVhvRGYyNjBkdjBOWU5veTVwb1djMTNaVStwc01NYzJqNFhzenphckk3dVA4cjQ3MTdrSkNJRUFLTDdLUE5WWVkiLCJtYWMiOiI0ZDE4NTlmNTMxOWM2NWZjNjI5YjgyZGRkNjdkZDRkMWY2NTA0NTM4ODdiOGIxMmFlYzg0YjRlZDc4OWI2OWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066036702\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-995290507 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6InFCYTVXZHdYUjdjNE1uVkFnVjlqcGc9PSIsInZhbHVlIjoiTnVlWWdmVDZraFBFWHNuc0VBVFNhK3lvNGtpd2V0cENSOWJQZ3pNWlBVbFRyOU9aRS9vVTA3OFFKYnBBLzN3bDR1YlJUcG9RckIrSDFXa3RUeU91b25ZN0p2a0N5UWtZVUtTNnBGZ1FTSEludk5HWUtTUURncFFrdFVyZXBUUGZhaGNXbEphL0JXK3lESWY5VnFHVkt0V0MvZDk0c1h4MTVnMEF5OUtEVzFmUFA5Y1hvdU00eG84SFRLZDd1MzRidnR0RFVSQkdSTVdmZEpMZis5TVJYNkhOamM2UnJCQldpa2tmeVBXbzNxOD0iLCJtYWMiOiI5NDY3NWVkYzY3M2IwYmRlNjA4MmY1YjY2YWIxZDExMTg0NzY0Y2ExNTBhNmEwYTgxNDdjY2MwOGI0NjJhMjg4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlF6TkFTbGVjTUhEQUtuK2UyeUFLc2c9PSIsInZhbHVlIjoiZ2t6VklmM3NJMEd0VkhBOEdUcnVLRS9pNWpvMTJ3SFNHOUt1K21tbTVkSDBQczcxL2wwTGRuNjlBZWVqRTBmdXdVdGxjMW9IUFZLUFFEYVZNM0N0MGM1ZGtkblZhN1pYWFlENDN1Z0hYZkdZOFNEdlJVR0NtaGZrV1pYOE05d0siLCJtYWMiOiJhODVmMTk5ZTZjMDBkZDQ3N2Y5ZDVjNTVjZThlZDhiNTE4ZjNjNDM4MWI2NDBjZDY5OTkzMGFhZTE3YTBkM2I3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdsQy9lWWErSFFvVWxRV0FkRkEwcUE9PSIsInZhbHVlIjoiUGxXRFZqTjdKT3ZNZGhNN3pJQ1k5WmRtc3VFYVpWUnd0bld1THM4d0llY1ArQkV4MEhUSDY0aEdSZ1g0ZU40bVhvRGYyNjBkdjBOWU5veTVwb1djMTNaVStwc01NYzJqNFhzenphckk3dVA4cjQ3MTdrSkNJRUFLTDdLUE5WWVkiLCJtYWMiOiI0ZDE4NTlmNTMxOWM2NWZjNjI5YjgyZGRkNjdkZDRkMWY2NTA0NTM4ODdiOGIxMmFlYzg0YjRlZDc4OWI2OWM5IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995290507\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-392817095 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 02 Jul 2026 02:24:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 09:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 02:24:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392817095\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-531920714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-531920714\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}