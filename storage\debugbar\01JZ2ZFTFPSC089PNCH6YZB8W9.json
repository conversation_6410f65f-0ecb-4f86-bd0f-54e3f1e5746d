{"__meta": {"id": "01JZ2ZFTFPSC089PNCH6YZB8W9", "datetime": "2025-07-01 12:30:48", "utime": *********8.311974, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": *********6.225605, "end": *********8.311996, "duration": 2.086390972137451, "duration_str": "2.09s", "measures": [{"label": "Booting", "start": *********6.225605, "relative_start": 0, "end": *********6.690254, "relative_end": *********6.690254, "duration": 0.***************, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": *********6.690716, "relative_start": 0.*****************, "end": *********8.311999, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.363127, "relative_start": 1.****************, "end": **********.365448, "relative_end": **********.365448, "duration": 0.002321004867553711, "duration_str": "2.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.560806, "relative_start": 1.****************, "end": **********.560806, "relative_end": **********.560806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.570834, "relative_start": 1.***************, "end": **********.570834, "relative_end": **********.570834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.580833, "relative_start": 1.3552279472351074, "end": **********.580833, "relative_end": **********.580833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.595469, "relative_start": 1.3698639869689941, "end": **********.595469, "relative_end": **********.595469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.60746, "relative_start": 1.3818550109863281, "end": **********.60746, "relative_end": **********.60746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.616635, "relative_start": 1.3910300731658936, "end": **********.616635, "relative_end": **********.616635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.621829, "relative_start": 1.396224021911621, "end": **********.621829, "relative_end": **********.621829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.625359, "relative_start": 1.3997540473937988, "end": **********.625359, "relative_end": **********.625359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.629821, "relative_start": 1.4042160511016846, "end": **********.629821, "relative_end": **********.629821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.635152, "relative_start": 1.4095470905303955, "end": **********.635152, "relative_end": **********.635152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.681555, "relative_start": 1.4559500217437744, "end": **********.681555, "relative_end": **********.681555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.691056, "relative_start": 1.4654510021209717, "end": **********.691056, "relative_end": **********.691056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.725845, "relative_start": 1.5002400875091553, "end": **********.725845, "relative_end": **********.725845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.729845, "relative_start": 1.5042400360107422, "end": **********.729845, "relative_end": **********.729845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.734325, "relative_start": 1.5087199211120605, "end": **********.734325, "relative_end": **********.734325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.74342, "relative_start": 1.5178148746490479, "end": **********.74342, "relative_end": **********.74342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.752679, "relative_start": 1.5270740985870361, "end": **********.752679, "relative_end": **********.752679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.758576, "relative_start": 1.532970905303955, "end": **********.758576, "relative_end": **********.758576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.767963, "relative_start": 1.5423579216003418, "end": **********.767963, "relative_end": **********.767963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.770891, "relative_start": 1.545285940170288, "end": **********.770891, "relative_end": **********.770891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.777711, "relative_start": 1.5521059036254883, "end": **********.777711, "relative_end": **********.777711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.783115, "relative_start": 1.5575098991394043, "end": **********.783115, "relative_end": **********.783115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.787971, "relative_start": 1.562366008758545, "end": **********.787971, "relative_end": **********.787971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.791619, "relative_start": 1.566014051437378, "end": **********.791619, "relative_end": **********.791619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.797349, "relative_start": 1.5717439651489258, "end": **********.797349, "relative_end": **********.797349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.806717, "relative_start": 1.5811119079589844, "end": **********.806717, "relative_end": **********.806717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.809223, "relative_start": 1.583617925643921, "end": **********.809223, "relative_end": **********.809223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.812113, "relative_start": 1.58650803565979, "end": **********.812113, "relative_end": **********.812113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.819852, "relative_start": 1.5942471027374268, "end": **********.819852, "relative_end": **********.819852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.827842, "relative_start": 1.6022369861602783, "end": **********.827842, "relative_end": **********.827842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.835066, "relative_start": 1.6094610691070557, "end": **********.835066, "relative_end": **********.835066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.843169, "relative_start": 1.6175639629364014, "end": **********.843169, "relative_end": **********.843169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.851424, "relative_start": 1.6258189678192139, "end": **********.851424, "relative_end": **********.851424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.863956, "relative_start": 1.6383509635925293, "end": **********.863956, "relative_end": **********.863956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.875625, "relative_start": 1.650019884109497, "end": **********.875625, "relative_end": **********.875625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.892722, "relative_start": 1.6671168804168701, "end": **********.892722, "relative_end": **********.892722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.905905, "relative_start": 1.680299997329712, "end": **********.905905, "relative_end": **********.905905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.913371, "relative_start": 1.6877660751342773, "end": **********.913371, "relative_end": **********.913371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.941222, "relative_start": 1.7156169414520264, "end": **********.941222, "relative_end": **********.941222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.952459, "relative_start": 1.7268540859222412, "end": **********.952459, "relative_end": **********.952459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.968861, "relative_start": 1.7432560920715332, "end": **********.968861, "relative_end": **********.968861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.980739, "relative_start": 1.755134105682373, "end": **********.980739, "relative_end": **********.980739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.99083, "relative_start": 1.7652249336242676, "end": **********.99083, "relative_end": **********.99083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.003007, "relative_start": 1.7774019241333008, "end": *********8.003007, "relative_end": *********8.003007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.016084, "relative_start": 1.7904789447784424, "end": *********8.016084, "relative_end": *********8.016084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.024541, "relative_start": 1.798935890197754, "end": *********8.024541, "relative_end": *********8.024541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.03926, "relative_start": 1.813654899597168, "end": *********8.03926, "relative_end": *********8.03926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.089605, "relative_start": 1.8640000820159912, "end": *********8.089605, "relative_end": *********8.089605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.098678, "relative_start": 1.8730731010437012, "end": *********8.098678, "relative_end": *********8.098678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.106874, "relative_start": 1.8812689781188965, "end": *********8.106874, "relative_end": *********8.106874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.112686, "relative_start": 1.8870809078216553, "end": *********8.112686, "relative_end": *********8.112686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.128631, "relative_start": 1.9030261039733887, "end": *********8.128631, "relative_end": *********8.128631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.152843, "relative_start": 1.9272379875183105, "end": *********8.152843, "relative_end": *********8.152843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.1606, "relative_start": 1.9349949359893799, "end": *********8.1606, "relative_end": *********8.1606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.171451, "relative_start": 1.9458460807800293, "end": *********8.171451, "relative_end": *********8.171451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.181483, "relative_start": 1.9558780193328857, "end": *********8.181483, "relative_end": *********8.181483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.189335, "relative_start": 1.9637300968170166, "end": *********8.189335, "relative_end": *********8.189335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.198102, "relative_start": 1.9724969863891602, "end": *********8.198102, "relative_end": *********8.198102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.204036, "relative_start": 1.978430986404419, "end": *********8.204036, "relative_end": *********8.204036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.210873, "relative_start": 1.9852678775787354, "end": *********8.210873, "relative_end": *********8.210873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.216641, "relative_start": 1.9910359382629395, "end": *********8.216641, "relative_end": *********8.216641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": *********8.22408, "relative_start": 1.9984750747680664, "end": *********8.22408, "relative_end": *********8.22408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.238582, "relative_start": 2.012976884841919, "end": *********8.238582, "relative_end": *********8.238582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": *********8.244046, "relative_start": 2.0184409618377686, "end": *********8.244046, "relative_end": *********8.244046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": *********8.255108, "relative_start": 2.029503107070923, "end": *********8.255108, "relative_end": *********8.255108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": *********8.266271, "relative_start": 2.040666103363037, "end": *********8.266271, "relative_end": *********8.266271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": *********8.273189, "relative_start": 2.047584056854248, "end": *********8.273189, "relative_end": *********8.273189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": *********8.300117, "relative_start": 2.074512004852295, "end": *********8.300117, "relative_end": *********8.300117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": *********8.306741, "relative_start": 2.0811359882354736, "end": *********8.309292, "relative_end": *********8.309292, "duration": 0.0025510787963867188, "duration_str": "2.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59252672, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 68, "nb_templates": 68, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.560782, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "1x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.570821, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.580822, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "29x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.595452, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 29, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "30x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.629809, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 30, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "2x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.681534, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": *********8.255091, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": *********8.266253, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 42, "nb_statements": 42, "nb_visible_statements": 42, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026860000000000005, "accumulated_duration_str": "26.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33' limit 1", "type": "query", "params": [], "bindings": ["OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.37003, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 1.973}, {"sql": "select * from `users` where `id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.381145, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.973, "width_percent": 2.718}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3846111, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.691, "width_percent": 3.09}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.387488, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.781, "width_percent": 1.34}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.388808, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.121, "width_percent": 0.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.4056242, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.94, "width_percent": 2.308}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.409069, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.249, "width_percent": 1.713}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (19) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.414004, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.961, "width_percent": 1.936}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = 19) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) and `milestones`.`id` = '100' limit 1", "type": "query", "params": [], "bindings": [19, "100"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.4620688, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.897, "width_percent": 7.446}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.465682, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.343, "width_percent": 2.792}, {"sql": "select * from `clients` where `clients`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 29, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 30, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 31, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.467474, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.136, "width_percent": 2.01}, {"sql": "select * from `milestones` where `project_id` = 161 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 373}], "start": **********.468726, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:330", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=330", "ajax": false, "filename": "MilestoneResource.php", "line": "330"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.146, "width_percent": 1.601}, {"sql": "select * from `payments` where `milestone_id` = 100 limit 1", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.472867, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.747, "width_percent": 3.686}, {"sql": "select * from `payments` where `milestone_id` = 101 limit 1", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.474835, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.433, "width_percent": 1.303}, {"sql": "select * from `payments` where `milestone_id` = 102 limit 1", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.4759958, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.736, "width_percent": 0.968}, {"sql": "select * from `payments` where `milestone_id` = 103 limit 1", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.476989, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.704, "width_percent": 0.894}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = 19) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n)", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.545522, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.597, "width_percent": 4.542}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where exists (select * from `projects` where `milestones`.`project_id` = `projects`.`id` and `user_id` = 19) and milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.5476809, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.139, "width_percent": 7.93}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.5510888, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.069, "width_percent": 2.271}, {"sql": "select * from `clients` where `clients`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.552674, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.34, "width_percent": 1.936}, {"sql": "select `projects`.`title`, `projects`.`id` from `projects` order by `projects`.`title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.6045332, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.276, "width_percent": 2.308}, {"sql": "select `clients`.`company_name`, `clients`.`id` from `clients` order by `clients`.`company_name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.613471, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.585, "width_percent": 1.713}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7360911, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.297, "width_percent": 1.862}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.738313, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.159, "width_percent": 1.489}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.740206, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.648, "width_percent": 1.303}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": **********.7448282, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.951, "width_percent": 1.638}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.748262, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.589, "width_percent": 3.909}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8138258, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.498, "width_percent": 1.564}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.81635, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.062, "width_percent": 2.159}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.81757, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.221, "width_percent": 2.42}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": **********.820925, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.641, "width_percent": 1.75}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.8234348, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.391, "width_percent": 2.159}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.9726522, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.55, "width_percent": 2.308}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.975584, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.859, "width_percent": 2.085}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.977458, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.943, "width_percent": 2.755}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": **********.982554, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.698, "width_percent": 2.569}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.986152, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.267, "width_percent": 2.085}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": *********8.162901, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.352, "width_percent": 2.159}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": *********8.1663408, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.512, "width_percent": 2.159}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": *********8.168028, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.671, "width_percent": 1.899}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": *********8.1729088, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.57, "width_percent": 2.234}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": *********8.176035, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.803, "width_percent": 2.197}]}, "models": {"data": {"App\\Models\\PricingModel": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Payment": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\Project": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Client": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 36, "is_counter": true}, "livewire": {"data": {"app.filament.resources.milestone-resource.pages.list-milestones #flJ1ao4HZH9nmyIyepZW": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:5 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"project_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"client\" => array:1 [\n        \"value\" => null\n      ]\n      \"due_date\" => array:2 [\n        \"due_from\" => null\n        \"due_until\" => null\n      ]\n      \"amount_range\" => array:2 [\n        \"amount_from\" => null\n        \"amount_to\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => array:1 [\n      0 => \"editAll\"\n    ]\n    \"mountedTableActionsData\" => array:1 [\n      0 => array:1 [\n        \"milestones\" => array:4 [\n          \"ef600781-5f40-43e3-bbdb-c0b5fe14fc7e\" => array:24 [\n            \"id\" => 100\n            \"project_id\" => 161\n            \"title\" => \"Week 1 Milestone\"\n            \"description\" => \"Auto-generated milestone 1 of 4\"\n            \"due_date\" => \"2025-07-08\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => \"0.00\"\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => \"2000.00\"\n            \"original_percentage\" => \"25.00\"\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:14:51.000000Z\"\n            \"payment_due_date\" => \"2025-07-08\"\n            \"payment_paid_date\" => \"2025-08-08\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"4000.00\"\n            \"payment_notes\" => null\n          ]\n          \"e9e4e87c-f4d5-4d01-b7c6-26bde5dc3dc4\" => array:24 [\n            \"id\" => 101\n            \"project_id\" => 161\n            \"title\" => \"Week 2 Milestone\"\n            \"description\" => \"Auto-generated milestone 2 of 4\"\n            \"due_date\" => \"2025-07-15\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:14:51.000000Z\"\n            \"payment_due_date\" => \"2025-07-15\"\n            \"payment_paid_date\" => \"2025-07-15\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n          ]\n          \"070f69c2-3e3f-447b-b731-d893a6bd9046\" => array:24 [\n            \"id\" => 102\n            \"project_id\" => 161\n            \"title\" => \"Week 3 Milestone\"\n            \"description\" => \"Auto-generated milestone 3 of 4\"\n            \"due_date\" => \"2025-07-22\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:14:51.000000Z\"\n            \"payment_due_date\" => \"2025-07-22\"\n            \"payment_paid_date\" => \"2025-07-22\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n          ]\n          \"3fff1a60-4996-4d75-a7c1-f853ab3aa884\" => array:24 [\n            \"id\" => 103\n            \"project_id\" => 161\n            \"title\" => \"Week 4 Milestone\"\n            \"description\" => \"Auto-generated milestone 4 of 4\"\n            \"due_date\" => \"2025-07-29\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:14:51.000000Z\"\n            \"payment_due_date\" => \"2025-07-29\"\n            \"payment_paid_date\" => \"2025-07-29\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n          ]\n        ]\n      ]\n    ]\n    \"mountedTableActionsArguments\" => array:1 [\n      0 => []\n    ]\n    \"mountedTableActionRecord\" => \"100\"\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.milestone-resource.pages.list-milestones\"\n  \"component\" => \"App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones\"\n  \"id\" => \"flJ1ao4HZH9nmyIyepZW\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 12, "messages": [{"message": "[\n  ability => create_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-156429512 data-indent-pad=\"  \"><span class=sf-dump-note>create_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">create_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156429512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417108, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2092393063 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092393063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.417922, "xdebug_link": null}, {"message": "[\n  ability => reorder_milestone,\n  target => null,\n  result => null,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-552861980 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">reorder_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-552861980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.426448, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Milestone,\n  result => false,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944513264 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944513264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.426592, "xdebug_link": null}, {"message": "[\n  ability => delete_any_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1823435296 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">delete_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823435296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.453715, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 19,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1285499689 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285499689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.453871, "xdebug_link": null}, {"message": "[\n  ability => update_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1360731963 data-indent-pad=\"  \"><span class=sf-dump-note>update_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">update_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360731963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.651691, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-23941355 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23941355\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.652001, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-7371775 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7371775\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67896, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233455077 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233455077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.679208, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1672205913 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672205913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.690448, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2141748798 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141748798\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.690686, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones@mountTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=171\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=171\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:171-234</a>", "middleware": "web", "duration": "2.09s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1847663368 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1847663368\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-243666580 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1889 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;project_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;due_date&quot;:[{&quot;due_from&quot;:null,&quot;due_until&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;amount_range&quot;:[{&quot;amount_from&quot;:null,&quot;amount_to&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;flJ1ao4HZH9nmyIyepZW&quot;,&quot;name&quot;:&quot;app.filament.resources.milestone-resource.pages.list-milestones&quot;,&quot;path&quot;:&quot;admin\\/milestones&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;378bdea6dc19b9502d90ae662ae4649ec6617b2cd42514f288a6ea0781eb870a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mountTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">editAll</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243666580\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-630667960 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjBjNUpza0JkbElON1VybGp1UENRT1E9PSIsInZhbHVlIjoiTlk4ZnZiQndFRFJWV21udDhRVkRJSHg1Z09WNG9TaDlvL3NlTWZQK3cwNWxwWHNMWXUvOFhBS3ZSaFdTNFVRbkdVSU1NWUxpd2VVaTZLdDJiaGJzVTE2dE9hOHNBY1BVcURRa05LcjRodEFyeXNkT0swaGZyR0pZVUZ0TGgxZTd1MHZ1VnVqVU01VWlxL01DSE5YYjRYcHVrR2NQTmhrUXNVQ1UvSGdSdnFrMG1Fa08ydGNRNFlFcjEvSzE3UTQ2N2VqL1FQT2JZSUM4V3pJeTdsSEs2TXF0RXFyRnJEcVZEdmkxeS8raE1qZz0iLCJtYWMiOiIxZWI0YTEyZDFmMmIxN2VkZjgxMTlkNjhhNWZkZmQ1N2JmYWFlZDdhZDJlM2YzZDg3MjkxOTI2YTE3ZDNjNDU2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlI5R29kRUJPNnZvRlhiK3hDTFVMSWc9PSIsInZhbHVlIjoieUxZYktmellFd1IvdUhEb2JQSFM1QmZmb3BuMVE4dlpENklDQ21SSkFNb0ZKOXRpOE1xMGcrb1N2VytOK3B6SW1ROXk0ZjM5bjZTR0hNVHVZRWZZK0ZqMzY0azFCOHViOGNjWWV5NkR0UVJybWVwaVJUcnlKWEdPT3daTStWQ2ciLCJtYWMiOiI3OTZjZmFjNTI1YWE3YTM2ZmNhODQ2OTczMmU5NjM5YmRhMWZjMmM2MGJjZDFhNWE1NzMzNDAwOWZkNmZiODI4IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkZQenQ1NmZGMXRnb2kzeE5QSFREZ1E9PSIsInZhbHVlIjoid2VhL0NPbUgxSzVyekl1M2k5VnlUTXRTb2M2TGNubUpTWndFM3ByQlMrVlE3K2hGRjUxQVdCM0lUbWhWcnFFb1RST3BISTVxNVdlaHVTV0xmdWxiZ2RKUi9DbDV6UlIwdG9uQ0Y2d0Mxek1KSDI1N01namZ4M2ZUM281MnQxT2YiLCJtYWMiOiJjYzQ1MmYwZWY2ZGZkMjNjY2ZiMTlkM2IwZDRlOGExMDA3Y2QzZTQ5NGVjMWEyZmI0YjQ0OWJhM2FhODY5ZjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630667960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1855904536 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|ceSSE69Z6lQRjclxArTEUqvoloq2EMgMZKE49WMA10W3Vcuc7S97Dvhk23GU|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855904536\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1532878806 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:30:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532878806\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-693681499 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693681499\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}