<?php

namespace App\Filament\Resources\ProjectResource\Pages;

use App\Filament\Resources\ProjectResource;
use App\Filament\Resources\ProjectResource\RelationManagers\MilestonesRelationManager;
use App\Filament\Resources\ProjectResource\RelationManagers\IncentivesRelationManager;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;

class EditProject extends EditRecord
{
    protected static string $resource = ProjectResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }

    public function getContentTabLabel(): ?string
    {
        return 'Project Details';
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('client_id')
                    ->relationship('client', 'company_name')
                    ->label('Client')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('project_type_id')
                            ->relationship('projectType', 'name')
                            ->label('Title')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                if ($state) {
                                    $projectType = \App\Models\ProjectType::find($state);
                                    $set('title', $projectType ? $projectType->name : 'New Project');
                                    if ($projectType && $projectType->name !== 'Product') {
                                        $set('product_id', null);
                                    }
                                }
                            }),
                        Forms\Components\DatePicker::make('won_date')
                            ->required()
                            ->dehydrated()
                            ->live(onBlur: true)
                            ->rules([
                                function (Get $get) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                        $startDate = $get('start_date');
                                        if ($startDate && $value) {
                                            $wonDateCarbon = \Carbon\Carbon::parse($value);
                                            $startDateCarbon = \Carbon\Carbon::parse($startDate);
                                            if ($wonDateCarbon->gt($startDateCarbon)) {
                                                $fail('The won date must be equal to or less than the start date (' . $startDateCarbon->format('Y-m-d') . ').');
                                            }
                                        }
                                    };
                                },
                            ]),
                    ]),

                Forms\Components\Select::make('product_id')
                    ->relationship('product', 'title')
                    ->label('Product')
                    ->searchable()
                    ->preload()
                    ->visible(function (Get $get) {
                        $projectTypeId = $get('project_type_id');
                        if (!$projectTypeId) return false;
                        $projectType = \App\Models\ProjectType::find($projectTypeId);
                        return $projectType && $projectType->name === 'Product';
                    })
                    ->live()
                    ->afterStateUpdated(function (Set $set, Get $get, $state) {
                        if ($state) {
                            $product = \App\Models\Product::find($state);
                            if ($product) {
                                $set('title', $product->title);
                                $set('total_payment', $product->amount);
                                $set('currency', $product->currency);
                                if ($get('start_date') && $get('duration') && $get('duration_unit')) {
                                    $this->generateMilestonesAndPayments($set, $get);
                                }
                            }
                        }
                    }),

                Forms\Components\Grid::make(5)
                    ->schema([
                        Forms\Components\TextInput::make('duration')
                            ->required()
                            ->numeric()
                            ->minValue(1)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->calculateEndDate($set, $get);
                                $this->generateMilestonesAndPayments($set, $get);
                            }),
                        Forms\Components\Select::make('duration_unit')
                            ->options([
                                'hours' => 'Hours',
                                'days' => 'Days',
                                'weeks' => 'Weeks',
                                'months' => 'Months',
                                'years' => 'Years',
                            ])
                            ->required()
                            ->dehydrated()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->calculateEndDate($set, $get);
                                $this->generateMilestonesAndPayments($set, $get);
                            }),
                        Forms\Components\Select::make('payment_cycle')
                            ->options([
                                'weekly' => 'Weekly',
                                'biweekly' => 'Bi-weekly',
                                'monthly' => 'Monthly',
                                'quarterly' => 'Quarterly',
                                'yearly' => 'Yearly',
                                'milestone' => 'Milestone-based',
                                'upfront' => 'Upfront',
                            ])
                            ->required()
                            ->dehydrated()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->generateMilestonesAndPayments($set, $get);
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->required()
                            ->dehydrated()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->calculateEndDate($set, $get);
                                $this->generateMilestonesAndPayments($set, $get);
                            })
                            ->rules([
                                function (Get $get) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                        $wonDate = $get('won_date');
                                        if ($wonDate && $value) {
                                            $wonDateCarbon = \Carbon\Carbon::parse($wonDate);
                                            $startDateCarbon = \Carbon\Carbon::parse($value);
                                            if ($startDateCarbon->lt($wonDateCarbon)) {
                                                $fail('The start date must be equal to or greater than the won date (' . $wonDateCarbon->format('Y-m-d') . ').');
                                            }
                                        }
                                    };
                                },
                            ]),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('End Date')
                            ->helperText('Auto-calculated based on start date and duration')
                            ->live()
                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                // Only auto-calculate if the field is empty or being cleared
                                if (empty($state)) {
                                    $this->calculateEndDate($set, $get);
                                }
                            }),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\Hidden::make('user_id')
                            ->default(auth()->id())
                            ->dehydrated(),
                        auth()->user()->hasRole('super_admin')
                        ? Forms\Components\Select::make('bde_select')
                            ->relationship('user', 'name')
                            ->label('BDE')
                            ->default(auth()->id())
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                $set('user_id', $state);
                            })
                        : Forms\Components\TextInput::make('bde_display')
                            ->label('BDE')
                            ->default(function ($operation, $record) {
                                // For create operation
                                if ($operation === 'create') {
                                    return auth()->user()->name;
                                }
                                // For edit operation - load the relationship properly
                                if ($record && $record->user_id) {
                                    return $record->user->name ?? 'Not assigned';
                                }
                                return auth()->user()->name;
                            })
                            ->disabled()
                            ->dehydrated(false)
                            ->afterStateHydrated(function (Forms\Components\TextInput $component, $record) {
                                // Ensure the name is loaded when the form hydrates
                                if ($record && $record->user_id) {
                                    $component->state($record->user->name);
                                }
                            }),
                        Forms\Components\Select::make('pricing_model_id')
                            ->relationship('pricingModel', 'name')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                if (!$state) return;

                                $pricingModel = \App\Models\PricingModel::find($state);

                                if ($pricingModel) {
                                    // Force currency update (even if manually changed)
                                    if (str_contains(strtoupper($pricingModel->name), 'USD')) {
                                        $set('currency', 'USD');
                                    }
                                    elseif (str_contains(strtoupper($pricingModel->name), 'INR')) {
                                        $set('currency', 'INR');
                                    }

                                    // Rest of your logic (milestones, etc.)
                                    if (str_contains($pricingModel->name, 'Fixed')) {
                                        $set('_generated_milestones', '');
                                        $set('_generated_payments', '');
                                    } else {
                                        $this->generateMilestonesAndPayments($set, $get);
                                    }
                                }
                            }),
                        Forms\Components\Select::make('currency')
                        ->options([
                            'INR' => 'Indian Rupee (₹)',
                            'USD' => 'US Dollar ($)',
                            'EUR' => 'Euro (€)',
                            'GBP' => 'British Pound (£)',
                        ])
                        ->default('INR')
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (Set $set, $state) {
                            $set('currency', $state);
                        }),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('total_payment')
                            ->required()
                            ->numeric()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                $this->generateMilestonesAndPayments($set, $get);
                            }),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'completed' => 'Completed',
                                'on_hold' => 'On Hold',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('active')
                            ->required(),
                    ]),

                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull(),

                Forms\Components\Hidden::make('_generated_milestones'),
                Forms\Components\Hidden::make('_generated_payments'),
                Forms\Components\Hidden::make('title')
                    ->default(function (Get $get) {
                        $projectTypeId = $get('project_type_id');
                        if ($projectTypeId) {
                            $projectType = \App\Models\ProjectType::find($projectTypeId);
                            return $projectType ? $projectType->name : 'New Project';
                        }
                        return 'New Project';
                    }),
            ]);
    }

    private function calculateEndDate(Set $set, Get $get)
    {
        $startDate = $get('start_date');
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');

        if (!$startDate || !$duration || !$durationUnit) {
            return;
        }

        try {
            $startCarbon = \Carbon\Carbon::parse($startDate);
            $durationValue = (int) $duration;

            if ($durationUnit === 'hours' && $paymentCycle === 'weekly') {
                $workHoursPerWeek = 40;
                $weeks = ceil($durationValue / $workHoursPerWeek);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            elseif ($durationUnit === 'days' && $paymentCycle === 'weekly') {
                $weeks = ceil($durationValue / 7);
                $endDate = $startCarbon->copy()->addWeeks($weeks);
            }
            else {
                $endDate = match($durationUnit) {
                    'hours' => $startCarbon->copy()->addHours($durationValue),
                    'days' => $startCarbon->copy()->addDays($durationValue),
                    'weeks' => $startCarbon->copy()->addWeeks($durationValue),
                    'months' => $startCarbon->copy()->addMonths($durationValue)->subDay(),
                    'years' => $startCarbon->copy()->addYears($durationValue)->subDay(),
                    default => $startCarbon->copy()->addMonths($durationValue),
                };
            }

            $set('end_date', $endDate->format('Y-m-d'));
        } catch (\Exception $e) {
            // Handle errors silently
        }
    }

    private function generateMilestonesAndPayments(Set $set, Get $get)
    {
        // This method will be implemented similar to the static version
        // but as an instance method for the edit page
        $duration = $get('duration');
        $durationUnit = $get('duration_unit');
        $paymentCycle = $get('payment_cycle');
        $startDate = $get('start_date');
        $totalPayment = $get('total_payment');

        if (!$duration || !$durationUnit || !$paymentCycle || !$startDate || !$totalPayment) {
            $set('_generated_milestones', '');
            $set('_generated_payments', '');
            return;
        }

        // For now, just clear the generated data
        // The full implementation would be similar to the static methods
        $set('_generated_milestones', '');
        $set('_generated_payments', '');
    }

    public function getRelationManagers(): array
    {
        return [
            MilestonesRelationManager::class,
            IncentivesRelationManager::class,
        ];
    }
}
