@php
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Support\Enums\FontWeight;

// Define currency symbol at the top so it's available everywhere
$currencySymbol = match($currency ?? 'INR') {
    'USD' => '$',
    'EUR' => '€',
    'GBP' => '£',
    'INR' => '₹',
    default => '₹'
};
@endphp

<div class="fi-ta-content relative divide-y divide-gray-200 overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10">
    <div class="fi-ta-header-ctn divide-y divide-gray-200 dark:divide-white/10">
        <div class="fi-ta-header flex flex-col gap-3 p-4 sm:px-6 sm:flex-row sm:items-center">
            <div class="grid gap-y-1">
                <h3 class="fi-ta-header-heading text-base font-semibold leading-6 text-gray-950 dark:text-white">
                    Project Milestones
                </h3>
                <p class="fi-ta-header-description text-sm text-gray-500 dark:text-gray-400">
                    Overview of milestone progress and payment status
                </p>
            </div>
        </div>
    </div>

    <div class="fi-ta-content relative overflow-x-auto">
        <table class="fi-ta-table w-full table-auto divide-y divide-gray-200 text-start dark:divide-white/5">
            <thead class="divide-y divide-gray-200 dark:divide-white/5">
                <tr class="bg-gray-50 dark:bg-white/5">
                    <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                        <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                            <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                Milestone
                            </span>
                        </span>
                    </th>
                    <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                        <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                            <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                Status
                            </span>
                        </span>
                    </th>
                    <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                        <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                            <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                Amount
                            </span>
                        </span>
                    </th>
                    <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                        <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                            <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                Payment Status
                            </span>
                        </span>
                    </th>
                    <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                        <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                            <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                Due Date
                            </span>
                        </span>
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
                @foreach($milestones as $milestone)
                <tr class="border-b border-gray-200 dark:border-gray-700">
                    <td class="px-6 py-4">
                        <div class="flex flex-col space-y-1">
                            <div class="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
                                {{ $milestone->title }}
                                @if($milestone->mergedMilestones->isNotEmpty())
                                <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                    Merged
                                </span>
                                @endif
                            </div>

                            @if($milestone->mergedMilestones->isNotEmpty())
                            <div class="merged-info text-xs text-gray-600 dark:text-gray-400 mt-1 pl-2">
                                <span class="font-medium">Includes merged milestones:</span>
                                @foreach($milestone->mergedMilestones as $merged)
                                <div class="flex items-baseline mt-1">
                                    <span class="mr-1">•</span>
                                    <span>{{ $merged->title }}</span>
                                    <span class="text-xs ml-2">
                                        ({{ $merged->original_percentage ?? $merged->percentage }}%,
                                        {{ $currencySymbol }}{{ number_format($merged->original_amount ?? $merged->amount, 2) }})
                                    </span>
                                    @if($merged->original_due_date)
                                    <span class="text-xs ml-2">
                                        Due: {{ $merged->original_due_date->format('M d, Y') }}
                                    </span>
                                    @endif
                                </div>
                                @endforeach
                            </div>
                            @endif

                            @if($milestone->description)
                            <div class="text-sm text-gray-600 dark:text-gray-300">
                                {{ Str::limit($milestone->description, 50) }}
                            </div>
                            @endif

                            @if($milestone->percentage)
                            <div class="text-xs font-medium text-blue-600 dark:text-blue-400">
                                {{ $milestone->percentage }}% Complete
                                @if($milestone->mergedMilestones->isNotEmpty())
                                <span class="text-gray-500 text-xs">(combined)</span>
                                @endif
                            </div>
                            @endif
                        </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @switch($milestone->status)
                                @case('completed')
                                    bg-green-100 text-green-800
                                    @break
                                @case('in_progress')
                                    bg-blue-100 text-blue-800
                                    @break
                                @case('pending')
                                    bg-yellow-100 text-yellow-800
                                    @break
                                @case('delayed')
                                    bg-red-100 text-red-800
                                    @break
                                @default
                                    bg-gray-100 text-gray-800
                            @endswitch
                        ">
                            {{ ucfirst(str_replace('_', ' ', $milestone->status)) }}
                        </span>
                    </td>

                    <td class="px-6 py-4">
                        <div class="text-sm font-semibold text-gray-900 dark:text-white">
                            {{ $currencySymbol }}{{ number_format($milestone->amount, 2) }}
                            @if($milestone->mergedMilestones->isNotEmpty())
                            <span class="text-xs text-gray-500 block mt-1">
                                (includes {{ $currencySymbol }}{{ number_format($milestone->mergedMilestones->sum('original_amount'), 2) }} merged)
                            </span>
                            @endif
                        </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($milestone->payments->isNotEmpty())
                        @foreach($milestone->payments as $payment)
                        <div class="mb-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                @switch($payment->status)
                                    @case('paid')
                                        bg-green-100 text-green-800
                                        @break
                                    @case('pending')
                                        bg-yellow-100 text-yellow-800
                                        @break
                                    @case('overdue')
                                        bg-red-100 text-red-800
                                        @break
                                    @case('partial')
                                        bg-blue-100 text-blue-800
                                        @break
                                    @default
                                        bg-gray-100 text-gray-800
                                @endswitch
                            ">
                                {{ ucfirst($payment->status) }}
                            </span>
                            @if($payment->paid_date)
                            <div class="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                Paid: {{ $payment->paid_date->format('M d, Y') }}
                            </div>
                            @endif
                        </div>
                        @endforeach
                        @else
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-800">
                            No Payment
                        </span>
                        @endif
                    </td>

                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                        @if($milestone->due_date)
                        <div class="flex flex-col space-y-1">
                            <span class="font-medium">{{ $milestone->due_date->format('M d, Y') }}</span>
                            @if($milestone->due_date->isPast() && $milestone->status !== 'completed')
                            <span class="text-xs font-medium text-red-600 dark:text-red-400">Overdue</span>
                            @elseif($milestone->due_date->isToday())
                            <span class="text-xs font-medium text-orange-600 dark:text-orange-400">Due Today</span>
                            @elseif($milestone->due_date->isTomorrow())
                            <span class="text-xs font-medium text-yellow-600 dark:text-yellow-400">Due Tomorrow</span>
                            @endif
                        </div>
                        @else
                        <span class="text-gray-500 dark:text-gray-400">No due date</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    @if($milestones->isNotEmpty())
    <div class="fi-ta-footer-ctn border-t border-gray-200 dark:border-white/10">
        <div class="fi-ta-footer flex items-center gap-x-4 px-4 py-3 sm:px-6">
            <div class="flex items-center gap-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>Total: {{ $milestones->count() }}</span>
                <span class="text-success-600 dark:text-success-400">Completed: {{ $milestones->where('status', 'completed')->count() }}</span>
                <span class="text-info-600 dark:text-info-400">In Progress: {{ $milestones->where('status', 'in_progress')->count() }}</span>
                <span class="text-warning-600 dark:text-warning-400">Pending: {{ $milestones->where('status', 'pending')->count() }}</span>
            </div>
            <div class="ms-auto">
                @php
                $totalAmount = $milestones->sum('amount');
                $completedAmount = $milestones->where('status', 'completed')->sum('amount');
                $progressPercentage = $totalAmount > 0 ? round(($completedAmount / $totalAmount) * 100, 1) : 0;
                @endphp
                <span class="text-sm font-medium text-gray-950 dark:text-white">
                    Progress: {{ $progressPercentage }}% ({{ $currencySymbol }}{{ number_format($completedAmount, 2) }}/{{ $currencySymbol }}{{ number_format($totalAmount, 2) }})
                </span>
            </div>
        </div>
    </div>
    @endif
</div>
