<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MilestoneResource\Pages;
use App\Filament\Resources\MilestoneResource\RelationManagers;
use App\Models\Milestone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class MilestoneResource extends Resource
{
    protected static ?string $model = Milestone::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?int $navigationSort = 3;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->can('view_any_milestone');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Except super admin, all users can only see milestones from their own projects
        if (auth()->check() && !auth()->user()->hasRole('super_admin')) {
            $query->whereHas('project', function (Builder $query) {
                $query->where('user_id', auth()->id());
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // EXACT SAME STRUCTURE AS Edit Client → Milestone Tab
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('project_id')
                            ->label('Project')
                            ->relationship('project', 'title', function (Builder $query) {
                                // If user should see only their own data, filter to their projects
                                if (auth()->check() && auth()->user()->shouldSeeOwnDataOnly()) {
                                    $query->where('user_id', auth()->id());
                                }
                                return $query;
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live(),
                    ]),
                Forms\Components\Repeater::make('milestones')
                    ->label('Milestone Details')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Milestone Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(65535),
                        Forms\Components\DatePicker::make('due_date')
                            ->label('Due Date')
                            ->required(),
                        Forms\Components\TextInput::make('percentage')
                            ->label('Percentage')
                            ->numeric()
                            ->suffix('%')
                            ->maxValue(100)
                            ->required(function (callable $get) {
                                $projectId = $get('../../project_id');
                                if (!$projectId) return false;

                                $project = \App\Models\Project::with('pricingModel')->find($projectId);
                                if (!$project || !$project->pricingModel) return false;

                                return !in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                            })
                            ->visible(function (callable $get) {
                                $projectId = $get('../../project_id');
                                if (!$projectId) return true;

                                $project = \App\Models\Project::with('pricingModel')->find($projectId);
                                if (!$project || !$project->pricingModel) return true;

                                return !in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                            }),
                        Forms\Components\TextInput::make('hours')
                            ->label('Hours')
                            ->numeric()
                            ->suffix(' hrs')
                            ->minValue(0)
                            ->required(function (callable $get) {
                                $projectId = $get('../../project_id');
                                if (!$projectId) return false;

                                $project = \App\Models\Project::with('pricingModel')->find($projectId);
                                if (!$project || !$project->pricingModel) return false;

                                return in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                            })
                            ->visible(function (callable $get) {
                                $projectId = $get('../../project_id');
                                if (!$projectId) return false;

                                $project = \App\Models\Project::with('pricingModel')->find($projectId);
                                if (!$project || !$project->pricingModel) return false;

                                return in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                            }),
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->required()
                            ->numeric(),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'delayed' => 'Delayed'
                            ])
                            ->default('pending')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, $set, $get) {
                                // Show payment fields when status is completed
                            }),

                        // Payment Section - Only visible when milestone status is completed
                        Forms\Components\Section::make('Payment Information')
                            ->description('Update payment details for this milestone')
                            ->schema([
                                Forms\Components\Grid::make(3)->schema([
                                    Forms\Components\DatePicker::make('payment_due_date')
                                        ->label('Payment Due Date')
                                        ->format('Y-m-d')
                                        ->displayFormat('Y-m-d'),
                                    Forms\Components\DatePicker::make('payment_paid_date')
                                        ->label('Payment Paid Date')
                                        ->format('Y-m-d')
                                        ->displayFormat('Y-m-d'),
                                    Forms\Components\Select::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'bank_transfer' => 'Bank Transfer',
                                            'credit_card' => 'Credit Card',
                                            'upi' => 'UPI',
                                            'cash' => 'Cash',
                                            'cheque' => 'Cheque',
                                            'other' => 'Other',
                                        ]),
                                ]),
                                Forms\Components\Grid::make(3)->schema([
                                    Forms\Components\TextInput::make('transaction_id')
                                        ->label('Transaction ID')
                                        ->maxLength(255),
                                    Forms\Components\Select::make('payment_status')
                                        ->label('Payment Status')
                                        ->options([
                                            'pending' => 'Pending',
                                            'paid' => 'Paid',
                                            'overdue' => 'Overdue',
                                            'cancelled' => 'Cancelled',
                                        ])
                                        ->default('pending'),
                                    Forms\Components\TextInput::make('payment_amount')
                                        ->label('Payment Amount')
                                        ->numeric()
                                        ->helperText('Leave empty to use milestone amount'),
                                ]),
                                Forms\Components\Textarea::make('payment_notes')
                                    ->label('Payment Notes')
                                    ->maxLength(65535)
                                    ->columnSpanFull(),
                            ])
                            ->visible(function (callable $get) {
                                return $get('status') === 'completed';
                            })
                            ->collapsible()
                            ->collapsed(),
                    ])
                    ->minItems(1)
                    ->createItemButtonLabel('Add Another Milestone')
                    ->reorderable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->modifyQueryUsing(function ($query) {
                // Show one entry per project - MySQL compatible approach
                $query->select('milestones.*')
                    ->join('projects', 'milestones.project_id', '=', 'projects.id')
                    ->whereRaw('milestones.id = (
                        SELECT m.id
                        FROM milestones m
                        WHERE m.project_id = milestones.project_id
                        ORDER BY
                            CASE
                                WHEN m.status = "in_progress" THEN 1
                                WHEN m.status = "pending" THEN 2
                                WHEN m.status = "delayed" THEN 3
                                WHEN m.status = "completed" THEN 4
                                ELSE 5
                            END,
                            m.due_date ASC,
                            m.created_at ASC
                        LIMIT 1
                    )');
            })
            ->columns([
                Tables\Columns\TextColumn::make('project.title')
                    ->label('Project')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('project.client.company_name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->label('Current Milestone')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'delayed' => 'danger',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money('USD')
                    ->sortable(),
                Tables\Columns\TextColumn::make('percentage')
                    ->suffix('%')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'delayed' => 'Delayed',
                    ]),
                Tables\Filters\SelectFilter::make('project_id')
                    ->relationship('project', 'title')
                    ->label('Project')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('client')
                    ->relationship('project.client', 'company_name')
                    ->label('Client')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('project.user_id')
                    ->relationship('project.user', 'name')
                    ->label('BDE')
                    ->visible(fn() => auth()->user()->hasRole('super_admin')),
                Tables\Filters\Filter::make('due_date')
                    ->form([
                        Forms\Components\DatePicker::make('due_from')
                            ->label('Due From'),
                        Forms\Components\DatePicker::make('due_until')
                            ->label('Due Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['due_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '>=', $date),
                            )
                            ->when(
                                $data['due_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('amount_range')
                    ->form([
                        Forms\Components\TextInput::make('amount_from')
                            ->label('Amount From')
                            ->numeric(),
                        Forms\Components\TextInput::make('amount_to')
                            ->label('Amount To')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['amount_from'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '>=', $amount),
                            )
                            ->when(
                                $data['amount_to'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '<=', $amount),
                            );
                    }),
            ])
            ->actions([
                // Edit All action with icon only (same as Edit Client → Milestone Tab)
                Tables\Actions\Action::make('editAll')
                    ->icon('heroicon-o-pencil-square')
                    ->label('')
                    ->modalWidth('7xl')
                    ->form(function ($record) {
                        $milestones = \App\Models\Milestone::where('project_id', $record->project_id)
                            ->orderBy('due_date', 'asc')
                            ->orderBy('created_at', 'asc')
                            ->get()
                            ->map(function ($milestone) {
                                // Convert milestone to array and handle due_date properly
                                $milestoneArray = $milestone->toArray();

                                // Handle due_date with multiple format possibilities
                                if ($milestone->due_date) {
                                    if ($milestone->due_date instanceof \Carbon\Carbon) {
                                        $milestoneArray['due_date'] = $milestone->due_date->format('Y-m-d');
                                    } elseif (is_string($milestone->due_date)) {
                                        $milestoneArray['due_date'] = \Carbon\Carbon::parse($milestone->due_date)->format('Y-m-d');
                                    } else {
                                        $milestoneArray['due_date'] = $milestone->due_date;
                                    }
                                } else {
                                    $milestoneArray['due_date'] = null;
                                }

                                // Get payment data for this milestone
                                $payment = \App\Models\Payment::where('milestone_id', $milestone->id)->first();
                                if ($payment) {
                                    $milestoneArray['payment_due_date'] = $payment->due_date ? $payment->due_date->format('Y-m-d') : null;
                                    $milestoneArray['payment_paid_date'] = $payment->paid_date ? $payment->paid_date->format('Y-m-d') : null;
                                    $milestoneArray['payment_method'] = $payment->payment_method;
                                    $milestoneArray['transaction_id'] = $payment->transaction_id;
                                    $milestoneArray['payment_status'] = $payment->status;
                                    $milestoneArray['payment_amount'] = $payment->amount;
                                    $milestoneArray['payment_notes'] = $payment->notes;
                                } else {
                                    // Default payment values
                                    $milestoneArray['payment_due_date'] = $milestoneArray['due_date']; // Default to milestone due date
                                    $milestoneArray['payment_paid_date'] = null;
                                    $milestoneArray['payment_method'] = null;
                                    $milestoneArray['transaction_id'] = null;
                                    $milestoneArray['payment_status'] = 'pending';
                                    $milestoneArray['payment_amount'] = $milestone->amount; // Default to milestone amount
                                    $milestoneArray['payment_notes'] = null;
                                }

                                return $milestoneArray;
                            });

                        return [
                            Forms\Components\Repeater::make('milestones')
                                ->label('Milestones')
                                ->schema([
                                    Forms\Components\Hidden::make('id'),
                                    Forms\Components\Grid::make(6)->schema([
                                        Forms\Components\TextInput::make('title')
                                            ->label('Title')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('description')
                                            ->label('Description')
                                            ->maxLength(65535),
                                        Forms\Components\DatePicker::make('due_date')
                                            ->label('Due Date')
                                            ->required()
                                            ->format('Y-m-d')
                                            ->displayFormat('Y-m-d'),
                                        Forms\Components\TextInput::make('percentage')
                                            ->label('Percentage')
                                            ->numeric()
                                            ->suffix('%')
                                            ->live(onBlur: true)
                                            ->maxValue(100)
                                            ->required(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return !($project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']));
                                            })
                                            ->visible(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return !($project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']));
                                            }),
                                        Forms\Components\TextInput::make('hours')
                                            ->label('Hours')
                                            ->numeric()
                                            ->suffix(' hrs')
                                            ->minValue(0)
                                            ->required(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                            })
                                            ->visible(function () use ($record) {
                                                $project = $record->project;
                                                $project->load('pricingModel');
                                                return $project->pricingModel && in_array($project->pricingModel->name, ['T&M-INR', 'T&M-USD']);
                                            }),
                                        Forms\Components\TextInput::make('amount')
                                            ->label('Amount')
                                            ->required()
                                            ->live(onBlur: true)
                                            ->numeric(),
                                        Forms\Components\Select::make('status')
                                            ->label('Status')
                                            ->options([
                                                'pending' => 'Pending',
                                                'in_progress' => 'In Progress',
                                                'completed' => 'Completed',
                                                'delayed' => 'Delayed'
                                            ])
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function ($state, $set, $get) {
                                                // Show payment fields when status is completed
                                            }),
                                    ]),

                                    // Second Row: Payment Information (only visible when status is completed)
                                    Forms\Components\Fieldset::make('Payment Information')
                                        ->schema([
                                            Forms\Components\Grid::make(6)->schema([
                                                Forms\Components\DatePicker::make('payment_due_date')
                                                    ->label('Payment Due Date')
                                                    ->format('Y-m-d')
                                                    ->displayFormat('Y-m-d'),
                                                Forms\Components\DatePicker::make('payment_paid_date')
                                                    ->label('Payment Paid Date')
                                                    ->format('Y-m-d')
                                                    ->displayFormat('Y-m-d'),
                                                Forms\Components\Select::make('payment_method')
                                                    ->label('Payment Method')
                                                    ->options([
                                                        'bank_transfer' => 'Bank Transfer',
                                                        'credit_card' => 'Credit Card',
                                                        'upi' => 'UPI',
                                                        'cash' => 'Cash',
                                                        'cheque' => 'Cheque',
                                                        'other' => 'Other',
                                                    ]),
                                                Forms\Components\TextInput::make('transaction_id')
                                                    ->label('Transaction ID')
                                                    ->maxLength(255),
                                                Forms\Components\Select::make('payment_status')
                                                    ->label('Payment Status')
                                                    ->options([
                                                        'pending' => 'Pending',
                                                        'paid' => 'Paid',
                                                        'overdue' => 'Overdue',
                                                        'cancelled' => 'Cancelled',
                                                    ])
                                                    ->default('pending'),
                                                Forms\Components\TextInput::make('payment_amount')
                                                    ->label('Payment Amount')
                                                    ->numeric()
                                                    ->helperText('Leave empty to use milestone amount'),
                                            ]),
                                            Forms\Components\Textarea::make('payment_notes')
                                                ->label('Payment Notes')
                                                ->maxLength(65535)
                                                ->columnSpanFull(),
                                        ])
                                        ->visible(function (callable $get) {
                                            return $get('status') === 'completed';
                                        }),
                                ])
                                ->default($milestones->toArray())
                                ->addable(false)
                                ->deletable(false)
                                ->reorderable(false),
                        ];
                    })
                    ->action(function (array $data, $record) {
                        // EXACT SAME DATA SUBMISSION LOGIC as Edit Client → Milestone Tab
                        foreach ($data['milestones'] as $milestoneData) {
                            $milestoneId = $milestoneData['id'] ?? null;

                            // Prepare milestone data
                            $milestoneUpdateData = [
                                'title' => $milestoneData['title'],
                                'description' => $milestoneData['description'],
                                'due_date' => $milestoneData['due_date'],
                                'percentage' => $milestoneData['percentage'] ?? null,
                                'hours' => $milestoneData['hours'] ?? null,
                                'amount' => $milestoneData['amount'],
                                'status' => $milestoneData['status'],
                            ];

                            if ($milestoneId) {
                                // Update existing milestone
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                if ($milestone) {
                                    $milestone->update($milestoneUpdateData);
                                }
                            }

                            // Handle payment data
                            if ($milestoneId) {
                                $paymentData = [
                                    'milestone_id' => $milestoneId,
                                    'due_date' => $milestoneData['payment_due_date'] ?? null,
                                    'paid_date' => $milestoneData['payment_paid_date'] ?? null,
                                    'payment_method' => $milestoneData['payment_method'] ?? null,
                                    'transaction_id' => $milestoneData['transaction_id'] ?? null,
                                    'status' => $milestoneData['payment_status'] ?? 'pending',
                                    'amount' => $milestoneData['payment_amount'] ?? $milestoneData['amount'],
                                    'notes' => $milestoneData['payment_notes'] ?? null,
                                ];

                                // Update or create payment
                                \App\Models\Payment::updateOrCreate(
                                    ['milestone_id' => $milestoneId],
                                    $paymentData
                                );
                            }
                        }
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMilestones::route('/'),
            'create' => Pages\CreateMilestone::route('/create'),
            'edit' => Pages\EditMilestone::route('/{record}/edit'),
        ];
    }
}
