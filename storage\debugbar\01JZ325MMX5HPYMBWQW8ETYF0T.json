{"__meta": {"id": "01JZ325MMX5HPYMBWQW8ETYF0T", "datetime": "2025-07-01 13:17:40", "utime": **********.38435, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[13:17:35] LOG.error: App\\Filament\\Pages\\Login::authenticate(): Return value must be of type ?Filament\\Http\\Responses\\Auth\\Contracts\\LoginResponse, Livewire\\Features\\SupportRedirects\\Redirector returned {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.485252, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751375853.991034, "end": **********.38446, "duration": 6.393425941467285, "duration_str": "6.39s", "measures": [{"label": "Booting", "start": 1751375853.991034, "relative_start": 0, "end": **********.570239, "relative_end": **********.570239, "duration": 0.****************, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.570255, "relative_start": 0.****************, "end": **********.384465, "relative_end": 5.0067901611328125e-06, "duration": 5.***************, "duration_str": "5.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.886841, "relative_start": 0.****************, "end": **********.889231, "relative_end": **********.889231, "duration": 0.0023899078369140625, "duration_str": "2.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::99503e4f2086f10483464823e7fe02ed", "start": **********.961846, "relative_start": 0.****************, "end": **********.961846, "relative_end": **********.961846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.642574, "relative_start": 1.****************, "end": **********.642574, "relative_end": **********.642574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.211262, "relative_start": 2.2202279567718506, "end": **********.211262, "relative_end": **********.211262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.340997, "relative_start": 2.3499629497528076, "end": **********.340997, "relative_end": **********.340997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.993809, "relative_start": 3.002774953842163, "end": **********.993809, "relative_end": **********.993809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.020959, "relative_start": 3.0299248695373535, "end": **********.020959, "relative_end": **********.020959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.023379, "relative_start": 3.0323450565338135, "end": **********.023379, "relative_end": **********.023379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.023624, "relative_start": 3.032589912414551, "end": **********.023624, "relative_end": **********.023624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.023879, "relative_start": 3.0328450202941895, "end": **********.023879, "relative_end": **********.023879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.047627, "relative_start": 3.0565929412841797, "end": **********.047627, "relative_end": **********.047627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.052271, "relative_start": 3.06123685836792, "end": **********.052271, "relative_end": **********.052271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.081289, "relative_start": 3.09025502204895, "end": **********.081289, "relative_end": **********.081289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.946085, "relative_start": 4.955050945281982, "end": **********.946085, "relative_end": **********.946085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.578728, "relative_start": 5.587693929672241, "end": **********.578728, "relative_end": **********.578728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.609842, "relative_start": 5.618808031082153, "end": **********.609842, "relative_end": **********.609842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.63875, "relative_start": 5.647716045379639, "end": **********.63875, "relative_end": **********.63875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.639161, "relative_start": 5.64812707901001, "end": **********.639161, "relative_end": **********.639161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.668227, "relative_start": 5.67719292640686, "end": **********.668227, "relative_end": **********.668227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.77361, "relative_start": 5.782576084136963, "end": **********.77361, "relative_end": **********.77361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.774202, "relative_start": 5.783168077468872, "end": **********.774202, "relative_end": **********.774202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.140775, "relative_start": 6.149740934371948, "end": **********.140775, "relative_end": **********.140775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.141549, "relative_start": 6.150515079498291, "end": **********.141549, "relative_end": **********.141549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.142137, "relative_start": 6.1511030197143555, "end": **********.142137, "relative_end": **********.142137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 55138200, "peak_usage_str": "53MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "TypeError", "message": "App\\Filament\\Pages\\Login::authenticate(): Return value must be of type ?Filament\\Http\\Responses\\Auth\\Contracts\\LoginResponse, Livewire\\Features\\SupportRedirects\\Redirector returned", "code": 0, "file": "app/Filament/Pages/Login.php", "line": 58, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-187790728 data-indent-pad=\"  \"><span class=sf-dump-note>array:62</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Filament\\Pages\\Login</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Container/Util.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>96</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unwrapIfClosure</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Container\\Util</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">callBoundMethod</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a><samp data-depth=5 id=sf-dump-187790728-ref23979 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">__id</span>: \"<span class=sf-dump-str title=\"20 characters\">rkvYjhkRKsbudS34222d</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">__name</span>: \"<span class=sf-dump-str title=\"24 characters\">app.filament.pages.login</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeCollection</span></span> {<a class=sf-dump-ref>#3859</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3683</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a><samp data-depth=9 id=sf-dump-187790728-ref23681 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">METHOD</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3684</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3682</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3680</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3679</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n              </samp>}\n              <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3678</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n              </samp>}\n              <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#3677</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23681 title=\"7 occurrences\">#3681</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n              </samp>}\n              <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#3676</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23736 title=\"2 occurrences\">#3736</a><samp data-depth=9 id=sf-dump-187790728-ref23736 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"8 characters\">PROPERTY</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#3734</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23736 title=\"2 occurrences\">#3736</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"15 characters\">actionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">rulesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messagesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">validationAttributesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">heading</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">subheading</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">maxContentWidth</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">extraBodyAttributes</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">defaultAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultActionArguments</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>authenticate</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23744 title=\"4 occurrences\">#3744</a><samp data-depth=7 id=sf-dump-187790728-ref23744 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n              #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"31 characters\">filament-actions::button-action</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"7 characters\">Sign in</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">icon</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#3748</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n23 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23744 title=\"4 occurrences\">#3744</a>}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">32 to 32</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#3749</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n23 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23744 title=\"4 occurrences\">#3744</a>}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">33 to 33</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">record</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">modelLabel</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">pluralModelLabel</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">recordTitle</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">recordTitleAttribute</span>: <span class=sf-dump-const>null</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">hasActionsModalRendered</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">componentFileAttachments</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">cachedForms</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref22281 title=\"4 occurrences\">#2281</a><samp data-depth=7 id=sf-dump-187790728-ref22281 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"35 characters\">filament-forms::component-container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">container</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n              +<span class=sf-dump-public title=\"Public property\">model</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">parentComponent</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">components</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextInput</span></span> {<a class=sf-dump-ref>#3573</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">component</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"37 characters\">filament-forms::components.text-input</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">field</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref22281 title=\"4 occurrences\">#2281</a>}\n                  #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">loadStateFromRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsBeforeChildrenUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedConcealingComponent</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedParentRepeater</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">actions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">actionFormModel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">childComponents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">id</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">key</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"13 characters\">Email address</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxWidth</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">meta</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateHydrated</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateUpdated</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">beforeStateDehydrated</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">dehydrateStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateDehydratedStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateStateForValidationUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasDefaultState</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydrated</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydratedWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: \"<span class=sf-dump-str title=\"10 characters\">data.email</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">stripCharacters</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedStripCharacters</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isAutofocused</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isMarkedAsRequired</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isRequired</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">regexPattern</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">rules</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>true</span>\n                    </samp>]\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">validationMessages</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">validationAttribute</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraFieldWrapperAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">helperText</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hint</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedHintActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">hintColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIconTooltip</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">mask</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isEmail</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isPassword</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isRevealable</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isTel</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isUrl</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxValue</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">minValue</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">telRegex</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">type</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">autocapitalize</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">autocomplete</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">length</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxLength</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">minLength</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isReadOnly</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedSuffixActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedPrefixActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixIconColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixIconColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isPrefixInline</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isSuffixInline</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">datalistOptions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraInputAttributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>tabindex</span>\" => <span class=sf-dump-num>1</span>\n                    </samp>]\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">inputMode</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">step</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraAlpineAttributes</span>: []\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TextInput</span></span> {<a class=sf-dump-ref>#3574</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">component</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"37 characters\">filament-forms::components.text-input</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">field</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref22281 title=\"4 occurrences\">#2281</a>}\n                  #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">loadStateFromRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsBeforeChildrenUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedConcealingComponent</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedParentRepeater</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">actions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">actionFormModel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">childComponents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">id</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">key</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"8 characters\">Password</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxWidth</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">meta</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateHydrated</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateUpdated</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">beforeStateDehydrated</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">dehydrateStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateDehydratedStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateStateForValidationUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasDefaultState</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydrated</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydratedWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: \"<span class=sf-dump-str title=\"13 characters\">data.password</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">stripCharacters</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedStripCharacters</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isAutofocused</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isMarkedAsRequired</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isRequired</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">regexPattern</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">rules</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">validationMessages</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">validationAttribute</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraFieldWrapperAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">helperText</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hint</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\HtmlString\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HtmlString</span></span> {<a class=sf-dump-ref>#2274</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">html</span>: \"\"\"\n                      <span class=sf-dump-str title=\"564 characters\">&lt;a<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">        href=&quot;http://localhost:8000/admin/password-reset/request&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">                                class=&quot;fi-link group/link relative inline-flex items-center justify-center outline-none fi-size-md fi-link-size-md gap-1.5 fi-color-custom fi-color-primary&quot; tabindex=&quot;3&quot;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">    &gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">        &lt;span class=&quot;font-semibold text-sm text-custom-600 dark:text-custom-400 group-hover/link:underline group-focus-visible/link:underline&quot; style=&quot;--c-400:var(--primary-400);--c-600:var(--primary-600);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">            Forgot password?<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">        &lt;/span&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n                      <span class=sf-dump-str title=\"564 characters\">            &lt;/a&gt;</span>\n                      \"\"\"\n                  </samp>}\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedHintActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">hintColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIconTooltip</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">mask</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isEmail</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isNumeric</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isPassword</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isRevealable</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isTel</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isUrl</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxValue</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">minValue</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">telRegex</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">type</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">autocapitalize</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">autocomplete</span>: \"<span class=sf-dump-str title=\"16 characters\">current-password</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">length</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxLength</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">minLength</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isReadOnly</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedSuffixActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixActions</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\ShowPasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShowPasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23578 title=\"3 occurrences\">#3578</a><samp data-depth=11 id=sf-dump-187790728-ref23578 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"31 characters\">filament-actions::button-action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: \"<span class=sf-dump-str title=\"25 characters\">isPasswordRevealed = true</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"13 characters\">Show password</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"12 characters\">showPassword</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: \"<span class=sf-dump-str title=\"4 characters\">gray</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>x-show</span>\" => \"<span class=sf-dump-str title=\"20 characters\">! isPasswordRevealed</span>\"\n                        </samp>]\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"14 characters\">heroicon-m-eye</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#3623</a><samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                        <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\ShowPasswordAction\n62 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShowPasswordAction</span></span>\"\n                        <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\ShowPasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShowPasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23578 title=\"3 occurrences\">#3578</a>}\n                        <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">32 to 32</span>\"\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#2278</a><samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                        <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\ShowPasswordAction\n62 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShowPasswordAction</span></span>\"\n                        <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\ShowPasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShowPasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23578 title=\"3 occurrences\">#3578</a>}\n                        <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">33 to 33</span>\"\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Filament\\Forms\\Components\\Component</span>\n                    </samp>}\n                    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\HidePasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HidePasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23587 title=\"3 occurrences\">#3587</a><samp data-depth=11 id=sf-dump-187790728-ref23587 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">view</span>: <span class=sf-dump-const title=\"Uninitialized property\">? string</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: \"<span class=sf-dump-str title=\"31 characters\">filament-actions::button-action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">livewireTarget</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">alpineClickHandler</span>: \"<span class=sf-dump-str title=\"26 characters\">isPasswordRevealed = false</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">group</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">authorization</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">labeledFrom</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isOutlined</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">parentActionCallLivewireClickHandler</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldClose</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">event</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">eventData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchDirection</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchToComponent</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldOpenUrlInNewTab</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">url</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">canSubmitForm</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">formToSubmit</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">formId</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">action</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isLivewireClickHandlerEnabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">arguments</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">groupedIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">keyBindings</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"13 characters\">Hide password</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"12 characters\">hidePassword</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultSize</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">size</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">tooltip</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badge</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeColor</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">badgeIconPosition</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">color</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">defaultColor</span>: \"<span class=sf-dump-str title=\"4 characters\">gray</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>x-cloak</span>\" => <span class=sf-dump-const>true</span>\n                          \"<span class=sf-dump-key>x-show</span>\" => \"<span class=sf-dump-str title=\"18 characters\">isPasswordRevealed</span>\"\n                        </samp>]\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">icon</span>: \"<span class=sf-dump-str title=\"20 characters\">heroicon-m-eye-slash</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">iconPosition</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">iconSize</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">livewire</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Livewire\\Component</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">mountUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">failureNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#3583</a><samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                        <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\HidePasswordAction\n62 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">HidePasswordAction</span></span>\"\n                        <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\HidePasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HidePasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23587 title=\"3 occurrences\">#3587</a>}\n                        <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">32 to 32</span>\"\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">successNotification</span>: <span class=sf-dump-note>Closure(Notification $notification): Notification</span> {<a class=sf-dump-ref>#3581</a><samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-meta>returnType</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Notifications\\Notification\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Notifications</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Notification</span></span>\"\n                        <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\HidePasswordAction\n62 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">HidePasswordAction</span></span>\"\n                        <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\TextInput\\Actions\\HidePasswordAction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components\\TextInput\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HidePasswordAction</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23587 title=\"3 occurrences\">#3587</a>}\n                        <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php\n66 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\actions\\</span><span class=\"sf-dump-ellipsis-tail\">src\\MountableAction.php</span></span>\"\n                        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">33 to 33</span>\"\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">failureNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">successNotificationTitle</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedExtraModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">extraModalFooterActions</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalFooterSticky</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalHeaderSticky</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedModalActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalActions</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalSlideOver</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalAlignment</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cachedModalFooterActions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalFooterActions</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalFooterActionsAlignment</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalCancelAction</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalCancelActionLabel</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalSubmitAction</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalSubmitActionLabel</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalContent</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalContentFooter</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalHeading</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalDescription</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalWidth</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasModal</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalHidden</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasModalCloseButton</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByClickingAway</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalClosedByEscaping</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isModalAutofocused</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalIcon</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modalIconColor</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">failureRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">successRedirectUrl</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hasDatabaseTransactions</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">extraModalWindowAttributes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">formData</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">form</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isFormDisabled</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">mutateFormDataUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">infolist</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">before</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">after</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">beforeFormFilled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">afterFormFilled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">beforeFormValidated</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">afterFormValidated</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">cancelParentActions</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isWizard</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">isWizardSkippable</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">wizardStartStep</span>: <span class=sf-dump-num>1</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">modifyWizardUsing</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Filament\\Forms\\Components\\Component</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedPrefixActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">prefixIconColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">suffixIconColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isPrefixInline</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isSuffixInline</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">datalistOptions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraInputAttributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>tabindex</span>\" => <span class=sf-dump-num>2</span>\n                    </samp>]\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">inputMode</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">placeholder</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">step</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraAlpineAttributes</span>: []\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Checkbox\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Checkbox</span></span> {<a class=sf-dump-ref>#3778</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">evaluationIdentifier</span>: \"<span class=sf-dump-str title=\"9 characters\">component</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"35 characters\">filament-forms::components.checkbox</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">viewData</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">viewIdentifier</span>: \"<span class=sf-dump-str title=\"5 characters\">field</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">canGrow</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Forms\\Form\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Forms</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Form</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref22281 title=\"4 occurrences\">#2281</a>}\n                  #<span class=sf-dump-protected title=\"Protected property\">model</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">loadStateFromRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">saveRelationshipsBeforeChildrenUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldSaveRelationshipsWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedConcealingComponent</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDisabled</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isVisible</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedParentRepeater</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">columnSpan</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">columnStart</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>sm</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>md</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>lg</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>xl</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>2xl</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">actions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">actionFormModel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">childComponents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">id</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabel</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">key</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLabelHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">label</span>: \"<span class=sf-dump-str title=\"11 characters\">Remember me</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">shouldTranslateLabel</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">maxWidth</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">meta</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateHydrated</span>: <span class=sf-dump-note>Closure(Checkbox $component, $state): void</span> {<a class=sf-dump-ref>#3588</a><samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-meta>returnType</span>: \"<span class=sf-dump-str title=\"4 characters\">void</span>\"\n                    <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Forms\\Components\\Checkbox\n34 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Forms\\Components</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Checkbox</span></span>\"\n                    <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Checkbox.php\n68 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\wamp64\\www\\smms\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\filament\\forms\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Components\\Checkbox.php</span></span>\"\n                    <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">23 to 25</span>\"\n                  </samp>}\n                  #<span class=sf-dump-protected title=\"Protected property\">afterStateUpdated</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">beforeStateDehydrated</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultState</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">dehydrateStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateDehydratedStateUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">mutateStateForValidationUsing</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hasDefaultState</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydrated</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isDehydratedWhenHidden</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"8 characters\">remember</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: \"<span class=sf-dump-str title=\"13 characters\">data.remember</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">stripCharacters</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedStripCharacters</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">columns</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isAutofocused</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isMarkedAsRequired</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">isRequired</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">regexPattern</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">rules</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>true</span>\n                    </samp>]\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">validationMessages</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">validationAttribute</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraFieldWrapperAttributes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">helperText</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hint</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">cachedHintActions</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintActions</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">hintColor</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIcon</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hintIconTooltip</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"8 characters\">remember</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">isInline</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">extraInputAttributes</span>: []\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">fieldWrapperView</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hasInlineLabels</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">operation</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">statePath</span>: \"<span class=sf-dump-str title=\"4 characters\">data</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">cachedAbsoluteStatePath</span>: \"<span class=sf-dump-str title=\"4 characters\">data</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">stateBindingModifiers</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">liveDebounce</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLive</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">isLiveOnBlur</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">extraAttributes</span>: []\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">hasCachedForms</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">isCachingForms</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">currentlyValidatingForm</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasFormsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">oldFormState</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsComponents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">hasInfolistsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedInfolists</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsComponent</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsInfolist</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">maxWidth</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasTopbar</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">data</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            \"<span class=sf-dump-key>remember</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cachedFormActions</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Filament\\Actions\\Action\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Filament\\Actions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Action</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23744 title=\"4 occurrences\">#3744</a>}\n          </samp>]\n        </samp>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vendor/livewire/livewire/src/Wrapped.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Pages\\Login\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Login</span></span> {<a class=sf-dump-ref href=#sf-dump-187790728-ref23979 title=\"13 occurrences\">#3979</a>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>474</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Livewire\\Wrapped</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">callMethods</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object App\\Filament\\Pages\\Login]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/livewire/livewire/src/LivewireManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>102</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n              \"<span class=sf-dump-key>remember</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">rkvYjhkRKsbudS34222d</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">app.filament.pages.login</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"11 characters\">admin/login</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">3e1fe980ab09a40e0d029e9989b8d7df711ff5109146c8857d0581c1ae1f52e5</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"73 characters\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>94</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Livewire\\LivewireManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n              \"<span class=sf-dump-key>remember</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">rkvYjhkRKsbudS34222d</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">app.filament.pages.login</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"11 characters\">admin/login</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">3e1fe980ab09a40e0d029e9989b8d7df711ff5109146c8857d0581c1ae1f52e5</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Livewire\\Mechanisms\\HandleRequests\\HandleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"58 characters\">[object Livewire\\Mechanisms\\HandleRequests\\HandleRequests]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">D:\\wamp64\\www\\smms\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187790728\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["\n", "        // Custom redirect based on user role\n", "        if ($user->hasRole('super_admin')) {\n", "            return redirect()->intended('/admin/dashboard');\n", "        }\n", "\n", "        if ($user->hasAnyRole(['jr_bde_team', 'bde_team'])) {\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=58", "ajax": false, "filename": "Login.php", "line": "58"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "__components::99503e4f2086f10483464823e7fe02ed", "param_count": null, "params": [], "start": **********.961829, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/99503e4f2086f10483464823e7fe02ed.blade.php__components::99503e4f2086f10483464823e7fe02ed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F99503e4f2086f10483464823e7fe02ed.blade.php&line=1", "ajax": false, "filename": "99503e4f2086f10483464823e7fe02ed.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.642542, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.211237, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.340972, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.99378, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.02094, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.023358, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.02361, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.023864, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.047605, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.052247, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.081266, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.945998, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.578659, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.609763, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.638688, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.639109, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.668104, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.77359, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.774158, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.140753, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.141528, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.142057, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 18, "nb_statements": 16, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01288, "accumulated_duration_str": "12.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.8937042, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 5.124}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:')", "type": "query", "params": [], "bindings": ["filament-excel:exports:"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.90731, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.124, "width_percent": 3.339}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:', 'illuminate:cache:flexible:created:filament-excel:exports:')", "type": "query", "params": [], "bindings": ["filament-excel:exports:", "illuminate:cache:flexible:created:filament-excel:exports:"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.909223, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.463, "width_percent": 2.795}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}], "start": **********.928801, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.258, "width_percent": 4.115}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}], "start": **********.9310622, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.373, "width_percent": 3.494}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer', 'i:1751375914;', 1751375914)", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5:timer", "i:1751375914;", 1751375914], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 164}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}, {"index": 15, "namespace": null, "name": "vendor/danharrin/livewire-rate-limiting/src/WithRateLimiting.php", "file": "D:\\wamp64\\www\\smms\\vendor\\danharrin\\livewire-rate-limiting\\src\\WithRateLimiting.php", "line": 38}], "start": **********.932078, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.866, "width_percent": 4.503}, {"sql": "select * from `cache` where `key` in ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5')", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}], "start": **********.933876, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.37, "width_percent": 11.724}, {"sql": "insert ignore into `cache` (`key`, `value`, `expiration`) values ('livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5', 'i:0;', 1751375914)", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5", "i:0;", 1751375914], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 169}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 300}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 168}], "start": **********.9362462, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:211", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=211", "ajax": false, "filename": "DatabaseStore.php", "line": "211"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.093, "width_percent": 3.649}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.940327, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.742, "width_percent": 0}, {"sql": "select * from `cache` where `key` = 'livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5' limit 1 for update", "type": "query", "params": [], "bindings": ["livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.940461, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:265", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 265}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=265", "ajax": false, "filename": "DatabaseStore.php", "line": "265"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.742, "width_percent": 6.755}, {"sql": "update `cache` set `value` = 'i:1;' where `key` = 'livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5'", "type": "query", "params": [], "bindings": ["i:1;", "livewire-rate-limiter:59d6ad626907b5a0341aba51c3754cd265bffec5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}], "start": **********.942039, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:290", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=290", "ajax": false, "filename": "DatabaseStore.php", "line": "290"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.497, "width_percent": 2.096}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 232}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 373}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 172}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php", "line": 149}], "start": **********.945876, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:261", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 261}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=261", "ajax": false, "filename": "DatabaseStore.php", "line": "261"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.593, "width_percent": 0}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.028087, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Login.php:32", "source": {"index": 16, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=32", "ajax": false, "filename": "Login.php", "line": "32"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.593, "width_percent": 11.102}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.033619, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Login.php:32", "source": {"index": 21, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=32", "ajax": false, "filename": "Login.php", "line": "32"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.696, "width_percent": 12.655}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 20, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.0372329, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.351, "width_percent": 4.736}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 414}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 411}, {"index": 25, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.039087, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:138", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=138", "ajax": false, "filename": "EloquentUserProvider.php", "line": "138"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.087, "width_percent": 4.193}, {"sql": "delete from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR'", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 578}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 549}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 422}], "start": **********.4609401, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.28, "width_percent": 3.571}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Filament/Pages/Login.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Pages\\Login.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.471532, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.851, "width_percent": 16.149}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Pages\\Login@authenticate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FPages%2FLogin.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Filament/Pages/Login.php:19-66</a>", "middleware": "web", "duration": "6.47s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-152204110 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-152204110\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1905492063 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bpmmx9EpQTx4qzzDyqm76UfPhGdHSohMPe3BqsaW</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"936 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;password&quot;:&quot;password&quot;,&quot;remember&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;rkvYjhkRKsbudS34222d&quot;,&quot;name&quot;:&quot;app.filament.pages.login&quot;,&quot;path&quot;:&quot;admin\\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;3e1fe980ab09a40e0d029e9989b8d7df711ff5109146c8857d0581c1ae1f52e5&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905492063\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2035768795 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1212</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1443 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; XSRF-TOKEN=eyJpdiI6IlZYM0ZXMVZFSHU5VndTL2JIVFE3RWc9PSIsInZhbHVlIjoiamViaDdqMjZDa0xmOGdpRmI1elJpbkJkL0FzbjM4Tmg3T0RNWTlkRkdWcGpYbWNra0J5RXRyQXpSTWk2aVZIenZvdGpiZnhmYktVakRUOW81QjRPZEM0SlkrNlVhZ3A4aWwrb0xBamZBd0phQ0hoWWVSVkppTUNLUEFPaVQ0bXciLCJtYWMiOiJlODczNTc1OGVlNzc1MmMwZWI3MmVkM2Q0ZWFiMDVjYTJiY2RiN2Y5ZmI4MjQxMGM1ZGM2ZDcyMTIzYzQ2MzA5IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IlA4SHIrZ0I1c3BtOEpwYnc3eU52UlE9PSIsInZhbHVlIjoibjZJbStjazRpMnovQW5BTlN6TmlsaHZIc3pVUGJFaGRYWVI5VjB1aHpXZ3NTcVlEbDNYSmhBVXdmZFdTbkxiMlZueDJrWUFJNDVEN3hkM1lZVGRDOHhmY1dLTjFQRFFLYmR0ZU43a1RmM01QOVhkR3F6QnVEeHd5R0dSL3o2dHYiLCJtYWMiOiI1NjEyM2E2MzEyZGQyY2Q5YzFkZmVjODYyMWUyNWExMzY0OGQ1MDM5OGU1ZGFhZmIxMDc2Y2Q1NTFkY2FmNjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035768795\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1548724030 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bpmmx9EpQTx4qzzDyqm76UfPhGdHSohMPe3BqsaW</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548724030\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1907027349 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 13:17:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907027349\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-718553570 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JkBrxExMneHJsPzVglmuWmJctVU691YvfMTxY73I</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718553570\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": "500 Internal Server Error"}}