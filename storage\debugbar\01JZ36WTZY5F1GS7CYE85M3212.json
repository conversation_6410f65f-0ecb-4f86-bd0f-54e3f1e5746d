{"__meta": {"id": "01JZ36WTZY5F1GS7CYE85M3212", "datetime": "2025-07-01 14:40:14", "utime": **********.846858, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751380813.559776, "end": **********.846874, "duration": 1.2870979309082031, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1751380813.559776, "relative_start": 0, "end": **********.198578, "relative_end": **********.198578, "duration": 0.****************, "duration_str": "639ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.198594, "relative_start": 0.****************, "end": **********.846875, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "648ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.731825, "relative_start": 1.****************, "end": **********.734262, "relative_end": **********.734262, "duration": 0.0024368762969970703, "duration_str": "2.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.844987, "relative_start": 1.****************, "end": **********.845578, "relative_end": **********.845578, "duration": 0.0005910396575927734, "duration_str": "591μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 39, "nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02099, "accumulated_duration_str": "20.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv' limit 1", "type": "query", "params": [], "bindings": ["6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.740155, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 7.527}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.7515981, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.527, "width_percent": 4.907}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.756525, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.434, "width_percent": 2.668}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.759857, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.102, "width_percent": 2.716}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.761331, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.818, "width_percent": 1.572}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7680838, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.39, "width_percent": 2.001}, {"sql": "select * from `payments` where year(`paid_date`) = 2025 and `status` in ('completed', 'paid') and `paid_date` is not null", "type": "query", "params": [], "bindings": [2025, "completed", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.769376, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.391, "width_percent": 2.668}, {"sql": "select `id`, `currency` from `projects` where `projects`.`id` in (160, 161, 162)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, {"index": 21, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.7727818, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MonthlyRevenueChart.php:79", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/MonthlyRevenueChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MonthlyRevenueChart.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMonthlyRevenueChart.php&line=79", "ajax": false, "filename": "MonthlyRevenueChart.php", "line": "79"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.059, "width_percent": 2.668}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.785518, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.727, "width_percent": 1.858}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.786419, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.585, "width_percent": 2.573}, {"sql": "select * from `cache` where `key` in ('usd_to_inr_rate')", "type": "query", "params": [], "bindings": ["usd_to_inr_rate"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.788901, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.158, "width_percent": 3.192}, {"sql": "select * from `roles` where `name` = 'bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.7934952, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.35, "width_percent": 3.907}, {"sql": "select * from `roles` where `name` = 'jr_bde_team' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["jr_bde_team", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 29, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}], "start": **********.7949872, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.256, "width_percent": 2.096}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (15, 16))", "type": "query", "params": [], "bindings": ["App\\Models\\User", 15, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.798423, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.353, "width_percent": 4.192}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4, 6, 19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8007011, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.545, "width_percent": 2.096}, {"sql": "select * from `projects` where `projects`.`user_id` in (4, 6, 19) and `won_date` between '2025-07-01 00:00:00' and '2025-07-31 23:59:59'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00", "2025-07-31 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.80232, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BdePerformanceChart.php:62", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/BdePerformanceChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\BdePerformanceChart.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FBdePerformanceChart.php&line=62", "ajax": false, "filename": "BdePerformanceChart.php", "line": "62"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.641, "width_percent": 3.573}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.8093, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.214, "width_percent": 2.049}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.810268, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.263, "width_percent": 1.143}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '02'", "type": "query", "params": [], "bindings": ["paid", 2025, "02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8115501, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.406, "width_percent": 2.239}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '03'", "type": "query", "params": [], "bindings": ["paid", 2025, "03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.812577, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.646, "width_percent": 1.572}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '04'", "type": "query", "params": [], "bindings": ["paid", 2025, "04"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8133962, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.218, "width_percent": 1.334}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '05'", "type": "query", "params": [], "bindings": ["paid", 2025, "05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.814105, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.552, "width_percent": 2.954}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '06'", "type": "query", "params": [], "bindings": ["paid", 2025, "06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.815171, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.505, "width_percent": 1.763}, {"sql": "select sum(`amount`) as aggregate from `payments` where `status` = 'paid' and year(`paid_date`) = 2025 and month(`paid_date`) = '07'", "type": "query", "params": [], "bindings": ["paid", 2025, "07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.816086, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:60", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=60", "ajax": false, "filename": "RevenueForecastChart.php", "line": "60"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.268, "width_percent": 2.287}, {"sql": "select * from `projects` where `status` in ('planning', 'in_progress')", "type": "query", "params": [], "bindings": ["planning", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}], "start": **********.8171532, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "RevenueForecastChart.php:252", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/RevenueForecastChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\RevenueForecastChart.php", "line": 252}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FRevenueForecastChart.php&line=252", "ajax": false, "filename": "RevenueForecastChart.php", "line": "252"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.555, "width_percent": 1.286}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.824366, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.841, "width_percent": 5.717}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.826839, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.558, "width_percent": 1.572}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '02' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["02", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.828379, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.131, "width_percent": 2.477}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '02' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["02", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.829881, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.608, "width_percent": 2.62}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '03' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["03", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.831047, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.228, "width_percent": 1.334}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '03' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["03", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.831959, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.562, "width_percent": 2.239}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '04' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["04", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.833107, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.801, "width_percent": 1.572}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '04' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["04", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.834086, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.374, "width_percent": 2.049}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '05' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["05", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8350918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.422, "width_percent": 1.191}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '05' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["05", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8359241, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.613, "width_percent": 4.717}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '06' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.8377771, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.33, "width_percent": 2.43}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '06' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["06", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.838959, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.759, "width_percent": 2.334}, {"sql": "select count(*) as aggregate from `milestones` where month(`due_date`) = '07' and year(`due_date`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.839941, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:66", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=66", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "66"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.094, "width_percent": 1.143}, {"sql": "select count(*) as aggregate from `payments` where exists (select * from `milestones` where `payments`.`milestone_id` = `milestones`.`id` and month(`due_date`) = '07' and year(`due_date`) = 2025) and `payments`.`status` = 'paid'", "type": "query", "params": [], "bindings": ["07", 2025, "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}], "start": **********.840688, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneDueVsReceivedChart.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/MilestoneDueVsReceivedChart.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\MilestoneDueVsReceivedChart.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FMilestoneDueVsReceivedChart.php&line=74", "ajax": false, "filename": "MilestoneDueVsReceivedChart.php", "line": "74"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.237, "width_percent": 1.763}]}, "models": {"data": {"App\\Models\\Payment": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.monthly-revenue-chart #l4QiAGq9U5hZsQnyOs3T": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"current_year\"\n    \"dataChecksum\" => \"cd90f05fa1811a21a22ad08c895b2b70\"\n  ]\n  \"name\" => \"app.filament.widgets.monthly-revenue-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MonthlyRevenueChart\"\n  \"id\" => \"l4QiAGq9U5hZsQnyOs3T\"\n]", "app.filament.widgets.bde-performance-chart #y5Dk3F4LMWh86mUEoo9s": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"this_month\"\n    \"dataChecksum\" => \"705f9f6a260c944b564b927a292dad89\"\n  ]\n  \"name\" => \"app.filament.widgets.bde-performance-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\BdePerformanceChart\"\n  \"id\" => \"y5Dk3F4LMWh86mUEoo9s\"\n]", "app.filament.widgets.revenue-forecast-chart #GTnaFrPUylU7Y7C1ZWWz": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"6_months\"\n    \"dataChecksum\" => \"0b4287a0535fd4b3b3dc29e9fa3d8c06\"\n  ]\n  \"name\" => \"app.filament.widgets.revenue-forecast-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\RevenueForecastChart\"\n  \"id\" => \"GTnaFrPUylU7Y7C1ZWWz\"\n]", "app.filament.widgets.milestone-due-vs-received-chart #VDHmXCEI8wgSzPENY8zc": "array:4 [\n  \"data\" => array:2 [\n    \"filter\" => \"last_6_months\"\n    \"dataChecksum\" => \"6edec273f189350582f767f28c874ffb\"\n  ]\n  \"name\" => \"app.filament.widgets.milestone-due-vs-received-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\MilestoneDueVsReceivedChart\"\n  \"id\" => \"VDHmXCEI8wgSzPENY8zc\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\MonthlyRevenueChart@updateChartData<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "1.29s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1740780824 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1740780824\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1312761405 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"357 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;current_year&quot;,&quot;dataChecksum&quot;:&quot;cd90f05fa1811a21a22ad08c895b2b70&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;l4QiAGq9U5hZsQnyOs3T&quot;,&quot;name&quot;:&quot;app.filament.widgets.monthly-revenue-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;3630d55051f9780f66fe1543101b319cb942583e757c43ddfe6f579860a67be9&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"355 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;this_month&quot;,&quot;dataChecksum&quot;:&quot;705f9f6a260c944b564b927a292dad89&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;y5Dk3F4LMWh86mUEoo9s&quot;,&quot;name&quot;:&quot;app.filament.widgets.bde-performance-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c34297350c8ab8a906726eedb90882cf8f6e4e3dc9f6a05ac7681d6d4814df4c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"354 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;6_months&quot;,&quot;dataChecksum&quot;:&quot;0b4287a0535fd4b3b3dc29e9fa3d8c06&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;GTnaFrPUylU7Y7C1ZWWz&quot;,&quot;name&quot;:&quot;app.filament.widgets.revenue-forecast-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;2f6fb342c5b8d12dd747e43ce4019f34ac5807f0d020e2800c1dc938d1cfdf8f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"368 characters\">{&quot;data&quot;:{&quot;filter&quot;:&quot;last_6_months&quot;,&quot;dataChecksum&quot;:&quot;6edec273f189350582f767f28c874ffb&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;VDHmXCEI8wgSzPENY8zc&quot;,&quot;name&quot;:&quot;app.filament.widgets.milestone-due-vs-received-chart&quot;,&quot;path&quot;:&quot;admin\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;bef99681dbb22bdf7066d93400d06c476c9a9f0f744879b94dfc5057175cec72&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312761405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-268857176 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2246</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1269 characters\">sb-updates=3.7.1; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjRDZXJsRHN1Ui9CUGlYZnhvT2pzS1E9PSIsInZhbHVlIjoiQk41QzFEOUh2Z1ZZN09sR0QzZHhhMWlKS2JERXkyT3E1SjZ5UDRnK205bXplVXZ6anZFOU9PYW9TV0lQV25aVXIwSUwzR2pMd1VHZHlkOVFPdDZkK2QveW9EcmRyMVlMcEMvNVhQU0x1MlJEeDdrMytyUmJycVdFZDZoS2NiM2xpbGVUZUZOYk1HM3J0cHI4Q3o4aVcwZ3ZtcENQRUdEWkdRcHlMbXNsUzlYYk1HVGdHK2pGYlRTRk5hL1A3Z2h1WjhidWpvamRNRXEvVFhRN25RWXZTVlRsai9LYTFRRCt0V0pWVjZocW45TT0iLCJtYWMiOiIzYjcxNzQ0ZmFkMGNjMGQ1YzYwM2I0NjczNGI5NjRmYWYyNDg5NmI3ZDRjNGJlOGFhZWFmZjlhZmQzY2RjZWU3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InIxOWd1L2xpTFhhRHFDZlBOUEVzdnc9PSIsInZhbHVlIjoiWDBiWlF4SUpCc3JVSUVuT0JpSzVVd0hEcmxnL2o2NWdUQWlRN1lQUHphZTdFZDBSNTM1WUlxZ2k4MW5jcjlteFo0aE1KakdrV3hjV0ZzZnlNcm95VFdJQXU1bENVTjNmTXFlVmVsL0cxL0FOemQrOGVEZ002SlNOTE80dHlTMHQiLCJtYWMiOiI2OTdlMTEzNWVlYTY5NzU0YWI2NTBjNTU1Njc0MmUyMjUxMWUwM2YzN2JmOTRhNGYyMWNjMWViM2VjOTY2Zjk1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imo4b2RPbWJmaTVYaHJYcmR6OWdDeWc9PSIsInZhbHVlIjoiZTdHeWY1TzBhTVBSeWREclhMMnRHSUJTSGljNWdDM1N1TVFLc09md2ppV0luYzhtbUNFbnovVUNYZmZxK2Y2cnI0TlZ5SkZJZXRXejY5U0J6NVdFWFpoS05VanZNSnlyRjBKdkxoZFFoZG5rRmVVcjk0bnJiaUV2QzVUbmZYdnMiLCJtYWMiOiIzODQwZDU5YWZlYzAwNzBmNzQ1ZTJkYThkMGVlYmQ3MTllZDNkMWFkNTZmMjc0MGIzMzRhNGJlMTAyZjlmYjJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268857176\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-448904300 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sb-updates</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6iwLrNGu1fy7pcLandt5vN6dRoysZoP6pKioJGqv</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448904300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1508973831 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 14:40:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508973831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-643450304 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2A03OOm0XooJXbEFOBTP2FZ54lGqBeVJ3Vjjw1L</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643450304\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}