<x-filament-panels::page>
    <div x-data="{ activeTab: 'details' }">
        <!-- Tab Navigation -->
        <div class="fi-ta">
            <div class="fi-ta-ctn overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <nav class="fi-ta-nav flex overflow-x-auto" aria-label="Tabs">
                    <button
                        @click="activeTab = 'details'"
                        :class="activeTab === 'details' ? 'fi-active' : ''"
                        class="fi-ta-item group flex items-center gap-x-2 px-3 py-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
                        :style="activeTab === 'details' ? 'background-color: rgb(59 130 246 / 0.1); color: rgb(59 130 246);' : ''">
                        <x-heroicon-o-document-text class="fi-ta-item-icon h-5 w-5 shrink-0" />
                        <span class="fi-ta-item-label">Project Details</span>
                    </button>

                    <button
                        @click="activeTab = 'milestones'"
                        :class="activeTab === 'milestones' ? 'fi-active' : ''"
                        class="fi-ta-item group flex items-center gap-x-2 px-3 py-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
                        :style="activeTab === 'milestones' ? 'background-color: rgb(59 130 246 / 0.1); color: rgb(59 130 246);' : ''">
                        <x-heroicon-o-flag class="fi-ta-item-icon h-5 w-5 shrink-0" />
                        <span class="fi-ta-item-label">Milestones</span>
                    </button>



                    <button
                        @click="activeTab = 'incentives'"
                        :class="activeTab === 'incentives' ? 'fi-active' : ''"
                        class="fi-ta-item group flex items-center gap-x-2 px-3 py-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
                        :style="activeTab === 'incentives' ? 'background-color: rgb(59 130 246 / 0.1); color: rgb(59 130 246);' : ''">
                        <x-heroicon-o-gift class="fi-ta-item-icon h-5 w-5 shrink-0" />
                        <span class="fi-ta-item-label">Incentives</span>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="mt-6">
            <!-- Project Details Tab -->
            <div x-show="activeTab === 'details'"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                <form wire:submit="save">
                    {{ $this->form }}

                    <div class="mt-6 flex justify-end space-x-3">
                        <x-filament::button
                            type="submit"
                            color="primary">
                            Save changes
                        </x-filament::button>
                    </div>
                </form>
            </div>

            <!-- Milestones Tab -->
            <div x-show="activeTab === 'milestones'"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                @livewire(\App\Filament\Resources\ProjectResource\RelationManagers\MilestonesRelationManager::class, [
                    'ownerRecord' => $record,
                    'pageClass' => static::class,
                ])
            </div>



            <!-- Incentives Tab -->
            <div x-show="activeTab === 'incentives'"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                @livewire(\App\Filament\Resources\ProjectResource\RelationManagers\IncentivesRelationManager::class, [
                    'ownerRecord' => $record,
                    'pageClass' => static::class,
                ])
            </div>
        </div>
    </div>
</x-filament-panels::page>
