{"__meta": {"id": "01JZ28G54E0NNYNS1XT6VN0CQN", "datetime": "2025-07-01 05:49:01", "utime": **********.96727, "method": "GET", "uri": "/admin/shield/roles/15/edit", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[05:48:58] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/shield\\/roles\\/15\\/edit\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.033224, "xdebug_link": null, "collector": "log"}, {"message": "[05:49:01] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/shield\\/roles\\/15\\/edit\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.962189, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.26691, "end": **********.967286, "duration": 4.700376033782959, "duration_str": "4.7s", "measures": [{"label": "Booting", "start": **********.26691, "relative_start": 0, "end": **********.685673, "relative_end": **********.685673, "duration": 0.****************, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.685688, "relative_start": 0.****************, "end": **********.967288, "relative_end": 1.9073486328125e-06, "duration": 4.***************, "duration_str": "4.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.980779, "relative_start": 0.****************, "end": **********.984813, "relative_end": **********.984813, "duration": 0.0040340423583984375, "duration_str": "4.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.275742, "relative_start": 2.****************, "end": **********.275742, "relative_end": **********.275742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.280826, "relative_start": 2.************, "end": **********.280826, "relative_end": **********.280826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.283681, "relative_start": 2.016770839691162, "end": **********.283681, "relative_end": **********.283681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.755188, "relative_start": 2.4882779121398926, "end": **********.755188, "relative_end": **********.755188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.574568, "relative_start": 4.307657957077026, "end": **********.574568, "relative_end": **********.574568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.578399, "relative_start": 4.31148886680603, "end": **********.578399, "relative_end": **********.578399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.582754, "relative_start": 4.315843820571899, "end": **********.582754, "relative_end": **********.582754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.59714, "relative_start": 4.330229997634888, "end": **********.59714, "relative_end": **********.59714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.600616, "relative_start": 4.333705902099609, "end": **********.600616, "relative_end": **********.600616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.604288, "relative_start": 4.337378025054932, "end": **********.604288, "relative_end": **********.604288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.61386, "relative_start": 4.346949815750122, "end": **********.61386, "relative_end": **********.61386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.620037, "relative_start": 4.3531270027160645, "end": **********.620037, "relative_end": **********.620037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.623963, "relative_start": 4.35705304145813, "end": **********.623963, "relative_end": **********.623963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.634264, "relative_start": 4.367353916168213, "end": **********.634264, "relative_end": **********.634264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.63615, "relative_start": 4.369239807128906, "end": **********.63615, "relative_end": **********.63615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.638889, "relative_start": 4.371978998184204, "end": **********.638889, "relative_end": **********.638889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.645845, "relative_start": 4.378934860229492, "end": **********.645845, "relative_end": **********.645845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.647693, "relative_start": 4.380782842636108, "end": **********.647693, "relative_end": **********.647693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.651689, "relative_start": 4.38477897644043, "end": **********.651689, "relative_end": **********.651689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.660447, "relative_start": 4.393536806106567, "end": **********.660447, "relative_end": **********.660447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.662935, "relative_start": 4.396024942398071, "end": **********.662935, "relative_end": **********.662935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.666508, "relative_start": 4.399597883224487, "end": **********.666508, "relative_end": **********.666508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.675408, "relative_start": 4.4084978103637695, "end": **********.675408, "relative_end": **********.675408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.678024, "relative_start": 4.411113977432251, "end": **********.678024, "relative_end": **********.678024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.681727, "relative_start": 4.414816856384277, "end": **********.681727, "relative_end": **********.681727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.690169, "relative_start": 4.423259019851685, "end": **********.690169, "relative_end": **********.690169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.692282, "relative_start": 4.425371885299683, "end": **********.692282, "relative_end": **********.692282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.695275, "relative_start": 4.428364992141724, "end": **********.695275, "relative_end": **********.695275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.704062, "relative_start": 4.437151908874512, "end": **********.704062, "relative_end": **********.704062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.705858, "relative_start": 4.438947916030884, "end": **********.705858, "relative_end": **********.705858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.708811, "relative_start": 4.441900968551636, "end": **********.708811, "relative_end": **********.708811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.716188, "relative_start": 4.449277877807617, "end": **********.716188, "relative_end": **********.716188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.718038, "relative_start": 4.451128005981445, "end": **********.718038, "relative_end": **********.718038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.720914, "relative_start": 4.454003810882568, "end": **********.720914, "relative_end": **********.720914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.728375, "relative_start": 4.461464881896973, "end": **********.728375, "relative_end": **********.728375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.730719, "relative_start": 4.463809013366699, "end": **********.730719, "relative_end": **********.730719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.734143, "relative_start": 4.467232942581177, "end": **********.734143, "relative_end": **********.734143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.742067, "relative_start": 4.475157022476196, "end": **********.742067, "relative_end": **********.742067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.744156, "relative_start": 4.477245807647705, "end": **********.744156, "relative_end": **********.744156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.748217, "relative_start": 4.481307029724121, "end": **********.748217, "relative_end": **********.748217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.757541, "relative_start": 4.490630865097046, "end": **********.757541, "relative_end": **********.757541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.759202, "relative_start": 4.4922919273376465, "end": **********.759202, "relative_end": **********.759202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.761645, "relative_start": 4.4947350025177, "end": **********.761645, "relative_end": **********.761645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.768403, "relative_start": 4.501492977142334, "end": **********.768403, "relative_end": **********.768403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.771108, "relative_start": 4.504197835922241, "end": **********.771108, "relative_end": **********.771108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.775418, "relative_start": 4.508507966995239, "end": **********.775418, "relative_end": **********.775418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.786551, "relative_start": 4.519640922546387, "end": **********.786551, "relative_end": **********.786551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.789451, "relative_start": 4.522540807723999, "end": **********.789451, "relative_end": **********.789451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.793798, "relative_start": 4.526887893676758, "end": **********.793798, "relative_end": **********.793798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.802978, "relative_start": 4.536067962646484, "end": **********.802978, "relative_end": **********.802978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.804655, "relative_start": 4.537744998931885, "end": **********.804655, "relative_end": **********.804655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.807091, "relative_start": 4.540180921554565, "end": **********.807091, "relative_end": **********.807091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.815692, "relative_start": 4.548781871795654, "end": **********.815692, "relative_end": **********.815692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.818397, "relative_start": 4.551486968994141, "end": **********.818397, "relative_end": **********.818397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.821966, "relative_start": 4.555055856704712, "end": **********.821966, "relative_end": **********.821966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.82606, "relative_start": 4.559149980545044, "end": **********.82606, "relative_end": **********.82606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.829635, "relative_start": 4.562724828720093, "end": **********.829635, "relative_end": **********.829635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.839873, "relative_start": 4.572962999343872, "end": **********.839873, "relative_end": **********.839873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.859547, "relative_start": 4.592636823654175, "end": **********.859547, "relative_end": **********.859547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.885058, "relative_start": 4.618147850036621, "end": **********.885058, "relative_end": **********.885058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.890715, "relative_start": 4.623804807662964, "end": **********.890715, "relative_end": **********.890715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.891709, "relative_start": 4.624799013137817, "end": **********.891709, "relative_end": **********.891709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.896393, "relative_start": 4.629482984542847, "end": **********.896393, "relative_end": **********.896393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.897359, "relative_start": 4.630448818206787, "end": **********.897359, "relative_end": **********.897359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.903127, "relative_start": 4.636216878890991, "end": **********.903127, "relative_end": **********.903127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.903425, "relative_start": 4.636514902114868, "end": **********.903425, "relative_end": **********.903425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.905152, "relative_start": 4.63824200630188, "end": **********.905152, "relative_end": **********.905152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.905389, "relative_start": 4.638478994369507, "end": **********.905389, "relative_end": **********.905389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.907042, "relative_start": 4.640131950378418, "end": **********.907042, "relative_end": **********.907042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.90728, "relative_start": 4.640369892120361, "end": **********.90728, "relative_end": **********.90728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.909121, "relative_start": 4.642210960388184, "end": **********.909121, "relative_end": **********.909121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.909364, "relative_start": 4.642453908920288, "end": **********.909364, "relative_end": **********.909364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.954166, "relative_start": 4.687255859375, "end": **********.954166, "relative_end": **********.954166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.954769, "relative_start": 4.687858819961548, "end": **********.954769, "relative_end": **********.954769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.958021, "relative_start": 4.691110849380493, "end": **********.958021, "relative_end": **********.958021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.958727, "relative_start": 4.691816806793213, "end": **********.958727, "relative_end": **********.958727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.959348, "relative_start": 4.6924378871917725, "end": **********.959348, "relative_end": **********.959348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.961812, "relative_start": 4.694901943206787, "end": **********.961914, "relative_end": **********.961914, "duration": 0.00010204315185546875, "duration_str": "102μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.964794, "relative_start": 4.69788384437561, "end": **********.964861, "relative_end": **********.964861, "duration": 6.699562072753906e-05, "duration_str": "67μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56337848, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 77, "nb_templates": 77, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.275727, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.283665, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.755172, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.574556, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.582735, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.826044, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.859536, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.885041, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.890703, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.891698, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.896378, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.897346, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.954155, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.954759, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.958011, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.958718, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.959339, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 24, "nb_statements": 24, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02732, "accumulated_duration_str": "27.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'lO9YpHC1LzPS7IGPthDvM4UO2sCRv82DE9kcrSEQ' limit 1", "type": "query", "params": [], "bindings": ["lO9YpHC1LzPS7IGPthDvM4UO2sCRv82DE9kcrSEQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.993173, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.013}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.9979591, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.013, "width_percent": 3.587}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.005202, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.6, "width_percent": 2.672}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.0171862, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.272, "width_percent": 9.407}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.021049, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.679, "width_percent": 1.501}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0246139, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.18, "width_percent": 2.196}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.0261478, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.376, "width_percent": 1.464}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.027459, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.84, "width_percent": 1.501}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.028703, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.341, "width_percent": 1.318}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.029882, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.659, "width_percent": 1.428}, {"sql": "select * from `roles` where `id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.03843, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.086, "width_percent": 2.233}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.043695, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.319, "width_percent": 2.599}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'view_client', 'create_client', 'update_client', 'delete_client', 'view_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'view_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'view_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'view_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'view_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'view_payment', 'create_payment', 'update_payment', 'delete_payment', 'view_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'view_product', 'create_product', 'update_product', 'delete_product', 'view_project', 'create_project', 'update_project', 'delete_project', 'view_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'view_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'view_user', 'create_user', 'update_user', 'delete_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "view_client", "create_client", "update_client", "delete_client", "view_incentive", "create_incentive", "update_incentive", "delete_incentive", "view_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "view_milestone", "create_milestone", "update_milestone", "delete_milestone", "view_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "view_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "view_payment", "create_payment", "update_payment", "delete_payment", "view_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "view_product", "create_product", "update_product", "delete_product", "view_project", "create_project", "update_project", "delete_project", "view_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "view_project::type", "create_project::type", "update_project::type", "delete_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "view_user", "create_user", "update_user", "delete_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.106932, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.918, "width_percent": 14.312}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.1169672, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.23, "width_percent": 4.246}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 24, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 28, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 29, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 229}], "start": **********.122386, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "Role.php:191", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=191", "ajax": false, "filename": "Role.php", "line": "191"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.476, "width_percent": 6.186}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.872726, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.662, "width_percent": 1.72}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.8862271, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.382, "width_percent": 13.397}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.892291, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.779, "width_percent": 10.212}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.899785, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.991, "width_percent": 8.492}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.903985, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.483, "width_percent": 1.318}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.905883, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.801, "width_percent": 1.208}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.907841, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.009, "width_percent": 1.501}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.909879, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.51, "width_percent": 1.794}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiOGZrZWFaZW9jNlBGTFU4SDJzN2lXcUN4VEFqR0pLRnJMcDVMdjVZYyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTUvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'lO9YpHC1LzPS7IGPthDvM4UO2sCRv82DE9kcrSEQ'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiOGZrZWFaZW9jNlBGTFU4SDJzN2lXcUN4VEFqR0pLRnJMcDVMdjVZYyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTUvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "lO9YpHC1LzPS7IGPthDvM4UO2sCRv82DE9kcrSEQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.962933, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.303, "width_percent": 3.697}]}, "models": {"data": {"App\\Models\\Permission": {"value": 86, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 99, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #L7qyGxNHUg5YmDYFL6bM": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => null\n    \"data\" => array:26 [\n      \"id\" => 15\n      \"name\" => \"bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:4 [\n        0 => \"view_app::notification\"\n        1 => \"create_app::notification\"\n        2 => \"update_app::notification\"\n        3 => \"delete_app::notification\"\n      ]\n      \"client\" => array:4 [\n        0 => \"view_client\"\n        1 => \"create_client\"\n        2 => \"update_client\"\n        3 => \"delete_client\"\n      ]\n      \"incentive\" => array:4 [\n        0 => \"view_incentive\"\n        1 => \"create_incentive\"\n        2 => \"update_incentive\"\n        3 => \"delete_incentive\"\n      ]\n      \"incentive::rule\" => array:4 [\n        0 => \"view_incentive::rule\"\n        1 => \"create_incentive::rule\"\n        2 => \"update_incentive::rule\"\n        3 => \"delete_incentive::rule\"\n      ]\n      \"milestone\" => array:4 [\n        0 => \"view_milestone\"\n        1 => \"create_milestone\"\n        2 => \"update_milestone\"\n        3 => \"delete_milestone\"\n      ]\n      \"notification::event\" => array:4 [\n        0 => \"view_notification::event\"\n        1 => \"create_notification::event\"\n        2 => \"update_notification::event\"\n        3 => \"delete_notification::event\"\n      ]\n      \"notification::role::preference\" => array:4 [\n        0 => \"view_notification::role::preference\"\n        1 => \"create_notification::role::preference\"\n        2 => \"update_notification::role::preference\"\n        3 => \"delete_notification::role::preference\"\n      ]\n      \"payment\" => array:4 [\n        0 => \"view_payment\"\n        1 => \"create_payment\"\n        2 => \"update_payment\"\n        3 => \"delete_payment\"\n      ]\n      \"pricing::model\" => array:4 [\n        0 => \"view_pricing::model\"\n        1 => \"create_pricing::model\"\n        2 => \"update_pricing::model\"\n        3 => \"delete_pricing::model\"\n      ]\n      \"product\" => array:4 [\n        0 => \"view_product\"\n        1 => \"create_product\"\n        2 => \"update_product\"\n        3 => \"delete_product\"\n      ]\n      \"project\" => array:4 [\n        0 => \"view_project\"\n        1 => \"create_project\"\n        2 => \"update_project\"\n        3 => \"delete_project\"\n      ]\n      \"project::status::log\" => array:4 [\n        0 => \"view_project::status::log\"\n        1 => \"create_project::status::log\"\n        2 => \"update_project::status::log\"\n        3 => \"delete_project::status::log\"\n      ]\n      \"project::type\" => array:4 [\n        0 => \"view_project::type\"\n        1 => \"create_project::type\"\n        2 => \"update_project::type\"\n        3 => \"delete_project::type\"\n      ]\n      \"role\" => array:6 [\n        0 => \"view_role\"\n        1 => \"view_any_role\"\n        2 => \"create_role\"\n        3 => \"update_role\"\n        4 => \"delete_role\"\n        5 => \"delete_any_role\"\n      ]\n      \"role::notification::settings\" => array:4 [\n        0 => \"view_role::notification::settings\"\n        1 => \"create_role::notification::settings\"\n        2 => \"update_role::notification::settings\"\n        3 => \"delete_role::notification::settings\"\n      ]\n      \"user\" => array:4 [\n        0 => \"view_user\"\n        1 => \"create_user\"\n        2 => \"update_user\"\n        3 => \"delete_user\"\n      ]\n      \"pages_tab\" => array:5 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_DashboardSettings\"\n        2 => \"page_ManageSetting\"\n        3 => \"page_Themes\"\n        4 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => array:15 [\n        0 => \"view_any_app::notification\"\n        1 => \"view_any_client\"\n        2 => \"view_any_incentive\"\n        3 => \"view_any_incentive::rule\"\n        4 => \"view_any_milestone\"\n        5 => \"view_any_notification::event\"\n        6 => \"view_any_notification::role::preference\"\n        7 => \"view_any_payment\"\n        8 => \"view_any_pricing::model\"\n        9 => \"view_any_product\"\n        10 => \"view_any_project\"\n        11 => \"view_any_project::status::log\"\n        12 => \"view_any_project::type\"\n        13 => \"view_any_role::notification::settings\"\n        14 => \"view_any_user\"\n      ]\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#2349\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #original: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2853\n          #items: array:86 [\n            0 => App\\Models\\Permission {#2942\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => App\\Models\\Permission {#2967\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => App\\Models\\Permission {#2966\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => App\\Models\\Permission {#2965\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => App\\Models\\Permission {#2964\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => App\\Models\\Permission {#2963\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => App\\Models\\Permission {#2962\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => App\\Models\\Permission {#2961\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => App\\Models\\Permission {#2960\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => App\\Models\\Permission {#2959\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => App\\Models\\Permission {#2958\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => App\\Models\\Permission {#2957\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => App\\Models\\Permission {#2956\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            13 => App\\Models\\Permission {#2955\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            14 => App\\Models\\Permission {#2954\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            15 => App\\Models\\Permission {#2953\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            16 => App\\Models\\Permission {#2952\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            17 => App\\Models\\Permission {#2951\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            18 => App\\Models\\Permission {#2950\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            19 => App\\Models\\Permission {#2949\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            20 => App\\Models\\Permission {#2948\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            21 => App\\Models\\Permission {#2968\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            22 => App\\Models\\Permission {#2969\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            23 => App\\Models\\Permission {#2970\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            24 => App\\Models\\Permission {#2971\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            25 => App\\Models\\Permission {#2972\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            26 => App\\Models\\Permission {#2973\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            27 => App\\Models\\Permission {#2974\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            28 => App\\Models\\Permission {#2975\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            29 => App\\Models\\Permission {#2976\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            30 => App\\Models\\Permission {#2977\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            31 => App\\Models\\Permission {#2978\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            32 => App\\Models\\Permission {#2979\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            33 => App\\Models\\Permission {#2980\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            34 => App\\Models\\Permission {#2981\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            35 => App\\Models\\Permission {#2982\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            36 => App\\Models\\Permission {#2983\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            37 => App\\Models\\Permission {#2984\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            38 => App\\Models\\Permission {#2985\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            39 => App\\Models\\Permission {#2986\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            40 => App\\Models\\Permission {#2987\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            41 => App\\Models\\Permission {#2988\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            42 => App\\Models\\Permission {#2989\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            43 => App\\Models\\Permission {#2990\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            44 => App\\Models\\Permission {#2991\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            45 => App\\Models\\Permission {#2992\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            46 => App\\Models\\Permission {#2993\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            47 => App\\Models\\Permission {#2994\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            48 => App\\Models\\Permission {#2995\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            49 => App\\Models\\Permission {#2996\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            50 => App\\Models\\Permission {#2997\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            51 => App\\Models\\Permission {#2998\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            52 => App\\Models\\Permission {#2999\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            53 => App\\Models\\Permission {#3000\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            54 => App\\Models\\Permission {#3001\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            55 => App\\Models\\Permission {#3002\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            56 => App\\Models\\Permission {#3003\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            57 => App\\Models\\Permission {#3004\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            58 => App\\Models\\Permission {#3005\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            59 => App\\Models\\Permission {#3006\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #previous: []\n              #casts: array:1 [ …1]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [ …2]\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            60 => App\\Models\\Permission {#3007\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n               …28\n            }\n            61 => App\\Models\\Permission {#3008 …37}\n            62 => App\\Models\\Permission {#3009 …37}\n            63 => App\\Models\\Permission {#3010 …37}\n            64 => App\\Models\\Permission {#3011 …37}\n            65 => App\\Models\\Permission {#3012 …37}\n            66 => App\\Models\\Permission {#3013 …37}\n            67 => App\\Models\\Permission {#3014 …37}\n            68 => App\\Models\\Permission {#3015 …37}\n            69 => App\\Models\\Permission {#3016 …37}\n            70 => App\\Models\\Permission {#3017 …37}\n            71 => App\\Models\\Permission {#3018 …37}\n            72 => App\\Models\\Permission {#3019 …37}\n            73 => App\\Models\\Permission {#3020 …37}\n            74 => App\\Models\\Permission {#3021 …37}\n            75 => App\\Models\\Permission {#3022 …37}\n            76 => App\\Models\\Permission {#3023 …37}\n            77 => App\\Models\\Permission {#3024 …37}\n            78 => App\\Models\\Permission {#3025 …37}\n            79 => App\\Models\\Permission {#3026 …37}\n            80 => App\\Models\\Permission {#3027 …37}\n            81 => App\\Models\\Permission {#3028 …37}\n            82 => App\\Models\\Permission {#3029 …37}\n            83 => App\\Models\\Permission {#3030 …37}\n            84 => App\\Models\\Permission {#3031 …37}\n            85 => App\\Models\\Permission {#3032 …37}\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: \"App\\Models\\Permission\"\n      -wildcardClass: \"\"\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"L7qyGxNHUg5YmDYFL6bM\"\n]", "filament.livewire.global-search #FasjTkAITJE1ZNUC4QGM": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"FasjTkAITJE1ZNUC4QGM\"\n]", "app.filament.widgets.notification-components.notification-bell #kBJQjkV63JtwvnerlgtX": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 177\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"kBJQjkV63JtwvnerlgtX\"\n]", "filament.livewire.notifications #pMV72s2U9JWFAt1MGkik": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3109\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"pMV72s2U9JWFAt1MGkik\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 29, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2129640333 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129640333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047865, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1742175312 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742175312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62932, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-701269157 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701269157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630581, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2011132056 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011132056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.834408, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-873865486 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873865486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.835093, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-512513582 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512513582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862682, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-615973332 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615973332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862938, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1025548976 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025548976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863827, "xdebug_link": null}, {"message": "[\n  ability => view_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1946335291 data-indent-pad=\"  \"><span class=sf-dump-note>view_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946335291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86421, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1123561546 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123561546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86467, "xdebug_link": null}, {"message": "[\n  ability => view_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2055121224 data-indent-pad=\"  \"><span class=sf-dump-note>view_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055121224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865043, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-290247359 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290247359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865616, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-633386102 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633386102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.866196, "xdebug_link": null}, {"message": "[\n  ability => view_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2076543218 data-indent-pad=\"  \"><span class=sf-dump-note>view_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076543218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.866553, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-351998797 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351998797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.86699, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114492565 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114492565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867531, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1605996568 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605996568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868085, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-662138750 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662138750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868633, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1175315416 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175315416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869192, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-431340766 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431340766\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869711, "xdebug_link": null}, {"message": "[\n  ability => view_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-567281100 data-indent-pad=\"  \"><span class=sf-dump-note>view_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567281100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870056, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-10495367 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10495367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870483, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1517287634 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517287634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871051, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1034816633 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034816633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871578, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444876070 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444876070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872091, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-899461178 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899461178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872435, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1477573865 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477573865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874448, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009176435 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009176435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875095, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1534838662 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534838662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879973, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/15/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole", "uri": "GET admin/shield/roles/{record}/edit", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/shield/roles", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "4.7s", "peak_memory": "62MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/admin/shield/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlJlQ3p1S0pzRkJnOWxPaFRlOXZwSnc9PSIsInZhbHVlIjoiWXlFWmZIazh5dTFWNW5pTWlHcFB5ZTB2WnNPWlc2MFJ4cmlFSHNCbDNia2tmTmJHNkZkYlNSa1ZPYWtqVGhHV1BSOFZLSTF0MVZxUWVLcERrQ0NCZnJzSmk1UWpNZFNxc0V1ZFJ5aEgyb3ZiMFRWelNRWXNHRGt5bThwczIwQkdOYXVoZUtSWGhEL2ZxTG5TZUxMYmFhbjI1bm1oZ1dETTQ4TlBQU1BzRkh6elRNaVpKRENQOXZQMW5WUXdoeWNPSDEzNy9EWXp2QWZFNXFaYWphQnBNakVoQm4zSEJuVDZLVitJVmEzTGZXQT0iLCJtYWMiOiJlMWEwODFlMzI2YTgzYTRhNDVkNTliZDgzNTU4MzAzMjRkNjM2MTMwM2IwN2FmNzJmYzkwYmM1MTI2NDJmYTU3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InphUnN1V0dEdjdiUFdFdjhhYWdXekE9PSIsInZhbHVlIjoiQWJ2U0pQRkhGS25XWHFraWJWOHN2cXhUSTk1UUs1NzErQTltQTBwMGFra002V3VlSElzV1R2bUN3ZFdlQzVaNzU1ZVZkcnpjOFo3eGxnSzM3MCtxakp5UGRsdHdUR29FT0RBUlEzMHA4bUUxSWdueWk3ZjM0YXZnck9HUmdZMzIiLCJtYWMiOiI3ZmUzN2Q4NmZjZDIxYThhZWYwNWFkZDk0YjY0NWNiOWY5ZjdkOTEwNjQ5MzUyYjk0OWEzYzIyMDFhMmMyY2IxIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6InZ2UnlsQ2dsVTV3TURFYXZ2Sm9zSGc9PSIsInZhbHVlIjoiSTN4ZmJPMzh2eTVDclhXaVNmRlFPQUo3WWkrcU1WeGJNYmRhRWMvOU5SK2ptc2dqaVNralZNWnZISEg3aHZmY3RxWndhTkJlTjlmZEVGRFRMeFFyMS8ranlrNXJVOVVsVDNCRHFjZlRHV2I4dE1sTFhCS253amdwWCtEbXhKSGoiLCJtYWMiOiIyZTBiYzEyZWYwYTYzMTJmYzFlNDE2MmE1NWJkNTU3MDg2NjE4ZDdkYjNhZTQxZWNlOWEzMzVlMTdkNDYxNGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1299417867 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|sPa90Sya520RCO4UZaWteRKQYL8v2mRifn9zs3IuMQRWpPnaBt1XFp3NCRt3|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8fkeaZeoc6PFLU8H2s7iWqCxTAjGJKFrLp5Lv5Yc</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lO9YpHC1LzPS7IGPthDvM4UO2sCRv82DE9kcrSEQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299417867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 05:49:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVtZFV6djI4LzlRYXBGN1NIelVwYlE9PSIsInZhbHVlIjoiaUJIRzRhc3JQdXVhQ3dzOStJVzB4aGlEUGQ5T1JYL1NNdXNOYUxJQ3JlV2RVc3p1SFU4aFpJdkdseU1KZEJOd0dlR3NKNGJjaWhVcFVjOHF1WXlMVnBXWFVBaE1QOGhEaW5tc3RpZUhtZHlEZzhsZ0ZFdzNmeGhZalVUS3JqQ3giLCJtYWMiOiI4YjUzZDE3NzE5NTAxMzQxZWQ3N2Y5N2MwOTM1MDgzMjQxMzg0ZDBhM2NmMGQ2ODQwZjcwYjJjZjJjYWEyMWU0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 07:49:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IjhiZ0lzZFBUck8xc2R0YjJRdjRtVWc9PSIsInZhbHVlIjoidE1WQVZoWkpIR3JYc0orWmNGNk43N1hyUFVjZmFxZ2JLWFc5Q3ZUb1d6UWNvSU8rYWg3a3RseGRFZ2Zzc004U040ek1aUTJzYi9BMEtlQm9JTzhad3FHeE5jLzZLVUlaNGRvVDBCcm5TYzE4OEw1ZWJ2U0ZjcjRjbEh0YnRDY0wiLCJtYWMiOiJiMmVhMTQ0MTYxM2U0MmJkNmRlNmY0NDEzMDA3NTRkOGM5MmUzOWYwM2JjOWMxZDZlYzgzZGM5NGZkZDQ5MzRlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 07:49:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVtZFV6djI4LzlRYXBGN1NIelVwYlE9PSIsInZhbHVlIjoiaUJIRzRhc3JQdXVhQ3dzOStJVzB4aGlEUGQ5T1JYL1NNdXNOYUxJQ3JlV2RVc3p1SFU4aFpJdkdseU1KZEJOd0dlR3NKNGJjaWhVcFVjOHF1WXlMVnBXWFVBaE1QOGhEaW5tc3RpZUhtZHlEZzhsZ0ZFdzNmeGhZalVUS3JqQ3giLCJtYWMiOiI4YjUzZDE3NzE5NTAxMzQxZWQ3N2Y5N2MwOTM1MDgzMjQxMzg0ZDBhM2NmMGQ2ODQwZjcwYjJjZjJjYWEyMWU0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 07:49:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IjhiZ0lzZFBUck8xc2R0YjJRdjRtVWc9PSIsInZhbHVlIjoidE1WQVZoWkpIR3JYc0orWmNGNk43N1hyUFVjZmFxZ2JLWFc5Q3ZUb1d6UWNvSU8rYWg3a3RseGRFZ2Zzc004U040ek1aUTJzYi9BMEtlQm9JTzhad3FHeE5jLzZLVUlaNGRvVDBCcm5TYzE4OEw1ZWJ2U0ZjcjRjbEh0YnRDY0wiLCJtYWMiOiJiMmVhMTQ0MTYxM2U0MmJkNmRlNmY0NDEzMDA3NTRkOGM5MmUzOWYwM2JjOWMxZDZlYzgzZGM5NGZkZDQ5MzRlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 07:49:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8fkeaZeoc6PFLU8H2s7iWqCxTAjGJKFrLp5Lv5Yc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/15/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole"}, "badge": null}}