[2025-07-01 11:42:21] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-01 11:42:22] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-01 11:42:23] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-01 11:42:24] local.DEBUG: RedirectByRole: User check {"user_id":4,"roles":["bde_team"],"current_path":"admin/payments"} 
[2025-07-01 11:42:33] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-01 11:42:33] local.DEBUG: RedirectByRole: User check {"user_id":4,"roles":["bde_team"],"current_path":"admin/payments"} 
[2025-07-01 11:42:34] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-01 11:42:34] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-01 11:42:50] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/app-notifications","authenticated":"yes"} 
[2025-07-01 11:42:51] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/app-notifications"} 
[2025-07-01 11:42:52] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/payments","authenticated":"yes"} 
[2025-07-01 11:42:53] local.DEBUG: RedirectByRole: User check {"user_id":4,"roles":["bde_team"],"current_path":"admin/payments"} 
[2025-07-01 11:44:20] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects","authenticated":"yes"} 
[2025-07-01 11:44:21] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects"} 
[2025-07-01 11:44:27] local.DEBUG: RedirectByRole: Middleware entered {"path":"admin/projects/160/edit","authenticated":"yes"} 
[2025-07-01 11:44:29] local.DEBUG: RedirectByRole: User check {"user_id":1,"roles":["super_admin"],"current_path":"admin/projects/160/edit"} 
