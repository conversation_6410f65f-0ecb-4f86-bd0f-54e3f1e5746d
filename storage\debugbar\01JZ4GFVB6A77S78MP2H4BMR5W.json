{"__meta": {"id": "01JZ4GFVB6A77S78MP2H4BMR5W", "datetime": "2025-07-02 02:47:09", "utime": **********.414804, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.530481, "end": **********.414825, "duration": 0.8843438625335693, "duration_str": "884ms", "measures": [{"label": "Booting", "start": **********.530481, "relative_start": 0, "end": **********.975966, "relative_end": **********.975966, "duration": 0.*****************, "duration_str": "445ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.975979, "relative_start": 0.***************, "end": **********.414827, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "439ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.347838, "relative_start": 0.****************, "end": **********.351898, "relative_end": **********.351898, "duration": 0.004060029983520508, "duration_str": "4.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.411702, "relative_start": 0.***************, "end": **********.412543, "relative_end": **********.412543, "duration": 0.0008411407470703125, "duration_str": "841μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.412571, "relative_start": 0.****************, "end": **********.412609, "relative_end": **********.412609, "duration": 3.814697265625e-05, "duration_str": "38μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "884ms", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-933707061 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933707061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-32282724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-32282724\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-660244743 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/25/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFSOG8yR3RDVWZJWjY0Q3RHN3lScWc9PSIsInZhbHVlIjoiSWlBR25rbCtWT0tnaXhTaWdQT3hpY0JsVXZwaFRRdkR3bHN4azYyVEpBWFB3cUduT3JGa1AydVlucXRWUmdZcWg3d0NBc2RrVDFoUTYwSG9Hb3pYYnZxdzBBSUZMZFU5RDAzT1ovSmIyeTFJUXhVWHM2VDJEQ3RVV3J5M2hKL3ZCbURRaTh1S1EvSUFYRFU4N2xrbkpNSXlXcHlvYTBCRXJMTXRPVXRheUgwZ25lWEZpdW9nNzZEMU94YXZ3RHl6a2wrYzBvcytZeit6bmJhMWNUWDE0WmN2YUgzUysyQVhQZXIrUXhKY2FTND0iLCJtYWMiOiIyNmY0MDgyZDE4OTU3MjMxNWZjZDY4MDVjYmJhODNkZDgwNTgyNmVhNzk0MDgzM2VjNGZlNzJkODUyNDY2M2EyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNKYklnWXpHaGlORG44RWkxN2EzK0E9PSIsInZhbHVlIjoiTm01MEI0cVN2Q2QxaERxT1FmS2VPWXNQSHhTTUx0KzFabm9WZGhsdUk2aDRxVGV2QlF4dU5KZzNTelNJK0RwdXNsRGZrdkVmdEZESjNOcmVVTzJsTlA5TnZrYzNRWnJEQ2JBQXREOWZ5cExja2JueHV1MW9wczZGMFRkL0lFTzgiLCJtYWMiOiJkMjEzZjkyMmUwODY4YWFjYzQ0NTU0NDY0NDIxM2EzMGQzYjM4OTcxZTA2NDI0MTc1MjUzMmQxNmVlNGEzMWY2IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IjllM3o1RTVvUkJ0YmwyQU9tNHdDVkE9PSIsInZhbHVlIjoieU1ML3VUOG9RY2lqVmowVjc2SmxZVGlIa1d3dDFPQ3U3T3BpTW14dU9XQ0RMSlp0UVMxVWtpeE9tSk45bllwZmNhWDI4VEdsc2diZ3d5b1lLS0M4bWdsRXJTZklxMmkwVk00Yzl5OVVHVmUrd0lKeUE5TVpBeWMwbVplOTAzTTYiLCJtYWMiOiI1YjRjMWFkMGJjMDQxYjE3MWFhNmM4YzE5ODA1ZjlmZDg0MjBiM2E4ODg5Y2I4Y2VlYTg5OWQ5NjljMDRlYTMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660244743\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1192551257 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6InFSOG8yR3RDVWZJWjY0Q3RHN3lScWc9PSIsInZhbHVlIjoiSWlBR25rbCtWT0tnaXhTaWdQT3hpY0JsVXZwaFRRdkR3bHN4azYyVEpBWFB3cUduT3JGa1AydVlucXRWUmdZcWg3d0NBc2RrVDFoUTYwSG9Hb3pYYnZxdzBBSUZMZFU5RDAzT1ovSmIyeTFJUXhVWHM2VDJEQ3RVV3J5M2hKL3ZCbURRaTh1S1EvSUFYRFU4N2xrbkpNSXlXcHlvYTBCRXJMTXRPVXRheUgwZ25lWEZpdW9nNzZEMU94YXZ3RHl6a2wrYzBvcytZeit6bmJhMWNUWDE0WmN2YUgzUysyQVhQZXIrUXhKY2FTND0iLCJtYWMiOiIyNmY0MDgyZDE4OTU3MjMxNWZjZDY4MDVjYmJhODNkZDgwNTgyNmVhNzk0MDgzM2VjNGZlNzJkODUyNDY2M2EyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNKYklnWXpHaGlORG44RWkxN2EzK0E9PSIsInZhbHVlIjoiTm01MEI0cVN2Q2QxaERxT1FmS2VPWXNQSHhTTUx0KzFabm9WZGhsdUk2aDRxVGV2QlF4dU5KZzNTelNJK0RwdXNsRGZrdkVmdEZESjNOcmVVTzJsTlA5TnZrYzNRWnJEQ2JBQXREOWZ5cExja2JueHV1MW9wczZGMFRkL0lFTzgiLCJtYWMiOiJkMjEzZjkyMmUwODY4YWFjYzQ0NTU0NDY0NDIxM2EzMGQzYjM4OTcxZTA2NDI0MTc1MjUzMmQxNmVlNGEzMWY2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjllM3o1RTVvUkJ0YmwyQU9tNHdDVkE9PSIsInZhbHVlIjoieU1ML3VUOG9RY2lqVmowVjc2SmxZVGlIa1d3dDFPQ3U3T3BpTW14dU9XQ0RMSlp0UVMxVWtpeE9tSk45bllwZmNhWDI4VEdsc2diZ3d5b1lLS0M4bWdsRXJTZklxMmkwVk00Yzl5OVVHVmUrd0lKeUE5TVpBeWMwbVplOTAzTTYiLCJtYWMiOiI1YjRjMWFkMGJjMDQxYjE3MWFhNmM4YzE5ODA1ZjlmZDg0MjBiM2E4ODg5Y2I4Y2VlYTg5OWQ5NjljMDRlYTMzIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192551257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-614376167 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 02 Jul 2026 02:47:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 09:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 02:47:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614376167\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-123928110 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-123928110\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}