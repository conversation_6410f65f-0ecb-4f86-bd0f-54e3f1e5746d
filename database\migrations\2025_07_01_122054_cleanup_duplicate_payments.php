<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clean up duplicate payments
        $this->cleanupDuplicatePayments();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as it removes duplicate data
    }

    private function cleanupDuplicatePayments(): void
    {
        // Find and remove duplicate payments
        // Keep the payment with the lowest ID (first created) for each duplicate group

        $duplicatePayments = DB::select("
            SELECT
                project_id,
                amount,
                due_date,
                status,
                COUNT(*) as count,
                GROUP_CONCAT(id ORDER BY id) as ids
            FROM payments
            GROUP BY project_id, amount, due_date, status
            HAVING COUNT(*) > 1
        ");

        foreach ($duplicatePayments as $duplicate) {
            $ids = explode(',', $duplicate->ids);
            // Keep the first ID, delete the rest
            $idsToDelete = array_slice($ids, 1);

            if (!empty($idsToDelete)) {
                DB::table('payments')
                    ->whereIn('id', $idsToDelete)
                    ->delete();

                echo "Removed " . count($idsToDelete) . " duplicate payments for project {$duplicate->project_id}\n";
            }
        }

        // Also clean up duplicate milestones
        $duplicateMilestones = DB::select("
            SELECT
                project_id,
                title,
                due_date,
                percentage,
                amount,
                COUNT(*) as count,
                GROUP_CONCAT(id ORDER BY id) as ids
            FROM milestones
            GROUP BY project_id, title, due_date, percentage, amount
            HAVING COUNT(*) > 1
        ");

        foreach ($duplicateMilestones as $duplicate) {
            $ids = explode(',', $duplicate->ids);
            // Keep the first ID, delete the rest
            $idsToDelete = array_slice($ids, 1);

            if (!empty($idsToDelete)) {
                DB::table('milestones')
                    ->whereIn('id', $idsToDelete)
                    ->delete();

                echo "Removed " . count($idsToDelete) . " duplicate milestones for project {$duplicate->project_id}\n";
            }
        }
    }
};
