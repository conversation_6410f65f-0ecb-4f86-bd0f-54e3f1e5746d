{"__meta": {"id": "01JZ2RHEZF6ZVK87NTAADJ292K", "datetime": "2025-07-01 10:29:22", "utime": **********.032104, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751365759.644542, "end": **********.032124, "duration": 2.3875820636749268, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1751365759.644542, "relative_start": 0, "end": **********.035533, "relative_end": **********.035533, "duration": 0.****************, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.035542, "relative_start": 0.*****************, "end": **********.032127, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.661972, "relative_start": 1.***************, "end": **********.665803, "relative_end": **********.665803, "duration": 0.0038309097290039062, "duration_str": "3.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.918435, "relative_start": 1.***************, "end": **********.918435, "relative_end": **********.918435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.92721, "relative_start": 1.***************, "end": **********.92721, "relative_end": **********.92721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.933579, "relative_start": 1.2890369892120361, "end": **********.933579, "relative_end": **********.933579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.944728, "relative_start": 1.3001859188079834, "end": **********.944728, "relative_end": **********.944728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.96182, "relative_start": 1.3172779083251953, "end": **********.96182, "relative_end": **********.96182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.969717, "relative_start": 1.3251750469207764, "end": **********.969717, "relative_end": **********.969717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.975848, "relative_start": 1.331305980682373, "end": **********.975848, "relative_end": **********.975848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.020981, "relative_start": 1.376439094543457, "end": 1751365761.020981, "relative_end": 1751365761.020981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.060224, "relative_start": 1.415682077407837, "end": 1751365761.060224, "relative_end": 1751365761.060224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.088474, "relative_start": 1.443932056427002, "end": 1751365761.088474, "relative_end": 1751365761.088474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.121238, "relative_start": 1.4766960144042969, "end": 1751365761.121238, "relative_end": 1751365761.121238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.155408, "relative_start": 1.5108659267425537, "end": 1751365761.155408, "relative_end": 1751365761.155408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.195771, "relative_start": 1.5512290000915527, "end": 1751365761.195771, "relative_end": 1751365761.195771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.226087, "relative_start": 1.581545114517212, "end": 1751365761.226087, "relative_end": 1751365761.226087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.254092, "relative_start": 1.6095499992370605, "end": 1751365761.254092, "relative_end": 1751365761.254092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.298511, "relative_start": 1.6539690494537354, "end": 1751365761.298511, "relative_end": 1751365761.298511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.339147, "relative_start": 1.6946051120758057, "end": 1751365761.339147, "relative_end": 1751365761.339147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.378787, "relative_start": 1.7342450618743896, "end": 1751365761.378787, "relative_end": 1751365761.378787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.420959, "relative_start": 1.7764170169830322, "end": 1751365761.420959, "relative_end": 1751365761.420959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.454725, "relative_start": 1.810183048248291, "end": 1751365761.454725, "relative_end": 1751365761.454725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.489416, "relative_start": 1.8448739051818848, "end": 1751365761.489416, "relative_end": 1751365761.489416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.524975, "relative_start": 1.8804330825805664, "end": 1751365761.524975, "relative_end": 1751365761.524975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.561258, "relative_start": 1.9167160987854004, "end": 1751365761.561258, "relative_end": 1751365761.561258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.61114, "relative_start": 1.9665980339050293, "end": 1751365761.61114, "relative_end": 1751365761.61114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.654491, "relative_start": 2.009948968887329, "end": 1751365761.654491, "relative_end": 1751365761.654491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.695843, "relative_start": 2.0513010025024414, "end": 1751365761.695843, "relative_end": 1751365761.695843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.765617, "relative_start": 2.121074914932251, "end": 1751365761.765617, "relative_end": 1751365761.765617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.808905, "relative_start": 2.164362907409668, "end": 1751365761.808905, "relative_end": 1751365761.808905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.854064, "relative_start": 2.209522008895874, "end": 1751365761.854064, "relative_end": 1751365761.854064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.898574, "relative_start": 2.2540321350097656, "end": 1751365761.898574, "relative_end": 1751365761.898574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.944666, "relative_start": 2.300123929977417, "end": 1751365761.944666, "relative_end": 1751365761.944666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": 1751365761.9989, "relative_start": 2.354357957839966, "end": 1751365761.9989, "relative_end": 1751365761.9989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.026497, "relative_start": 2.3819549083709717, "end": **********.030138, "relative_end": **********.030138, "duration": 0.0036411285400390625, "duration_str": "3.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56488192, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 32, "nb_templates": 32, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.91841, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.927192, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.933532, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.944708, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.961803, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.969705, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.975831, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.020965, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.060211, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.088461, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.121219, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.155378, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.195755, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.226076, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.254076, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.298489, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.339118, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.378772, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.420938, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.454713, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.4894, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.524956, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.561241, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.611121, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.654478, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.695819, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.765599, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.808887, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.854037, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.898556, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.944648, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1751365761.99888, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.033240000000000006, "accumulated_duration_str": "33.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV' limit 1", "type": "query", "params": [], "bindings": ["UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.6716912, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 8.845}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6859682, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.845, "width_percent": 4.031}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.6907198, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.876, "width_percent": 2.888}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.6944442, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.764, "width_percent": 4.543}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.697218, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.307, "width_percent": 1.655}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.717294, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.961, "width_percent": 10.951}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.725367, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.912, "width_percent": 3.189}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.731518, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.101, "width_percent": 6.408}, {"sql": "select * from `app_notifications` where `user_id` = 4 and `app_notifications`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 16, 25, 27, 29, 31, 35, 43, 135, 139, 143, 147, 209, 210) order by `app_notifications`.`id` asc", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 160}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Actions/Concerns/InteractsWithRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Concerns\\InteractsWithRecords.php", "line": 67}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 73}], "start": **********.851909, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:371", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=371", "ajax": false, "filename": "HasBulkActions.php", "line": "371"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.509, "width_percent": 6.558}, {"sql": "select * from `users` where `users`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 160}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Actions/Concerns/InteractsWithRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Concerns\\InteractsWithRecords.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 73}], "start": **********.856134, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:371", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=371", "ajax": false, "filename": "HasBulkActions.php", "line": "371"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.067, "width_percent": 2.798}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 160}, {"index": 27, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Actions/Concerns/InteractsWithRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Concerns\\InteractsWithRecords.php", "line": 67}, {"index": 29, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 73}], "start": **********.858241, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:371", "source": {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=371", "ajax": false, "filename": "HasBulkActions.php", "line": "371"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.865, "width_percent": 1.384}, {"sql": "select * from `notification_events` where `notification_events`.`id` in (7, 8, 11, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasBulkActions.php", "line": 160}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Actions/Concerns/InteractsWithRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\Concerns\\InteractsWithRecords.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 73}], "start": **********.85995, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "HasBulkActions.php:371", "source": {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasBulkActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasBulkActions.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=371", "ajax": false, "filename": "HasBulkActions.php", "line": "371"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.249, "width_percent": 7.912}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 1", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.865178, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.161, "width_percent": 12.425}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 2", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.8747861, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.586, "width_percent": 2.557}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 3", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.8768928, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.143, "width_percent": 1.264}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 4", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.8784559, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.407, "width_percent": 4.302}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 5", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.8811681, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.709, "width_percent": 1.354}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 8", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.882753, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.063, "width_percent": 1.083}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 35", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 35], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.884387, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.146, "width_percent": 0.903}, {"sql": "update `app_notifications` set `read_at` = null, `app_notifications`.`updated_at` = '2025-07-01 10:29:20' where `id` = 43", "type": "query", "params": [], "bindings": [null, "2025-07-01 10:29:20", 43], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/actions/src/MountableAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\MountableAction.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Actions/BulkAction.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Actions\\BulkAction.php", "line": 33}], "start": **********.8855839, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AppNotificationResource.php:199", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/AppNotificationResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\AppNotificationResource.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FAppNotificationResource.php&line=199", "ajax": false, "filename": "AppNotificationResource.php", "line": "199"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.048, "width_percent": 1.083}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.903676, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.131, "width_percent": 2.166}, {"sql": "select * from `app_notifications` where `user_id` = 4 order by `app_notifications`.`id` asc limit 25 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.9055288, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.297, "width_percent": 3.43}, {"sql": "select * from `users` where `users`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.908427, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.727, "width_percent": 1.745}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.910098, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.472, "width_percent": 1.384}, {"sql": "select * from `notification_events` where `notification_events`.`id` in (7, 8, 11, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.91159, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.856, "width_percent": 1.384}, {"sql": "select `notification_events`.`module`, `notification_events`.`id` from `notification_events` order by `notification_events`.`module` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.958629, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.239, "width_percent": 3.761}]}, "models": {"data": {"App\\Models\\AppNotification": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 64, "is_counter": true}, "livewire": {"data": {"app.filament.resources.app-notification-resource.pages.list-app-notifications #6CDc1cOZlbZ1sit9OfSa": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"read\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.app-notification-resource.pages.list-app-notifications\"\n  \"component\" => \"App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications\"\n  \"id\" => \"6CDc1cOZlbZ1sit9OfSa\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 256, "messages": [{"message": "[\n  ability => create_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2116428437 data-indent-pad=\"  \"><span class=sf-dump-note>create_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116428437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.736384, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\AppNotification,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-28291613 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28291613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.73689, "xdebug_link": null}, {"message": "[\n  ability => {{ Reorder }},\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2018978180 data-indent-pad=\"  \"><span class=sf-dump-note>{{ Reorder }} </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">{{ Reorder }}</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018978180\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.74567, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\AppNotification,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1722441110 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722441110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.745823, "xdebug_link": null}, {"message": "[\n  ability => delete_any_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1347488723 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">delete_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347488723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.770003, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\AppNotification,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1205696667 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205696667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.770208, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1738332805 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738332805\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.990672, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=1),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729542713 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729542713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.990963, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989019758 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989019758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.993907, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=1),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-468726529 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468726529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.9941, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1261859564 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261859564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.995476, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=1),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-457806990 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457806990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.995652, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-850030402 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850030402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.016244, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=1),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1320435235 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320435235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.016428, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-250648582 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250648582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.018942, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=1),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-533336074 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533336074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.019107, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-95986223 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95986223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.029279, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=2),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1890328201 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890328201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.029501, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281054600 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281054600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.031975, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=2),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1779654837 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779654837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.032217, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2006167346 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006167346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.0334, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=2),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1264394121 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264394121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.033645, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-461139242 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461139242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.05447, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=2),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2097021561 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097021561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.054589, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1762237498 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762237498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.05713, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=2),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2014218639 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014218639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.057548, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1587652542 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587652542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.065949, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=3),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-81318930 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81318930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.06613, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1935390335 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935390335\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.068281, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=3),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1425173962 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425173962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.068402, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-177021154 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177021154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.069277, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=3),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-443580502 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443580502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.069396, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1720158718 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720158718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.085069, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=3),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-237254127 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237254127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.08522, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-72548629 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72548629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.087043, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=3),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-503298822 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503298822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.087166, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2063671145 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063671145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.094326, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1170229505 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170229505\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.094462, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1139163957 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139163957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.096244, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2080362720 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080362720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.096363, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1543516789 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543516789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.097206, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-723990075 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723990075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.09732, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-260152351 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260152351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.115717, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1365996077 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365996077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.116003, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1429358803 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429358803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.118984, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-750576367 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750576367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.119241, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1921273628 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921273628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.128121, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=5),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-899877226 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899877226\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.128239, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478452864 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478452864\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.129863, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=5),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2044749724 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044749724\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.129978, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1424619681 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424619681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.130946, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=5),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1859274872 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859274872\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.131053, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1870196868 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870196868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.150348, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=5),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1585653359 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585653359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.150635, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1593533013 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593533013\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.152931, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=5),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008219142 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008219142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.153152, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1660183400 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660183400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.164041, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-238058011 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238058011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.164331, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-510371165 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510371165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.168276, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1933681208 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933681208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.168534, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1732055570 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732055570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.170533, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-11076523 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11076523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.170687, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-847184253 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847184253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.190741, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-435758314 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435758314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.19099, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-512759512 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512759512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.193786, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1404645455 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404645455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.193958, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-658133113 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658133113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.201906, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-391216772 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391216772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.20205, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1340084350 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340084350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.203937, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-775990618 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775990618\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.204052, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-718279208 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718279208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.205061, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-816298874 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816298874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.205173, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-832449370 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832449370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.221105, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-631790445 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631790445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.22125, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-757957981 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757957981\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.223949, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1794646213 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794646213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.224378, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1492321380 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492321380\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.230459, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1332577007 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332577007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.230579, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1137456733 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137456733\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.232525, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-228033657 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228033657\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.232753, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2107298882 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107298882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.233756, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176482443 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176482443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.233887, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-227450325 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227450325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.250549, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1454071678 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454071678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.250688, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1914689744 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914689744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.25255, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-515133799 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515133799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.252657, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-569306675 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569306675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.260207, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=9),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1650109201 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650109201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.260382, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-247982479 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247982479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.263755, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=9),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-941749621 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941749621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.264465, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492938791 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492938791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.266956, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=9),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-219003996 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219003996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.267145, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-685662046 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685662046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.289466, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=9),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1220590519 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220590519\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.291562, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-225541224 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225541224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.295582, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=9),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-596936504 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596936504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.295905, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-60663720 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60663720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.305568, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674070074 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674070074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.305754, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1801286752 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801286752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.30946, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-169406171 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169406171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.309634, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-687098598 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687098598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.311444, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2107576683 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107576683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.311708, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2118450461 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118450461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.334145, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426894196 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426894196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.334331, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-802951425 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802951425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.337107, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1647781755 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647781755\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.33729, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1621603675 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621603675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.346254, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=11),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-807948949 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807948949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.346426, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-542186624 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542186624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.348965, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=11),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1707424712 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707424712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.349129, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-135345832 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135345832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.350381, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=11),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-615420347 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615420347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.350526, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1695526225 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695526225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.372462, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=11),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1890460326 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890460326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.372633, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-431085443 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431085443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.376607, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=11),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-178035813 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=11)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=11)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178035813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.376862, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1799200137 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799200137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.385102, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1482917713 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482917713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.385281, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-532772474 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532772474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.38826, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1900176446 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900176446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.388481, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2128517625 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128517625\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.390674, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2132067145 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132067145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.391095, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179408006 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179408006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.41529, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-177392762 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177392762\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.41551, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-277786199 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277786199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.418555, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1237730817 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237730817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.418949, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-234060574 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234060574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.428517, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059973260 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059973260\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.428679, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-413142339 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413142339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.430682, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-194857683 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194857683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.430807, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-523139167 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523139167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.431776, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-513604521 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-513604521\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.431917, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-149295425 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149295425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.451351, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1694059850 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694059850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.451498, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-471090690 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471090690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.453293, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-476875113 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476875113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.453421, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1774832157 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774832157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.461044, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=25),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2001067932 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=25)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=25)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001067932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.461232, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-436873988 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436873988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.463871, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=25),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1922259779 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=25)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=25)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922259779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.464052, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1953534324 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953534324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.465391, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=25),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-700855099 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=25)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=25)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700855099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.465568, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1404986733 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404986733\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.484799, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=25),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1776206216 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=25)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=25)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776206216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.484982, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084884348 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084884348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.487544, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=25),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-149075662 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=25)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=25)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149075662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.487704, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1707259494 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707259494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.496552, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=27),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-53473177 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=27)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=27)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53473177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.49677, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1449361751 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449361751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.4995, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=27),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1080024338 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=27)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=27)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080024338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.499694, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-637463108 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637463108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.501156, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=27),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1351821642 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=27)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=27)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1351821642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.501324, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927199235 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927199235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.520294, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=27),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-27162214 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=27)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=27)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27162214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.520449, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1132905231 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132905231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.522353, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=27),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1692832925 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=27)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=27)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692832925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.522498, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-134239536 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134239536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.531338, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1318162322 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318162322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.531554, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1992958552 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992958552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.534254, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-790378967 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790378967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.534432, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-49185550 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49185550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.535777, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-373773195 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373773195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.535905, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508444318 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508444318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.555376, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-694217995 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694217995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.555591, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1480338288 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480338288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.559407, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-237535270 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237535270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.559601, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1512670462 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512670462\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.566724, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=31),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-538824997 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=31)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=31)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538824997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.566869, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-781951477 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781951477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.569436, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=31),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1503033416 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=31)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=31)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503033416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.569624, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1753006826 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753006826\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.571054, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=31),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1658988703 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=31)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=31)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658988703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.571237, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1148085322 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148085322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.600911, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=31),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177037863 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=31)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=31)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177037863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.601135, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1109667106 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109667106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.605542, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=31),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-535642163 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=31)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=31)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535642163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.606049, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1489103673 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489103673\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.618461, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=35),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-589372240 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=35)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=35)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589372240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.61865, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-771422643 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771422643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.622447, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=35),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1265604073 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=35)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=35)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265604073\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.622635, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-211584523 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211584523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.626491, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=35),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819587283 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=35)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=35)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819587283\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.626712, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-340237688 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340237688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.650388, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=35),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-508989006 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=35)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=35)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508989006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.650568, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1863017307 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863017307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.652928, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=35),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-205100891 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=35)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=35)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205100891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.653054, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-84112285 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84112285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.662244, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-132610311 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132610311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.66241, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-52022633 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52022633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.66536, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-914871699 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914871699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.665526, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-169268135 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169268135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.666791, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-985051595 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985051595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.666937, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081129092 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081129092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.688525, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1348349675 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348349675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.688713, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1666420988 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666420988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.69351, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2142124727 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142124727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.693812, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2088025799 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088025799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.703422, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=135),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1862937774 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=135)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=135)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862937774\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.703604, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1534429978 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534429978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.711562, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=135),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1700445144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=135)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=135)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700445144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.712117, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-703403839 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703403839\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.717421, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=135),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-324379962 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=135)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=135)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324379962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.717678, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492249967 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492249967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.759435, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=135),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-99750443 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=135)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=135)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99750443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.759646, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-847193588 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847193588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.763235, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=135),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1879088004 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=135)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=135)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879088004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.7635, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-488193317 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488193317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.773326, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=139),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1526080157 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=139)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=139)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526080157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.773669, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-166492431 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166492431\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.777649, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=139),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544921286 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=139)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=139)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544921286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.777882, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310577719 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310577719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.779502, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=139),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845177562 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=139)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=139)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845177562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.779704, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1958013541 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958013541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.801864, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=139),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-925698410 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=139)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=139)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925698410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.80205, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-161652655 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161652655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.805011, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=139),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-352165405 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=139)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=139)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352165405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.805224, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-705916552 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705916552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.815763, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=143),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-496387264 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=143)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=143)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496387264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.815996, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-375698675 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375698675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.818966, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=143),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-488172299 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=143)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=143)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488172299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.81916, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2068721883 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068721883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.820762, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=143),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1145069919 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=143)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=143)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145069919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.821, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-331998400 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331998400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.847337, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=143),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1657965694 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=143)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=143)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657965694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.847675, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1156085150 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156085150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.851659, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=143),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1291703643 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=143)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=143)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291703643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.851894, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-18980730 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18980730\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.863776, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=147),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-741420313 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=147)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=147)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741420313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.864009, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1227707571 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227707571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.867497, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=147),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1421713281 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=147)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=147)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421713281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.867691, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-962746523 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962746523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.869171, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=147),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1471321045 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=147)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=147)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471321045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.869333, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394922611 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394922611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.893143, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=147),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1633899281 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=147)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=147)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633899281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.893416, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1288198409 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288198409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.89645, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=147),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-250022394 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=147)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=147)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250022394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.896656, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1906548177 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906548177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.905783, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=209),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059865652 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=209)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=209)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059865652\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.905974, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-809444108 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809444108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.910041, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=209),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-34643180 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=209)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=209)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34643180\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.910225, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-532451229 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532451229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.911811, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=209),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-863864443 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=209)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=209)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863864443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.911976, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-119187815 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119187815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.936199, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=209),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615977556 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=209)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=209)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615977556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.936418, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2082106361 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082106361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.942275, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=209),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1684271402 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=209)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=209)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684271402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.942518, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1335890122 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335890122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.952573, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=210),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1557761234 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=210)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=210)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557761234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.952905, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-558913317 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558913317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.960325, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=210),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1956072143 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=210)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=210)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956072143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.960543, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1072507071 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072507071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.962713, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=210),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1448736366 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=210)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=210)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448736366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.962972, "xdebug_link": null}, {"message": "[\n  ability => update_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1202910663 data-indent-pad=\"  \"><span class=sf-dump-note>update_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">update_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202910663\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.993001, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=210),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1167896109 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=210)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=210)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167896109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.9932, "xdebug_link": null}, {"message": "[\n  ability => delete_app::notification,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-229760379 data-indent-pad=\"  \"><span class=sf-dump-note>delete_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229760379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.996113, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=210),\n  result => false,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1986766803 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=210)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">App\\Models\\AppNotification(id=210)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986766803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": 1751365761.996331, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications@mountTableBulkAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasBulkActions.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasBulkActions.php:137-188</a>", "middleware": "web", "duration": "2.39s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1707070191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1707070191\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2056009392 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1780 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;module&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;read&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:&quot;all&quot;,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6CDc1cOZlbZ1sit9OfSa&quot;,&quot;name&quot;:&quot;app.filament.resources.app-notification-resource.pages.list-app-notifications&quot;,&quot;path&quot;:&quot;admin\\/app-notifications&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f22419d6f4a0e3712e2730b1837df2dc53a4b28a856b60ada97a4acd05cb71cd&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:25</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>selectedTableRecords.0</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.1</span>\" => \"<span class=sf-dump-str>2</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.2</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.3</span>\" => \"<span class=sf-dump-str>4</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.4</span>\" => \"<span class=sf-dump-str>5</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.5</span>\" => \"<span class=sf-dump-str>6</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.6</span>\" => \"<span class=sf-dump-str>7</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.7</span>\" => \"<span class=sf-dump-str>8</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.8</span>\" => \"<span class=sf-dump-str>9</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.9</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.10</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.11</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.12</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.13</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.14</span>\" => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.15</span>\" => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.16</span>\" => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.17</span>\" => \"<span class=sf-dump-str title=\"2 characters\">35</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.18</span>\" => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.19</span>\" => \"<span class=sf-dump-str title=\"3 characters\">135</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.20</span>\" => \"<span class=sf-dump-str title=\"3 characters\">139</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.21</span>\" => \"<span class=sf-dump-str title=\"3 characters\">143</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.22</span>\" => \"<span class=sf-dump-str title=\"3 characters\">147</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.23</span>\" => \"<span class=sf-dump-str title=\"3 characters\">209</span>\"\n        \"<span class=sf-dump-key>selectedTableRecords.24</span>\" => \"<span class=sf-dump-str title=\"3 characters\">210</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">mountTableBulkAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">mark_unread_bulk</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056009392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-218459277 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2941</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/app-notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InRnSUFIeWtvempaTktBRjBFWE9McGc9PSIsInZhbHVlIjoiQ2tWYXhmRjdyL1ArclpXeXFqL2JOUmJXa0tNL0ZSOVVjNDBackZSOWMvTWZtdG9JRVBjQ3JIL1dqcmxCZXRGVGwvTzBkVHp6cnFHSVBlMVFIMVZNZVY3bWEzTWlqaHRsZkUxMisyaldVMVNyOGlXT0FHajZuL2k2cGJldlI3TkZ5dFA4LysrckdjdWlZNUM1ZEFjRXgzZUlyQWQzZGZqNSsxN0JiTHRHaEZCZTlDaEhsNVFSYmtXSHphbjZZQm9oZHAwekxJZGR1KzdlV2FIbEV3UlA1Qzd5ZlkzNWkzd01XUGl5SVd3MGREWT0iLCJtYWMiOiI2NGZlZTAzMWUxNGI0ZWNjNDQ4YWFkZTgwZmE1MGRjN2U5ZDY2ZGEyZmExNzNiM2M5NTZjMzkyOTc5ODczMzhkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFEemRyT3BlWXFxR1JNMGxldkc0WGc9PSIsInZhbHVlIjoia1hYL2JXZmpQc2VnRG5kMll1cngrZ1JMYjN3NzJtUEFKaWFJOG1HNyt2RDBxVElzMDBDSDBIVGxsL3EwYkM4MVNvRHFNanJ0Y0tQY1pNQ0JaUk9XL2laclJrZ0R6SmtzLy9mV00wTWFYVGZod3l5dkNidWZNNVpmWWxuaUtveU4iLCJtYWMiOiJlZjJhYjk5N2JkMWU1M2QzYzU1MjkwYjdjNDg0YzFhNjY3ZWM0YWFjZWFhM2I1N2IyOGZmMzk5ODA5MGVhNDQ3IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ilg5OE9nQTBVRWIzTVMxaE5QeS9sbkE9PSIsInZhbHVlIjoiUFcyOXU0eGNxMG0rRjZmbkh6cm1HdGdJQWt1OVBNWXBDWlhQVXBKYzM5L2ZRNTNNUWZGZ2k1WWlyWGJqSGhheHhwbzhFNmljRTNYcm5kTi91YVY4dTErLzZDdkVuZDkyNHRZZWNINmlOZmtMSVgySU5iMGpJYTIrY3U2K1hFMU8iLCJtYWMiOiI1ZjI5N2VlYzc2YjhiZDVmNTBhZDgzYTgxYjhiYjY5N2M1ODI5YzNmMTA2MDIzNjcyOTc0OWRmYWQ2NWExNDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218459277\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1411497160 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|N8eoFvYEnIkXq8v1Jp9cXichQYhN1SR8YPk8jKbVbaThkHwX5k1OMDiVGjY5|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411497160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-602146427 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 10:29:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602146427\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1279628626 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/app-notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279628626\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}