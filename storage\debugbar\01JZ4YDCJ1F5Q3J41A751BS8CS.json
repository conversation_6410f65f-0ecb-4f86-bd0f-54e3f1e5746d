{"__meta": {"id": "01JZ4YDCJ1F5Q3J41A751BS8CS", "datetime": "2025-07-02 06:50:28", "utime": **********.802518, "method": "GET", "uri": "/admin/app-notifications", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[06:50:27] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/app-notifications\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.415606, "xdebug_link": null, "collector": "log"}, {"message": "[06:50:28] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/app-notifications\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.797056, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751439026.576254, "end": **********.802538, "duration": 2.2262840270996094, "duration_str": "2.23s", "measures": [{"label": "Booting", "start": 1751439026.576254, "relative_start": 0, "end": **********.037934, "relative_end": **********.037934, "duration": 0.*****************, "duration_str": "462ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.037947, "relative_start": 0.*****************, "end": **********.802541, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.373627, "relative_start": 0.****************, "end": **********.375869, "relative_end": **********.375869, "duration": 0.0022420883178710938, "duration_str": "2.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.49773, "relative_start": 0.****************, "end": **********.49773, "relative_end": **********.49773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.506432, "relative_start": 0.***************, "end": **********.506432, "relative_end": **********.506432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.512127, "relative_start": 0.9358730316162109, "end": **********.512127, "relative_end": **********.512127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.516951, "relative_start": 0.940697193145752, "end": **********.516951, "relative_end": **********.516951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.534029, "relative_start": 0.9577751159667969, "end": **********.534029, "relative_end": **********.534029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.549897, "relative_start": 0.9736430644989014, "end": **********.549897, "relative_end": **********.549897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.558554, "relative_start": 0.9823000431060791, "end": **********.558554, "relative_end": **********.558554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.563505, "relative_start": 0.9872510433197021, "end": **********.563505, "relative_end": **********.563505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.608043, "relative_start": 1.0317890644073486, "end": **********.608043, "relative_end": **********.608043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.612497, "relative_start": 1.036243200302124, "end": **********.612497, "relative_end": **********.612497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.615678, "relative_start": 1.039424180984497, "end": **********.615678, "relative_end": **********.615678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.642272, "relative_start": 1.0660181045532227, "end": **********.642272, "relative_end": **********.642272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.646414, "relative_start": 1.070160150527954, "end": **********.646414, "relative_end": **********.646414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.649762, "relative_start": 1.0735080242156982, "end": **********.649762, "relative_end": **********.649762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.674087, "relative_start": 1.0978331565856934, "end": **********.674087, "relative_end": **********.674087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.681177, "relative_start": 1.1049230098724365, "end": **********.681177, "relative_end": **********.681177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.687114, "relative_start": 1.1108601093292236, "end": **********.687114, "relative_end": **********.687114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.715431, "relative_start": 1.1391770839691162, "end": **********.715431, "relative_end": **********.715431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.721001, "relative_start": 1.144747018814087, "end": **********.721001, "relative_end": **********.721001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.723727, "relative_start": 1.1474730968475342, "end": **********.723727, "relative_end": **********.723727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.750653, "relative_start": 1.1743991374969482, "end": **********.750653, "relative_end": **********.750653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.754667, "relative_start": 1.1784131526947021, "end": **********.754667, "relative_end": **********.754667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.757418, "relative_start": 1.181164026260376, "end": **********.757418, "relative_end": **********.757418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.790753, "relative_start": 1.2144989967346191, "end": **********.790753, "relative_end": **********.790753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.79474, "relative_start": 1.2184860706329346, "end": **********.79474, "relative_end": **********.79474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.797608, "relative_start": 1.2213540077209473, "end": **********.797608, "relative_end": **********.797608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.827204, "relative_start": 1.2509500980377197, "end": **********.827204, "relative_end": **********.827204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.831045, "relative_start": 1.254791021347046, "end": **********.831045, "relative_end": **********.831045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.834268, "relative_start": 1.25801420211792, "end": **********.834268, "relative_end": **********.834268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.859958, "relative_start": 1.2837040424346924, "end": **********.859958, "relative_end": **********.859958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.863923, "relative_start": 1.2876691818237305, "end": **********.863923, "relative_end": **********.863923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.869621, "relative_start": 1.2933671474456787, "end": **********.869621, "relative_end": **********.869621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.901277, "relative_start": 1.3250231742858887, "end": **********.901277, "relative_end": **********.901277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.905859, "relative_start": 1.3296051025390625, "end": **********.905859, "relative_end": **********.905859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.908438, "relative_start": 1.332184076309204, "end": **********.908438, "relative_end": **********.908438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.940626, "relative_start": 1.3643720149993896, "end": **********.940626, "relative_end": **********.940626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.94426, "relative_start": 1.3680059909820557, "end": **********.94426, "relative_end": **********.94426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.946984, "relative_start": 1.3707301616668701, "end": **********.946984, "relative_end": **********.946984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.97629, "relative_start": 1.400036096572876, "end": **********.97629, "relative_end": **********.97629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.981032, "relative_start": 1.404778003692627, "end": **********.981032, "relative_end": **********.981032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.985434, "relative_start": 1.4091801643371582, "end": **********.985434, "relative_end": **********.985434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.016002, "relative_start": 1.4397480487823486, "end": **********.016002, "relative_end": **********.016002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.020189, "relative_start": 1.4439351558685303, "end": **********.020189, "relative_end": **********.020189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.022948, "relative_start": 1.4466941356658936, "end": **********.022948, "relative_end": **********.022948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.05167, "relative_start": 1.4754161834716797, "end": **********.05167, "relative_end": **********.05167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.055003, "relative_start": 1.4787490367889404, "end": **********.055003, "relative_end": **********.055003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.057814, "relative_start": 1.4815599918365479, "end": **********.057814, "relative_end": **********.057814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.082307, "relative_start": 1.5060532093048096, "end": **********.082307, "relative_end": **********.082307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.085633, "relative_start": 1.5093791484832764, "end": **********.085633, "relative_end": **********.085633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.088431, "relative_start": 1.5121769905090332, "end": **********.088431, "relative_end": **********.088431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.120915, "relative_start": 1.544661045074463, "end": **********.120915, "relative_end": **********.120915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.12615, "relative_start": 1.549896001815796, "end": **********.12615, "relative_end": **********.12615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.130065, "relative_start": 1.5538110733032227, "end": **********.130065, "relative_end": **********.130065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.16423, "relative_start": 1.5879762172698975, "end": **********.16423, "relative_end": **********.16423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.168337, "relative_start": 1.592083215713501, "end": **********.168337, "relative_end": **********.168337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.171181, "relative_start": 1.5949270725250244, "end": **********.171181, "relative_end": **********.171181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.19733, "relative_start": 1.6210761070251465, "end": **********.19733, "relative_end": **********.19733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.203188, "relative_start": 1.6269340515136719, "end": **********.203188, "relative_end": **********.203188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.207188, "relative_start": 1.6309340000152588, "end": **********.207188, "relative_end": **********.207188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.24706, "relative_start": 1.6708061695098877, "end": **********.24706, "relative_end": **********.24706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.25231, "relative_start": 1.676056146621704, "end": **********.25231, "relative_end": **********.25231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.254857, "relative_start": 1.678603172302246, "end": **********.254857, "relative_end": **********.254857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.278978, "relative_start": 1.7027242183685303, "end": **********.278978, "relative_end": **********.278978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.282752, "relative_start": 1.706498146057129, "end": **********.282752, "relative_end": **********.282752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.289386, "relative_start": 1.7131321430206299, "end": **********.289386, "relative_end": **********.289386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.312725, "relative_start": 1.736471176147461, "end": **********.312725, "relative_end": **********.312725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.317198, "relative_start": 1.7409441471099854, "end": **********.317198, "relative_end": **********.317198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.320099, "relative_start": 1.7438452243804932, "end": **********.320099, "relative_end": **********.320099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.349629, "relative_start": 1.7733750343322754, "end": **********.349629, "relative_end": **********.349629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.353089, "relative_start": 1.7768352031707764, "end": **********.353089, "relative_end": **********.353089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.356005, "relative_start": 1.7797510623931885, "end": **********.356005, "relative_end": **********.356005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.387422, "relative_start": 1.8111681938171387, "end": **********.387422, "relative_end": **********.387422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.391052, "relative_start": 1.81479811668396, "end": **********.391052, "relative_end": **********.391052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.394042, "relative_start": 1.8177881240844727, "end": **********.394042, "relative_end": **********.394042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.419768, "relative_start": 1.8435142040252686, "end": **********.419768, "relative_end": **********.419768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.42339, "relative_start": 1.8471360206604004, "end": **********.42339, "relative_end": **********.42339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.427574, "relative_start": 1.8513200283050537, "end": **********.427574, "relative_end": **********.427574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.46231, "relative_start": 1.8860561847686768, "end": **********.46231, "relative_end": **********.46231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.466482, "relative_start": 1.890228033065796, "end": **********.466482, "relative_end": **********.466482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.469364, "relative_start": 1.8931100368499756, "end": **********.469364, "relative_end": **********.469364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.500459, "relative_start": 1.9242050647735596, "end": **********.500459, "relative_end": **********.500459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.505637, "relative_start": 1.9293830394744873, "end": **********.505637, "relative_end": **********.505637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.50947, "relative_start": 1.9332160949707031, "end": **********.50947, "relative_end": **********.50947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.545439, "relative_start": 1.9691851139068604, "end": **********.545439, "relative_end": **********.545439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.551159, "relative_start": 1.974905014038086, "end": **********.551159, "relative_end": **********.551159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.554577, "relative_start": 1.978323221206665, "end": **********.554577, "relative_end": **********.554577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.582529, "relative_start": 2.006275177001953, "end": **********.582529, "relative_end": **********.582529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.587742, "relative_start": 2.011488199234009, "end": **********.587742, "relative_end": **********.587742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.591362, "relative_start": 2.015108108520508, "end": **********.591362, "relative_end": **********.591362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.618584, "relative_start": 2.042330026626587, "end": **********.618584, "relative_end": **********.618584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.622337, "relative_start": 2.0460832118988037, "end": **********.622337, "relative_end": **********.622337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.625087, "relative_start": 2.048833131790161, "end": **********.625087, "relative_end": **********.625087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.645079, "relative_start": 2.0688250064849854, "end": **********.645079, "relative_end": **********.645079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.683467, "relative_start": 2.107213020324707, "end": **********.683467, "relative_end": **********.683467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.719336, "relative_start": 2.1430821418762207, "end": **********.719336, "relative_end": **********.719336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.723367, "relative_start": 2.147113084793091, "end": **********.723367, "relative_end": **********.723367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.724472, "relative_start": 2.1482181549072266, "end": **********.724472, "relative_end": **********.724472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.72754, "relative_start": 2.1512861251831055, "end": **********.72754, "relative_end": **********.72754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.728727, "relative_start": 2.152473211288452, "end": **********.728727, "relative_end": **********.728727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.731394, "relative_start": 2.1551401615142822, "end": **********.731394, "relative_end": **********.731394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.731661, "relative_start": 2.155407190322876, "end": **********.731661, "relative_end": **********.731661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.734419, "relative_start": 2.158165216445923, "end": **********.734419, "relative_end": **********.734419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.734704, "relative_start": 2.158450126647949, "end": **********.734704, "relative_end": **********.734704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.736653, "relative_start": 2.1603991985321045, "end": **********.736653, "relative_end": **********.736653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.736867, "relative_start": 2.1606130599975586, "end": **********.736867, "relative_end": **********.736867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.738302, "relative_start": 2.162048101425171, "end": **********.738302, "relative_end": **********.738302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.738535, "relative_start": 2.162281036376953, "end": **********.738535, "relative_end": **********.738535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.783718, "relative_start": 2.2074642181396484, "end": **********.783718, "relative_end": **********.783718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.784589, "relative_start": 2.2083351612091064, "end": **********.784589, "relative_end": **********.784589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.790259, "relative_start": 2.2140049934387207, "end": **********.790259, "relative_end": **********.790259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.791333, "relative_start": 2.2150790691375732, "end": **********.791333, "relative_end": **********.791333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.792217, "relative_start": 2.215963125228882, "end": **********.792217, "relative_end": **********.792217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.79646, "relative_start": 2.2202060222625732, "end": **********.796624, "relative_end": **********.796624, "duration": 0.000164031982421875, "duration_str": "164μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.800428, "relative_start": 2.2241740226745605, "end": **********.800479, "relative_end": **********.800479, "duration": 5.1021575927734375e-05, "duration_str": "51μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 58408136, "peak_usage_str": "56MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 112, "nb_templates": 112, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.497715, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "3x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.506412, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.534015, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "3x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.549885, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "84x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.608032, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 84, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.645064, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.683442, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.719318, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.723351, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.724458, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.727524, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.728712, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.783701, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.784574, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.790244, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.791318, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.792203, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01499, "accumulated_duration_str": "14.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.385678, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.736}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3932412, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.736, "width_percent": 4.336}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.396814, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.072, "width_percent": 3.402}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.40258, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.474, "width_percent": 3.602}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.40433, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.077, "width_percent": 2.335}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.408102, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.412, "width_percent": 3.002}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.4093041, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.414, "width_percent": 3.069}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.410346, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.482, "width_percent": 1.935}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.411274, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.417, "width_percent": 1.734}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.412197, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.151, "width_percent": 2.001}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.424442, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.153, "width_percent": 7.005}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.485503, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.157, "width_percent": 3.803}, {"sql": "select * from `app_notifications` where `user_id` = 1 order by `app_notifications`.`id` asc limit 28 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.487336, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.96, "width_percent": 4.803}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.4891338, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.763, "width_percent": 2.735}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.49017, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.498, "width_percent": 2.201}, {"sql": "select * from `notification_events` where `notification_events`.`id` in (1, 2, 5, 6, 11, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.491148, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.7, "width_percent": 3.536}, {"sql": "select `notification_events`.`module`, `notification_events`.`id` from `notification_events` order by `notification_events`.`module` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.545948, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.235, "width_percent": 5.937}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.7026532, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.173, "width_percent": 2.935}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.720578, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.108, "width_percent": 8.339}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.725178, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.447, "width_percent": 6.204}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.729876, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.651, "width_percent": 3.469}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.732522, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.12, "width_percent": 4.47}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.735249, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.59, "width_percent": 3.336}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.7372859, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.925, "width_percent": 2.602}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.7389822, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.527, "width_percent": 3.002}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.79811, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.529, "width_percent": 6.471}]}, "models": {"data": {"App\\Models\\AppNotification": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 48, "is_counter": true}, "livewire": {"data": {"app.filament.resources.app-notification-resource.pages.list-app-notifications #TfdJt4NZsMH8wZUqhfNg": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"read\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.app-notification-resource.pages.list-app-notifications\"\n  \"component\" => \"App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications\"\n  \"id\" => \"TfdJt4NZsMH8wZUqhfNg\"\n]", "filament.livewire.global-search #kl6Pi0LxcUbDI3ijDfVB": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"kl6Pi0LxcUbDI3ijDfVB\"\n]", "app.filament.widgets.notification-components.notification-bell #4bUKRcJjDK6ccWolfujK": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 28\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"4bUKRcJjDK6ccWolfujK\"\n]", "filament.livewire.notifications #agff03EIZpQO6raOCpNs": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2736\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"agff03EIZpQO6raOCpNs\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 199, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-525154494 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525154494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430105, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-125889476 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125889476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.431564, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-680527012 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680527012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.438492, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-516395519 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516395519\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.462395, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083979134 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083979134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.583302, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1778140425 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778140425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.584989, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-898855888 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898855888\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6039, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1501428881 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501428881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.604954, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1643655695 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643655695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606677, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-725658548 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725658548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.61228, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-121258973 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121258973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.619398, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-926110069 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926110069\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.62109, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-499411273 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499411273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.6381, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-764357227 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764357227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.639226, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1730214829 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730214829\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.640867, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1681437649 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681437649\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.646211, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-819729075 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819729075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.653481, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1206354357 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206354357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.655185, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-573757892 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573757892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669665, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-791443714 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791443714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670741, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-964183234 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964183234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.672337, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1570071811 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570071811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.68094, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-948215111 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948215111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.691858, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-760890960 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760890960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693831, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1994730432 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994730432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711001, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-283623582 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283623582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711929, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-681741711 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681741711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713759, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582597204 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582597204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72025, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-446339606 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446339606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72737, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-305385107 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305385107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729803, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-400583257 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400583257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.745968, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-391947447 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391947447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.747044, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-946378062 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946378062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748969, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-432856251 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432856251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754431, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1523040315 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523040315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761786, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-13146086 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13146086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.763368, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1064044547 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064044547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.786582, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2140959193 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140959193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787706, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-143760255 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143760255\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789485, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864032415 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864032415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794532, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1227099824 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227099824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.803353, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1569081571 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569081571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.805103, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1527013483 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527013483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.821438, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-345117098 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345117098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.822707, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2098982326 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098982326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.825089, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-338739741 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338739741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.830846, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-783687595 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783687595\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.837871, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1445341162 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445341162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.839534, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1182824972 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182824972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856041, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-428277133 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428277133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.857097, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1257385929 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257385929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858687, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1233760271 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233760271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863699, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1054120276 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054120276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873876, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2002767037 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002767037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87557, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-643500643 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643500643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.895113, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1183336395 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183336395\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.896511, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-955661975 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955661975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.8998, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1085556388 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085556388\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.905651, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1400260524 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400260524\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.912039, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-288078439 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288078439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.913772, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-383113393 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383113393\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.931423, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-498755150 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498755150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.933768, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1252986272 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252986272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.93935, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612688997 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612688997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944015, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1359753111 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359753111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953148, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-189779452 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189779452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.954901, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229736658 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229736658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971036, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2070921332 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070921332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972264, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1278933337 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278933337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.974448, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-871080160 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871080160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.980739, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1457973356 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457973356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.989376, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-15484773 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15484773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.990958, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1915704664 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915704664\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008536, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627942249 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627942249\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010574, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-661975753 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661975753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.013596, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-671414155 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671414155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.019962, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-175172007 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175172007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026759, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2067769909 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067769909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028457, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-29662853 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29662853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046008, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1171003967 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171003967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047479, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1634490262 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634490262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.050448, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1428577066 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1428577066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.054797, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-246117046 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246117046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.061545, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1807798115 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807798115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.063217, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1172143198 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172143198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077857, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-266904333 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266904333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078973, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-453655162 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453655162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080639, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1218184611 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218184611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.085416, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-294327349 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294327349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092185, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-78874114 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78874114\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093901, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857163649 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857163649\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.115834, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-31527854 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31527854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.117262, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-823471895 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823471895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.11904, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-413072257 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413072257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.125815, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2057197868 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057197868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135392, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-969419275 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969419275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137127, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1511060070 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511060070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15811, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-777614944 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777614944\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160109, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1269126025 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269126025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.162281, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-917834882 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917834882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168116, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1685966951 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685966951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174756, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1696927586 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696927586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.176176, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932148003 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932148003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190666, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952522141 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952522141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.192124, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-714064168 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714064168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.194616, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1849310494 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849310494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.202747, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-474643015 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474643015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21204, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-221564636 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221564636\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.214282, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-64168560 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64168560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.239557, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-782955075 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782955075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241818, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-535653525 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535653525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245017, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1843899279 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843899279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.252094, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-921343179 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921343179\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258421, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1390277209 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390277209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260079, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1996345853 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996345853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274913, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1701673361 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701673361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276034, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1001206660 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001206660\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277725, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-845469817 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845469817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282525, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085303347 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085303347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.293067, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-282538392 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282538392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.294704, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255149166 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255149166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308754, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-598811602 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598811602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309829, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-21230218 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21230218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311463, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989727869 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989727869\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316869, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2037657543 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037657543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323506, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-456907119 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456907119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.325192, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-866793681 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866793681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344143, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2056872093 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056872093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.346128, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-37696567 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37696567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.348013, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1536032419 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536032419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352852, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-993329999 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993329999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.359754, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-852749215 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852749215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361413, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1339708815 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339708815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.382871, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-498060643 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498060643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.384241, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-610993809 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610993809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.386039, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-454825316 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454825316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.390821, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1808125050 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808125050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.400352, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2120495763 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120495763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401903, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1479312544 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479312544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415759, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-995122659 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995122659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.416833, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1380235824 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380235824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418512, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1316361093 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316361093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42316, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-225302137 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225302137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.434216, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1621847747 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621847747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437687, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1228288146 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228288146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.457912, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2047234104 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047234104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.459268, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2016650108 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016650108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.460991, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1434238183 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434238183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.466031, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1181549197 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181549197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.473196, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-208093887 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208093887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.475146, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1377582768 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377582768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494864, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1333269628 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333269628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.496323, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555285929 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555285929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.49891, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059300143 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059300143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.505218, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-562995188 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562995188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.5146, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1523350548 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523350548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.517777, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1860214154 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860214154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538783, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1841964603 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841964603\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540518, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1587697742 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587697742\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543441, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2035293256 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035293256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550822, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1946036269 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946036269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558964, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-319887614 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319887614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.561067, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1173143689 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173143689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.577783, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1635247372 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635247372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.578843, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-862918929 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862918929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.58053, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-915727985 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915727985\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587403, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1868948119 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868948119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59605, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298541382 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298541382\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.598631, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2070975648 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070975648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614189, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1105050036 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105050036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.615546, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1663268722 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663268722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617311, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1039966727 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039966727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.622075, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-900655958 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900655958\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.687171, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-118867571 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118867571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.687453, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-451790503 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-451790503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.688301, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-587899781 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587899781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.688662, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532578384 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532578384\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.689462, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1626608643 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626608643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.689917, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-299044602 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299044602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.69064, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2062946919 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062946919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.691149, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-586295434 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586295434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.691498, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-656692188 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656692188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.691864, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1279283190 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279283190\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.692606, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1333032379 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333032379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693384, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1978292316 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978292316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693785, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1620704671 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620704671\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.694353, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-740188814 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740188814\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.694792, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1472654014 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472654014\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695048, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-4757242 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4757242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695739, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1618417844 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618417844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.696469, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-957800459 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957800459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.696948, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-868520692 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868520692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.697621, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-712912201 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712912201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.698963, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2036788286 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036788286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.699904, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-614350848 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614350848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.70071, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395977827 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395977827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.701511, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-218836829 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218836829\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.705226, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1661475108 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661475108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.706167, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-604177369 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604177369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712866, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications", "uri": "GET admin/app-notifications", "controller": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/app-notifications", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.23s", "peak_memory": "64MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNueU5kcTlTcnQvMU5YUHFOeFNSaHc9PSIsInZhbHVlIjoic1NmaDExdDdUeVF2NEFuMGVSbVZxK2NjcVNXaFZkTmFFMFdQcWpZa0wzdGEzSWk0VmVUUnJDWUd2cWRxbkZjR3ZRSlNnSktkS3F0c2FERUFPRExGZ2o3d282L0J1SzdvR0VTcm1yQnkrajZkQnRQWHY3OU9YczVYREsrcndBeDciLCJtYWMiOiJlMzU5Y2U5YzgyMWRiNDM1NGZjNmFiOGQ3N2JlMzYzZDIxNTRmZWYwNDFlMzQ1MGU2NzE1OTZjMDQzNzMxMTBlIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IndPdy9MbWUxelliN0RXV1FHK2M3a0E9PSIsInZhbHVlIjoiczVmWVhnSnpmbTlwOXNHK0xmbXR6d2c2aEsrM2F5SXlIQXlxaDhSM1BnUkt4QnMvbDhrSkVjZnlTYUhkOTZiKzhOM3lTSHJDZTdjakEwcHo5TTZPcjg5L1Bwdk43NjhXVWZEVnpDbXliMDh5aXRnTTdJcHlvblpzVDI0VzlNY0kiLCJtYWMiOiI0NjZjNDA1NzI3YjA3OTJkZjA4MzhiNTk5MzA2OGI5OWJhOGIwNmZjZjdjOWI0N2RiN2JhY2I1OTVjY2Q1YzgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-288797512 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288797512\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1387500283 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 06:50:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZ1WkdOZ1JGU3h6KzdNanQxajF2VlE9PSIsInZhbHVlIjoiY01hRmo5YzRwNEhYN05COGZzdjV3Z2JpN2FjZzZHUEZ5d2ZrVDRPVXg1WHJHelRKTVd2aUJaRGZEOHltVVJ3M0VMNEN4cnY3cWpSM0hTcHc3WW4rQXM5UFFSQTJJU0w5MlRXUi8zKzVUL0Fud3Z0VVh6RDNPRjVEM1M1UitualEiLCJtYWMiOiIxMzdlZGY3MDM4NTQ5ODU2NDQ0YWVmMjBlMTdkM2QwYjkzN2VkZjE5ZjNjOGNmOGE1MWVjZTI5OWM1ZGNlNTQ3IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 08:50:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IlNmT1p5VTVOeWkyR2JXRXFDRkVjdWc9PSIsInZhbHVlIjoiblRWWjJQR0wwWjFMRjdJMkh0VWpNUm1JcjNZUmJ2RU9yMnJRYVlQamdENGJlMGhiQXRvNEJteGRKcnY1eXNLRVFtanhaQ3NoMjc3ajFpNU1SMlJhaDRJQk5RTExaUnhzS0EyWndQWTlLTUozcmhUQ2N3Y2Y5QTR0VEliMnBqS0EiLCJtYWMiOiI0ODA0ZGU5OGM3Y2QwYmJlZjU2ZDM0NDZhNTI2MmVhMzliMGRjMTU0ZGQ4Y2Y2ZmFmOTdjZjQ1Mzc0NTVkNGUzIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 08:50:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZ1WkdOZ1JGU3h6KzdNanQxajF2VlE9PSIsInZhbHVlIjoiY01hRmo5YzRwNEhYN05COGZzdjV3Z2JpN2FjZzZHUEZ5d2ZrVDRPVXg1WHJHelRKTVd2aUJaRGZEOHltVVJ3M0VMNEN4cnY3cWpSM0hTcHc3WW4rQXM5UFFSQTJJU0w5MlRXUi8zKzVUL0Fud3Z0VVh6RDNPRjVEM1M1UitualEiLCJtYWMiOiIxMzdlZGY3MDM4NTQ5ODU2NDQ0YWVmMjBlMTdkM2QwYjkzN2VkZjE5ZjNjOGNmOGE1MWVjZTI5OWM1ZGNlNTQ3IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 08:50:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IlNmT1p5VTVOeWkyR2JXRXFDRkVjdWc9PSIsInZhbHVlIjoiblRWWjJQR0wwWjFMRjdJMkh0VWpNUm1JcjNZUmJ2RU9yMnJRYVlQamdENGJlMGhiQXRvNEJteGRKcnY1eXNLRVFtanhaQ3NoMjc3ajFpNU1SMlJhaDRJQk5RTExaUnhzS0EyWndQWTlLTUozcmhUQ2N3Y2Y5QTR0VEliMnBqS0EiLCJtYWMiOiI0ODA0ZGU5OGM3Y2QwYmJlZjU2ZDM0NDZhNTI2MmVhMzliMGRjMTU0ZGQ4Y2Y2ZmFmOTdjZjQ1Mzc0NTVkNGUzIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 08:50:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387500283\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2119239222 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/app-notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119239222\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications"}, "badge": null}}