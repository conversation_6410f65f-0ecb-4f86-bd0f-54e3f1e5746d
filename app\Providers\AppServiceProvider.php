<?php
namespace App\Providers;

use App\Models\User;
use App\Providers\LivewireServiceProvider;
use App\Providers\NotificationServiceProvider;
use Filament\Support\Facades\FilamentView;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use App\Observers\IncentiveObserver;
use App\Models\Incentive;
use App\Services\NotificationService;
use App\Observers\PaymentObserver;
use App\Observers\MilestoneObserver;
use App\Models\Payment;
use App\Models\Milestone;
use App\Services\IncentiveService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        parent::register();
        FilamentView::registerRenderHook('panels::body.end', fn(): string => Blade::render("@vite('resources/js/app.js')"));

        // Register service providers
        $this->app->register(NotificationServiceProvider::class);
        $this->app->register(LivewireServiceProvider::class);

        // Register IncentiveService as a singleton
        $this->app->singleton(IncentiveService::class, function ($app) {
            return new IncentiveService();
        });

        // Register custom LoginResponse
        $this->app->bind(
            \Filament\Http\Responses\Auth\Contracts\LoginResponse::class,
            \App\Http\Responses\LoginResponse::class
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        Gate::define('viewApiDocs', function (User $user) {
            return true;
        });
        // Gate::policy()
        Event::listen(function (\SocialiteProviders\Manager\SocialiteWasCalled $event) {
            $event->extendSocialite('discord', \SocialiteProviders\Google\Provider::class);
        });
        Schema::defaultStringLength(191);

        Incentive::observe(
            new IncentiveObserver(app(NotificationService::class))
        );

        // Register Payment observer for auto incentive calculation with proper dependency injection
        Payment::observe(new PaymentObserver(app(IncentiveService::class)));

        // Register Milestone observer for auto payment entry
        Milestone::observe(MilestoneObserver::class);
    }
}
