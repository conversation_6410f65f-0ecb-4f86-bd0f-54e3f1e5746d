{"__meta": {"id": "01JZ2P46ZXPF7KHWY53N0DFDS4", "datetime": "2025-07-01 09:47:10", "utime": **********.718133, "method": "GET", "uri": "/admin/shield/roles/15/edit", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[09:47:07] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/shield\\/roles\\/15\\/edit\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.928984, "xdebug_link": null, "collector": "log"}, {"message": "[09:47:10] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/shield\\/roles\\/15\\/edit\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.711318, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751363226.310033, "end": **********.718162, "duration": 4.408128976821899, "duration_str": "4.41s", "measures": [{"label": "Booting", "start": 1751363226.310033, "relative_start": 0, "end": **********.251509, "relative_end": **********.251509, "duration": 0.****************, "duration_str": "941ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.251528, "relative_start": 0.****************, "end": **********.718166, "relative_end": 4.0531158447265625e-06, "duration": 3.****************, "duration_str": "3.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.880458, "relative_start": 1.***************, "end": **********.886526, "relative_end": **********.886526, "duration": 0.006067991256713867, "duration_str": "6.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.564967, "relative_start": 2.****************, "end": **********.564967, "relative_end": **********.564967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.57154, "relative_start": 2.***************, "end": **********.57154, "relative_end": **********.57154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.573929, "relative_start": 2.2638959884643555, "end": **********.573929, "relative_end": **********.573929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": 1751363229.045491, "relative_start": 2.7354578971862793, "end": 1751363229.045491, "relative_end": 1751363229.045491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.208655, "relative_start": 3.8986220359802246, "end": **********.208655, "relative_end": **********.208655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.212787, "relative_start": 3.9027538299560547, "end": **********.212787, "relative_end": **********.212787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.218891, "relative_start": 3.908857822418213, "end": **********.218891, "relative_end": **********.218891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.26427, "relative_start": 3.9542369842529297, "end": **********.26427, "relative_end": **********.26427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.266129, "relative_start": 3.9560959339141846, "end": **********.266129, "relative_end": **********.266129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.270853, "relative_start": 3.960819959640503, "end": **********.270853, "relative_end": **********.270853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.281571, "relative_start": 3.9715378284454346, "end": **********.281571, "relative_end": **********.281571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.284013, "relative_start": 3.973979949951172, "end": **********.284013, "relative_end": **********.284013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.288912, "relative_start": 3.978878974914551, "end": **********.288912, "relative_end": **********.288912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.297526, "relative_start": 3.987492799758911, "end": **********.297526, "relative_end": **********.297526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.300116, "relative_start": 3.9900829792022705, "end": **********.300116, "relative_end": **********.300116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.30493, "relative_start": 3.99489688873291, "end": **********.30493, "relative_end": **********.30493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.314511, "relative_start": 4.0044779777526855, "end": **********.314511, "relative_end": **********.314511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.317573, "relative_start": 4.007539987564087, "end": **********.317573, "relative_end": **********.317573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.323505, "relative_start": 4.013471841812134, "end": **********.323505, "relative_end": **********.323505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.330746, "relative_start": 4.020712852478027, "end": **********.330746, "relative_end": **********.330746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.333332, "relative_start": 4.023298978805542, "end": **********.333332, "relative_end": **********.333332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.338445, "relative_start": 4.028411865234375, "end": **********.338445, "relative_end": **********.338445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.347923, "relative_start": 4.0378899574279785, "end": **********.347923, "relative_end": **********.347923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.350042, "relative_start": 4.040009021759033, "end": **********.350042, "relative_end": **********.350042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.356265, "relative_start": 4.046231985092163, "end": **********.356265, "relative_end": **********.356265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.36623, "relative_start": 4.056196928024292, "end": **********.36623, "relative_end": **********.36623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.369747, "relative_start": 4.059713840484619, "end": **********.369747, "relative_end": **********.369747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.374723, "relative_start": 4.064689874649048, "end": **********.374723, "relative_end": **********.374723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.383936, "relative_start": 4.07390284538269, "end": **********.383936, "relative_end": **********.383936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.38615, "relative_start": 4.0761168003082275, "end": **********.38615, "relative_end": **********.38615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.390061, "relative_start": 4.08002781867981, "end": **********.390061, "relative_end": **********.390061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.397814, "relative_start": 4.087780952453613, "end": **********.397814, "relative_end": **********.397814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.399585, "relative_start": 4.08955192565918, "end": **********.399585, "relative_end": **********.399585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.405583, "relative_start": 4.095549821853638, "end": **********.405583, "relative_end": **********.405583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.414266, "relative_start": 4.104233026504517, "end": **********.414266, "relative_end": **********.414266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.416805, "relative_start": 4.106771945953369, "end": **********.416805, "relative_end": **********.416805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.421697, "relative_start": 4.111663818359375, "end": **********.421697, "relative_end": **********.421697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.431061, "relative_start": 4.121027946472168, "end": **********.431061, "relative_end": **********.431061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.433706, "relative_start": 4.123672962188721, "end": **********.433706, "relative_end": **********.433706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.439444, "relative_start": 4.129410982131958, "end": **********.439444, "relative_end": **********.439444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.451904, "relative_start": 4.141870975494385, "end": **********.451904, "relative_end": **********.451904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.455361, "relative_start": 4.145327806472778, "end": **********.455361, "relative_end": **********.455361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.462109, "relative_start": 4.152076005935669, "end": **********.462109, "relative_end": **********.462109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.470637, "relative_start": 4.160604000091553, "end": **********.470637, "relative_end": **********.470637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.472999, "relative_start": 4.162966012954712, "end": **********.472999, "relative_end": **********.472999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.477319, "relative_start": 4.167285919189453, "end": **********.477319, "relative_end": **********.477319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.486701, "relative_start": 4.176667928695679, "end": **********.486701, "relative_end": **********.486701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.488593, "relative_start": 4.178560018539429, "end": **********.488593, "relative_end": **********.488593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.492056, "relative_start": 4.1820228099823, "end": **********.492056, "relative_end": **********.492056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.501001, "relative_start": 4.190967798233032, "end": **********.501001, "relative_end": **********.501001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.503012, "relative_start": 4.192978858947754, "end": **********.503012, "relative_end": **********.503012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.50629, "relative_start": 4.196256875991821, "end": **********.50629, "relative_end": **********.50629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.519181, "relative_start": 4.209147930145264, "end": **********.519181, "relative_end": **********.519181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.521859, "relative_start": 4.211825847625732, "end": **********.521859, "relative_end": **********.521859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.526055, "relative_start": 4.21602201461792, "end": **********.526055, "relative_end": **********.526055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.531354, "relative_start": 4.221320867538452, "end": **********.531354, "relative_end": **********.531354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.536282, "relative_start": 4.2262489795684814, "end": **********.536282, "relative_end": **********.536282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.549536, "relative_start": 4.239502906799316, "end": **********.549536, "relative_end": **********.549536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.584519, "relative_start": 4.27448582649231, "end": **********.584519, "relative_end": **********.584519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": **********.614607, "relative_start": 4.304574012756348, "end": **********.614607, "relative_end": **********.614607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": **********.619883, "relative_start": 4.309849977493286, "end": **********.619883, "relative_end": **********.619883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": **********.620821, "relative_start": 4.310787916183472, "end": **********.620821, "relative_end": **********.620821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.624183, "relative_start": 4.314149856567383, "end": **********.624183, "relative_end": **********.624183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.625267, "relative_start": 4.315233945846558, "end": **********.625267, "relative_end": **********.625267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.628588, "relative_start": 4.318554878234863, "end": **********.628588, "relative_end": **********.628588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.628854, "relative_start": 4.318820953369141, "end": **********.628854, "relative_end": **********.628854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.630313, "relative_start": 4.320279836654663, "end": **********.630313, "relative_end": **********.630313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.630549, "relative_start": 4.320515871047974, "end": **********.630549, "relative_end": **********.630549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.631832, "relative_start": 4.321798801422119, "end": **********.631832, "relative_end": **********.631832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.632074, "relative_start": 4.322041034698486, "end": **********.632074, "relative_end": **********.632074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": **********.634256, "relative_start": 4.324222803115845, "end": **********.634256, "relative_end": **********.634256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": **********.63461, "relative_start": 4.3245768547058105, "end": **********.63461, "relative_end": **********.63461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": **********.698203, "relative_start": 4.388170003890991, "end": **********.698203, "relative_end": **********.698203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": **********.699105, "relative_start": 4.389071941375732, "end": **********.699105, "relative_end": **********.699105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": **********.705271, "relative_start": 4.395237922668457, "end": **********.705271, "relative_end": **********.705271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": **********.706249, "relative_start": 4.396215915679932, "end": **********.706249, "relative_end": **********.706249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": **********.707124, "relative_start": 4.397090911865234, "end": **********.707124, "relative_end": **********.707124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.710789, "relative_start": 4.400755882263184, "end": **********.710926, "relative_end": **********.710926, "duration": 0.00013709068298339844, "duration_str": "137μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.71521, "relative_start": 4.405176877975464, "end": **********.715288, "relative_end": **********.715288, "duration": 7.796287536621094e-05, "duration_str": "78μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56546784, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 77, "nb_templates": 77, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.564947, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.573916, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1751363229.045465, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.208637, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.218878, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.53134, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.5845, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": **********.614591, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": **********.619871, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": **********.620811, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": **********.624165, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": **********.625245, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": **********.698186, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": **********.699087, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": **********.705254, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": **********.706234, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": **********.70711, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 24, "nb_statements": 24, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025510000000000005, "accumulated_duration_str": "25.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.897554, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.666}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.9047499, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.666, "width_percent": 2.783}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.9086008, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.449, "width_percent": 2.352}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.914379, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.801, "width_percent": 2.156}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9161012, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.957, "width_percent": 2.43}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.92034, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.387, "width_percent": 2.47}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.921882, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.857, "width_percent": 1.372}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.923095, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.229, "width_percent": 1.294}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.9242501, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.523, "width_percent": 1.215}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.9253662, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.738, "width_percent": 1.215}, {"sql": "select * from `roles` where `id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.9357822, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.953, "width_percent": 2.47}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.941241, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.423, "width_percent": 3.489}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.25786, "duration": 0.00816, "duration_str": "8.16ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.911, "width_percent": 31.987}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.272336, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.899, "width_percent": 2.47}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 24, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 28, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 87}, {"index": 29, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 229}], "start": **********.276194, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "Role.php:191", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=191", "ajax": false, "filename": "Role.php", "line": "191"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.368, "width_percent": 9.486}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": **********.598151, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.855, "width_percent": 1.686}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.615761, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.541, "width_percent": 12.779}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.621399, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.32, "width_percent": 4.939}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.627517, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.259, "width_percent": 1.568}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.629335, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.827, "width_percent": 1.568}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.6309571, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.395, "width_percent": 1.254}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.632517, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.65, "width_percent": 1.411}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.635222, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.061, "width_percent": 2.234}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTUvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSUtWVTRHSDFNNWJCUzQ1UEE1eUJQZ1lIZ09KOWp4bmxrNzhmTThFSyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9zaGllbGQvcm9sZXMvMTUvZWRpdCI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRuZlhrY0VKNVdGUXRJMjVpYzBqVUxlNnNVdDNmQU1USFJ5dWp4eEJSaC5HN2RnTEVJanFsVyI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.713296, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.295, "width_percent": 2.705}]}, "models": {"data": {"App\\Models\\Permission": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 52, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #gcNWqKGmv3egTTB2d8H6": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => null\n    \"data\" => array:26 [\n      \"id\" => 15\n      \"name\" => \"bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:2 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n      ]\n      \"client\" => array:5 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n      ]\n      \"incentive\" => array:2 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n      ]\n      \"incentive::rule\" => array:2 [\n        0 => \"view_incentive::rule\"\n        1 => \"view_any_incentive::rule\"\n      ]\n      \"milestone\" => array:5 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n      ]\n      \"notification::event\" => []\n      \"notification::role::preference\" => []\n      \"payment\" => array:5 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n      ]\n      \"pricing::model\" => array:2 [\n        0 => \"view_pricing::model\"\n        1 => \"view_any_pricing::model\"\n      ]\n      \"product\" => array:2 [\n        0 => \"view_product\"\n        1 => \"view_any_product\"\n      ]\n      \"project\" => array:5 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n      ]\n      \"project::status::log\" => array:2 [\n        0 => \"view_project::status::log\"\n        1 => \"view_any_project::status::log\"\n      ]\n      \"project::type\" => array:2 [\n        0 => \"view_project::type\"\n        1 => \"view_any_project::type\"\n      ]\n      \"role\" => []\n      \"role::notification::settings\" => []\n      \"user\" => []\n      \"pages_tab\" => array:5 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_DashboardSettings\"\n        2 => \"page_ManageSetting\"\n        3 => \"page_Themes\"\n        4 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => []\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#2347\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #original: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#2871\n          #items: array:39 [\n            0 => App\\Models\\Permission {#2913\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 167\n                \"name\" => \"page_MyProfilePage\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 167\n                \"name\" => \"page_MyProfilePage\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 167\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2873\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Role {#2863 …36}\n                  +pivotRelated: App\\Models\\Permission {#2864 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => App\\Models\\Permission {#2938\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 166\n                \"name\" => \"page_Themes\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 166\n                \"name\" => \"page_Themes\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 166\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2910\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Role {#2863 …36}\n                  +pivotRelated: App\\Models\\Permission {#2864 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => App\\Models\\Permission {#2937\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 165\n                \"name\" => \"page_ManageSetting\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 165\n                \"name\" => \"page_ManageSetting\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 165\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2909\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Role {#2863 …36}\n                  +pivotRelated: App\\Models\\Permission {#2864 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => App\\Models\\Permission {#2936\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 164\n                \"name\" => \"page_DashboardSettings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 164\n                \"name\" => \"page_DashboardSettings\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 164\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2908\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Role {#2863 …36}\n                  +pivotRelated: App\\Models\\Permission {#2864 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => App\\Models\\Permission {#2935\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 163\n                \"name\" => \"page_BdeDashboard\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 163\n                \"name\" => \"page_BdeDashboard\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 163\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2907\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  +pivotParent: App\\Models\\Role {#2863 …36}\n                  +pivotRelated: App\\Models\\Permission {#2864 …37}\n                  #foreignKey: \"role_id\"\n                  #relatedKey: \"permission_id\"\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => App\\Models\\Permission {#2934\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 110\n                \"name\" => \"view_any_project::type\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 110\n                \"name\" => \"view_any_project::type\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 110\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2906\n                  #connection: \"mysql\"\n                  #table: \"role_has_permissions\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: false\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:2 [ …2]\n                  #original: array:2 [ …2]\n                  #changes: []\n                  #previous: []\n                  #casts: []\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: []\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  #relationAutoloadCallback: null\n                  #relationAutoloadContext: null\n                  +timestamps: false\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                   …5\n                }\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => App\\Models\\Permission {#2933\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 109\n                \"name\" => \"view_project::type\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 109\n                \"name\" => \"view_project::type\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 109\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2905 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => App\\Models\\Permission {#2932\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 232\n                \"name\" => \"view_any_project::status::log\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n              ]\n              #original: array:7 [\n                \"id\" => 232\n                \"name\" => \"view_any_project::status::log\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 232\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2904 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => App\\Models\\Permission {#2931\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 222\n                \"name\" => \"view_project::status::log\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-18 15:11:04\"\n                \"updated_at\" => \"2025-06-18 15:11:04\"\n              ]\n              #original: array:7 [\n                \"id\" => 222\n                \"name\" => \"view_project::status::log\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-18 15:11:04\"\n                \"updated_at\" => \"2025-06-18 15:11:04\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 222\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2903 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => App\\Models\\Permission {#2930\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 98\n                \"name\" => \"view_any_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 98\n                \"name\" => \"view_any_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 98\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2902 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => App\\Models\\Permission {#2929\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 105\n                \"name\" => \"delete_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 105\n                \"name\" => \"delete_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 105\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2901 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => App\\Models\\Permission {#2928\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 100\n                \"name\" => \"update_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 100\n                \"name\" => \"update_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 100\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2900 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => App\\Models\\Permission {#2927\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 99\n                \"name\" => \"create_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 99\n                \"name\" => \"create_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 99\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2899 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            13 => App\\Models\\Permission {#2926\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 97\n                \"name\" => \"view_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 97\n                \"name\" => \"view_project\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 97\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2898 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            14 => App\\Models\\Permission {#2925\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 231\n                \"name\" => \"view_any_product\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n              ]\n              #original: array:7 [\n                \"id\" => 231\n                \"name\" => \"view_any_product\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 231\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2897 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            15 => App\\Models\\Permission {#2924\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 226\n                \"name\" => \"view_product\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-26 06:15:45\"\n                \"updated_at\" => \"2025-06-26 06:15:45\"\n              ]\n              #original: array:7 [\n                \"id\" => 226\n                \"name\" => \"view_product\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-26 06:15:45\"\n                \"updated_at\" => \"2025-06-26 06:15:45\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 226\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2896 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            16 => App\\Models\\Permission {#2923\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 230\n                \"name\" => \"view_any_pricing::model\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n              ]\n              #original: array:7 [\n                \"id\" => 230\n                \"name\" => \"view_any_pricing::model\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-30 08:11:10\"\n                \"updated_at\" => \"2025-06-30 09:38:41\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 230\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2895 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            17 => App\\Models\\Permission {#2922\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 218\n                \"name\" => \"view_pricing::model\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-18 15:11:04\"\n                \"updated_at\" => \"2025-06-18 15:11:04\"\n              ]\n              #original: array:7 [\n                \"id\" => 218\n                \"name\" => \"view_pricing::model\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-06-18 15:11:04\"\n                \"updated_at\" => \"2025-06-18 15:11:04\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 218\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2894 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            18 => App\\Models\\Permission {#2921\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 87\n                \"name\" => \"create_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 87\n                \"name\" => \"create_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 87\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2893 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            19 => App\\Models\\Permission {#2920\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 93\n                \"name\" => \"delete_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 93\n                \"name\" => \"delete_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 93\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2892 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            20 => App\\Models\\Permission {#2919\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 88\n                \"name\" => \"update_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 88\n                \"name\" => \"update_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 88\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2891 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            21 => App\\Models\\Permission {#2939\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 86\n                \"name\" => \"view_any_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 86\n                \"name\" => \"view_any_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 86\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2890 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            22 => App\\Models\\Permission {#2940\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 85\n                \"name\" => \"view_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 85\n                \"name\" => \"view_payment\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 85\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2889 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            23 => App\\Models\\Permission {#2941\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 57\n                \"name\" => \"delete_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 57\n                \"name\" => \"delete_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 57\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2888 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            24 => App\\Models\\Permission {#2942\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 52\n                \"name\" => \"update_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 52\n                \"name\" => \"update_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 52\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2887 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            25 => App\\Models\\Permission {#2943\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 51\n                \"name\" => \"create_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 51\n                \"name\" => \"create_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 51\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2886 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            26 => App\\Models\\Permission {#2944\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 50\n                \"name\" => \"view_any_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 50\n                \"name\" => \"view_any_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 50\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2885 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            27 => App\\Models\\Permission {#2945\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 49\n                \"name\" => \"view_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 49\n                \"name\" => \"view_milestone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 49\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2884 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            28 => App\\Models\\Permission {#2946\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 38\n                \"name\" => \"view_any_incentive::rule\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 38\n                \"name\" => \"view_any_incentive::rule\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 38\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2883 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            29 => App\\Models\\Permission {#2947\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 37\n                \"name\" => \"view_incentive::rule\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 37\n                \"name\" => \"view_incentive::rule\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 37\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2882 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            30 => App\\Models\\Permission {#2948\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 26\n                \"name\" => \"view_any_incentive\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 26\n                \"name\" => \"view_any_incentive\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 26\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2881 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            31 => App\\Models\\Permission {#2949\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 25\n                \"name\" => \"view_incentive\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 25\n                \"name\" => \"view_incentive\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 25\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2880 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            32 => App\\Models\\Permission {#2950\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 21\n                \"name\" => \"delete_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 21\n                \"name\" => \"delete_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 21\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2877 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            33 => App\\Models\\Permission {#2951\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 16\n                \"name\" => \"update_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 16\n                \"name\" => \"update_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 16\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2879 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            34 => App\\Models\\Permission {#2952\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 15\n                \"name\" => \"create_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 15\n                \"name\" => \"create_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 15\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2878 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            35 => App\\Models\\Permission {#2953\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 14\n                \"name\" => \"view_any_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 14\n                \"name\" => \"view_any_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 14\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2874 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            36 => App\\Models\\Permission {#2954\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"view_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"view_client\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2876 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            37 => App\\Models\\Permission {#2955\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 2\n                \"name\" => \"view_any_app::notification\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 2\n                \"name\" => \"view_any_app::notification\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 2\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2875 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            38 => App\\Models\\Permission {#2956\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"view_app::notification\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"view_app::notification\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => \"2025-05-28 05:14:40\"\n                \"updated_at\" => \"2025-05-28 05:14:40\"\n                \"pivot_role_id\" => 15\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #previous: []\n              #casts: array:1 [\n                \"id\" => \"integer\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#2870 …37}\n              ]\n              #touches: []\n              #relationAutoloadCallback: null\n              #relationAutoloadContext: null\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: \"App\\Models\\Permission\"\n      -wildcardClass: \"\"\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"gcNWqKGmv3egTTB2d8H6\"\n]", "filament.livewire.global-search #eYlmchWdRV7kLIGtVKhH": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"eYlmchWdRV7kLIGtVKhH\"\n]", "app.filament.widgets.notification-components.notification-bell #wgtm83FS31mAw1pD20gU": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 177\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"wgtm83FS31mAw1pD20gU\"\n]", "filament.livewire.notifications #OHRn3uBypgCsWHVJO80u": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3370\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"OHRn3uBypgCsWHVJO80u\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 30, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1463251383 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463251383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945895, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-700834546 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700834546\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529034, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083805761 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083805761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530759, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-168203579 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168203579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540852, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-209286979 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209286979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541544, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-594321837 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594321837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.587866, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-582787473 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582787473\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.588092, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-793315388 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793315388\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589118, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2080941845 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080941845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.589553, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-873127110 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873127110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590081, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1712188600 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712188600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590481, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-484538248 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484538248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.590939, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-354819215 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354819215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.591531, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-403011435 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403011435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.591907, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-756750122 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756750122\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592356, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-257675366 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257675366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592928, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-667591734 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667591734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593504, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1541176099 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541176099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.593851, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1553108448 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553108448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594287, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-8906397 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8906397\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.594826, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-617718281 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617718281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595258, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1000715370 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000715370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595503, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1300610932 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300610932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595891, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1044820830 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044820830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596413, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-643966073 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643966073\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.596913, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1484403715 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484403715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597478, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1790449005 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790449005\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59787, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1295315998 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295315998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600081, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1136210394 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136210394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.600925, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-156847951 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156847951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.608964, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/15/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole", "uri": "GET admin/shield/roles/{record}/edit", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/shield/roles", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "4.41s", "peak_memory": "62MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/admin/shield/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVSSkp5RHYrT0dOOUMyYVNWdEhwT2c9PSIsInZhbHVlIjoiZ09TRTVGVi9MNm1vU2t0NVdaczJXQkw5SEdaRFExRFp4ZFgwd05QbnhvRWZDbkpGQ0tKOXRxOHczZGhEVjVJbmxIb1lBaVdHQnNHWHU0R0JXYnRENW56QXpsSWlJbE16bmUyVDE1QmhjV3pzdTh6REhPelhsVkNuRlAvZTFJMXkiLCJtYWMiOiI0OGI1MmUxYmI2YWU3YzM1MjgwZmZmNGUzYmRhNGE4ZmM5ZGM0NjFiNWNmYWRhNDMyNWIxMmMwOGNiNTNlOTUyIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6ImdxQjZyUTB0azFUU2NSYVczUFpPK0E9PSIsInZhbHVlIjoiMXlpUFEvbTlabmRZQmZQUVVoV0V3WkUzeHAvQnFjYy9ZbUVwRW84bnBOaWRtclZVRlAwbDNJYW5jaWhhbklCR0NCMUlUOTdTMnhINGxFWVJkYml0WnZYSk9vL1NDUmc0WGEwOGRVM0x4a0twRVk0RlVvb3hIWXNmeFczc00rbjAiLCJtYWMiOiIxOWYyNDE2OGYyZTUyOGU4MzljZWY5YjZhMjc1MDZjZjQ1ZTIzN2I1YTdkZGM4MGRmNjgzOGJkMzViYTNkODJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1931494403 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931494403\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1152132755 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:47:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklKNDk5dWhQa09FRjlUT3plYlhxOWc9PSIsInZhbHVlIjoid3gvQk1yVTdHM0JnRUI1b2JzMmVybGRZN0tTdVFTdXR4V3FwaUNJcjhyMVpiTldPY2szVVgzSDJ3T1c2SnJkVDNPSFVHV0JGNkZuTittYlZIeUl1eGdyZkVUKzExOVBTQlRCeWdjTndKRW5FejZuSHJ2eFRhZDZ6TnN2TUErZ0EiLCJtYWMiOiIyMmNkZTRhNDIzMDgxMmZhN2RmMDY0NjhjYWQxNTM1NmYwMjUzYjhjZWY5OTlmMTY3YmViODdhYzFlMjZjN2U2IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:47:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IjRYeWZJQjFQTHlOQVZYMndTNjZLVHc9PSIsInZhbHVlIjoiYmw1SlFlaVhBOVI0UHFuZWVoWERMNkpveTkzQi9EcTZtQ3hscUtKdjgzRm82bXRXNndWRTBnZFM2bjJGcWpCd3hyTW1lOCtGQlRreEVhYnBmTXRvYXVKL29SbmdnU2RqN01OVXN1Zmx4T1BXeEp4ZksvcHlkRWptcmszam5GMysiLCJtYWMiOiIxYTliNTFiMjBmMjFiMzE0NzMyODhiYWYwMzQ3OTBjMzczOGNjOTAxNTE3OWU0OGNmZTE3Mjk2OGU2ZjdkOTRlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:47:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklKNDk5dWhQa09FRjlUT3plYlhxOWc9PSIsInZhbHVlIjoid3gvQk1yVTdHM0JnRUI1b2JzMmVybGRZN0tTdVFTdXR4V3FwaUNJcjhyMVpiTldPY2szVVgzSDJ3T1c2SnJkVDNPSFVHV0JGNkZuTittYlZIeUl1eGdyZkVUKzExOVBTQlRCeWdjTndKRW5FejZuSHJ2eFRhZDZ6TnN2TUErZ0EiLCJtYWMiOiIyMmNkZTRhNDIzMDgxMmZhN2RmMDY0NjhjYWQxNTM1NmYwMjUzYjhjZWY5OTlmMTY3YmViODdhYzFlMjZjN2U2IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:47:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IjRYeWZJQjFQTHlOQVZYMndTNjZLVHc9PSIsInZhbHVlIjoiYmw1SlFlaVhBOVI0UHFuZWVoWERMNkpveTkzQi9EcTZtQ3hscUtKdjgzRm82bXRXNndWRTBnZFM2bjJGcWpCd3hyTW1lOCtGQlRreEVhYnBmTXRvYXVKL29SbmdnU2RqN01OVXN1Zmx4T1BXeEp4ZksvcHlkRWptcmszam5GMysiLCJtYWMiOiIxYTliNTFiMjBmMjFiMzE0NzMyODhiYWYwMzQ3OTBjMzczOGNjOTAxNTE3OWU0OGNmZTE3Mjk2OGU2ZjdkOTRlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:47:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152132755\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/shield/roles/15/edit", "action_name": "filament.admin.resources.shield.roles.edit", "controller_action": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole"}, "badge": null}}