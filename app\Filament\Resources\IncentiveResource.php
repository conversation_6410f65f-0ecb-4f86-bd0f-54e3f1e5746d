<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IncentiveResource\Pages;
use App\Filament\Resources\IncentiveResource\RelationManagers;
use App\Models\Incentive;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Exports\IncentivesExport;
use App\Filament\Exports\IncentivesExporter;
use Filament\Actions\Exports\Enums\ExportFormat;
use App\Models\User;

class IncentiveResource extends Resource
{
    protected static ?string $model = Incentive::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?int $navigationSort = 4;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->can('view_any_incentive');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Eager load relationships to prevent N+1 queries
        $query->with([
            'user',
            'project.projectType',
            'project.pricingModel',
            'milestone',
            'approver'
        ]);



        // Apply role-based filtering
        // Admin users can see all incentives
        // BDE-type users can only see their own incentives
        if (Auth::check() && Auth::user() instanceof User) {
            $user = Auth::user();

            if ($user->isAdminUser()) {
                // Admin users see all incentives - no filtering needed
            } elseif ($user->shouldSeeOwnDataOnly()) {
                // BDE-type users only see their own incentives
                $query->where('user_id', Auth::id());
            }
            // Default: Other users see all incentives
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Incentive Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->label('BDE')
                            ->default(fn() => Auth::id())
                            ->disabled(fn() => Auth::user()->shouldSeeOwnDataOnly())
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                // Recalculate amount when user changes
                                $projectId = $get('project_id');
                                $milestoneId = $get('milestone_id');
                                $userId = $state;
                                if (!$projectId || !$milestoneId || !$userId) {
                                    $set('amount', '');
                                    return;
                                }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    $set('amount', '');
                                    return;
                                }
                                // Existing calculation logic
                                $roleId = isset($user->role_id) ? $user->role_id : \DB::table('model_has_roles')
                                    ->where('model_type', 'App\\Models\\User')
                                    ->where('model_id', $user->id)
                                    ->value('role_id');
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    $set('amount', 0);
                                    return;
                                }
                                $amount = 0;
                                $projectCurrency = $project->currency;
                                $ruleCurrency = $rule->currency;
                                $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);

                                switch ($rule->calculation_type) {
                                    case 'percentage':
                                    case 'Percentage of Contract Value':
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                        break;
                                    case 'fixed':
                                    case 'Fixed Amount':
                                        $amount = $rule->fixed_amount;
                                        break;
                                    case 'hybrid':
                                    case 'Hybrid (Percentage + Fixed)':
                                        $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                        break;
                                    case 'time_based':
                                    case 'Time-based (Monthly)':
                                        $months = max(1, $project->duration ?? 1);
                                        $amount = $rule->fixed_amount * $months;
                                        break;
                                    default:
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                }
                                $set('amount', round($amount, 2));
                            }),

                        // Project, Milestone, and Amount in a single row
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('project_id')
                                    ->relationship('project', 'title', function (Builder $query) {
                                        // If user should see only their own data, filter to their projects
                                        if (Auth::check() && Auth::user() instanceof User && Auth::user()->shouldSeeOwnDataOnly()) {
                                            $query->where('user_id', Auth::id());
                                        }
                                        return $query;
                                    })
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $set('milestone_id', null);

                                        // Set currency from selected project
                                        if ($state) {
                                            $project = \App\Models\Project::find($state);
                                            if ($project && $project->currency) {
                                                $set('currency', $project->currency);
                                            }
                                        }

                                        // Recalculate amount when project changes
                                        $userId = $get('user_id');
                                        $milestoneId = $get('milestone_id');
                                        $projectId = $state;
                                        if (!$projectId || !$milestoneId || !$userId) {
                                            $set('amount', '');
                                            return;
                                        }
                                        $project = \App\Models\Project::find($projectId);
                                        $milestone = \App\Models\Milestone::find($milestoneId);
                                        $user = \App\Models\User::find($userId);
                                        if (!$project || !$milestone || !$user) {
                                            $set('amount', '');
                                            return;
                                        }
                                        // Get role_id from Spatie model_has_roles table if not present on user
                                        $roleId = null;
                                        if (isset($user->role_id)) {
                                            $roleId = $user->role_id;
                                        } else {
                                            $roleId = DB::table('model_has_roles')
                                                ->where('model_type', 'App\\Models\\User')
                                                ->where('model_id', $user->id)
                                                ->value('role_id');
                                        }
                                        // DEBUG: Check values used for rule lookup
                                        // \Log::info('INCENTIVE DEBUG', ['project_type_id' => $project->project_type_id, 'role_id' => $roleId, 'milestone_percent' => $milestone->percentage]);
                                        $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                            ->where('role_id', $roleId)
                                            ->where('duration_percentage', '<=', $milestone->percentage)
                                            ->orderByDesc('duration_percentage')
                                            ->first();
                                        if (!$rule) {
                                            $set('amount', 0);
                                            return;
                                        }
                                        $amount = 0;
                                        $projectCurrency = $project->currency;
                                        $ruleCurrency = $rule->currency;
                                        $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);

                                        switch ($rule->calculation_type) {
                                            case 'percentage':
                                            case 'Percentage of Contract Value':
                                                $amount = $convertedAmount * ($rule->percentage / 100);
                                                break;
                                            case 'fixed':
                                            case 'Fixed Amount':
                                                $amount = $rule->fixed_amount;
                                                break;
                                            case 'hybrid':
                                            case 'Hybrid (Percentage + Fixed)':
                                                $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                                break;
                                            case 'time_based':
                                            case 'Time-based (Monthly)':
                                                $months = max(1, $project->duration ?? 1);
                                                $amount = $rule->fixed_amount * $months;
                                                break;
                                            default:
                                                $amount = $convertedAmount * ($rule->percentage / 100);
                                        }
                                        $set('amount', round($amount, 2));
                                    }),
                                Forms\Components\Select::make('milestone_id')
                                    ->label('Milestone')
                                    ->options(function (callable $get) {
                                        $projectId = $get('project_id');
                                        if (!$projectId) {
                                            return [];
                                        }
                                        // Only show milestones with a paid payment
                                        $paidMilestoneIds = \App\Models\Payment::where('project_id', $projectId)
                                                            ->where('status', 'paid')
                                                            ->pluck('milestone_id')
                                                            ->filter(fn($id) => !is_null($id) && $id !== '')
                                                            ->unique()
                                                            ->values()
                                                            ->toArray();
                                        if (empty($paidMilestoneIds)) {
                                            return [];
                                        }
                                        return \App\Models\Milestone::where('project_id', $projectId)
                                            ->whereIn('id', $paidMilestoneIds)
                                            ->pluck('title', 'id')
                                            ->toArray();
                                    })
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        // Recalculate amount when milestone changes
                                        $projectId = $get('project_id');
                                        $milestoneId = $state;
                                        $userId = $get('user_id');
                                        if (!$projectId || !$milestoneId || !$userId) {
                                            $set('amount', '');
                                            return;
                                        }
                                $project = \App\Models\Project::find($projectId);
                                $milestone = \App\Models\Milestone::find($milestoneId);
                                $user = \App\Models\User::find($userId);
                                if (!$project || !$milestone || !$user) {
                                    $set('amount', '');
                                    return;
                                }
                                // Get role_id from Spatie model_has_roles table if not present on user
                                $roleId = null;
                                if (isset($user->role_id)) {
                                    $roleId = $user->role_id;
                                } else {
                                    $roleId = DB::table('model_has_roles')
                                        ->where('model_type', 'App\\Models\\User')
                                        ->where('model_id', $user->id)
                                        ->value('role_id');
                                }
                                // DEBUG: Check values used for rule lookup
                                // \Log::info('INCENTIVE DEBUG', ['project_type_id' => $project->project_type_id, 'role_id' => $roleId, 'milestone_percent' => $milestone->percentage]);
                                $rule = \App\Models\IncentiveRule::where('project_type_id', $project->project_type_id)
                                    ->where('role_id', $roleId)
                                    ->where('duration_percentage', '<=', $milestone->percentage)
                                    ->orderByDesc('duration_percentage')
                                    ->first();
                                if (!$rule) {
                                    $set('amount', 0);
                                    return;
                                }
                                $amount = 0;
                                $projectCurrency = $project->currency;
                                $ruleCurrency = $rule->currency;
                                $convertedAmount = \App\Services\CurrencyConverter::convert($projectCurrency, $ruleCurrency, $project->total_payment ?? 0);

                                switch ($rule->calculation_type) {
                                    case 'percentage':
                                    case 'Percentage of Contract Value':
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                        break;
                                    case 'fixed':
                                    case 'Fixed Amount':
                                        $amount = $rule->fixed_amount;
                                        break;
                                    case 'hybrid':
                                    case 'Hybrid (Percentage + Fixed)':
                                        $amount = ($convertedAmount * ($rule->percentage / 100)) + $rule->fixed_amount;
                                        break;
                                    case 'time_based':
                                    case 'Time-based (Monthly)':
                                        $months = max(1, $project->duration ?? 1);
                                        $amount = $rule->fixed_amount * $months;
                                        break;
                                    default:
                                        $amount = $convertedAmount * ($rule->percentage / 100);
                                }
                                $set('amount', round($amount, 2));
                            }),
                                Forms\Components\TextInput::make('amount')
                                    ->required()
                                    ->numeric()
                                    ->prefix(function (callable $get) {
                                        $projectId = $get('project_id');
                                        if ($projectId) {
                                            $project = \App\Models\Project::find($projectId);
                                            if ($project && $project->currency) {
                                                return match($project->currency) {
                                                    'USD' => '$',
                                                    'EUR' => '€',
                                                    'GBP' => '£',
                                                    'INR' => '₹',
                                                    default => $project->currency . ' '
                                                };
                                            }
                                        }
                                        return '₹'; // Default fallback
                                    })
                                    ->reactive()
                                    ->disabled(),
                            ]),
                        Forms\Components\Hidden::make('currency')
                            ->default(function (callable $get) {
                                $projectId = $get('project_id');
                                if ($projectId) {
                                    $project = \App\Models\Project::find($projectId);
                                    return $project->currency ?? 'INR';
                                }
                                return 'INR';
                            }),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\DatePicker::make('calculation_date')
                                    ->required()
                                    ->default(now())
                                    ->label('Calculation Date'),
                                Forms\Components\DatePicker::make('payment_date')
                                    ->label('Payment Date'),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        'pending' => 'Pending',
                                        'approved' => 'Approved',
                                        'paid' => 'Paid',
                                        'rejected' => 'Rejected',
                                    ])
                                    ->default('pending')
                                    ->required()
                                    ->label('Status'),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\Select::make('approved_by')
                            ->relationship('approver', 'name')
                            ->searchable()
                            ->preload()
                            ->visible(fn() => !Auth::user()->shouldSeeOwnDataOnly()),
                    ])
                    ->columns(2),
            ]);
    }

   // public static function table(Table $table): Table
    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('BDE')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('project.title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('project.pricingModel.name')
                    ->label('Pricing Model')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money(fn ($record) => $record->currency)
                    ->sortable(),
                Tables\Columns\TextColumn::make('calculation_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'approved' => 'success',
                        'paid' => 'info',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('approver.name')
                    ->label('Approved By')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('payment_date')
                    ->form([
                        Forms\Components\DatePicker::make('payment_date')
                            ->label('Payment Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payment_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('payment_date', $date),
                            );
                    }),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'paid' => 'Paid',
                        'rejected' => 'Rejected',
                    ]),

                Tables\Filters\SelectFilter::make('approved_by')
                    ->label('Approved By')
                    ->relationship('approver', 'name')
                    ->searchable(),

                Tables\Filters\SelectFilter::make('project_type')
                    ->label('Project Type')
                    ->relationship('project.projectType', 'name')
                    ->searchable(),

                Tables\Filters\SelectFilter::make('pricing_model')
                    ->label('Pricing Model')
                    ->relationship('project.pricingModel', 'name')
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                ExportAction::make()
                    ->label('Export Incentives')
                    ->exporter(IncentivesExporter::class)
                    ->formats([
                        ExportFormat::Xlsx,
                    ])
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIncentives::route('/'),
            'create' => Pages\CreateIncentive::route('/create'),
            'edit' => Pages\EditIncentive::route('/{record}/edit'),
        ];
    }
}
