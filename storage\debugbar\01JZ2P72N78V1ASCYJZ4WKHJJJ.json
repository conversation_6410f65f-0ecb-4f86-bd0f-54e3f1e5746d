{"__meta": {"id": "01JZ2P72N78V1ASCYJZ4WKHJJJ", "datetime": "2025-07-01 09:48:44", "utime": **********.584519, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.391033, "end": **********.584542, "duration": 1.1935091018676758, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": **********.391033, "relative_start": 0, "end": **********.738107, "relative_end": **********.738107, "duration": 0.***************, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.738116, "relative_start": 0.*****************, "end": **********.584545, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "846ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.959188, "relative_start": 0.**************, "end": **********.961504, "relative_end": **********.961504, "duration": 0.002315998077392578, "duration_str": "2.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.210981, "relative_start": 0.****************, "end": **********.210981, "relative_end": **********.210981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.219491, "relative_start": 0.****************, "end": **********.219491, "relative_end": **********.219491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.221455, "relative_start": 0.8304221630096436, "end": **********.221455, "relative_end": **********.221455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.225136, "relative_start": 0.8341031074523926, "end": **********.225136, "relative_end": **********.225136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.245498, "relative_start": 0.8544650077819824, "end": **********.245498, "relative_end": **********.245498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.249965, "relative_start": 0.8589320182800293, "end": **********.249965, "relative_end": **********.249965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.254395, "relative_start": 0.8633620738983154, "end": **********.254395, "relative_end": **********.254395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.263937, "relative_start": 0.8729040622711182, "end": **********.263937, "relative_end": **********.263937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.266506, "relative_start": 0.8754730224609375, "end": **********.266506, "relative_end": **********.266506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.270234, "relative_start": 0.8792011737823486, "end": **********.270234, "relative_end": **********.270234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.277679, "relative_start": 0.886646032333374, "end": **********.277679, "relative_end": **********.277679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.279892, "relative_start": 0.8888590335845947, "end": **********.279892, "relative_end": **********.279892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.283706, "relative_start": 0.8926730155944824, "end": **********.283706, "relative_end": **********.283706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.291611, "relative_start": 0.9005780220031738, "end": **********.291611, "relative_end": **********.291611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.293704, "relative_start": 0.9026710987091064, "end": **********.293704, "relative_end": **********.293704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.297536, "relative_start": 0.9065029621124268, "end": **********.297536, "relative_end": **********.297536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.30558, "relative_start": 0.9145469665527344, "end": **********.30558, "relative_end": **********.30558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.307678, "relative_start": 0.9166450500488281, "end": **********.307678, "relative_end": **********.307678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.312062, "relative_start": 0.9210290908813477, "end": **********.312062, "relative_end": **********.312062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.32114, "relative_start": 0.9301071166992188, "end": **********.32114, "relative_end": **********.32114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.323399, "relative_start": 0.932366132736206, "end": **********.323399, "relative_end": **********.323399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.327221, "relative_start": 0.9361879825592041, "end": **********.327221, "relative_end": **********.327221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.33557, "relative_start": 0.9445371627807617, "end": **********.33557, "relative_end": **********.33557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.337351, "relative_start": 0.9463181495666504, "end": **********.337351, "relative_end": **********.337351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.341465, "relative_start": 0.9504320621490479, "end": **********.341465, "relative_end": **********.341465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.349869, "relative_start": 0.9588360786437988, "end": **********.349869, "relative_end": **********.349869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.352118, "relative_start": 0.9610850811004639, "end": **********.352118, "relative_end": **********.352118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.355673, "relative_start": 0.9646401405334473, "end": **********.355673, "relative_end": **********.355673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.363874, "relative_start": 0.9728410243988037, "end": **********.363874, "relative_end": **********.363874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.366705, "relative_start": 0.9756720066070557, "end": **********.366705, "relative_end": **********.366705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.370918, "relative_start": 0.9798851013183594, "end": **********.370918, "relative_end": **********.370918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.378969, "relative_start": 0.9879360198974609, "end": **********.378969, "relative_end": **********.378969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.381034, "relative_start": 0.9900009632110596, "end": **********.381034, "relative_end": **********.381034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.385608, "relative_start": 0.994575023651123, "end": **********.385608, "relative_end": **********.385608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.39356, "relative_start": 1.0025269985198975, "end": **********.39356, "relative_end": **********.39356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.395804, "relative_start": 1.0047709941864014, "end": **********.395804, "relative_end": **********.395804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.400189, "relative_start": 1.0091559886932373, "end": **********.400189, "relative_end": **********.400189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.410209, "relative_start": 1.0191760063171387, "end": **********.410209, "relative_end": **********.410209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.412432, "relative_start": 1.0213990211486816, "end": **********.412432, "relative_end": **********.412432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.418197, "relative_start": 1.0271639823913574, "end": **********.418197, "relative_end": **********.418197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.42655, "relative_start": 1.0355169773101807, "end": **********.42655, "relative_end": **********.42655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.42865, "relative_start": 1.0376169681549072, "end": **********.42865, "relative_end": **********.42865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.433494, "relative_start": 1.0424611568450928, "end": **********.433494, "relative_end": **********.433494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.442884, "relative_start": 1.0518510341644287, "end": **********.442884, "relative_end": **********.442884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.445371, "relative_start": 1.054337978363037, "end": **********.445371, "relative_end": **********.445371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.452736, "relative_start": 1.0617029666900635, "end": **********.452736, "relative_end": **********.452736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.461283, "relative_start": 1.0702500343322754, "end": **********.461283, "relative_end": **********.461283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.463403, "relative_start": 1.0723700523376465, "end": **********.463403, "relative_end": **********.463403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.470654, "relative_start": 1.0796210765838623, "end": **********.470654, "relative_end": **********.470654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.480618, "relative_start": 1.0895850658416748, "end": **********.480618, "relative_end": **********.480618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.48546, "relative_start": 1.0944271087646484, "end": **********.48546, "relative_end": **********.48546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.491243, "relative_start": 1.1002099514007568, "end": **********.491243, "relative_end": **********.491243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.509196, "relative_start": 1.1181631088256836, "end": **********.509196, "relative_end": **********.509196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.512515, "relative_start": 1.1214821338653564, "end": **********.512515, "relative_end": **********.512515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.520407, "relative_start": 1.1293740272521973, "end": **********.520407, "relative_end": **********.520407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.527875, "relative_start": 1.1368420124053955, "end": **********.527875, "relative_end": **********.527875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.538144, "relative_start": 1.147111177444458, "end": **********.538144, "relative_end": **********.538144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.554669, "relative_start": 1.1636359691619873, "end": **********.554669, "relative_end": **********.554669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.579501, "relative_start": 1.1884679794311523, "end": **********.581362, "relative_end": **********.581362, "duration": 0.0018610954284667969, "duration_str": "1.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56620424, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 58, "nb_templates": 58, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.210948, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.221442, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.225125, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.245486, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.254374, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.527857, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 42, "nb_statements": 42, "nb_visible_statements": 42, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020090000000000007, "accumulated_duration_str": "20.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.9678109, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 2.937}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.980089, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.937, "width_percent": 3.584}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.984937, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.521, "width_percent": 3.036}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.988133, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.557, "width_percent": 2.539}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.9897149, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.096, "width_percent": 1.244}, {"sql": "select * from `roles` where `roles`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.003933, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.34, "width_percent": 4.281}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.015566, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.621, "width_percent": 5.774}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.0894208, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.395, "width_percent": 6.471}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'bde_team' and `roles`.`id` <> '15'", "type": "query", "params": [], "bindings": ["bde_team", "15"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.123509, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.866, "width_percent": 2.887}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/RefreshesPermissionCache.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\RefreshesPermissionCache.php", "line": 12}], "start": **********.135419, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.753, "width_percent": 1.991}, {"sql": "select * from `permissions` where (`name` = 'view_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1364892, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.744, "width_percent": 2.19}, {"sql": "select * from `permissions` where (`name` = 'view_any_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.137482, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.934, "width_percent": 2.489}, {"sql": "select * from `permissions` where (`name` = 'view_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.138494, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.423, "width_percent": 1.941}, {"sql": "select * from `permissions` where (`name` = 'view_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.139317, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.364, "width_percent": 1.344}, {"sql": "select * from `permissions` where (`name` = 'create_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.140018, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.708, "width_percent": 1.593}, {"sql": "select * from `permissions` where (`name` = 'update_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1407971, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.301, "width_percent": 1.543}, {"sql": "select * from `permissions` where (`name` = 'delete_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.141589, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.844, "width_percent": 1.643}, {"sql": "select * from `permissions` where (`name` = 'delete_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.142428, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.486, "width_percent": 1.294}, {"sql": "select * from `permissions` where (`name` = 'view_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.143181, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.78, "width_percent": 1.493}, {"sql": "select * from `permissions` where (`name` = 'view_any_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.143971, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.274, "width_percent": 1.543}, {"sql": "select * from `permissions` where (`name` = 'view_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.144772, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.817, "width_percent": 1.593}, {"sql": "select * from `permissions` where (`name` = 'view_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.145577, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.41, "width_percent": 1.593}, {"sql": "select * from `permissions` where (`name` = 'create_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.146386, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.002, "width_percent": 1.294}, {"sql": "select * from `permissions` where (`name` = 'update_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.147133, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.297, "width_percent": 1.593}, {"sql": "select * from `permissions` where (`name` = 'delete_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.147975, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.889, "width_percent": 3.634}, {"sql": "select * from `permissions` where (`name` = 'delete_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1495519, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.523, "width_percent": 2.837}, {"sql": "select * from `permissions` where (`name` = 'view_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.150817, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.36, "width_percent": 2.688}, {"sql": "select * from `permissions` where (`name` = 'view_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1520162, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.048, "width_percent": 2.29}, {"sql": "select * from `permissions` where (`name` = 'create_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1530762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.338, "width_percent": 1.394}, {"sql": "select * from `permissions` where (`name` = 'update_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.153873, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.732, "width_percent": 1.543}, {"sql": "select * from `permissions` where (`name` = 'delete_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.154723, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.275, "width_percent": 2.091}, {"sql": "select * from `permissions` where (`name` = 'delete_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.155622, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.365, "width_percent": 1.543}, {"sql": "select * from `permissions` where (`name` = 'view_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.156407, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.908, "width_percent": 1.593}, {"sql": "select * from `permissions` where (`name` = 'view_any_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.157196, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.501, "width_percent": 1.493}, {"sql": "select * from `permissions` where (`name` = 'create_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.157973, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.995, "width_percent": 1.543}, {"sql": "select * from `permissions` where (`name` = 'update_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.158761, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.538, "width_percent": 1.891}, {"sql": "select * from `permissions` where (`name` = 'delete_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.159631, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.429, "width_percent": 2.339}, {"sql": "select * from `permissions` where (`name` = 'page_BdeDashboard' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_BdeDashboard", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.160619, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.769, "width_percent": 1.941}, {"sql": "select * from `permissions` where (`name` = 'page_MyProfilePage' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_MyProfilePage", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.161495, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.71, "width_percent": 1.593}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 15", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1623988, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:453", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=453", "ajax": false, "filename": "HasPermissions.php", "line": "453"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.303, "width_percent": 6.521}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 15), (2, 15), (13, 15), (14, 15), (15, 15), (16, 15), (21, 15), (22, 15), (25, 15), (26, 15), (49, 15), (50, 15), (51, 15), (52, 15), (57, 15), (58, 15), (85, 15), (86, 15), (87, 15), (88, 15), (93, 15), (94, 15), (97, 15), (98, 15), (99, 15), (100, 15), (105, 15), (163, 15), (167, 15)", "type": "query", "params": [], "bindings": [1, 15, 2, 15, 13, 15, 14, 15, 15, 15, 16, 15, 21, 15, 22, 15, 25, 15, 26, 15, 49, 15, 50, 15, 51, 15, 52, 15, 57, 15, 58, 15, 85, 15, 86, 15, 87, 15, 88, 15, 93, 15, 94, 15, 97, 15, 98, 15, 99, 15, 100, 15, 105, 15, 163, 15, 167, 15], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}], "start": **********.166301, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:406", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=406", "ajax": false, "filename": "HasPermissions.php", "line": "406"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.823, "width_percent": 3.235}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 554}], "start": **********.167804, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.059, "width_percent": 1.941}]}, "models": {"data": {"App\\Models\\Permission": {"value": 29, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 32, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #gcNWqKGmv3egTTB2d8H6": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => Illuminate\\Support\\Collection {#2593\n      #items: array:29 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"view_client\"\n        3 => \"view_any_client\"\n        4 => \"create_client\"\n        5 => \"update_client\"\n        6 => \"delete_client\"\n        7 => \"delete_any_client\"\n        8 => \"view_incentive\"\n        9 => \"view_any_incentive\"\n        10 => \"view_milestone\"\n        11 => \"view_any_milestone\"\n        12 => \"create_milestone\"\n        13 => \"update_milestone\"\n        14 => \"delete_milestone\"\n        15 => \"delete_any_milestone\"\n        16 => \"view_payment\"\n        17 => \"view_any_payment\"\n        18 => \"create_payment\"\n        19 => \"update_payment\"\n        20 => \"delete_payment\"\n        21 => \"delete_any_payment\"\n        22 => \"view_project\"\n        23 => \"view_any_project\"\n        24 => \"create_project\"\n        25 => \"update_project\"\n        26 => \"delete_project\"\n        27 => \"page_BdeDashboard\"\n        28 => \"page_MyProfilePage\"\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"data\" => array:26 [\n      \"id\" => 15\n      \"name\" => \"bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:04.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:2 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n      ]\n      \"client\" => array:6 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n        5 => \"delete_any_client\"\n      ]\n      \"incentive\" => array:2 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n      ]\n      \"incentive::rule\" => []\n      \"milestone\" => array:6 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n        5 => \"delete_any_milestone\"\n      ]\n      \"notification::event\" => []\n      \"notification::role::preference\" => []\n      \"payment\" => array:6 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n        5 => \"delete_any_payment\"\n      ]\n      \"pricing::model\" => []\n      \"product\" => []\n      \"project\" => array:5 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n      ]\n      \"project::status::log\" => []\n      \"project::type\" => []\n      \"role\" => []\n      \"role::notification::settings\" => []\n      \"user\" => []\n      \"pages_tab\" => array:2 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => []\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#3958\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #original: array:5 [\n        \"id\" => 15\n        \"name\" => \"bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:04\"\n        \"updated_at\" => \"2025-06-18 15:11:04\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"gcNWqKGmv3egTTB2d8H6\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1939385337 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939385337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021122, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1782920699 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782920699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101701, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1788614854 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788614854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544804, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=15),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1305938471 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=15)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=15)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305938471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54543, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@save<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Resources/Pages/EditRecord.php:134-177</a>", "middleware": "web", "duration": "1.2s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-318247288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-318247288\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1997891835 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"3379 characters\">{&quot;data&quot;:{&quot;permissions&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;,&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;,&quot;view_incentive&quot;,&quot;view_any_incentive&quot;,&quot;view_incentive::rule&quot;,&quot;view_any_incentive::rule&quot;,&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;,&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;,&quot;view_pricing::model&quot;,&quot;view_any_pricing::model&quot;,&quot;view_product&quot;,&quot;view_any_product&quot;,&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;,&quot;view_project::status::log&quot;,&quot;view_any_project::status::log&quot;,&quot;view_project::type&quot;,&quot;view_any_project::type&quot;,&quot;page_BdeDashboard&quot;,&quot;page_MyProfilePage&quot;],{&quot;class&quot;:&quot;Illuminate\\\\Support\\\\Collection&quot;,&quot;s&quot;:&quot;clctn&quot;}],&quot;data&quot;:[{&quot;id&quot;:15,&quot;name&quot;:&quot;bde_team&quot;,&quot;guard_name&quot;:&quot;web&quot;,&quot;created_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-18T15:11:04.000000Z&quot;,&quot;select_all&quot;:false,&quot;app::notification&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[[&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive&quot;:[[&quot;view_incentive&quot;,&quot;view_any_incentive&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive::rule&quot;:[[&quot;view_incentive::rule&quot;,&quot;view_any_incentive::rule&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;milestone&quot;:[[&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::event&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::role::preference&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;payment&quot;:[[&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;pricing::model&quot;:[[&quot;view_pricing::model&quot;,&quot;view_any_pricing::model&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;product&quot;:[[&quot;view_product&quot;,&quot;view_any_product&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project&quot;:[[&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::status::log&quot;:[[&quot;view_project::status::log&quot;,&quot;view_any_project::status::log&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::type&quot;:[[&quot;view_project::type&quot;,&quot;view_any_project::type&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;role&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;role::notification::settings&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;user&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;pages_tab&quot;:[[&quot;page_BdeDashboard&quot;,&quot;page_MyProfilePage&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;widgets_tab&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;custom_permissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;team_id&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/localhost:8000\\/admin\\/shield\\/roles&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Role&quot;,&quot;key&quot;:15,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;gcNWqKGmv3egTTB2d8H6&quot;,&quot;name&quot;:&quot;app.filament.resources.role-resource.pages.edit-role&quot;,&quot;path&quot;:&quot;admin\\/shield\\/roles\\/15\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c2c86e10acbcf86f0d7853ff3e10d076c710ebce6c5236ac573adf0a261460e3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.client.5</span>\" => \"<span class=sf-dump-str title=\"17 characters\">delete_any_client</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.milestone.5</span>\" => \"<span class=sf-dump-str title=\"20 characters\">delete_any_milestone</span>\"\n        \"<span class=sf-dump-key>data.payment.5</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_payment</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.product.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.product.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::status::log.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::status::log.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997891835\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4406</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFMMFQwYUQ2Y3VwTUQyMi8vZ040RWc9PSIsInZhbHVlIjoiNUM3ekpnaWplS212aExxd1hYdXNLSEFvMU1tQVYrbHJqRktrWEZiNjJ3UVdONmZlMDdtUzJ6SWpjbXpSaEVCbEZBWmIwYnJVSkJJWDNZNzMvY2J5QzJyTUs4S1F1NzVOcE9lNGxZcnN3VUZHUFZGNWQwRnFnbkJKL2tnUWdMUGsiLCJtYWMiOiIxNDIzODllY2Y2MDIzMmRjYjQ0NGEwNGM1ODM1NjkzNGQxMTUzZmY5MGM5ZjIxMmU0MTMzZDVkNTY0ZmY2NDAyIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ijl2S3FNL0hPR3FkVFc0REhoakg2cmc9PSIsInZhbHVlIjoicjRNRnpvci91c0kwU2Mra3NIMVFlKzVPbmNxaUVMZWNkN3kxYzVZTkw2YVBmYXE3clBpTUxoNGRmQm8wNXE4UXI1SWFFLzBQMklYMTE2SmZFUGFxakdpOStYemRyak9ZYjJsbjhtQ1B6VGhhSWpDdmlVOFVkQ3lVaFhLbUNnd0giLCJtYWMiOiIxMmU3YTAxZDM5NGM5ZDE0MmQyMGIwYTRjZDg4OWE2ZTBlM2YxMDRkMWM0NGE1OTA5YjhiMDlkNGQ3YTZkZmI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1396637814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396637814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297879978 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:48:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297879978\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-436239307 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/15/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f491ae1-9044-4464-aee9-32cad3dcf4a9</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436239307\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}