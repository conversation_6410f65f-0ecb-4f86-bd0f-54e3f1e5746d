<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentResource\Pages;
use App\Filament\Resources\PaymentResource\RelationManagers;
use App\Models\Payment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\DateFilter;
use Illuminate\Support\Facades\Log;
use App\Services\CurrencyConverter;
use Closure;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->can('view_any_payment');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // If user should see only their own data, filter payments for their projects
        if (auth()->check() && auth()->user()->shouldSeeOwnDataOnly()) {
            $query->whereHas('project', function (Builder $query) {
                $query->where('user_id', auth()->id());
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // EXACT SAME STRUCTURE AS Edit Client → Payment Tab
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('project_id')
                            ->label('Project')
                            ->relationship('project', 'title', function (Builder $query) {
                                // If user should see only their own data, filter to their projects
                                if (auth()->check() && auth()->user()->shouldSeeOwnDataOnly()) {
                                    $query->where('user_id', auth()->id());
                                }
                                return $query;
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live(),
                    ]),
                Forms\Components\Repeater::make('payments')
                    ->label('Payment Details')
                    ->schema([
                        Forms\Components\Grid::make(6)->schema([
                            Forms\Components\Select::make('milestone_id')
                                ->label('Milestone')
                                ->options(function (callable $get) {
                                    $projectId = $get('../../project_id');
                                    if (!$projectId) {
                                        return [];
                                    }
                                    return \App\Models\Milestone::where('project_id', $projectId)
                                        ->pluck('title', 'id')
                                        ->toArray();
                                })
                                ->searchable()
                                ->preload()
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    // Auto-calculate amount based on selected milestone
                                    $milestone = \App\Models\Milestone::find($state);
                                    if ($milestone) {
                                        $set('amount', $milestone->amount);
                                    }
                                }),
                            Forms\Components\TextInput::make('amount')
                                ->label('Amount')
                                ->required()
                                ->numeric()
                                ->live(onBlur: true),
                            Forms\Components\DatePicker::make('due_date')
                                ->label('Due Date')
                                ->required(),
                            Forms\Components\DatePicker::make('paid_date')
                                ->label('Paid Date'),
                            Forms\Components\Select::make('status')
                                ->label('Status')
                                ->options([
                                    'pending' => 'Pending',
                                    'paid' => 'Paid',
                                    'overdue' => 'Overdue',
                                    'cancelled' => 'Cancelled',
                                ])
                                ->default('pending')
                                ->required(),
                            Forms\Components\Select::make('payment_method')
                                ->label('Payment Method')
                                ->options([
                                    'bank_transfer' => 'Bank Transfer',
                                    'credit_card' => 'Credit Card',
                                    'upi' => 'UPI',
                                    'cash' => 'Cash',
                                    'cheque' => 'Cheque',
                                    'other' => 'Other',
                                ]),
                        ]),
                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\TextInput::make('transaction_id')
                                ->label('Transaction ID')
                                ->maxLength(255),
                            Forms\Components\Textarea::make('notes')
                                ->label('Notes')
                                ->maxLength(65535),
                        ]),
                    ])
                    ->minItems(1)
                    ->createItemButtonLabel('+')
                    ->reorderable()
                    ->rules([
                        function ($get) {
                            return function (string $attribute, $value, Closure $fail) use ($get) {
                                $projectId = $get('project_id');
                                if ($projectId) {
                                    $project = \App\Models\Project::find($projectId);
                                    if ($project) {
                                        // Check for duplicate milestones in current form
                                        $milestoneIds = collect($value)->pluck('milestone_id')->filter();
                                        $duplicateMilestones = $milestoneIds->duplicates();

                                        if ($duplicateMilestones->isNotEmpty()) {
                                            $duplicateMilestoneNames = \App\Models\Milestone::whereIn('id', $duplicateMilestones)
                                                ->pluck('title')
                                                ->implode(', ');
                                            $fail("Duplicate milestone entries found: {$duplicateMilestoneNames}. Each milestone can only have one payment entry.");
                                        }

                                        // Check for existing payments with same milestones in database
                                        $existingPaymentMilestones = \App\Models\Payment::where('project_id', $projectId)
                                            ->whereIn('milestone_id', $milestoneIds)
                                            ->with('milestone')
                                            ->get();

                                        if ($existingPaymentMilestones->isNotEmpty()) {
                                            $existingMilestoneNames = $existingPaymentMilestones->pluck('milestone.title')->implode(', ');
                                            $fail("Payment entries already exist for these milestones: {$existingMilestoneNames}. Please use the edit function to modify existing payments.");
                                        }

                                        // Get existing payments for this project
                                        $existingPayments = \App\Models\Payment::where('project_id', $projectId)->get();
                                        $existingTotalAmount = $existingPayments->sum('amount');

                                        // Calculate new totals
                                        $newTotalAmount = collect($value)->sum('amount');

                                        // Calculate final totals (existing + new)
                                        $finalTotalAmount = $existingTotalAmount + $newTotalAmount;

                                        // Validate amount against project total
                                        if ($finalTotalAmount > $project->total_payment) {
                                            $remainingAmount = $project->total_payment - $existingTotalAmount;
                                            $fail("The total payment amount cannot exceed the project total payment of {$project->total_payment}. Existing: {$existingTotalAmount}, Available: {$remainingAmount}, Trying to add: {$newTotalAmount}");
                                        }
                                    }
                                }
                            };
                        },
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('project.title')
            ->modifyQueryUsing(function (Builder $query) {
                // Show only one entry per project (most recently added) - SAME AS Edit Client → Payment Tab
                $query->whereRaw('payments.id IN (
                    SELECT MAX(p2.id)
                    FROM payments p2
                    WHERE p2.project_id = payments.project_id
                    GROUP BY p2.project_id
                )');
            })
            ->columns([
                Tables\Columns\TextColumn::make('project.title')
                    ->label('Project')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('project.client.company_name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('milestone.title')
                    ->label('Milestone')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->money(fn ($record) => $record->project->currency ?? 'INR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('due_date')
                    ->label('Due Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('paid_date')
                    ->label('Paid Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn($state) => match($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'overdue' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray'
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'overdue' => 'Overdue',
                        'cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\SelectFilter::make('project_id')
                    ->relationship('project', 'title')
                    ->label('Project')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('client')
                    ->relationship('project.client', 'company_name')
                    ->label('Client')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('project.user_id')
                    ->relationship('project.user', 'name')
                    ->label('BDE')
                    ->visible(fn() => auth()->check() && !auth()->user()->shouldSeeOwnDataOnly()),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->options([
                        'bank_transfer' => 'Bank Transfer',
                        'credit_card' => 'Credit Card',
                        'paypal' => 'PayPal',
                        'cash' => 'Cash',
                        'check' => 'Check',
                        'other' => 'Other',
                    ]),
                Tables\Filters\Filter::make('due_date')
                    ->form([
                        Forms\Components\DatePicker::make('due_from')
                            ->label('Due From'),
                        Forms\Components\DatePicker::make('due_until')
                            ->label('Due Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['due_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '>=', $date),
                            )
                            ->when(
                                $data['due_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('due_date', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('paid_date')
                    ->form([
                        Forms\Components\DatePicker::make('paid_from')
                            ->label('Paid From'),
                        Forms\Components\DatePicker::make('paid_until')
                            ->label('Paid Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['paid_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('paid_date', '>=', $date),
                            )
                            ->when(
                                $data['paid_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('paid_date', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('amount_range')
                    ->form([
                        Forms\Components\TextInput::make('amount_from')
                            ->label('Amount From')
                            ->numeric(),
                        Forms\Components\TextInput::make('amount_to')
                            ->label('Amount To')
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['amount_from'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '>=', $amount),
                            )
                            ->when(
                                $data['amount_to'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '<=', $amount),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('addPayments')
                    ->label('Add Payments')
                    ->icon('heroicon-o-plus-circle')
                    ->color('primary')
                    ->modalHeading('Add Payments')
                    ->modalDescription('Add one or more payments for this project')
                    ->modalWidth('7xl')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('project_id')
                                    ->label('Project')
                                    ->relationship('project', 'title', function (Builder $query) {
                                        // If user should see only their own data, filter to their projects
                                        if (auth()->check() && auth()->user()->shouldSeeOwnDataOnly()) {
                                            $query->where('user_id', auth()->id());
                                        }
                                        return $query;
                                    })
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->live(),
                            ]),
                        Forms\Components\Repeater::make('payments')
                            ->label('Payment Details')
                            ->schema([
                                Forms\Components\Grid::make(6)->schema([
                                    Forms\Components\Select::make('milestone_id')
                                        ->label('Milestone')
                                        ->options(function (callable $get) {
                                            $projectId = $get('../../project_id');
                                            if (!$projectId) {
                                                return [];
                                            }
                                            return \App\Models\Milestone::where('project_id', $projectId)
                                                ->pluck('title', 'id')
                                                ->toArray();
                                        })
                                        ->searchable()
                                        ->preload()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            // Auto-calculate amount based on selected milestone
                                            $milestone = \App\Models\Milestone::find($state);
                                            if ($milestone) {
                                                $set('amount', $milestone->amount);
                                            }
                                        }),
                                    Forms\Components\TextInput::make('amount')
                                        ->label('Amount')
                                        ->required()
                                        ->numeric()
                                        ->live(onBlur: true),
                                    Forms\Components\DatePicker::make('due_date')
                                        ->label('Due Date')
                                        ->required(),
                                    Forms\Components\DatePicker::make('paid_date')
                                        ->label('Paid Date'),
                                    Forms\Components\Select::make('status')
                                        ->label('Status')
                                        ->options([
                                            'pending' => 'Pending',
                                            'paid' => 'Paid',
                                            'overdue' => 'Overdue',
                                            'cancelled' => 'Cancelled',
                                        ])
                                        ->default('pending')
                                        ->required(),
                                    Forms\Components\Select::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'bank_transfer' => 'Bank Transfer',
                                            'credit_card' => 'Credit Card',
                                            'upi' => 'UPI',
                                            'cash' => 'Cash',
                                            'cheque' => 'Cheque',
                                            'other' => 'Other',
                                        ]),
                                ]),
                                Forms\Components\Grid::make(2)->schema([
                                    Forms\Components\TextInput::make('transaction_id')
                                        ->label('Transaction ID')
                                        ->maxLength(255),
                                    Forms\Components\Textarea::make('notes')
                                        ->label('Notes')
                                        ->maxLength(65535),
                                ]),
                            ])
                            ->minItems(1)
                            ->createItemButtonLabel('+')
                            ->reorderable()
                            ->rules([
                                function ($get) {
                                    return function (string $attribute, $value, Closure $fail) use ($get) {
                                        $projectId = $get('project_id');
                                        if ($projectId) {
                                            $project = \App\Models\Project::find($projectId);
                                            if ($project) {
                                                // Check for duplicate milestones in current form
                                                $milestoneIds = collect($value)->pluck('milestone_id')->filter();
                                                $duplicateMilestones = $milestoneIds->duplicates();

                                                if ($duplicateMilestones->isNotEmpty()) {
                                                    $duplicateMilestoneNames = \App\Models\Milestone::whereIn('id', $duplicateMilestones)
                                                        ->pluck('title')
                                                        ->implode(', ');
                                                    $fail("Duplicate milestone entries found: {$duplicateMilestoneNames}. Each milestone can only have one payment entry.");
                                                }

                                                // Check for existing payments with same milestones in database
                                                $existingPaymentMilestones = \App\Models\Payment::where('project_id', $projectId)
                                                    ->whereIn('milestone_id', $milestoneIds)
                                                    ->with('milestone')
                                                    ->get();

                                                if ($existingPaymentMilestones->isNotEmpty()) {
                                                    $existingMilestoneNames = $existingPaymentMilestones->pluck('milestone.title')->implode(', ');
                                                    $fail("Payment entries already exist for these milestones: {$existingMilestoneNames}. Please use the edit function to modify existing payments.");
                                                }

                                                // Get existing payments for this project
                                                $existingPayments = \App\Models\Payment::where('project_id', $projectId)->get();
                                                $existingTotalAmount = $existingPayments->sum('amount');

                                                // Calculate new totals
                                                $newTotalAmount = collect($value)->sum('amount');

                                                // Calculate final totals (existing + new)
                                                $finalTotalAmount = $existingTotalAmount + $newTotalAmount;

                                                // Validate amount against project total
                                                if ($finalTotalAmount > $project->total_payment) {
                                                    $remainingAmount = $project->total_payment - $existingTotalAmount;
                                                    $fail("The total payment amount cannot exceed the project total payment of {$project->total_payment}. Existing: {$existingTotalAmount}, Available: {$remainingAmount}, Trying to add: {$newTotalAmount}");
                                                }
                                            }
                                        }
                                    };
                                },
                            ]),
                    ])
                    ->action(function (array $data) {
                        $projectId = $data['project_id'];

                        foreach ($data['payments'] as $paymentData) {
                            \App\Models\Payment::create(array_merge($paymentData, ['project_id' => $projectId]));
                        }
                    }),
            ])
            ->actions([
                // Edit All action with icon only (same as Edit Client → Payment Tab)
                Tables\Actions\Action::make('editAll')
                    ->icon('heroicon-o-pencil-square')
                    ->label('')
                    ->modalWidth('7xl')
                    ->form(function ($record) {
                        $payments = \App\Models\Payment::where('project_id', $record->project_id)
                            ->orderBy('due_date', 'asc')
                            ->orderBy('created_at', 'asc')
                            ->get()
                            ->map(function ($payment) {
                                $paymentArray = $payment->toArray();

                                // Format dates for DatePicker fields
                                if ($payment->due_date) {
                                    if ($payment->due_date instanceof \Carbon\Carbon) {
                                        $paymentArray['due_date'] = $payment->due_date->format('Y-m-d');
                                    } elseif (is_string($payment->due_date)) {
                                        $paymentArray['due_date'] = \Carbon\Carbon::parse($payment->due_date)->format('Y-m-d');
                                    }
                                }

                                if ($payment->paid_date) {
                                    if ($payment->paid_date instanceof \Carbon\Carbon) {
                                        $paymentArray['paid_date'] = $payment->paid_date->format('Y-m-d');
                                    } elseif (is_string($payment->paid_date)) {
                                        $paymentArray['paid_date'] = \Carbon\Carbon::parse($payment->paid_date)->format('Y-m-d');
                                    }
                                }

                                return $paymentArray;
                            });

                        return [
                            Forms\Components\Repeater::make('payments')
                                ->label('Payments')
                                ->schema([
                                    Forms\Components\Hidden::make('id'),
                                    Forms\Components\Grid::make(6)->schema([
                                        Forms\Components\Select::make('milestone_id')
                                            ->label('Milestone')
                                            ->options(function () use ($record) {
                                                return \App\Models\Milestone::where('project_id', $record->project_id)
                                                    ->pluck('title', 'id')
                                                    ->toArray();
                                            })
                                            ->searchable()
                                            ->preload()
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Auto-calculate amount based on selected milestone
                                                $milestone = \App\Models\Milestone::find($state);
                                                if ($milestone) {
                                                    $set('amount', $milestone->amount);
                                                }
                                            }),
                                        Forms\Components\TextInput::make('amount')
                                            ->label('Amount')
                                            ->required()
                                            ->numeric()
                                            ->live(onBlur: true),
                                        Forms\Components\DatePicker::make('due_date')
                                            ->label('Due Date')
                                            ->required()
                                            ->format('Y-m-d')
                                            ->displayFormat('Y-m-d'),
                                        Forms\Components\DatePicker::make('paid_date')
                                            ->label('Paid Date')
                                            ->format('Y-m-d')
                                            ->displayFormat('Y-m-d'),
                                        Forms\Components\Select::make('status')
                                            ->label('Status')
                                            ->options([
                                                'pending' => 'Pending',
                                                'paid' => 'Paid',
                                                'overdue' => 'Overdue',
                                                'cancelled' => 'Cancelled',
                                            ])
                                            ->required(),
                                        Forms\Components\Select::make('payment_method')
                                            ->label('Payment Method')
                                            ->options([
                                                'bank_transfer' => 'Bank Transfer',
                                                'credit_card' => 'Credit Card',
                                                'upi' => 'UPI',
                                                'cash' => 'Cash',
                                                'cheque' => 'Cheque',
                                                'other' => 'Other',
                                            ]),
                                    ]),
                                    Forms\Components\Grid::make(2)->schema([
                                        Forms\Components\TextInput::make('transaction_id')
                                            ->label('Transaction ID')
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('notes')
                                            ->label('Notes')
                                            ->maxLength(65535),
                                    ]),
                                ])
                                ->default($payments)
                                ->minItems(1)
                                ->createItemButtonLabel('+')
                                ->reorderable()
                                ->rules([
                                    function () use ($record) {
                                        return function (string $attribute, $value, Closure $fail) use ($record) {
                                            $project = $record->project;

                                            // Check for duplicate milestones in current form
                                            $milestoneIds = collect($value)->pluck('milestone_id')->filter();
                                            $duplicateMilestones = $milestoneIds->duplicates();

                                            if ($duplicateMilestones->isNotEmpty()) {
                                                $duplicateMilestoneNames = \App\Models\Milestone::whereIn('id', $duplicateMilestones)
                                                    ->pluck('title')
                                                    ->implode(', ');
                                                $fail("Duplicate milestone entries found: {$duplicateMilestoneNames}. Each milestone can only have one payment entry.");
                                            }

                                            // Get current payment IDs being edited
                                            $currentPaymentIds = collect($value)->pluck('id')->filter();

                                            // Check for existing payments with same milestones in database (excluding current ones)
                                            $existingPaymentMilestones = \App\Models\Payment::where('project_id', $project->id)
                                                ->whereIn('milestone_id', $milestoneIds)
                                                ->whereNotIn('id', $currentPaymentIds)
                                                ->with('milestone')
                                                ->get();

                                            if ($existingPaymentMilestones->isNotEmpty()) {
                                                $existingMilestoneNames = $existingPaymentMilestones->pluck('milestone.title')->implode(', ');
                                                $fail("Payment entries already exist for these milestones: {$existingMilestoneNames}. Each milestone can only have one payment entry.");
                                            }

                                            // Calculate totals from form data
                                            $totalAmount = collect($value)->sum('amount');

                                            // Validate amount against project total
                                            $projectTotalAmount = $project->total_payment;
                                            if ($totalAmount > $projectTotalAmount) {
                                                $fail("The total payment amount cannot exceed the project total payment of {$projectTotalAmount}. Available: {$projectTotalAmount}, Trying to set: {$totalAmount}");
                                            }
                                        };
                                    },
                                ]),
                        ];
                    })
                    ->action(function ($record, $data) {
                        // EXACT SAME DATA SUBMISSION LOGIC as Edit Client → Payment Tab
                        $projectId = $record->project_id;
                        $existingIds = collect($data['payments'])->pluck('id')->filter();

                        // Delete removed payments
                        \App\Models\Payment::where('project_id', $projectId)
                            ->whereNotIn('id', $existingIds)
                            ->delete();

                        // Update or create payments
                        foreach ($data['payments'] as $paymentData) {
                            if (isset($paymentData['id']) && !empty($paymentData['id'])) {
                                \App\Models\Payment::find($paymentData['id'])->update($paymentData);
                            } else {
                                \App\Models\Payment::create(array_merge(
                                    $paymentData,
                                    ['project_id' => $projectId]
                                ));
                            }
                        }
                    }),
                Tables\Actions\DeleteAction::make()->label(''),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            'create' => Pages\CreatePayment::route('/create'),
            'edit' => Pages\EditPayment::route('/{record}/edit'),
        ];
    }
}
