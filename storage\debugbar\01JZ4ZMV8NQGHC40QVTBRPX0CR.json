{"__meta": {"id": "01JZ4ZMV8NQGHC40QVTBRPX0CR", "datetime": "2025-07-02 07:12:01", "utime": ********21.813773, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.151753, "end": ********21.813785, "duration": 1.662032127380371, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": **********.151753, "relative_start": 0, "end": **********.517055, "relative_end": **********.517055, "duration": 0.*****************, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.517067, "relative_start": 0.****************, "end": ********21.813787, "relative_end": 1.9073486328125e-06, "duration": 1.***************, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.768774, "relative_start": 0.****************, "end": **********.770695, "relative_end": **********.770695, "duration": 0.001920938491821289, "duration_str": "1.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.962857, "relative_start": 0.****************, "end": **********.962857, "relative_end": **********.962857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.969106, "relative_start": 0.****************, "end": **********.969106, "relative_end": **********.969106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.976204, "relative_start": 0.8244509696960449, "end": **********.976204, "relative_end": **********.976204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.986782, "relative_start": 0.835029125213623, "end": **********.986782, "relative_end": **********.986782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.996929, "relative_start": 0.8451759815216064, "end": **********.996929, "relative_end": **********.996929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.004523, "relative_start": 0.8527700901031494, "end": ********21.004523, "relative_end": ********21.004523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.011596, "relative_start": 0.8598430156707764, "end": ********21.011596, "relative_end": ********21.011596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.015454, "relative_start": 0.8637011051177979, "end": ********21.015454, "relative_end": ********21.015454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.017918, "relative_start": 0.8661651611328125, "end": ********21.017918, "relative_end": ********21.017918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.021817, "relative_start": 0.8700640201568604, "end": ********21.021817, "relative_end": ********21.021817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.025126, "relative_start": 0.8733730316162109, "end": ********21.025126, "relative_end": ********21.025126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.056631, "relative_start": 0.9048781394958496, "end": ********21.056631, "relative_end": ********21.056631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.063045, "relative_start": 0.9112920761108398, "end": ********21.063045, "relative_end": ********21.063045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.081792, "relative_start": 0.9300391674041748, "end": ********21.081792, "relative_end": ********21.081792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.085175, "relative_start": 0.9334220886230469, "end": ********21.085175, "relative_end": ********21.085175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.105071, "relative_start": 0.9533181190490723, "end": ********21.105071, "relative_end": ********21.105071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.108262, "relative_start": 0.9565091133117676, "end": ********21.108262, "relative_end": ********21.108262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.127033, "relative_start": 0.9752800464630127, "end": ********21.127033, "relative_end": ********21.127033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.130608, "relative_start": 0.9788551330566406, "end": ********21.130608, "relative_end": ********21.130608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.148515, "relative_start": 0.9967620372772217, "end": ********21.148515, "relative_end": ********21.148515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.151737, "relative_start": 0.9999840259552002, "end": ********21.151737, "relative_end": ********21.151737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.169375, "relative_start": 1.0176219940185547, "end": ********21.169375, "relative_end": ********21.169375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": ********21.172433, "relative_start": 1.0206799507141113, "end": ********21.172433, "relative_end": ********21.172433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.194408, "relative_start": 1.0426549911499023, "end": ********21.194408, "relative_end": ********21.194408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.19721, "relative_start": 1.045457124710083, "end": ********21.19721, "relative_end": ********21.19721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.199703, "relative_start": 1.047950029373169, "end": ********21.199703, "relative_end": ********21.199703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.206209, "relative_start": 1.0544559955596924, "end": ********21.206209, "relative_end": ********21.206209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.212436, "relative_start": 1.060683012008667, "end": ********21.212436, "relative_end": ********21.212436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.21656, "relative_start": 1.0648069381713867, "end": ********21.21656, "relative_end": ********21.21656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.223074, "relative_start": 1.0713210105895996, "end": ********21.223074, "relative_end": ********21.223074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.225312, "relative_start": 1.073559045791626, "end": ********21.225312, "relative_end": ********21.225312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.228813, "relative_start": 1.0770599842071533, "end": ********21.228813, "relative_end": ********21.228813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.234873, "relative_start": 1.0831201076507568, "end": ********21.234873, "relative_end": ********21.234873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.240706, "relative_start": 1.0889530181884766, "end": ********21.240706, "relative_end": ********21.240706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.246132, "relative_start": 1.09437894821167, "end": ********21.246132, "relative_end": ********21.246132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.251432, "relative_start": 1.0996789932250977, "end": ********21.251432, "relative_end": ********21.251432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.253873, "relative_start": 1.1021201610565186, "end": ********21.253873, "relative_end": ********21.253873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.257542, "relative_start": 1.1057889461517334, "end": ********21.257542, "relative_end": ********21.257542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.260765, "relative_start": 1.1090121269226074, "end": ********21.260765, "relative_end": ********21.260765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.265529, "relative_start": 1.1137759685516357, "end": ********21.265529, "relative_end": ********21.265529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.268184, "relative_start": 1.1164309978485107, "end": ********21.268184, "relative_end": ********21.268184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.271799, "relative_start": 1.1200461387634277, "end": ********21.271799, "relative_end": ********21.271799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.281966, "relative_start": 1.1302130222320557, "end": ********21.281966, "relative_end": ********21.281966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.285516, "relative_start": 1.133763074874878, "end": ********21.285516, "relative_end": ********21.285516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.288853, "relative_start": 1.1370999813079834, "end": ********21.288853, "relative_end": ********21.288853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.29824, "relative_start": 1.1464869976043701, "end": ********21.29824, "relative_end": ********21.29824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.306549, "relative_start": 1.1547961235046387, "end": ********21.306549, "relative_end": ********21.306549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.316221, "relative_start": 1.1644680500030518, "end": ********21.316221, "relative_end": ********21.316221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.324584, "relative_start": 1.1728310585021973, "end": ********21.324584, "relative_end": ********21.324584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.330788, "relative_start": 1.179034948348999, "end": ********21.330788, "relative_end": ********21.330788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.334653, "relative_start": 1.1828999519348145, "end": ********21.334653, "relative_end": ********21.334653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.34586, "relative_start": 1.1941070556640625, "end": ********21.34586, "relative_end": ********21.34586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.354746, "relative_start": 1.2029931545257568, "end": ********21.354746, "relative_end": ********21.354746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.362066, "relative_start": 1.210313081741333, "end": ********21.362066, "relative_end": ********21.362066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.370673, "relative_start": 1.2189199924468994, "end": ********21.370673, "relative_end": ********21.370673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.373616, "relative_start": 1.221863031387329, "end": ********21.373616, "relative_end": ********21.373616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.379031, "relative_start": 1.2272779941558838, "end": ********21.379031, "relative_end": ********21.379031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.38937, "relative_start": 1.237617015838623, "end": ********21.38937, "relative_end": ********21.38937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.400618, "relative_start": 1.2488651275634766, "end": ********21.400618, "relative_end": ********21.400618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.406872, "relative_start": 1.2551190853118896, "end": ********21.406872, "relative_end": ********21.406872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.416932, "relative_start": 1.26517915725708, "end": ********21.416932, "relative_end": ********21.416932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.420098, "relative_start": 1.2683451175689697, "end": ********21.420098, "relative_end": ********21.420098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.423827, "relative_start": 1.2720739841461182, "end": ********21.423827, "relative_end": ********21.423827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.434383, "relative_start": 1.2826299667358398, "end": ********21.434383, "relative_end": ********21.434383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.441638, "relative_start": 1.2898850440979004, "end": ********21.441638, "relative_end": ********21.441638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.448093, "relative_start": 1.296339988708496, "end": ********21.448093, "relative_end": ********21.448093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.454825, "relative_start": 1.3030719757080078, "end": ********21.454825, "relative_end": ********21.454825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.458525, "relative_start": 1.306771993637085, "end": ********21.458525, "relative_end": ********21.458525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.465775, "relative_start": 1.3140220642089844, "end": ********21.465775, "relative_end": ********21.465775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.469447, "relative_start": 1.3176939487457275, "end": ********21.469447, "relative_end": ********21.469447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.474919, "relative_start": 1.3231661319732666, "end": ********21.474919, "relative_end": ********21.474919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.47922, "relative_start": 1.3274669647216797, "end": ********21.47922, "relative_end": ********21.47922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.482345, "relative_start": 1.330592155456543, "end": ********21.482345, "relative_end": ********21.482345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.488511, "relative_start": 1.3367581367492676, "end": ********21.488511, "relative_end": ********21.488511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.490819, "relative_start": 1.3390660285949707, "end": ********21.490819, "relative_end": ********21.490819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.495282, "relative_start": 1.3435289859771729, "end": ********21.495282, "relative_end": ********21.495282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.5024, "relative_start": 1.35064697265625, "end": ********21.5024, "relative_end": ********21.5024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.50898, "relative_start": 1.357227087020874, "end": ********21.50898, "relative_end": ********21.50898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.514004, "relative_start": 1.3622510433197021, "end": ********21.514004, "relative_end": ********21.514004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.520799, "relative_start": 1.3690459728240967, "end": ********21.520799, "relative_end": ********21.520799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.523204, "relative_start": 1.3714511394500732, "end": ********21.523204, "relative_end": ********21.523204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.526662, "relative_start": 1.3749091625213623, "end": ********21.526662, "relative_end": ********21.526662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.539799, "relative_start": 1.3880460262298584, "end": ********21.539799, "relative_end": ********21.539799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.547794, "relative_start": 1.3960411548614502, "end": ********21.547794, "relative_end": ********21.547794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.553261, "relative_start": 1.401508092880249, "end": ********21.553261, "relative_end": ********21.553261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.561437, "relative_start": 1.4096839427947998, "end": ********21.561437, "relative_end": ********21.561437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.564597, "relative_start": 1.412843942642212, "end": ********21.564597, "relative_end": ********21.564597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.568034, "relative_start": 1.41628098487854, "end": ********21.568034, "relative_end": ********21.568034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.575503, "relative_start": 1.4237501621246338, "end": ********21.575503, "relative_end": ********21.575503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.583547, "relative_start": 1.4317941665649414, "end": ********21.583547, "relative_end": ********21.583547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.589018, "relative_start": 1.437265157699585, "end": ********21.589018, "relative_end": ********21.589018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.597352, "relative_start": 1.44559907913208, "end": ********21.597352, "relative_end": ********21.597352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.599789, "relative_start": 1.4480359554290771, "end": ********21.599789, "relative_end": ********21.599789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.603451, "relative_start": 1.4516980648040771, "end": ********21.603451, "relative_end": ********21.603451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.612359, "relative_start": 1.4606060981750488, "end": ********21.612359, "relative_end": ********21.612359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.617906, "relative_start": 1.4661531448364258, "end": ********21.617906, "relative_end": ********21.617906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.622156, "relative_start": 1.4704029560089111, "end": ********21.622156, "relative_end": ********21.622156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.629632, "relative_start": 1.4778790473937988, "end": ********21.629632, "relative_end": ********21.629632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.633081, "relative_start": 1.481328010559082, "end": ********21.633081, "relative_end": ********21.633081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.638285, "relative_start": 1.4865319728851318, "end": ********21.638285, "relative_end": ********21.638285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.641987, "relative_start": 1.490234136581421, "end": ********21.641987, "relative_end": ********21.641987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.648067, "relative_start": 1.4963140487670898, "end": ********21.648067, "relative_end": ********21.648067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.650856, "relative_start": 1.49910306930542, "end": ********21.650856, "relative_end": ********21.650856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.653791, "relative_start": 1.5020380020141602, "end": ********21.653791, "relative_end": ********21.653791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.661979, "relative_start": 1.5102260112762451, "end": ********21.661979, "relative_end": ********21.661979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.665257, "relative_start": 1.5135040283203125, "end": ********21.665257, "relative_end": ********21.665257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.668714, "relative_start": 1.5169610977172852, "end": ********21.668714, "relative_end": ********21.668714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.676914, "relative_start": 1.5251610279083252, "end": ********21.676914, "relative_end": ********21.676914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.683498, "relative_start": 1.5317449569702148, "end": ********21.683498, "relative_end": ********21.683498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.687533, "relative_start": 1.5357799530029297, "end": ********21.687533, "relative_end": ********21.687533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.692478, "relative_start": 1.5407249927520752, "end": ********21.692478, "relative_end": ********21.692478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.696377, "relative_start": 1.5446240901947021, "end": ********21.696377, "relative_end": ********21.696377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.700824, "relative_start": 1.5490710735321045, "end": ********21.700824, "relative_end": ********21.700824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.703896, "relative_start": 1.5521430969238281, "end": ********21.703896, "relative_end": ********21.703896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.70837, "relative_start": 1.556617021560669, "end": ********21.70837, "relative_end": ********21.70837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.712809, "relative_start": 1.561056137084961, "end": ********21.712809, "relative_end": ********21.712809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.716415, "relative_start": 1.564661979675293, "end": ********21.716415, "relative_end": ********21.716415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.722984, "relative_start": 1.5712311267852783, "end": ********21.722984, "relative_end": ********21.722984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.725508, "relative_start": 1.5737550258636475, "end": ********21.725508, "relative_end": ********21.725508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.729788, "relative_start": 1.5780351161956787, "end": ********21.729788, "relative_end": ********21.729788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.737756, "relative_start": 1.586003065109253, "end": ********21.737756, "relative_end": ********21.737756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.74501, "relative_start": 1.593256950378418, "end": ********21.74501, "relative_end": ********21.74501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.749612, "relative_start": 1.5978591442108154, "end": ********21.749612, "relative_end": ********21.749612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.75461, "relative_start": 1.6028571128845215, "end": ********21.75461, "relative_end": ********21.75461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.758186, "relative_start": 1.6064331531524658, "end": ********21.758186, "relative_end": ********21.758186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.764349, "relative_start": 1.612596035003662, "end": ********21.764349, "relative_end": ********21.764349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.767402, "relative_start": 1.6156489849090576, "end": ********21.767402, "relative_end": ********21.767402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********21.771283, "relative_start": 1.6195299625396729, "end": ********21.771283, "relative_end": ********21.771283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.774059, "relative_start": 1.6223061084747314, "end": ********21.774059, "relative_end": ********21.774059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********21.778601, "relative_start": 1.6268479824066162, "end": ********21.778601, "relative_end": ********21.778601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": ********21.782254, "relative_start": 1.6305010318756104, "end": ********21.782254, "relative_end": ********21.782254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********21.789762, "relative_start": 1.6380090713500977, "end": ********21.789762, "relative_end": ********21.789762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********21.7948, "relative_start": 1.6430470943450928, "end": ********21.7948, "relative_end": ********21.7948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********21.804404, "relative_start": 1.652651071548462, "end": ********21.804404, "relative_end": ********21.804404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********21.808213, "relative_start": 1.6564600467681885, "end": ********21.81157, "relative_end": ********21.81157, "duration": 0.00335693359375, "duration_str": "3.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 62027384, "peak_usage_str": "59MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 134, "nb_templates": 134, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.962842, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "1x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.969094, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.976191, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "50x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.98677, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 50, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "65x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": ********21.021806, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 65, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "12x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": ********21.056616, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 12, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": ********21.782238, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": ********21.789746, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 89, "nb_statements": 89, "nb_visible_statements": 89, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05473000000000001, "accumulated_duration_str": "54.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.774472, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 1.151}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.784748, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.151, "width_percent": 1.334}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.788028, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.485, "width_percent": 0.987}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.790633, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.472, "width_percent": 0.914}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.7921262, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.385, "width_percent": 0.804}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.806761, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.189, "width_percent": 1.59}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) and `milestones`.`id` = '127' limit 1", "type": "query", "params": [], "bindings": ["127"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.846361, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.779, "width_percent": 1.9}, {"sql": "select * from `projects` where `projects`.`id` in (165)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.848213, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.679, "width_percent": 0.987}, {"sql": "select * from `clients` where `clients`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 29, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 30, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 31, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.8492951, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.666, "width_percent": 5.39}, {"sql": "select * from `milestones` where `project_id` = 165 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 373}], "start": **********.8529801, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:330", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 330}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=330", "ajax": false, "filename": "MilestoneResource.php", "line": "330"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.056, "width_percent": 1.48}, {"sql": "select * from `payments` where `milestone_id` = 125 limit 1", "type": "query", "params": [], "bindings": [125], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.85783, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.536, "width_percent": 4.458}, {"sql": "select * from `payments` where `milestone_id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8616462, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.994, "width_percent": 1.151}, {"sql": "select * from `payments` where `milestone_id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8631198, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.145, "width_percent": 1.023}, {"sql": "select * from `payments` where `milestone_id` = 119 limit 1", "type": "query", "params": [], "bindings": [119], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.864454, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.168, "width_percent": 0.968}, {"sql": "select * from `payments` where `milestone_id` = 127 limit 1", "type": "query", "params": [], "bindings": [127], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.865756, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.137, "width_percent": 0.987}, {"sql": "select * from `payments` where `milestone_id` = 117 limit 1", "type": "query", "params": [], "bindings": [117], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.867068, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.123, "width_percent": 0.877}, {"sql": "select * from `payments` where `milestone_id` = 121 limit 1", "type": "query", "params": [], "bindings": [121], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.868332, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 26, "width_percent": 1.023}, {"sql": "select * from `payments` where `milestone_id` = 122 limit 1", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8696399, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.024, "width_percent": 1.041}, {"sql": "select * from `payments` where `milestone_id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8709629, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.065, "width_percent": 1.06}, {"sql": "select * from `payments` where `milestone_id` = 124 limit 1", "type": "query", "params": [], "bindings": [124], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8723001, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.125, "width_percent": 0.914}, {"sql": "select * from `payments` where `milestone_id` = 126 limit 1", "type": "query", "params": [], "bindings": [126], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.873636, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.038, "width_percent": 1.041}, {"sql": "select * from `payments` where `milestone_id` = 128 limit 1", "type": "query", "params": [], "bindings": [128], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 331}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.8750691, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:349", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=349", "ajax": false, "filename": "MilestoneResource.php", "line": "349"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.08, "width_percent": 0.877}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.951348, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.957, "width_percent": 2.229}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `milestones`.`project_id` = `projects`.`id` where milestones.id = (\nSELECT m.id\nFROM milestones m\nWHERE m.project_id = milestones.project_id\nORDER BY\nCASE\nWHEN m.status = \"in_progress\" THEN 1\nWHEN m.status = \"pending\" THEN 2\nWHEN m.status = \"delayed\" THEN 3\nWHEN m.status = \"completed\" THEN 4\nELSE 5\nEND,\nm.due_date ASC,\nm.created_at ASC\nLIMIT 1\n) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.95356, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.186, "width_percent": 1.919}, {"sql": "select * from `projects` where `projects`.`id` in (160, 161, 162, 163, 164, 165)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.955222, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.105, "width_percent": 0.987}, {"sql": "select * from `clients` where `clients`.`id` in (25, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.956275, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.091, "width_percent": 0.987}, {"sql": "select `projects`.`title`, `projects`.`id` from `projects` order by `projects`.`title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.994548, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.078, "width_percent": 1.188}, {"sql": "select `clients`.`company_name`, `clients`.`id` from `clients` order by `clients`.`company_name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": ********21.0023382, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.265, "width_percent": 0.859}, {"sql": "select `users`.`name`, `users`.`id` from `users` order by `users`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.008805, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.124, "width_percent": 1.151}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.2012942, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.275, "width_percent": 1.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.2034168, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 42.445, "width_percent": 0.731}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.204502, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 43.176, "width_percent": 0.658}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.20701, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 43.833, "width_percent": 0.914}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.208874, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.747, "width_percent": 0.731}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.230079, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 45.478, "width_percent": 1.023}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.231956, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.501, "width_percent": 0.987}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.2331, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.488, "width_percent": 0.786}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.235641, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.273, "width_percent": 1.115}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.237458, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.388, "width_percent": 1.626}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.290159, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.014, "width_percent": 1.151}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.292176, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.165, "width_percent": 1.041}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.294977, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.207, "width_percent": 1.242}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.299503, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.449, "width_percent": 0.895}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.302258, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.344, "width_percent": 1.224}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.336694, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.569, "width_percent": 1.425}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.339514, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.994, "width_percent": 1.078}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.34111, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.072, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.3472538, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.766, "width_percent": 1.115}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.350844, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.881, "width_percent": 0.968}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.3813949, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.849, "width_percent": 1.096}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.385354, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.945, "width_percent": 1.041}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.3869178, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.987, "width_percent": 0.786}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.3915489, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.773, "width_percent": 1.644}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.39595, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.417, "width_percent": 1.188}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.425822, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.605, "width_percent": 4.294}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.430392, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.898, "width_percent": 0.914}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.4318662, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.812, "width_percent": 0.877}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.435524, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.689, "width_percent": 0.95}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.438021, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.639, "width_percent": 0.932}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.497122, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.571, "width_percent": 0.822}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.4993708, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.393, "width_percent": 0.767}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.500641, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.161, "width_percent": 0.64}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.5032358, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.8, "width_percent": 0.749}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.505466, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 78.549, "width_percent": 0.64}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.528743, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.189, "width_percent": 0.749}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.53091, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.938, "width_percent": 0.877}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.53205, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.815, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.540915, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.509, "width_percent": 0.877}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.5433052, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.386, "width_percent": 1.845}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.569756, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.232, "width_percent": 0.914}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.572112, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.145, "width_percent": 0.548}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.573307, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.693, "width_percent": 0.566}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.5775518, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.26, "width_percent": 1.115}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.58035, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.374, "width_percent": 0.95}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.605348, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.325, "width_percent": 0.822}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.607753, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.147, "width_percent": 0.95}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.609091, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.097, "width_percent": 0.713}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.613252, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.809, "width_percent": 0.713}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.615009, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.522, "width_percent": 0.676}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.670598, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.198, "width_percent": 1.06}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.6731648, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.258, "width_percent": 0.713}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.674484, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 93.97, "width_percent": 0.53}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.678329, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.5, "width_percent": 0.658}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.680691, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.158, "width_percent": 0.804}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.731929, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:403", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=403", "ajax": false, "filename": "MilestoneResource.php", "line": "403"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.962, "width_percent": 0.95}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.7340882, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.912, "width_percent": 0.84}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.735571, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.753, "width_percent": 0.749}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********21.7386239, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:398", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 398}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=398", "ajax": false, "filename": "MilestoneResource.php", "line": "398"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.502, "width_percent": 0.767}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********21.740437, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestoneResource.php:418", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MilestoneResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\MilestoneResource.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FMilestoneResource.php&line=418", "ajax": false, "filename": "MilestoneResource.php", "line": "418"}, "connection": "local_kit_db", "explain": null, "start_percent": 99.269, "width_percent": 0.731}]}, "models": {"data": {"App\\Models\\PricingModel": {"value": 60, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Payment": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\Project": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Client": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 103, "is_counter": true}, "livewire": {"data": {"app.filament.resources.milestone-resource.pages.list-milestones #mIbyhOyJ0bcaVcL1DSC7": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:6 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"project_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"client\" => array:1 [\n        \"value\" => null\n      ]\n      \"project\" => array:1 [\n        \"user_id\" => array:1 [\n          \"value\" => null\n        ]\n      ]\n      \"due_date\" => array:2 [\n        \"due_from\" => null\n        \"due_until\" => null\n      ]\n      \"amount_range\" => array:2 [\n        \"amount_from\" => null\n        \"amount_to\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => array:1 [\n      0 => \"editAll\"\n    ]\n    \"mountedTableActionsData\" => array:1 [\n      0 => array:1 [\n        \"milestones\" => array:12 [\n          \"4c08d242-3b05-4995-8540-10ff5be67be5\" => array:24 [\n            \"id\" => 125\n            \"project_id\" => 165\n            \"title\" => \"Month 9\"\n            \"description\" => \"Auto-generated milestone 9 of 12\"\n            \"due_date\" => \"2024-01-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"delayed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:58:22.000000Z\"\n            \"payment_due_date\" => \"2026-04-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"1b6b3cf1-c75f-4a27-89e0-5c12356270e6\" => array:24 [\n            \"id\" => 118\n            \"project_id\" => 165\n            \"title\" => \"Month 2\"\n            \"description\" => \"Auto-generated milestone 2 of 12\"\n            \"due_date\" => \"2025-02-02\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:47:21.000000Z\"\n            \"payment_due_date\" => \"2025-09-01\"\n            \"payment_paid_date\" => \"2025-07-01\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"aec0172f-fef8-4448-853e-e3cc1de54252\" => array:24 [\n            \"id\" => 120\n            \"project_id\" => 165\n            \"title\" => \"Month 4\"\n            \"description\" => \"Auto-generated milestone 4 of 12\"\n            \"due_date\" => \"2025-06-29\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"delayed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:25:13.000000Z\"\n            \"payment_due_date\" => \"2025-11-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"e3faa50f-86cf-4b2b-b941-597ff38b2958\" => array:24 [\n            \"id\" => 119\n            \"project_id\" => 165\n            \"title\" => \"Month 3\"\n            \"description\" => \"Auto-generated milestone 3 of 12\"\n            \"due_date\" => \"2025-06-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"delayed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:15:09.000000Z\"\n            \"payment_due_date\" => \"2025-06-27\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"1ac3a8bb-8a67-49f7-85fb-bdfbbc4168bf\" => array:24 [\n            \"id\" => 127\n            \"project_id\" => 165\n            \"title\" => \"Month 11\"\n            \"description\" => \"Auto-generated milestone 11 of 12\"\n            \"due_date\" => \"2025-07-28\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"pending\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:49:54.000000Z\"\n            \"payment_due_date\" => \"2026-06-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"499fcf11-6839-4309-a1cb-06f04f66c175\" => array:24 [\n            \"id\" => 117\n            \"project_id\" => 165\n            \"title\" => \"Month 1\"\n            \"description\" => \"Auto-generated milestone 1 of 12\"\n            \"due_date\" => \"2025-08-01\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:01:12.000000Z\"\n            \"payment_due_date\" => \"2025-08-01\"\n            \"payment_paid_date\" => \"2025-08-01\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"1d0f82f1-a8bd-4d0b-8bdb-11494ad84f15\" => array:24 [\n            \"id\" => 121\n            \"project_id\" => 165\n            \"title\" => \"Month 5\"\n            \"description\" => \"Auto-generated milestone 5 of 12\"\n            \"due_date\" => \"2025-08-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"pending\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:14:43.000000Z\"\n            \"payment_due_date\" => \"2025-12-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"dee0e5dd-9cd9-40c6-a78f-5e3d7bcbbf14\" => array:24 [\n            \"id\" => 122\n            \"project_id\" => 165\n            \"title\" => \"Month 6\"\n            \"description\" => \"Auto-generated milestone 6 of 12\"\n            \"due_date\" => \"2025-09-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"pending\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:14:43.000000Z\"\n            \"payment_due_date\" => \"2026-01-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"e85cb7b0-2220-4bed-9d43-d31bb6bfcc1d\" => array:24 [\n            \"id\" => 123\n            \"project_id\" => 165\n            \"title\" => \"Month 7\"\n            \"description\" => \"Auto-generated milestone 7 of 12\"\n            \"due_date\" => \"2025-10-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"pending\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T06:14:43.000000Z\"\n            \"payment_due_date\" => \"2026-02-01\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"0aae3c92-ad4e-41a5-a99e-d4ff402ce696\" => array:24 [\n            \"id\" => 124\n            \"project_id\" => 165\n            \"title\" => \"Month 8\"\n            \"description\" => \"Auto-generated milestone 8 of 12\"\n            \"due_date\" => \"2025-11-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T07:00:28.000000Z\"\n            \"payment_due_date\" => \"2025-07-02\"\n            \"payment_paid_date\" => \"2025-07-01\"\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"b44b9bec-65c7-48e4-8489-da98e5409b71\" => array:24 [\n            \"id\" => 126\n            \"project_id\" => 165\n            \"title\" => \"Month 10\"\n            \"description\" => \"Auto-generated milestone 10 of 12\"\n            \"due_date\" => \"2026-01-30\"\n            \"percentage\" => \"8.33\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T07:06:14.000000Z\"\n            \"payment_due_date\" => \"2025-07-04\"\n            \"payment_paid_date\" => \"2025-07-02\"\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n          \"0844363f-e326-4aae-8578-d81195895848\" => array:24 [\n            \"id\" => 128\n            \"project_id\" => 165\n            \"title\" => \"Month 12\"\n            \"description\" => \"Auto-generated milestone 12 of 12\"\n            \"due_date\" => \"2026-03-30\"\n            \"percentage\" => \"8.37\"\n            \"hours\" => null\n            \"amount\" => \"1000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-02T05:56:52.000000Z\"\n            \"updated_at\" => \"2025-07-02T07:06:14.000000Z\"\n            \"payment_due_date\" => \"2025-07-01\"\n            \"payment_paid_date\" => \"2025-07-04\"\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"1000.00\"\n            \"payment_notes\" => null\n          ]\n        ]\n      ]\n    ]\n    \"mountedTableActionsArguments\" => array:1 [\n      0 => []\n    ]\n    \"mountedTableActionRecord\" => \"127\"\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.milestone-resource.pages.list-milestones\"\n  \"component\" => \"App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones\"\n  \"id\" => \"mIbyhOyJ0bcaVcL1DSC7\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 21, "messages": [{"message": "[\n  ability => create,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1498543545 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498543545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.812006, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-333862988 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333862988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.818193, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2027086057 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027086057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.838148, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=97),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-613736250 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=97)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\Milestone(id=97)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613736250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.037826, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=97),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-281310234 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=97)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\Milestone(id=97)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281310234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.055423, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=97),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-429973664 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=97)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\Milestone(id=97)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429973664\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.062732, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1431550250 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431550250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.066863, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-548949306 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548949306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.08094, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021477316 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021477316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.084967, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-963783780 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963783780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.088879, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2091162366 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091162366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.104315, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=107),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-871274977 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=107)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=107)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871274977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.108075, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=110),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110468166 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=110)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=110)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110468166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.112799, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=110),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2071377417 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=110)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=110)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071377417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.125254, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=110),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1105889145 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=110)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=110)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105889145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.130412, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=113),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2040480806 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=113)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=113)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040480806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.133889, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=113),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864193586 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=113)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=113)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864193586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.147783, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=113),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1871612678 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=113)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=113)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871612678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.151552, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Milestone(id=127),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155816960 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Milestone(id=127)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=127)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155816960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.154939, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=127),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1438013026 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=127)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=127)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438013026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.168671, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=127),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-537123512 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=127)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=127)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537123512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********21.172252, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\MilestoneResource\\Pages\\ListMilestones@mountTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=171\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=171\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:171-234</a>", "middleware": "web", "duration": "1.67s", "peak_memory": "68MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-619875227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-619875227\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-479488225 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1954 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;status&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;project_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;project&quot;:[{&quot;user_id&quot;:[{&quot;value&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;due_date&quot;:[{&quot;due_from&quot;:null,&quot;due_until&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;amount_range&quot;:[{&quot;amount_from&quot;:null,&quot;amount_to&quot;:null},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;mIbyhOyJ0bcaVcL1DSC7&quot;,&quot;name&quot;:&quot;app.filament.resources.milestone-resource.pages.list-milestones&quot;,&quot;path&quot;:&quot;admin\\/milestones&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;336927a14927c546d7bc93fd8ac9d15a15fc1affe9d5f843e5a6338fe6f7286e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mountTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">editAll</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">127</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479488225\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1707031236 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2379</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1EWjAzS280MTZ1SGNPV244ZisrV0E9PSIsInZhbHVlIjoic1loaXJWZFpaWWE2R0ZuVFRmaEE3bmQxbzJUaW85WjFQRWhodUsyQ0piSkdySmlFV1BhOXdsY1NnQjUzc043NE54dlVRbEsraDJlMlFXbDRoeUdlVjFJR3lGSkQ2aU5FNE45NmlMS2hraGNUS214QjFJUEtWOC9PR3dhcUt4R0YiLCJtYWMiOiIxNTRkNTQ1OTA0YWEwODM5ODhlYWY4NjhmZmU5MTFlYzc1OWNmODNhMWNmZWUxMmI2Nzk0MGVlOGJkNDVhOTBkIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IlFaSm9SOERaWDlXZTh0dkozSVREb1E9PSIsInZhbHVlIjoiM0wzek0wc082SkxSUjBodnN6dzNxSXVhcmVGM0lZT0h1ZHkzVDVSY3YrU2NBdmw1aFRQUUFGRlZTOWNSK1p6UHJHcUp6ZnZDRUxJajFyeXdEN3hobzI2K2VsdXNWWWtZbENhUnRZQjZUT0xuZ1o5WENJRXVsTm9TU0NBdkRSdmUiLCJtYWMiOiI1ZTEwZDcwNTMxYjg4M2UxZmU4MWUwYWJhZDRkYzVjMmQ3ZTgwM2ViZDE2OWM1ZmUyMDE3NWY4NjkxZmRjZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707031236\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1715393369 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715393369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1303052022 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:12:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303052022\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-442612935 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442612935\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}