{"__meta": {"id": "01JZ2P35PQXARFF0R0DNJH8F54", "datetime": "2025-07-01 09:46:36", "utime": ********96.632772, "method": "GET", "uri": "/admin/projects", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[09:46:35] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/projects\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.345849, "xdebug_link": null, "collector": "log"}, {"message": "[09:46:36] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 4,\n    \"roles\": [\n        \"bde_team\"\n    ],\n    \"current_path\": \"admin\\/projects\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": ********96.626617, "xdebug_link": null, "collector": "log"}]}, "time": {"start": ********94.587095, "end": ********96.632794, "duration": 2.045698881149292, "duration_str": "2.05s", "measures": [{"label": "Booting", "start": ********94.587095, "relative_start": 0, "end": **********.055148, "relative_end": **********.055148, "duration": 0.*****************, "duration_str": "468ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.055162, "relative_start": 0.***************, "end": ********96.632797, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.299852, "relative_start": 0.***************, "end": **********.304742, "relative_end": **********.304742, "duration": 0.0048902034759521484, "duration_str": "4.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.607634, "relative_start": 1.****************, "end": **********.607634, "relative_end": **********.607634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.615433, "relative_start": 1.****************, "end": **********.615433, "relative_end": **********.615433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.63076, "relative_start": 1.0436649322509766, "end": **********.63076, "relative_end": **********.63076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.647483, "relative_start": 1.0603880882263184, "end": **********.647483, "relative_end": **********.647483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.658875, "relative_start": 1.0717799663543701, "end": **********.658875, "relative_end": **********.658875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.664593, "relative_start": 1.077497959136963, "end": **********.664593, "relative_end": **********.664593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.669756, "relative_start": 1.0826609134674072, "end": **********.669756, "relative_end": **********.669756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.710166, "relative_start": 1.1230709552764893, "end": **********.710166, "relative_end": **********.710166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": ********96.454645, "relative_start": 1.8675498962402344, "end": ********96.454645, "relative_end": ********96.454645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": ********96.460015, "relative_start": 1.872920036315918, "end": ********96.460015, "relative_end": ********96.460015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": ********96.461559, "relative_start": 1.8744640350341797, "end": ********96.461559, "relative_end": ********96.461559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": ********96.465653, "relative_start": 1.8785579204559326, "end": ********96.465653, "relative_end": ********96.465653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": ********96.467785, "relative_start": 1.8806898593902588, "end": ********96.467785, "relative_end": ********96.467785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": ********96.473294, "relative_start": 1.8861989974975586, "end": ********96.473294, "relative_end": ********96.473294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": ********96.473718, "relative_start": 1.8866229057312012, "end": ********96.473718, "relative_end": ********96.473718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": ********96.476585, "relative_start": 1.8894898891448975, "end": ********96.476585, "relative_end": ********96.476585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": ********96.477062, "relative_start": 1.8899669647216797, "end": ********96.477062, "relative_end": ********96.477062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": ********96.479789, "relative_start": 1.8926939964294434, "end": ********96.479789, "relative_end": ********96.479789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": ********96.480161, "relative_start": 1.8930659294128418, "end": ********96.480161, "relative_end": ********96.480161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": ********96.482737, "relative_start": 1.8956420421600342, "end": ********96.482737, "relative_end": ********96.482737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": ********96.483176, "relative_start": 1.8960809707641602, "end": ********96.483176, "relative_end": ********96.483176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": ********96.598523, "relative_start": 2.011427879333496, "end": ********96.598523, "relative_end": ********96.598523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": ********96.600626, "relative_start": 2.013530969619751, "end": ********96.600626, "relative_end": ********96.600626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": ********96.614747, "relative_start": 2.0276520252227783, "end": ********96.614747, "relative_end": ********96.614747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": ********96.616873, "relative_start": 2.029778003692627, "end": ********96.616873, "relative_end": ********96.616873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": ********96.619442, "relative_start": 2.0323469638824463, "end": ********96.619442, "relative_end": ********96.619442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********96.62539, "relative_start": 2.038295030593872, "end": ********96.625838, "relative_end": ********96.625838, "duration": 0.00044798851013183594, "duration_str": "448μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": ********96.630215, "relative_start": 2.0431199073791504, "end": ********96.630298, "relative_end": ********96.630298, "duration": 8.296966552734375e-05, "duration_str": "83μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 54846104, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 26, "nb_templates": 26, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.607614, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.615409, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.630745, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.647462, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.658854, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.664566, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.669739, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.710148, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": ********96.454593, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}}, {"name": "filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": ********96.459996, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}}, {"name": "__components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": ********96.461543, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": ********96.465631, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": ********96.4677, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": ********96.473273, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": ********96.473699, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": ********96.476568, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": ********96.47703, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": ********96.479767, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": ********96.480145, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": ********96.482719, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}}, {"name": "__components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": ********96.483158, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}}, {"name": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": ********96.598464, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}}, {"name": "filament-impersonate::components.banner", "param_count": null, "params": [], "start": ********96.600589, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": ********96.614715, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}, {"name": "__components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": ********96.61685, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}}, {"name": "components.sidebar-fix-v2", "param_count": null, "params": [], "start": ********96.619416, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}}]}, "queries": {"count": 27, "nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04978999999999998, "accumulated_duration_str": "49.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f' limit 1", "type": "query", "params": [], "bindings": ["mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3134031, "duration": 0.00801, "duration_str": "8.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 16.088}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.325962, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.088, "width_percent": 1.627}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.329904, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.714, "width_percent": 1.265}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.335367, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.98, "width_percent": 1.205}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.337122, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.185, "width_percent": 0.783}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.340181, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.968, "width_percent": 0.683}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.3411589, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.651, "width_percent": 0.382}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.341868, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.033, "width_percent": 0.362}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.342568, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.394, "width_percent": 0.341}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.34325, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.735, "width_percent": 0.341}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.354235, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.077, "width_percent": 3.515}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.43218, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.592, "width_percent": 0.844}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.433585, "duration": 0.0061600000000000005, "duration_str": "6.16ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.435, "width_percent": 12.372}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 431}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.4446301, "duration": 0.012060000000000001, "duration_str": "12.06ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.807, "width_percent": 24.222}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1751449595, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:242:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:22:\\\"view_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:26:\\\"view_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:24:\\\"create_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:24:\\\"update_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:25:\\\"restore_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:29:\\\"restore_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:27:\\\"replicate_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:25:\\\"reorder_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:24:\\\"delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:28:\\\"delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:30:\\\"force_delete_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:34:\\\"force_delete_any_app::notification\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:11:\\\"view_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:15:\\\"view_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:13:\\\"create_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:13:\\\"update_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:14:\\\"restore_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:18:\\\"restore_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:16:\\\"replicate_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:14:\\\"reorder_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:13:\\\"delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:17:\\\"delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:19:\\\"force_delete_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:23:\\\"force_delete_any_client\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:14:\\\"view_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:18:\\\"view_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:16:\\\"create_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:16:\\\"update_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:17:\\\"restore_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:21:\\\"restore_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:19:\\\"replicate_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:17:\\\"reorder_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:16:\\\"delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:20:\\\"delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:22:\\\"force_delete_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:26:\\\"force_delete_any_incentive\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:20:\\\"view_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:24:\\\"view_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:22:\\\"create_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:22:\\\"update_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:23:\\\"restore_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:27:\\\"restore_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:25:\\\"replicate_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:23:\\\"reorder_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:22:\\\"delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:26:\\\"delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:28:\\\"force_delete_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:32:\\\"force_delete_any_incentive::rule\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:14:\\\"view_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:18:\\\"view_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"create_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:16:\\\"update_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:17:\\\"restore_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:21:\\\"restore_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:19:\\\"replicate_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:17:\\\"reorder_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:16:\\\"delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:20:\\\"delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:22:\\\"force_delete_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:26:\\\"force_delete_any_milestone\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:24:\\\"view_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:61;a:4:{s:1:\\\"a\\\";i:62;s:1:\\\"b\\\";s:28:\\\"view_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:62;a:4:{s:1:\\\"a\\\";i:63;s:1:\\\"b\\\";s:26:\\\"create_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:63;a:4:{s:1:\\\"a\\\";i:64;s:1:\\\"b\\\";s:26:\\\"update_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:64;a:4:{s:1:\\\"a\\\";i:65;s:1:\\\"b\\\";s:27:\\\"restore_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:65;a:4:{s:1:\\\"a\\\";i:66;s:1:\\\"b\\\";s:31:\\\"restore_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:66;a:4:{s:1:\\\"a\\\";i:67;s:1:\\\"b\\\";s:29:\\\"replicate_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:67;a:4:{s:1:\\\"a\\\";i:68;s:1:\\\"b\\\";s:27:\\\"reorder_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:68;a:4:{s:1:\\\"a\\\";i:69;s:1:\\\"b\\\";s:26:\\\"delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:69;a:4:{s:1:\\\"a\\\";i:70;s:1:\\\"b\\\";s:30:\\\"delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:70;a:4:{s:1:\\\"a\\\";i:71;s:1:\\\"b\\\";s:32:\\\"force_delete_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:71;a:4:{s:1:\\\"a\\\";i:72;s:1:\\\"b\\\";s:36:\\\"force_delete_any_notification::event\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:72;a:4:{s:1:\\\"a\\\";i:73;s:1:\\\"b\\\";s:35:\\\"view_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:73;a:4:{s:1:\\\"a\\\";i:74;s:1:\\\"b\\\";s:39:\\\"view_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:74;a:4:{s:1:\\\"a\\\";i:75;s:1:\\\"b\\\";s:37:\\\"create_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:75;a:4:{s:1:\\\"a\\\";i:76;s:1:\\\"b\\\";s:37:\\\"update_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:76;a:4:{s:1:\\\"a\\\";i:77;s:1:\\\"b\\\";s:38:\\\"restore_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:77;a:4:{s:1:\\\"a\\\";i:78;s:1:\\\"b\\\";s:42:\\\"restore_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:78;a:4:{s:1:\\\"a\\\";i:79;s:1:\\\"b\\\";s:40:\\\"replicate_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:79;a:4:{s:1:\\\"a\\\";i:80;s:1:\\\"b\\\";s:38:\\\"reorder_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:80;a:4:{s:1:\\\"a\\\";i:81;s:1:\\\"b\\\";s:37:\\\"delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:81;a:4:{s:1:\\\"a\\\";i:82;s:1:\\\"b\\\";s:41:\\\"delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:82;a:4:{s:1:\\\"a\\\";i:83;s:1:\\\"b\\\";s:43:\\\"force_delete_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:83;a:4:{s:1:\\\"a\\\";i:84;s:1:\\\"b\\\";s:47:\\\"force_delete_any_notification::role::preference\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:84;a:4:{s:1:\\\"a\\\";i:85;s:1:\\\"b\\\";s:12:\\\"view_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\\\"a\\\";i:86;s:1:\\\"b\\\";s:16:\\\"view_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\\\"a\\\";i:87;s:1:\\\"b\\\";s:14:\\\"create_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\\\"a\\\";i:88;s:1:\\\"b\\\";s:14:\\\"update_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\\\"a\\\";i:89;s:1:\\\"b\\\";s:15:\\\"restore_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:89;a:4:{s:1:\\\"a\\\";i:90;s:1:\\\"b\\\";s:19:\\\"restore_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:90;a:4:{s:1:\\\"a\\\";i:91;s:1:\\\"b\\\";s:17:\\\"replicate_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:91;a:4:{s:1:\\\"a\\\";i:92;s:1:\\\"b\\\";s:15:\\\"reorder_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:92;a:4:{s:1:\\\"a\\\";i:93;s:1:\\\"b\\\";s:14:\\\"delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\\\"a\\\";i:94;s:1:\\\"b\\\";s:18:\\\"delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:94;a:4:{s:1:\\\"a\\\";i:95;s:1:\\\"b\\\";s:20:\\\"force_delete_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:95;a:4:{s:1:\\\"a\\\";i:96;s:1:\\\"b\\\";s:24:\\\"force_delete_any_payment\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:96;a:4:{s:1:\\\"a\\\";i:97;s:1:\\\"b\\\";s:12:\\\"view_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\\\"a\\\";i:98;s:1:\\\"b\\\";s:16:\\\"view_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\\\"a\\\";i:99;s:1:\\\"b\\\";s:14:\\\"create_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\\\"a\\\";i:100;s:1:\\\"b\\\";s:14:\\\"update_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\\\"a\\\";i:101;s:1:\\\"b\\\";s:15:\\\"restore_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:101;a:4:{s:1:\\\"a\\\";i:102;s:1:\\\"b\\\";s:19:\\\"restore_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:102;a:4:{s:1:\\\"a\\\";i:103;s:1:\\\"b\\\";s:17:\\\"replicate_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:103;a:4:{s:1:\\\"a\\\";i:104;s:1:\\\"b\\\";s:15:\\\"reorder_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:104;a:4:{s:1:\\\"a\\\";i:105;s:1:\\\"b\\\";s:14:\\\"delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\\\"a\\\";i:106;s:1:\\\"b\\\";s:18:\\\"delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\\\"a\\\";i:107;s:1:\\\"b\\\";s:20:\\\"force_delete_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:107;a:4:{s:1:\\\"a\\\";i:108;s:1:\\\"b\\\";s:24:\\\"force_delete_any_project\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:108;a:4:{s:1:\\\"a\\\";i:109;s:1:\\\"b\\\";s:18:\\\"view_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:109;a:4:{s:1:\\\"a\\\";i:110;s:1:\\\"b\\\";s:22:\\\"view_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:110;a:4:{s:1:\\\"a\\\";i:111;s:1:\\\"b\\\";s:20:\\\"create_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:111;a:4:{s:1:\\\"a\\\";i:112;s:1:\\\"b\\\";s:20:\\\"update_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:112;a:4:{s:1:\\\"a\\\";i:113;s:1:\\\"b\\\";s:21:\\\"restore_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:113;a:4:{s:1:\\\"a\\\";i:114;s:1:\\\"b\\\";s:25:\\\"restore_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:114;a:4:{s:1:\\\"a\\\";i:115;s:1:\\\"b\\\";s:23:\\\"replicate_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:115;a:4:{s:1:\\\"a\\\";i:116;s:1:\\\"b\\\";s:21:\\\"reorder_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:116;a:4:{s:1:\\\"a\\\";i:117;s:1:\\\"b\\\";s:20:\\\"delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:117;a:4:{s:1:\\\"a\\\";i:118;s:1:\\\"b\\\";s:24:\\\"delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:118;a:4:{s:1:\\\"a\\\";i:119;s:1:\\\"b\\\";s:26:\\\"force_delete_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:119;a:4:{s:1:\\\"a\\\";i:120;s:1:\\\"b\\\";s:30:\\\"force_delete_any_project::type\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:120;a:4:{s:1:\\\"a\\\";i:121;s:1:\\\"b\\\";s:9:\\\"view_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:121;a:4:{s:1:\\\"a\\\";i:122;s:1:\\\"b\\\";s:13:\\\"view_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:122;a:4:{s:1:\\\"a\\\";i:123;s:1:\\\"b\\\";s:11:\\\"create_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:123;a:4:{s:1:\\\"a\\\";i:124;s:1:\\\"b\\\";s:11:\\\"update_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:124;a:4:{s:1:\\\"a\\\";i:125;s:1:\\\"b\\\";s:11:\\\"delete_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:125;a:4:{s:1:\\\"a\\\";i:126;s:1:\\\"b\\\";s:15:\\\"delete_any_role\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:126;a:4:{s:1:\\\"a\\\";i:127;s:1:\\\"b\\\";s:33:\\\"view_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:127;a:4:{s:1:\\\"a\\\";i:128;s:1:\\\"b\\\";s:37:\\\"view_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:128;a:4:{s:1:\\\"a\\\";i:129;s:1:\\\"b\\\";s:35:\\\"create_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:129;a:4:{s:1:\\\"a\\\";i:130;s:1:\\\"b\\\";s:35:\\\"update_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:130;a:4:{s:1:\\\"a\\\";i:131;s:1:\\\"b\\\";s:36:\\\"restore_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:131;a:4:{s:1:\\\"a\\\";i:132;s:1:\\\"b\\\";s:40:\\\"restore_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:132;a:4:{s:1:\\\"a\\\";i:133;s:1:\\\"b\\\";s:38:\\\"replicate_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:133;a:4:{s:1:\\\"a\\\";i:134;s:1:\\\"b\\\";s:36:\\\"reorder_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:134;a:4:{s:1:\\\"a\\\";i:135;s:1:\\\"b\\\";s:35:\\\"delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:135;a:4:{s:1:\\\"a\\\";i:136;s:1:\\\"b\\\";s:39:\\\"delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:136;a:4:{s:1:\\\"a\\\";i:137;s:1:\\\"b\\\";s:41:\\\"force_delete_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:137;a:4:{s:1:\\\"a\\\";i:138;s:1:\\\"b\\\";s:45:\\\"force_delete_any_role::notification::settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:138;a:4:{s:1:\\\"a\\\";i:139;s:1:\\\"b\\\";s:10:\\\"view_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:139;a:4:{s:1:\\\"a\\\";i:140;s:1:\\\"b\\\";s:14:\\\"view_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:140;a:4:{s:1:\\\"a\\\";i:141;s:1:\\\"b\\\";s:12:\\\"create_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:141;a:4:{s:1:\\\"a\\\";i:142;s:1:\\\"b\\\";s:12:\\\"update_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:142;a:4:{s:1:\\\"a\\\";i:143;s:1:\\\"b\\\";s:13:\\\"restore_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:143;a:4:{s:1:\\\"a\\\";i:144;s:1:\\\"b\\\";s:17:\\\"restore_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:144;a:4:{s:1:\\\"a\\\";i:145;s:1:\\\"b\\\";s:15:\\\"replicate_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:145;a:4:{s:1:\\\"a\\\";i:146;s:1:\\\"b\\\";s:13:\\\"reorder_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:146;a:4:{s:1:\\\"a\\\";i:147;s:1:\\\"b\\\";s:12:\\\"delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:147;a:4:{s:1:\\\"a\\\";i:148;s:1:\\\"b\\\";s:16:\\\"delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:148;a:4:{s:1:\\\"a\\\";i:149;s:1:\\\"b\\\";s:18:\\\"force_delete_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:149;a:4:{s:1:\\\"a\\\";i:150;s:1:\\\"b\\\";s:22:\\\"force_delete_any_token\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:150;a:4:{s:1:\\\"a\\\";i:151;s:1:\\\"b\\\";s:9:\\\"view_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:151;a:4:{s:1:\\\"a\\\";i:152;s:1:\\\"b\\\";s:13:\\\"view_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:152;a:4:{s:1:\\\"a\\\";i:153;s:1:\\\"b\\\";s:11:\\\"create_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:153;a:4:{s:1:\\\"a\\\";i:154;s:1:\\\"b\\\";s:11:\\\"update_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:154;a:4:{s:1:\\\"a\\\";i:155;s:1:\\\"b\\\";s:12:\\\"restore_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:155;a:4:{s:1:\\\"a\\\";i:156;s:1:\\\"b\\\";s:16:\\\"restore_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:156;a:4:{s:1:\\\"a\\\";i:157;s:1:\\\"b\\\";s:14:\\\"replicate_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:157;a:4:{s:1:\\\"a\\\";i:158;s:1:\\\"b\\\";s:12:\\\"reorder_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:158;a:4:{s:1:\\\"a\\\";i:159;s:1:\\\"b\\\";s:11:\\\"delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:159;a:4:{s:1:\\\"a\\\";i:160;s:1:\\\"b\\\";s:15:\\\"delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:160;a:4:{s:1:\\\"a\\\";i:161;s:1:\\\"b\\\";s:17:\\\"force_delete_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:161;a:4:{s:1:\\\"a\\\";i:162;s:1:\\\"b\\\";s:21:\\\"force_delete_any_user\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:162;a:4:{s:1:\\\"a\\\";i:163;s:1:\\\"b\\\";s:17:\\\"page_BdeDashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\\\"a\\\";i:164;s:1:\\\"b\\\";s:22:\\\"page_DashboardSettings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:164;a:4:{s:1:\\\"a\\\";i:165;s:1:\\\"b\\\";s:18:\\\"page_ManageSetting\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:165;a:4:{s:1:\\\"a\\\";i:166;s:1:\\\"b\\\";s:11:\\\"page_Themes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:166;a:4:{s:1:\\\"a\\\";i:167;s:1:\\\"b\\\";s:18:\\\"page_MyProfilePage\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\\\"a\\\";i:168;s:1:\\\"b\\\";s:26:\\\"widget_NotificationsWidget\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:168;a:4:{s:1:\\\"a\\\";i:169;s:1:\\\"b\\\";s:9:\\\"view_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:169;a:4:{s:1:\\\"a\\\";i:170;s:1:\\\"b\\\";s:13:\\\"view_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:170;a:4:{s:1:\\\"a\\\";i:171;s:1:\\\"b\\\";s:11:\\\"create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:171;a:4:{s:1:\\\"a\\\";i:172;s:1:\\\"b\\\";s:11:\\\"update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:172;a:4:{s:1:\\\"a\\\";i:173;s:1:\\\"b\\\";s:12:\\\"restore_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:173;a:4:{s:1:\\\"a\\\";i:174;s:1:\\\"b\\\";s:16:\\\"restore_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:174;a:4:{s:1:\\\"a\\\";i:175;s:1:\\\"b\\\";s:14:\\\"replicate_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:175;a:4:{s:1:\\\"a\\\";i:176;s:1:\\\"b\\\";s:12:\\\"reorder_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:176;a:4:{s:1:\\\"a\\\";i:177;s:1:\\\"b\\\";s:11:\\\"delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:177;a:4:{s:1:\\\"a\\\";i:178;s:1:\\\"b\\\";s:15:\\\"delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:178;a:4:{s:1:\\\"a\\\";i:179;s:1:\\\"b\\\";s:17:\\\"force_delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:179;a:4:{s:1:\\\"a\\\";i:180;s:1:\\\"b\\\";s:21:\\\"force_delete_any_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:180;a:4:{s:1:\\\"a\\\";i:181;s:1:\\\"b\\\";s:16:\\\"book:create_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:181;a:4:{s:1:\\\"a\\\";i:182;s:1:\\\"b\\\";s:16:\\\"book:update_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:182;a:4:{s:1:\\\"a\\\";i:183;s:1:\\\"b\\\";s:16:\\\"book:delete_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:183;a:4:{s:1:\\\"a\\\";i:184;s:1:\\\"b\\\";s:20:\\\"book:pagination_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:184;a:4:{s:1:\\\"a\\\";i:185;s:1:\\\"b\\\";s:16:\\\"book:detail_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:185;a:4:{s:1:\\\"a\\\";i:186;s:1:\\\"b\\\";s:14:\\\"view_dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:186;a:4:{s:1:\\\"a\\\";i:187;s:1:\\\"b\\\";s:18:\\\"page_Bde_Dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:187;a:4:{s:1:\\\"a\\\";i:188;s:1:\\\"b\\\";s:11:\\\"export_book\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:188;a:4:{s:1:\\\"a\\\";i:189;s:1:\\\"b\\\";s:12:\\\"view_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:189;a:4:{s:1:\\\"a\\\";i:190;s:1:\\\"b\\\";s:16:\\\"view_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:190;a:4:{s:1:\\\"a\\\";i:191;s:1:\\\"b\\\";s:14:\\\"create_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:191;a:4:{s:1:\\\"a\\\";i:192;s:1:\\\"b\\\";s:14:\\\"update_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:192;a:4:{s:1:\\\"a\\\";i:193;s:1:\\\"b\\\";s:15:\\\"restore_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:193;a:4:{s:1:\\\"a\\\";i:194;s:1:\\\"b\\\";s:19:\\\"restore_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:194;a:4:{s:1:\\\"a\\\";i:195;s:1:\\\"b\\\";s:17:\\\"replicate_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:195;a:4:{s:1:\\\"a\\\";i:196;s:1:\\\"b\\\";s:15:\\\"reorder_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:196;a:4:{s:1:\\\"a\\\";i:197;s:1:\\\"b\\\";s:14:\\\"delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:197;a:4:{s:1:\\\"a\\\";i:198;s:1:\\\"b\\\";s:18:\\\"delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:198;a:4:{s:1:\\\"a\\\";i:199;s:1:\\\"b\\\";s:20:\\\"force_delete_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:199;a:4:{s:1:\\\"a\\\";i:200;s:1:\\\"b\\\";s:24:\\\"force_delete_any_contact\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:200;a:4:{s:1:\\\"a\\\";i:201;s:1:\\\"b\\\";s:9:\\\"view_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:201;a:4:{s:1:\\\"a\\\";i:202;s:1:\\\"b\\\";s:13:\\\"view_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:202;a:4:{s:1:\\\"a\\\";i:203;s:1:\\\"b\\\";s:11:\\\"create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:203;a:4:{s:1:\\\"a\\\";i:204;s:1:\\\"b\\\";s:11:\\\"update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:204;a:4:{s:1:\\\"a\\\";i:205;s:1:\\\"b\\\";s:12:\\\"restore_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:205;a:4:{s:1:\\\"a\\\";i:206;s:1:\\\"b\\\";s:16:\\\"restore_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:206;a:4:{s:1:\\\"a\\\";i:207;s:1:\\\"b\\\";s:14:\\\"replicate_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:207;a:4:{s:1:\\\"a\\\";i:208;s:1:\\\"b\\\";s:12:\\\"reorder_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:208;a:4:{s:1:\\\"a\\\";i:209;s:1:\\\"b\\\";s:11:\\\"delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:209;a:4:{s:1:\\\"a\\\";i:210;s:1:\\\"b\\\";s:15:\\\"delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:210;a:4:{s:1:\\\"a\\\";i:211;s:1:\\\"b\\\";s:17:\\\"force_delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:211;a:4:{s:1:\\\"a\\\";i:212;s:1:\\\"b\\\";s:21:\\\"force_delete_any_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:212;a:4:{s:1:\\\"a\\\";i:213;s:1:\\\"b\\\";s:16:\\\"post:create_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:213;a:4:{s:1:\\\"a\\\";i:214;s:1:\\\"b\\\";s:16:\\\"post:update_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:214;a:4:{s:1:\\\"a\\\";i:215;s:1:\\\"b\\\";s:16:\\\"post:delete_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:215;a:4:{s:1:\\\"a\\\";i:216;s:1:\\\"b\\\";s:20:\\\"post:pagination_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:216;a:4:{s:1:\\\"a\\\";i:217;s:1:\\\"b\\\";s:16:\\\"post:detail_post\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:16;}}i:217;a:4:{s:1:\\\"a\\\";i:218;s:1:\\\"b\\\";s:19:\\\"view_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:15;i:1;i:16;}}i:218;a:4:{s:1:\\\"a\\\";i:219;s:1:\\\"b\\\";s:21:\\\"create_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:219;a:4:{s:1:\\\"a\\\";i:220;s:1:\\\"b\\\";s:21:\\\"update_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:220;a:4:{s:1:\\\"a\\\";i:221;s:1:\\\"b\\\";s:21:\\\"delete_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:221;a:4:{s:1:\\\"a\\\";i:222;s:1:\\\"b\\\";s:25:\\\"view_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:15;i:1;i:16;}}i:222;a:4:{s:1:\\\"a\\\";i:223;s:1:\\\"b\\\";s:27:\\\"create_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:223;a:4:{s:1:\\\"a\\\";i:224;s:1:\\\"b\\\";s:27:\\\"update_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:224;a:4:{s:1:\\\"a\\\";i:225;s:1:\\\"b\\\";s:27:\\\"delete_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:16;}}i:225;a:4:{s:1:\\\"a\\\";i:226;s:1:\\\"b\\\";s:12:\\\"view_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:15;}}i:226;a:3:{s:1:\\\"a\\\";i:227;s:1:\\\"b\\\";s:14:\\\"create_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:227;a:3:{s:1:\\\"a\\\";i:228;s:1:\\\"b\\\";s:14:\\\"update_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:228;a:3:{s:1:\\\"a\\\";i:229;s:1:\\\"b\\\";s:14:\\\"delete_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:229;a:4:{s:1:\\\"a\\\";i:230;s:1:\\\"b\\\";s:23:\\\"view_any_pricing::model\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:15;}}i:230;a:4:{s:1:\\\"a\\\";i:231;s:1:\\\"b\\\";s:16:\\\"view_any_product\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:15;}}i:231;a:4:{s:1:\\\"a\\\";i:232;s:1:\\\"b\\\";s:29:\\\"view_any_project::status::log\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:15;}}i:232;a:3:{s:1:\\\"a\\\";i:233;s:1:\\\"b\\\";s:25:\\\"delete_any_pricing::model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:233;a:3:{s:1:\\\"a\\\";i:234;s:1:\\\"b\\\";s:18:\\\"delete_any_product\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:234;a:3:{s:1:\\\"a\\\";i:235;s:1:\\\"b\\\";s:31:\\\"delete_any_project::status::log\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:235;a:3:{s:1:\\\"a\\\";i:236;s:1:\\\"b\\\";s:21:\\\"view_any_project_type\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:236;a:3:{s:1:\\\"a\\\";i:237;s:1:\\\"b\\\";s:22:\\\"view_any_pricing_model\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:237;a:3:{s:1:\\\"a\\\";i:238;s:1:\\\"b\\\";s:23:\\\"view_any_incentive_rule\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:238;a:3:{s:1:\\\"a\\\";i:239;s:1:\\\"b\\\";s:27:\\\"view_any_notification_event\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:239;a:3:{s:1:\\\"a\\\";i:240;s:1:\\\"b\\\";s:27:\\\"view_any_dashboard_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:240;a:3:{s:1:\\\"a\\\";i:241;s:1:\\\"b\\\";s:35:\\\"view_any_role_notification_settings\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}i:241;a:3:{s:1:\\\"a\\\";i:242;s:1:\\\"b\\\";s:37:\\\"view_any_notification_role_preference\\\";s:1:\\\"c\\\";s:0:\\\"\\\";}}s:5:\\\"roles\\\";a:3:{i:0;a:3:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:8:\\\"bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"super_admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:11:\\\"jr_bde_team\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1751449595, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:242:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:22:\"view_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:26:\"view_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:24:\"create_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:24:\"update_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:25:\"restore_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:29:\"restore_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:27:\"replicate_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:25:\"reorder_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:24:\"delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:28:\"delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:30:\"force_delete_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:34:\"force_delete_any_app::notification\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:11:\"view_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:15:\"view_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"create_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:13:\"update_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:14:\"restore_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:18:\"restore_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:16:\"replicate_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:14:\"reorder_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:13:\"delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:17:\"delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"force_delete_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:23:\"force_delete_any_client\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:14:\"view_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:18:\"view_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:16:\"create_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"update_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:17:\"restore_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:21:\"restore_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:19:\"replicate_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:17:\"reorder_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:16:\"delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:22:\"force_delete_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:26:\"force_delete_any_incentive\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:24:\"view_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:22:\"create_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:22:\"update_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:23:\"restore_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:27:\"restore_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:25:\"replicate_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:23:\"reorder_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:22:\"delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:26:\"delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:28:\"force_delete_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:32:\"force_delete_any_incentive::rule\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:14:\"view_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:18:\"view_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"create_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:16:\"update_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:17:\"restore_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:21:\"restore_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:19:\"replicate_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:17:\"reorder_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:16:\"delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:20:\"delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:22:\"force_delete_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:26:\"force_delete_any_milestone\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:24:\"view_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:28:\"view_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:26:\"create_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:26:\"update_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:27:\"restore_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:31:\"restore_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:29:\"replicate_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:27:\"reorder_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:26:\"delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:30:\"delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:32:\"force_delete_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:36:\"force_delete_any_notification::event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:35:\"view_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:39:\"view_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:37:\"create_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:37:\"update_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:38:\"restore_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:42:\"restore_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:40:\"replicate_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:38:\"reorder_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:37:\"delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:41:\"delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:43:\"force_delete_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:47:\"force_delete_any_notification::role::preference\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"view_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:16:\"view_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:14:\"create_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:14:\"update_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:15:\"restore_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:19:\"restore_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:17:\"replicate_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:15:\"reorder_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:14:\"delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:18:\"delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:20:\"force_delete_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:24:\"force_delete_any_payment\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:12:\"view_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:16:\"view_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:14:\"create_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:14:\"update_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:15:\"restore_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:19:\"restore_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:17:\"replicate_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:15:\"reorder_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:14:\"delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:18:\"delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"force_delete_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:24:\"force_delete_any_project\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:18:\"view_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:22:\"view_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:20:\"create_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:20:\"update_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:21:\"restore_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:25:\"restore_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:23:\"replicate_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:21:\"reorder_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:20:\"delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:117;a:4:{s:1:\"a\";i:118;s:1:\"b\";s:24:\"delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:118;a:4:{s:1:\"a\";i:119;s:1:\"b\";s:26:\"force_delete_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:119;a:4:{s:1:\"a\";i:120;s:1:\"b\";s:30:\"force_delete_any_project::type\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:120;a:4:{s:1:\"a\";i:121;s:1:\"b\";s:9:\"view_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:121;a:4:{s:1:\"a\";i:122;s:1:\"b\";s:13:\"view_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:122;a:4:{s:1:\"a\";i:123;s:1:\"b\";s:11:\"create_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:123;a:4:{s:1:\"a\";i:124;s:1:\"b\";s:11:\"update_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:124;a:4:{s:1:\"a\";i:125;s:1:\"b\";s:11:\"delete_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:125;a:4:{s:1:\"a\";i:126;s:1:\"b\";s:15:\"delete_any_role\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:126;a:4:{s:1:\"a\";i:127;s:1:\"b\";s:33:\"view_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:127;a:4:{s:1:\"a\";i:128;s:1:\"b\";s:37:\"view_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:128;a:4:{s:1:\"a\";i:129;s:1:\"b\";s:35:\"create_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:129;a:4:{s:1:\"a\";i:130;s:1:\"b\";s:35:\"update_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:130;a:4:{s:1:\"a\";i:131;s:1:\"b\";s:36:\"restore_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:131;a:4:{s:1:\"a\";i:132;s:1:\"b\";s:40:\"restore_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:132;a:4:{s:1:\"a\";i:133;s:1:\"b\";s:38:\"replicate_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:133;a:4:{s:1:\"a\";i:134;s:1:\"b\";s:36:\"reorder_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:134;a:4:{s:1:\"a\";i:135;s:1:\"b\";s:35:\"delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:135;a:4:{s:1:\"a\";i:136;s:1:\"b\";s:39:\"delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:136;a:4:{s:1:\"a\";i:137;s:1:\"b\";s:41:\"force_delete_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:137;a:4:{s:1:\"a\";i:138;s:1:\"b\";s:45:\"force_delete_any_role::notification::settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:138;a:4:{s:1:\"a\";i:139;s:1:\"b\";s:10:\"view_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:139;a:4:{s:1:\"a\";i:140;s:1:\"b\";s:14:\"view_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:140;a:4:{s:1:\"a\";i:141;s:1:\"b\";s:12:\"create_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:141;a:4:{s:1:\"a\";i:142;s:1:\"b\";s:12:\"update_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:142;a:4:{s:1:\"a\";i:143;s:1:\"b\";s:13:\"restore_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:143;a:4:{s:1:\"a\";i:144;s:1:\"b\";s:17:\"restore_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:144;a:4:{s:1:\"a\";i:145;s:1:\"b\";s:15:\"replicate_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:145;a:4:{s:1:\"a\";i:146;s:1:\"b\";s:13:\"reorder_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:146;a:4:{s:1:\"a\";i:147;s:1:\"b\";s:12:\"delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:147;a:4:{s:1:\"a\";i:148;s:1:\"b\";s:16:\"delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:148;a:4:{s:1:\"a\";i:149;s:1:\"b\";s:18:\"force_delete_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:149;a:4:{s:1:\"a\";i:150;s:1:\"b\";s:22:\"force_delete_any_token\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:150;a:4:{s:1:\"a\";i:151;s:1:\"b\";s:9:\"view_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:151;a:4:{s:1:\"a\";i:152;s:1:\"b\";s:13:\"view_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:152;a:4:{s:1:\"a\";i:153;s:1:\"b\";s:11:\"create_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:153;a:4:{s:1:\"a\";i:154;s:1:\"b\";s:11:\"update_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:154;a:4:{s:1:\"a\";i:155;s:1:\"b\";s:12:\"restore_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:155;a:4:{s:1:\"a\";i:156;s:1:\"b\";s:16:\"restore_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:156;a:4:{s:1:\"a\";i:157;s:1:\"b\";s:14:\"replicate_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:157;a:4:{s:1:\"a\";i:158;s:1:\"b\";s:12:\"reorder_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:158;a:4:{s:1:\"a\";i:159;s:1:\"b\";s:11:\"delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:159;a:4:{s:1:\"a\";i:160;s:1:\"b\";s:15:\"delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:160;a:4:{s:1:\"a\";i:161;s:1:\"b\";s:17:\"force_delete_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:161;a:4:{s:1:\"a\";i:162;s:1:\"b\";s:21:\"force_delete_any_user\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:162;a:4:{s:1:\"a\";i:163;s:1:\"b\";s:17:\"page_BdeDashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:163;a:4:{s:1:\"a\";i:164;s:1:\"b\";s:22:\"page_DashboardSettings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:164;a:4:{s:1:\"a\";i:165;s:1:\"b\";s:18:\"page_ManageSetting\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:165;a:4:{s:1:\"a\";i:166;s:1:\"b\";s:11:\"page_Themes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:166;a:4:{s:1:\"a\";i:167;s:1:\"b\";s:18:\"page_MyProfilePage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:15;i:1;i:1;i:2;i:16;}}i:167;a:4:{s:1:\"a\";i:168;s:1:\"b\";s:26:\"widget_NotificationsWidget\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:168;a:4:{s:1:\"a\";i:169;s:1:\"b\";s:9:\"view_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:169;a:4:{s:1:\"a\";i:170;s:1:\"b\";s:13:\"view_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:170;a:4:{s:1:\"a\";i:171;s:1:\"b\";s:11:\"create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:171;a:4:{s:1:\"a\";i:172;s:1:\"b\";s:11:\"update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:172;a:4:{s:1:\"a\";i:173;s:1:\"b\";s:12:\"restore_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:173;a:4:{s:1:\"a\";i:174;s:1:\"b\";s:16:\"restore_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:174;a:4:{s:1:\"a\";i:175;s:1:\"b\";s:14:\"replicate_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:175;a:4:{s:1:\"a\";i:176;s:1:\"b\";s:12:\"reorder_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:176;a:4:{s:1:\"a\";i:177;s:1:\"b\";s:11:\"delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:177;a:4:{s:1:\"a\";i:178;s:1:\"b\";s:15:\"delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:178;a:4:{s:1:\"a\";i:179;s:1:\"b\";s:17:\"force_delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:179;a:4:{s:1:\"a\";i:180;s:1:\"b\";s:21:\"force_delete_any_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:180;a:4:{s:1:\"a\";i:181;s:1:\"b\";s:16:\"book:create_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:181;a:4:{s:1:\"a\";i:182;s:1:\"b\";s:16:\"book:update_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:182;a:4:{s:1:\"a\";i:183;s:1:\"b\";s:16:\"book:delete_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:183;a:4:{s:1:\"a\";i:184;s:1:\"b\";s:20:\"book:pagination_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:184;a:4:{s:1:\"a\";i:185;s:1:\"b\";s:16:\"book:detail_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:185;a:4:{s:1:\"a\";i:186;s:1:\"b\";s:14:\"view_dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:186;a:4:{s:1:\"a\";i:187;s:1:\"b\";s:18:\"page_Bde_Dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:187;a:4:{s:1:\"a\";i:188;s:1:\"b\";s:11:\"export_book\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:188;a:4:{s:1:\"a\";i:189;s:1:\"b\";s:12:\"view_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:189;a:4:{s:1:\"a\";i:190;s:1:\"b\";s:16:\"view_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:190;a:4:{s:1:\"a\";i:191;s:1:\"b\";s:14:\"create_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:191;a:4:{s:1:\"a\";i:192;s:1:\"b\";s:14:\"update_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:192;a:4:{s:1:\"a\";i:193;s:1:\"b\";s:15:\"restore_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:193;a:4:{s:1:\"a\";i:194;s:1:\"b\";s:19:\"restore_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:194;a:4:{s:1:\"a\";i:195;s:1:\"b\";s:17:\"replicate_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:195;a:4:{s:1:\"a\";i:196;s:1:\"b\";s:15:\"reorder_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:196;a:4:{s:1:\"a\";i:197;s:1:\"b\";s:14:\"delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:197;a:4:{s:1:\"a\";i:198;s:1:\"b\";s:18:\"delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:198;a:4:{s:1:\"a\";i:199;s:1:\"b\";s:20:\"force_delete_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:199;a:4:{s:1:\"a\";i:200;s:1:\"b\";s:24:\"force_delete_any_contact\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:200;a:4:{s:1:\"a\";i:201;s:1:\"b\";s:9:\"view_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:201;a:4:{s:1:\"a\";i:202;s:1:\"b\";s:13:\"view_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:202;a:4:{s:1:\"a\";i:203;s:1:\"b\";s:11:\"create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:203;a:4:{s:1:\"a\";i:204;s:1:\"b\";s:11:\"update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:204;a:4:{s:1:\"a\";i:205;s:1:\"b\";s:12:\"restore_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:205;a:4:{s:1:\"a\";i:206;s:1:\"b\";s:16:\"restore_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:206;a:4:{s:1:\"a\";i:207;s:1:\"b\";s:14:\"replicate_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:207;a:4:{s:1:\"a\";i:208;s:1:\"b\";s:12:\"reorder_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:208;a:4:{s:1:\"a\";i:209;s:1:\"b\";s:11:\"delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:209;a:4:{s:1:\"a\";i:210;s:1:\"b\";s:15:\"delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:210;a:4:{s:1:\"a\";i:211;s:1:\"b\";s:17:\"force_delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:211;a:4:{s:1:\"a\";i:212;s:1:\"b\";s:21:\"force_delete_any_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:212;a:4:{s:1:\"a\";i:213;s:1:\"b\";s:16:\"post:create_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:213;a:4:{s:1:\"a\";i:214;s:1:\"b\";s:16:\"post:update_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:214;a:4:{s:1:\"a\";i:215;s:1:\"b\";s:16:\"post:delete_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:215;a:4:{s:1:\"a\";i:216;s:1:\"b\";s:20:\"post:pagination_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:216;a:4:{s:1:\"a\";i:217;s:1:\"b\";s:16:\"post:detail_post\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:16;}}i:217;a:4:{s:1:\"a\";i:218;s:1:\"b\";s:19:\"view_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:15;i:1;i:16;}}i:218;a:4:{s:1:\"a\";i:219;s:1:\"b\";s:21:\"create_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:219;a:4:{s:1:\"a\";i:220;s:1:\"b\";s:21:\"update_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:220;a:4:{s:1:\"a\";i:221;s:1:\"b\";s:21:\"delete_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:221;a:4:{s:1:\"a\";i:222;s:1:\"b\";s:25:\"view_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:15;i:1;i:16;}}i:222;a:4:{s:1:\"a\";i:223;s:1:\"b\";s:27:\"create_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:223;a:4:{s:1:\"a\";i:224;s:1:\"b\";s:27:\"update_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:224;a:4:{s:1:\"a\";i:225;s:1:\"b\";s:27:\"delete_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:16;}}i:225;a:4:{s:1:\"a\";i:226;s:1:\"b\";s:12:\"view_product\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:15;}}i:226;a:3:{s:1:\"a\";i:227;s:1:\"b\";s:14:\"create_product\";s:1:\"c\";s:3:\"web\";}i:227;a:3:{s:1:\"a\";i:228;s:1:\"b\";s:14:\"update_product\";s:1:\"c\";s:3:\"web\";}i:228;a:3:{s:1:\"a\";i:229;s:1:\"b\";s:14:\"delete_product\";s:1:\"c\";s:3:\"web\";}i:229;a:4:{s:1:\"a\";i:230;s:1:\"b\";s:23:\"view_any_pricing::model\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:15;}}i:230;a:4:{s:1:\"a\";i:231;s:1:\"b\";s:16:\"view_any_product\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:15;}}i:231;a:4:{s:1:\"a\";i:232;s:1:\"b\";s:29:\"view_any_project::status::log\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:15;}}i:232;a:3:{s:1:\"a\";i:233;s:1:\"b\";s:25:\"delete_any_pricing::model\";s:1:\"c\";s:0:\"\";}i:233;a:3:{s:1:\"a\";i:234;s:1:\"b\";s:18:\"delete_any_product\";s:1:\"c\";s:0:\"\";}i:234;a:3:{s:1:\"a\";i:235;s:1:\"b\";s:31:\"delete_any_project::status::log\";s:1:\"c\";s:0:\"\";}i:235;a:3:{s:1:\"a\";i:236;s:1:\"b\";s:21:\"view_any_project_type\";s:1:\"c\";s:0:\"\";}i:236;a:3:{s:1:\"a\";i:237;s:1:\"b\";s:22:\"view_any_pricing_model\";s:1:\"c\";s:0:\"\";}i:237;a:3:{s:1:\"a\";i:238;s:1:\"b\";s:23:\"view_any_incentive_rule\";s:1:\"c\";s:0:\"\";}i:238;a:3:{s:1:\"a\";i:239;s:1:\"b\";s:27:\"view_any_notification_event\";s:1:\"c\";s:0:\"\";}i:239;a:3:{s:1:\"a\";i:240;s:1:\"b\";s:27:\"view_any_dashboard_settings\";s:1:\"c\";s:0:\"\";}i:240;a:3:{s:1:\"a\";i:241;s:1:\"b\";s:35:\"view_any_role_notification_settings\";s:1:\"c\";s:0:\"\";}i:241;a:3:{s:1:\"a\";i:242;s:1:\"b\";s:37:\"view_any_notification_role_preference\";s:1:\"c\";s:0:\"\";}}s:5:\"roles\";a:3:{i:0;a:3:{s:1:\"a\";i:15;s:1:\"b\";s:8:\"bde_team\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"super_admin\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:16;s:1:\"b\";s:11:\"jr_bde_team\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.504501, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.029, "width_percent": 11.227}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.534976, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.256, "width_percent": 2.531}, {"sql": "select count(*) as aggregate from `projects` where `user_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6006608, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.787, "width_percent": 2.27}, {"sql": "select `pricing_models`.`name`, `pricing_models`.`id` from `pricing_models` order by `pricing_models`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.643248, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.056, "width_percent": 3.756}, {"sql": "select `users`.`name`, `users`.`id` from `users` order by `users`.`name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.6558402, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.812, "width_percent": 1.265}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 4 and `read_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": ********96.456698, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.077, "width_percent": 3.575}, {"sql": "select * from `app_notifications` where `user_id` = 4 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": ********96.4626021, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.652, "width_percent": 2.35}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": ********96.471263, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.002, "width_percent": 1.828}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": ********96.474449, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.83, "width_percent": 1.506}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": ********96.477957, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 94.336, "width_percent": 1.305}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": ********96.480882, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 95.642, "width_percent": 1.366}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": ********96.484206, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 97.007, "width_percent": 1.346}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiN2hKV0k0N3RDWVdTQWtUYVJmeVh4UnRSNVc0U01pRWs1b05iWWNJQyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzY6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9wcm9qZWN0cyI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRvLmszU0JEMVF0ejU2aEsyVS9zQ1MuSzlINzh6cUd0M1A3eUQwYWYxak9qeUpHU2tkOU5MVyI7fQ==', `last_activity` = ********96, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiN2hKV0k0N3RDWVdTQWtUYVJmeVh4UnRSNVc0U01pRWs1b05iWWNJQyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzY6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9wcm9qZWN0cyI7fXM6NTA6ImxvZ2luX3dlYl8zZGM3YTkxM2VmNWZkNGI4OTBlY2FiZTM0ODcwODU1NzNlMTZjZjgyIjtpOjQ7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRvLmszU0JEMVF0ejU2aEsyVS9zQ1MuSzlINzh6cUd0M1A3eUQwYWYxak9qeUpHU2tkOU5MVyI7fQ==", ********96, 4, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": ********96.628303, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.353, "width_percent": 1.647}]}, "models": {"data": {"App\\Models\\Role": {"value": 482, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Permission": {"value": 242, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\AppNotification": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 735, "is_counter": true}, "livewire": {"data": {"app.filament.resources.project-resource.pages.list-projects #YTXngkROQHJMkKIgSnIm": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"pricing_model_id\" => array:1 [\n        \"value\" => null\n      ]\n      \"user_id\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:1 [\n      \"client\" => array:1 [\n        \"company_name\" => true\n      ]\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.project-resource.pages.list-projects\"\n  \"component\" => \"App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects\"\n  \"id\" => \"YTXngkROQHJMkKIgSnIm\"\n]", "filament.livewire.global-search #bRAS5Qmt1mZzYIoxLkSm": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"bRAS5Qmt1mZzYIoxLkSm\"\n]", "app.filament.widgets.notification-components.notification-bell #3uCXrell06wqeZEym2cj": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 17\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"3uCXrell06wqeZEym2cj\"\n]", "filament.livewire.notifications #vz1XIswP76GqvOOkfmlS": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2967\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"vz1XIswP76GqvOOkfmlS\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 51, "messages": [{"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-690632830 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690632830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538621, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383553752 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383553752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539045, "xdebug_link": null}, {"message": "[\n  ability => reorder_project,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-294798823 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">reorder_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294798823\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.545221, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Project,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1972887055 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972887055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.545348, "xdebug_link": null}, {"message": "[\n  ability => create_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084085061 data-indent-pad=\"  \"><span class=sf-dump-note>create_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084085061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.569138, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-177877839 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177877839\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.569318, "xdebug_link": null}, {"message": "[\n  ability => delete_any_project,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1754493492 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">delete_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754493492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.573529, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Project,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1708014500 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708014500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.57369, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-15745382 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15745382\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713803, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-539187560 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539187560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.765366, "xdebug_link": null}, {"message": "[\n  ability => view_any_app::notification,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-331404341 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_app::notification </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">view_any_app::notification</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331404341\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76982, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1244422960 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244422960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770021, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612519627 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612519627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77089, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1927031079 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927031079\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774868, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1683430553 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683430553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775235, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2031378723 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031378723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776898, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005522632 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005522632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780316, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1463752956 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463752956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780509, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive::rule,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-235889734 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive::rule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_incentive::rule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235889734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.892305, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-427729748 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427729748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.892551, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1344232492 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344232492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.893723, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-272416162 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272416162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.896934, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-125918509 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125918509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.897119, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::event,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1651824740 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">view_any_notification::event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651824740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.900638, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1263537436 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263537436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.900999, "xdebug_link": null}, {"message": "[\n  ability => view_any_notification::role::preference,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081040537 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_notification::role::preference </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"39 characters\">view_any_notification::role::preference</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081040537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.067093, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2134185218 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134185218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.067459, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-764211591 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764211591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.069755, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1729320103 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729320103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.07344, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1915660560 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915660560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.073606, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-674403504 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674403504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.077163, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-901140169 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901140169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.077353, "xdebug_link": null}, {"message": "[\n  ability => view_any_product,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-136925817 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136925817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.082561, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-217132050 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217132050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.082972, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-370114667 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370114667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.084458, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1723799215 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723799215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.088572, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444181373 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444181373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.088726, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::status::log,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1815567702 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::status::log </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">view_any_project::status::log</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1815567702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.171683, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078627089 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078627089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.171868, "xdebug_link": null}, {"message": "[\n  ability => view_any_project::type,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-298625571 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project::type </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view_any_project::type</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298625571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.175992, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-360854135 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360854135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.176204, "xdebug_link": null}, {"message": "[\n  ability => view_any_role::notification::settings,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2019951723 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role::notification::settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"37 characters\">view_any_role::notification::settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019951723\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.33648, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090794800 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090794800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.336897, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2091237456 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091237456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.339875, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-546133922 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546133922\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.34005, "xdebug_link": null}, {"message": "[\n  ability => view_any_user,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1164017639 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164017639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.342667, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => false,\n  user => 4,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-210262169 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210262169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.342843, "xdebug_link": null}, {"message": "[\n  ability => view_any_token,\n  target => null,\n  result => null,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1095115142 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_token </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_token</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095115142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.432403, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => false,\n  user => 4,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1192383560 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192383560\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": ********96.432596, "xdebug_link": null}, {"message": "[\n  ability => view_any_pricing::model,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-438259617 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_pricing::model </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">view_any_pricing::model</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438259617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.442537, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 4,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1927049792 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927049792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": ********96.442813, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/projects", "action_name": "filament.admin.resources.projects.index", "controller_action": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects", "uri": "GET admin/projects", "controller": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/projects", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.05s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/admin/project-types</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ii9PRmk3ejlQTzRHTStZOFlpQkRZUkE9PSIsInZhbHVlIjoibGd3UVVZM1UwTzJMcFdIaDdzTkJwZTduMHZlK3V2c2FpbVNRMkczeCtxM1dtRDVFcXFmMUhhN2gyL1kvQVcybGhiTmRaWlR0TlpBU0lqZjNBSjR6cUJLRStNM25BRzNQSGFQMDRpaFZZQzY5VUxQSUxEOFZoeldIa29aS2lZZEV3UlFPWlA2NmhJMTE1dTBVVld5RXFxeGQ5bmJFQldKNkRQRDU4UDJYM1p5YVRzWHNnWXdLUjZFbHZxNVhybHNvZFJBaXUwSE4rVXhPNFB3ckx1RVgrRUp3aFdpeStSS3FvbC9IK3VYWS82RT0iLCJtYWMiOiJiZjAwZGE5NjhmYjJmNTVhYjgyNTNhOWM3YTJjNmI4MmNmODMwMGU2ZWI0NTJhZjA0NGNiNmYzMzVhMzI0ZjBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkwzWW1JWVJ2V1kzblArdEZqMEZLNHc9PSIsInZhbHVlIjoiQ28yd1VBcVZxcVZWSHBuMEcwTnhxVzFPcnhlWmptQnBldnFGcktIRmUzUDJWV29EbjZUYW13c05zaTlBSm1GTHM2azVxaFo4QTgvWnFRS2dBcHRXOWIraDhLQWJOOCtqWXFaOWR4MExHNEJ5S1FhTWU5RytBVWcyNnFjanlDRXIiLCJtYWMiOiIxYzA4NzI5Mzk1OTVlOTA4N2NhMDQxYjlkNmFhMWViZThhYTU2OTUyZWNiM2M1MDY1ZWQzMGM5NWE4MDNjMGVlIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ilg2b2owdGh3ckM1WWJLR1dBYzRLS0E9PSIsInZhbHVlIjoiTVpxK0pnaHlnSlZMd2d0ZVdPRVA0VHBMdEtlajBkOXhHS2JDbCszYXNGbFprSm5IT3NtZ21GOXJOU1pyU3ZGK1p5NGhscTk4YWJueFNvVHd0VzZZVTZYOHlZTitXRGlYVWNUSmxxcmN1bnJDTVlCWEl5V3pNR211dVVVNm02OUwiLCJtYWMiOiJmOTRkYjRkM2I3NDllMzZkYzNjZWM2MzFhNzQ3MzExYjBkYjIyNTE4OGU3Y2FkM2QwNTU2ZjQ3ZWIxOTU0NGM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1182190281 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|3V9UEaUGqSwxxRR9V5ZDPTi63jHRtaJZ5kgsZ0VbL3DXQjB9OGul8EU2LisB|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7hJWI47tCYWSAkTaRfyXxRtR5W4SMiEk5oNbYcIC</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mKcegPgeMhYSdJSttoLnFmnQOwR4IMKQqPtYOf2f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182190281\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1620280748 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 09:46:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktGSUlnbVo0Tmx6dXpKNmJwWXgxUEE9PSIsInZhbHVlIjoieDBWeFhpM1FRdUMvOGN4dmF0Tit5Zm01NThLaXpmNUtia1ZiaVhDWGhmbzM5TElMamJKVzZvcHpsUHVuenUxZ0ZIM2Z1MVYvOVBPSHBGZm5uT2k0WU5aVHZBNVlZUUVobGpGQUhjSTBsZWoxQk9LcVlJd00vL0M3dWVqcDV3SG8iLCJtYWMiOiI0NjYzZDIzZTY4ODQ0OGY0MzhjNTFmYjAwNGU5YmQyNjBiZWVmZTJjY2IwOTdiMjg4ZDFmMTA1YzVlOTM5Yzk1IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:46:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IkJvRnk5eHV3dWRKRkRuK2t1c0lnbmc9PSIsInZhbHVlIjoiYVkxTWFkQll4V2RKWkpRT25taVpoOUJxcEgyUVBTcW1VR1NZZmdhNndETlhHQUs0TGpJN0lQeUpQdkNHbTdISjlTS2NJT3IvZURjZXZmSkFMalpBayszenJLUUdyRTF5K1BuWVZwbDRtMkh3Ry9JNWx5eGgvRmtrZVBBYVpqZ1AiLCJtYWMiOiI4Yjg3NGYyMDJiOWQ4YTIzMDA0Mjc0ZTMyMGJiNDhiNGZmZDM0NGU5MjA3MzIyOGEyMDE4NjU0OTgwNTZkODZjIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 11:46:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktGSUlnbVo0Tmx6dXpKNmJwWXgxUEE9PSIsInZhbHVlIjoieDBWeFhpM1FRdUMvOGN4dmF0Tit5Zm01NThLaXpmNUtia1ZiaVhDWGhmbzM5TElMamJKVzZvcHpsUHVuenUxZ0ZIM2Z1MVYvOVBPSHBGZm5uT2k0WU5aVHZBNVlZUUVobGpGQUhjSTBsZWoxQk9LcVlJd00vL0M3dWVqcDV3SG8iLCJtYWMiOiI0NjYzZDIzZTY4ODQ0OGY0MzhjNTFmYjAwNGU5YmQyNjBiZWVmZTJjY2IwOTdiMjg4ZDFmMTA1YzVlOTM5Yzk1IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:46:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IkJvRnk5eHV3dWRKRkRuK2t1c0lnbmc9PSIsInZhbHVlIjoiYVkxTWFkQll4V2RKWkpRT25taVpoOUJxcEgyUVBTcW1VR1NZZmdhNndETlhHQUs0TGpJN0lQeUpQdkNHbTdISjlTS2NJT3IvZURjZXZmSkFMalpBayszenJLUUdyRTF5K1BuWVZwbDRtMkh3Ry9JNWx5eGgvRmtrZVBBYVpqZ1AiLCJtYWMiOiI4Yjg3NGYyMDJiOWQ4YTIzMDA0Mjc0ZTMyMGJiNDhiNGZmZDM0NGU5MjA3MzIyOGEyMDE4NjU0OTgwNTZkODZjIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 11:46:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620280748\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-588375244 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7hJWI47tCYWSAkTaRfyXxRtR5W4SMiEk5oNbYcIC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-588375244\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/projects", "action_name": "filament.admin.resources.projects.index", "controller_action": "App\\Filament\\Resources\\ProjectResource\\Pages\\ListProjects"}, "badge": null}}