{"__meta": {"id": "01JZ2ZA069BSZPFPGDQARTQP2C", "datetime": "2025-07-01 12:27:37", "utime": **********.546261, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.600702, "end": **********.546273, "duration": 1.945570945739746, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": **********.600702, "relative_start": 0, "end": **********.966547, "relative_end": **********.966547, "duration": 0.****************, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.966558, "relative_start": 0.****************, "end": **********.546275, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.342022, "relative_start": 0.****************, "end": **********.346603, "relative_end": **********.346603, "duration": 0.004580974578857422, "duration_str": "4.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.722967, "relative_start": 1.****************, "end": **********.722967, "relative_end": **********.722967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.741436, "relative_start": 1.****************, "end": **********.741436, "relative_end": **********.741436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-shield::forms.shield-toggle", "start": **********.750327, "relative_start": 1.1496250629425049, "end": **********.750327, "relative_end": **********.750327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.760695, "relative_start": 1.1599929332733154, "end": **********.760695, "relative_end": **********.760695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.79614, "relative_start": 1.1954379081726074, "end": **********.79614, "relative_end": **********.79614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.823787, "relative_start": 1.2230849266052246, "end": **********.823787, "relative_end": **********.823787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.844396, "relative_start": 1.2436940670013428, "end": **********.844396, "relative_end": **********.844396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.880942, "relative_start": 1.2802400588989258, "end": **********.880942, "relative_end": **********.880942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.883658, "relative_start": 1.2829558849334717, "end": **********.883658, "relative_end": **********.883658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.933594, "relative_start": 1.3328919410705566, "end": **********.933594, "relative_end": **********.933594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.945931, "relative_start": 1.345228910446167, "end": **********.945931, "relative_end": **********.945931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.948934, "relative_start": 1.3482320308685303, "end": **********.948934, "relative_end": **********.948934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.957156, "relative_start": 1.3564538955688477, "end": **********.957156, "relative_end": **********.957156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.967382, "relative_start": 1.3666799068450928, "end": **********.967382, "relative_end": **********.967382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.992947, "relative_start": 1.3922450542449951, "end": **********.992947, "relative_end": **********.992947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.997959, "relative_start": 1.397256851196289, "end": **********.997959, "relative_end": **********.997959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.01212, "relative_start": 1.4114179611206055, "end": **********.01212, "relative_end": **********.01212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.015378, "relative_start": 1.4146759510040283, "end": **********.015378, "relative_end": **********.015378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.024021, "relative_start": 1.423318862915039, "end": **********.024021, "relative_end": **********.024021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.037946, "relative_start": 1.437243938446045, "end": **********.037946, "relative_end": **********.037946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.042471, "relative_start": 1.4417688846588135, "end": **********.042471, "relative_end": **********.042471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.075106, "relative_start": 1.4744038581848145, "end": **********.075106, "relative_end": **********.075106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.089387, "relative_start": 1.488684892654419, "end": **********.089387, "relative_end": **********.089387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.092351, "relative_start": 1.4916489124298096, "end": **********.092351, "relative_end": **********.092351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.097577, "relative_start": 1.4968750476837158, "end": **********.097577, "relative_end": **********.097577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.114566, "relative_start": 1.5138640403747559, "end": **********.114566, "relative_end": **********.114566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.117298, "relative_start": 1.5165958404541016, "end": **********.117298, "relative_end": **********.117298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.123252, "relative_start": 1.5225498676300049, "end": **********.123252, "relative_end": **********.123252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.137029, "relative_start": 1.5363268852233887, "end": **********.137029, "relative_end": **********.137029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.139989, "relative_start": 1.5392868518829346, "end": **********.139989, "relative_end": **********.139989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.144252, "relative_start": 1.5435500144958496, "end": **********.144252, "relative_end": **********.144252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.153739, "relative_start": 1.5530369281768799, "end": **********.153739, "relative_end": **********.153739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.156084, "relative_start": 1.5553820133209229, "end": **********.156084, "relative_end": **********.156084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.15986, "relative_start": 1.5591578483581543, "end": **********.15986, "relative_end": **********.15986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.195982, "relative_start": 1.5952799320220947, "end": **********.195982, "relative_end": **********.195982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.206393, "relative_start": 1.6056909561157227, "end": **********.206393, "relative_end": **********.206393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.229766, "relative_start": 1.6290638446807861, "end": **********.229766, "relative_end": **********.229766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.250785, "relative_start": 1.650083065032959, "end": **********.250785, "relative_end": **********.250785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.263826, "relative_start": 1.6631238460540771, "end": **********.263826, "relative_end": **********.263826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.288783, "relative_start": 1.6880810260772705, "end": **********.288783, "relative_end": **********.288783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.31423, "relative_start": 1.7135279178619385, "end": **********.31423, "relative_end": **********.31423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.320281, "relative_start": 1.7195789813995361, "end": **********.320281, "relative_end": **********.320281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.331574, "relative_start": 1.7308719158172607, "end": **********.331574, "relative_end": **********.331574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.355967, "relative_start": 1.7552649974822998, "end": **********.355967, "relative_end": **********.355967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.358763, "relative_start": 1.7580609321594238, "end": **********.358763, "relative_end": **********.358763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.364488, "relative_start": 1.7637858390808105, "end": **********.364488, "relative_end": **********.364488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.392621, "relative_start": 1.7919189929962158, "end": **********.392621, "relative_end": **********.392621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.3998, "relative_start": 1.799098014831543, "end": **********.3998, "relative_end": **********.3998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.423893, "relative_start": 1.8231909275054932, "end": **********.423893, "relative_end": **********.423893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.471367, "relative_start": 1.8706648349761963, "end": **********.471367, "relative_end": **********.471367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.479042, "relative_start": 1.8783400058746338, "end": **********.479042, "relative_end": **********.479042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.484436, "relative_start": 1.8837339878082275, "end": **********.484436, "relative_end": **********.484436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.49935, "relative_start": 1.8986480236053467, "end": **********.49935, "relative_end": **********.49935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.50431, "relative_start": 1.9036078453063965, "end": **********.50431, "relative_end": **********.50431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.510665, "relative_start": 1.9099628925323486, "end": **********.510665, "relative_end": **********.510665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.516717, "relative_start": 1.9160149097442627, "end": **********.516717, "relative_end": **********.516717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.523186, "relative_start": 1.9224839210510254, "end": **********.523186, "relative_end": **********.523186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.531839, "relative_start": 1.9311368465423584, "end": **********.531839, "relative_end": **********.531839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.542852, "relative_start": 1.9421498775482178, "end": **********.544619, "relative_end": **********.544619, "duration": 0.0017671585083007812, "duration_str": "1.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 56812488, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 58, "nb_templates": 58, "templates": [{"name": "2x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.722935, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "1x filament-shield::forms.shield-toggle", "param_count": null, "params": [], "start": **********.750304, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\/../resources/views/forms/shield-toggle.blade.phpfilament-shield::forms.shield-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fresources%2Fviews%2Fforms%2Fshield-toggle.blade.php&line=1", "ajax": false, "filename": "shield-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-shield::forms.shield-toggle"}, {"name": "1x __components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.760672, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9b0aa906eb507785d5e713f2ff316d37"}, {"name": "34x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.79612, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 34, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "17x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.844359, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 17, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}, {"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.51669, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}]}, "queries": {"count": 47, "nb_statements": 47, "nb_visible_statements": 47, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04521999999999999, "accumulated_duration_str": "45.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR' limit 1", "type": "query", "params": [], "bindings": ["MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.360399, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 1.592}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.37573, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.592, "width_percent": 1.769}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.380104, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.361, "width_percent": 1.482}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.384037, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.843, "width_percent": 1.636}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.3861098, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.479, "width_percent": 0.951}, {"sql": "select * from `roles` where `roles`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.398888, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.43, "width_percent": 2.167}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.4132018, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.598, "width_percent": 2.079}, {"sql": "select `name` from `permissions` where lower(name) not in ('view_app::notification', 'view_any_app::notification', 'create_app::notification', 'update_app::notification', 'delete_app::notification', 'delete_any_app::notification', 'view_client', 'view_any_client', 'create_client', 'update_client', 'delete_client', 'delete_any_client', 'view_incentive', 'view_any_incentive', 'create_incentive', 'update_incentive', 'delete_incentive', 'delete_any_incentive', 'view_incentive::rule', 'view_any_incentive::rule', 'create_incentive::rule', 'update_incentive::rule', 'delete_incentive::rule', 'delete_any_incentive::rule', 'view_milestone', 'view_any_milestone', 'create_milestone', 'update_milestone', 'delete_milestone', 'delete_any_milestone', 'view_notification::event', 'view_any_notification::event', 'create_notification::event', 'update_notification::event', 'delete_notification::event', 'delete_any_notification::event', 'view_notification::role::preference', 'view_any_notification::role::preference', 'create_notification::role::preference', 'update_notification::role::preference', 'delete_notification::role::preference', 'delete_any_notification::role::preference', 'view_payment', 'view_any_payment', 'create_payment', 'update_payment', 'delete_payment', 'delete_any_payment', 'view_pricing::model', 'view_any_pricing::model', 'create_pricing::model', 'update_pricing::model', 'delete_pricing::model', 'delete_any_pricing::model', 'view_product', 'view_any_product', 'create_product', 'update_product', 'delete_product', 'delete_any_product', 'view_project', 'view_any_project', 'create_project', 'update_project', 'delete_project', 'delete_any_project', 'view_project::status::log', 'view_any_project::status::log', 'create_project::status::log', 'update_project::status::log', 'delete_project::status::log', 'delete_any_project::status::log', 'view_project::type', 'view_any_project::type', 'create_project::type', 'update_project::type', 'delete_project::type', 'delete_any_project::type', 'view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role', 'view_role::notification::settings', 'view_any_role::notification::settings', 'create_role::notification::settings', 'update_role::notification::settings', 'delete_role::notification::settings', 'delete_any_role::notification::settings', 'view_user', 'view_any_user', 'create_user', 'update_user', 'delete_user', 'delete_any_user', 'page_bdedashboard', 'page_dashboardsettings', 'page_managesetting', 'page_themes', 'page_myprofilepage', '_notificationswidget', '_businessstatsoverview', '_revenueperformancechart', '_bdeperformancechart', '_clientrevenuechart', '_monthlyrevenuechart', '_paymentstatuschart', '_milestoneduevsreceivedchart', '_revenueforecastchart')", "type": "query", "params": [], "bindings": ["view_app::notification", "view_any_app::notification", "create_app::notification", "update_app::notification", "delete_app::notification", "delete_any_app::notification", "view_client", "view_any_client", "create_client", "update_client", "delete_client", "delete_any_client", "view_incentive", "view_any_incentive", "create_incentive", "update_incentive", "delete_incentive", "delete_any_incentive", "view_incentive::rule", "view_any_incentive::rule", "create_incentive::rule", "update_incentive::rule", "delete_incentive::rule", "delete_any_incentive::rule", "view_milestone", "view_any_milestone", "create_milestone", "update_milestone", "delete_milestone", "delete_any_milestone", "view_notification::event", "view_any_notification::event", "create_notification::event", "update_notification::event", "delete_notification::event", "delete_any_notification::event", "view_notification::role::preference", "view_any_notification::role::preference", "create_notification::role::preference", "update_notification::role::preference", "delete_notification::role::preference", "delete_any_notification::role::preference", "view_payment", "view_any_payment", "create_payment", "update_payment", "delete_payment", "delete_any_payment", "view_pricing::model", "view_any_pricing::model", "create_pricing::model", "update_pricing::model", "delete_pricing::model", "delete_any_pricing::model", "view_product", "view_any_product", "create_product", "update_product", "delete_product", "delete_any_product", "view_project", "view_any_project", "create_project", "update_project", "delete_project", "delete_any_project", "view_project::status::log", "view_any_project::status::log", "create_project::status::log", "update_project::status::log", "delete_project::status::log", "delete_any_project::status::log", "view_project::type", "view_any_project::type", "create_project::type", "update_project::type", "delete_project::type", "delete_any_project::type", "view_role", "view_any_role", "create_role", "update_role", "delete_role", "delete_any_role", "view_role::notification::settings", "view_any_role::notification::settings", "create_role::notification::settings", "update_role::notification::settings", "delete_role::notification::settings", "delete_any_role::notification::settings", "view_user", "view_any_user", "create_user", "update_user", "delete_user", "delete_any_user", "page_bdedashboard", "page_dashboardsettings", "page_managesetting", "page_themes", "page_myprofilepage", "_notificationswidget", "_businessstatsoverview", "_revenueperformancechart", "_bdeperformancechart", "_clientrevenuechart", "_monthlyrevenuechart", "_paymentstatuschart", "_milestoneduevsreceivedchart", "_revenueforecastchart"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, {"index": 16, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 190}, {"index": 18, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/Traits/HasShieldFormComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\Traits\\HasShieldFormComponents.php", "line": 25}, {"index": 19, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 79}], "start": **********.4959512, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "FilamentShield.php:388", "source": {"index": 14, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShield.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShield.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fbezhansalleh%2Ffilament-shield%2Fsrc%2FFilamentShield.php&line=388", "ajax": false, "filename": "FilamentShield.php", "line": "388"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.676, "width_percent": 7.253}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'jr_bde_team' and `roles`.`id` <> '16'", "type": "query", "params": [], "bindings": ["jr_bde_team", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.582249, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.93, "width_percent": 1.747}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/RefreshesPermissionCache.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\RefreshesPermissionCache.php", "line": 12}], "start": **********.5978231, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.677, "width_percent": 2.609}, {"sql": "select * from `permissions` where (`name` = 'view_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.60043, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.286, "width_percent": 2.344}, {"sql": "select * from `permissions` where (`name` = 'view_any_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.602726, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.63, "width_percent": 1.437}, {"sql": "select * from `permissions` where (`name` = 'delete_any_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.604743, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.068, "width_percent": 1.415}, {"sql": "select * from `permissions` where (`name` = 'delete_app::notification' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_app::notification", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.606549, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.483, "width_percent": 1.614}, {"sql": "select * from `permissions` where (`name` = 'view_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.608301, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.097, "width_percent": 1.172}, {"sql": "select * from `permissions` where (`name` = 'view_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.609872, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.269, "width_percent": 1.15}, {"sql": "select * from `permissions` where (`name` = 'create_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.611532, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.419, "width_percent": 1.261}, {"sql": "select * from `permissions` where (`name` = 'update_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6132178, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.68, "width_percent": 1.305}, {"sql": "select * from `permissions` where (`name` = 'delete_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.614973, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.985, "width_percent": 1.415}, {"sql": "select * from `permissions` where (`name` = 'delete_any_client' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.616537, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.4, "width_percent": 1.128}, {"sql": "select * from `permissions` where (`name` = 'view_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.618156, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.528, "width_percent": 1.548}, {"sql": "select * from `permissions` where (`name` = 'view_any_incentive' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_incentive", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.619828, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.076, "width_percent": 1.172}, {"sql": "select * from `permissions` where (`name` = 'view_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6213388, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.248, "width_percent": 2.897}, {"sql": "select * from `permissions` where (`name` = 'view_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.623637, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 43.145, "width_percent": 1.349}, {"sql": "select * from `permissions` where (`name` = 'create_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.625316, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.494, "width_percent": 2.433}, {"sql": "select * from `permissions` where (`name` = 'update_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.627564, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.926, "width_percent": 1.482}, {"sql": "select * from `permissions` where (`name` = 'delete_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.629361, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.408, "width_percent": 1.194}, {"sql": "select * from `permissions` where (`name` = 'delete_any_milestone' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_milestone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.630874, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.602, "width_percent": 1.172}, {"sql": "select * from `permissions` where (`name` = 'view_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.632456, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.774, "width_percent": 1.128}, {"sql": "select * from `permissions` where (`name` = 'view_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6341212, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.902, "width_percent": 2.609}, {"sql": "select * from `permissions` where (`name` = 'create_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.636272, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.511, "width_percent": 1.194}, {"sql": "select * from `permissions` where (`name` = 'update_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.638035, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.705, "width_percent": 1.415}, {"sql": "select * from `permissions` where (`name` = 'delete_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6397228, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.121, "width_percent": 1.327}, {"sql": "select * from `permissions` where (`name` = 'delete_any_payment' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_payment", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.641313, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.448, "width_percent": 1.106}, {"sql": "select * from `permissions` where (`name` = 'view_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6426878, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.553, "width_percent": 1.349}, {"sql": "select * from `permissions` where (`name` = 'view_any_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.644451, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.902, "width_percent": 1.305}, {"sql": "select * from `permissions` where (`name` = 'create_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["create_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.646128, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.207, "width_percent": 1.15}, {"sql": "select * from `permissions` where (`name` = 'update_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["update_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.647554, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.357, "width_percent": 1.238}, {"sql": "select * from `permissions` where (`name` = 'delete_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.649156, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.595, "width_percent": 1.15}, {"sql": "select * from `permissions` where (`name` = 'delete_any_project' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["delete_any_project", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.650702, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.745, "width_percent": 1.858}, {"sql": "select * from `permissions` where (`name` = 'view_project::status::log' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_project::status::log", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.652668, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.603, "width_percent": 1.371}, {"sql": "select * from `permissions` where (`name` = 'view_any_project::status::log' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["view_any_project::status::log", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.6547928, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.974, "width_percent": 1.747}, {"sql": "select * from `permissions` where (`name` = 'page_BdeDashboard' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_BdeDashboard", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.65665, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.721, "width_percent": 1.216}, {"sql": "select * from `permissions` where (`name` = 'page_MyProfilePage' and `guard_name` = 'web') limit 1", "type": "query", "params": [], "bindings": ["page_MyProfilePage", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.658197, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EditRole.php:46", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource%2FPages%2FEditRole.php&line=46", "ajax": false, "filename": "EditRole.php", "line": "46"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.937, "width_percent": 1.194}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 16", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.659841, "duration": 0.0074, "duration_str": "7.4ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:453", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=453", "ajax": false, "filename": "HasPermissions.php", "line": "453"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.131, "width_percent": 16.364}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 16), (2, 16), (10, 16), (9, 16), (13, 16), (14, 16), (15, 16), (16, 16), (21, 16), (22, 16), (25, 16), (26, 16), (49, 16), (50, 16), (51, 16), (52, 16), (57, 16), (58, 16), (85, 16), (86, 16), (87, 16), (88, 16), (93, 16), (94, 16), (97, 16), (98, 16), (99, 16), (100, 16), (105, 16), (106, 16), (222, 16), (232, 16), (163, 16), (167, 16)", "type": "query", "params": [], "bindings": [1, 16, 2, 16, 10, 16, 9, 16, 13, 16, 14, 16, 15, 16, 16, 16, 21, 16, 22, 16, 25, 16, 26, 16, 49, 16, 50, 16, 51, 16, 52, 16, 57, 16, 58, 16, 85, 16, 86, 16, 87, 16, 88, 16, 93, 16, 94, 16, 97, 16, 98, 16, 99, 16, 100, 16, 105, 16, 106, 16, 222, 16, 232, 16, 163, 16, 167, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 457}, {"index": 13, "namespace": null, "name": "app/Filament/Resources/RoleResource/Pages/EditRole.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource\\Pages\\EditRole.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 153}], "start": **********.6707249, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:406", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=406", "ajax": false, "filename": "HasPermissions.php", "line": "406"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.496, "width_percent": 9.465}, {"sql": "delete from `cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 143}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 554}], "start": **********.676145, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 98.961, "width_percent": 1.039}]}, "models": {"data": {"App\\Models\\Permission": {"value": 34, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 37, "is_counter": true}, "livewire": {"data": {"app.filament.resources.role-resource.pages.edit-role #dDbaj3NzED4N4GiEvBES": "array:4 [\n  \"data\" => array:20 [\n    \"permissions\" => Illuminate\\Support\\Collection {#4935\n      #items: array:34 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"delete_any_app::notification\"\n        3 => \"delete_app::notification\"\n        4 => \"view_client\"\n        5 => \"view_any_client\"\n        6 => \"create_client\"\n        7 => \"update_client\"\n        8 => \"delete_client\"\n        9 => \"delete_any_client\"\n        10 => \"view_incentive\"\n        11 => \"view_any_incentive\"\n        12 => \"view_milestone\"\n        13 => \"view_any_milestone\"\n        14 => \"create_milestone\"\n        15 => \"update_milestone\"\n        16 => \"delete_milestone\"\n        17 => \"delete_any_milestone\"\n        18 => \"view_payment\"\n        19 => \"view_any_payment\"\n        20 => \"create_payment\"\n        21 => \"update_payment\"\n        22 => \"delete_payment\"\n        23 => \"delete_any_payment\"\n        24 => \"view_project\"\n        25 => \"view_any_project\"\n        26 => \"create_project\"\n        27 => \"update_project\"\n        28 => \"delete_project\"\n        29 => \"delete_any_project\"\n        30 => \"view_project::status::log\"\n        31 => \"view_any_project::status::log\"\n        32 => \"page_BdeDashboard\"\n        33 => \"page_MyProfilePage\"\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"data\" => array:26 [\n      \"id\" => 16\n      \"name\" => \"jr_bde_team\"\n      \"guard_name\" => \"web\"\n      \"created_at\" => \"2025-06-18T15:11:18.000000Z\"\n      \"updated_at\" => \"2025-06-18T15:11:18.000000Z\"\n      \"select_all\" => false\n      \"app::notification\" => array:4 [\n        0 => \"view_app::notification\"\n        1 => \"view_any_app::notification\"\n        2 => \"delete_any_app::notification\"\n        3 => \"delete_app::notification\"\n      ]\n      \"client\" => array:6 [\n        0 => \"view_client\"\n        1 => \"view_any_client\"\n        2 => \"create_client\"\n        3 => \"update_client\"\n        4 => \"delete_client\"\n        5 => \"delete_any_client\"\n      ]\n      \"incentive\" => array:2 [\n        0 => \"view_incentive\"\n        1 => \"view_any_incentive\"\n      ]\n      \"incentive::rule\" => []\n      \"milestone\" => array:6 [\n        0 => \"view_milestone\"\n        1 => \"view_any_milestone\"\n        2 => \"create_milestone\"\n        3 => \"update_milestone\"\n        4 => \"delete_milestone\"\n        5 => \"delete_any_milestone\"\n      ]\n      \"notification::event\" => []\n      \"notification::role::preference\" => []\n      \"payment\" => array:6 [\n        0 => \"view_payment\"\n        1 => \"view_any_payment\"\n        2 => \"create_payment\"\n        3 => \"update_payment\"\n        4 => \"delete_payment\"\n        5 => \"delete_any_payment\"\n      ]\n      \"pricing::model\" => []\n      \"product\" => []\n      \"project\" => array:6 [\n        0 => \"view_project\"\n        1 => \"view_any_project\"\n        2 => \"create_project\"\n        3 => \"update_project\"\n        4 => \"delete_project\"\n        5 => \"delete_any_project\"\n      ]\n      \"project::status::log\" => array:2 [\n        0 => \"view_project::status::log\"\n        1 => \"view_any_project::status::log\"\n      ]\n      \"project::type\" => []\n      \"role\" => []\n      \"role::notification::settings\" => []\n      \"user\" => []\n      \"pages_tab\" => array:2 [\n        0 => \"page_BdeDashboard\"\n        1 => \"page_MyProfilePage\"\n      ]\n      \"widgets_tab\" => []\n      \"custom_permissions\" => array:134 [\n        0 => \"book:create_book\"\n        1 => \"book:delete_book\"\n        2 => \"book:detail_book\"\n        3 => \"book:pagination_book\"\n        4 => \"book:update_book\"\n        5 => \"create_book\"\n        6 => \"create_contact\"\n        7 => \"create_post\"\n        8 => \"create_token\"\n        9 => \"delete_any_book\"\n        10 => \"delete_any_contact\"\n        11 => \"delete_any_post\"\n        12 => \"delete_any_token\"\n        13 => \"delete_book\"\n        14 => \"delete_contact\"\n        15 => \"delete_post\"\n        16 => \"delete_token\"\n        17 => \"export_book\"\n        18 => \"force_delete_any_app::notification\"\n        19 => \"force_delete_any_book\"\n        20 => \"force_delete_any_client\"\n        21 => \"force_delete_any_contact\"\n        22 => \"force_delete_any_incentive\"\n        23 => \"force_delete_any_incentive::rule\"\n        24 => \"force_delete_any_milestone\"\n        25 => \"force_delete_any_notification::event\"\n        26 => \"force_delete_any_notification::role::preference\"\n        27 => \"force_delete_any_payment\"\n        28 => \"force_delete_any_post\"\n        29 => \"force_delete_any_project\"\n        30 => \"force_delete_any_project::type\"\n        31 => \"force_delete_any_role::notification::settings\"\n        32 => \"force_delete_any_token\"\n        33 => \"force_delete_any_user\"\n        34 => \"force_delete_app::notification\"\n        35 => \"force_delete_book\"\n        36 => \"force_delete_client\"\n        37 => \"force_delete_contact\"\n        38 => \"force_delete_incentive\"\n        39 => \"force_delete_incentive::rule\"\n        40 => \"force_delete_milestone\"\n        41 => \"force_delete_notification::event\"\n        42 => \"force_delete_notification::role::preference\"\n        43 => \"force_delete_payment\"\n        44 => \"force_delete_post\"\n        45 => \"force_delete_project\"\n        46 => \"force_delete_project::type\"\n        47 => \"force_delete_role::notification::settings\"\n        48 => \"force_delete_token\"\n        49 => \"force_delete_user\"\n        50 => \"page_Bde_Dashboard\"\n        51 => \"post:create_post\"\n        52 => \"post:delete_post\"\n        53 => \"post:detail_post\"\n        54 => \"post:pagination_post\"\n        55 => \"post:update_post\"\n        56 => \"reorder_app::notification\"\n        57 => \"reorder_book\"\n        58 => \"reorder_client\"\n        59 => \"reorder_contact\"\n        60 => \"reorder_incentive\"\n        61 => \"reorder_incentive::rule\"\n        62 => \"reorder_milestone\"\n        63 => \"reorder_notification::event\"\n        64 => \"reorder_notification::role::preference\"\n        65 => \"reorder_payment\"\n        66 => \"reorder_post\"\n        67 => \"reorder_project\"\n        68 => \"reorder_project::type\"\n        69 => \"reorder_role::notification::settings\"\n        70 => \"reorder_token\"\n        71 => \"reorder_user\"\n        72 => \"replicate_app::notification\"\n        73 => \"replicate_book\"\n        74 => \"replicate_client\"\n        75 => \"replicate_contact\"\n        76 => \"replicate_incentive\"\n        77 => \"replicate_incentive::rule\"\n        78 => \"replicate_milestone\"\n        79 => \"replicate_notification::event\"\n        80 => \"replicate_notification::role::preference\"\n        81 => \"replicate_payment\"\n        82 => \"replicate_post\"\n        83 => \"replicate_project\"\n        84 => \"replicate_project::type\"\n        85 => \"replicate_role::notification::settings\"\n        86 => \"replicate_token\"\n        87 => \"replicate_user\"\n        88 => \"restore_any_app::notification\"\n        89 => \"restore_any_book\"\n        90 => \"restore_any_client\"\n        91 => \"restore_any_contact\"\n        92 => \"restore_any_incentive\"\n        93 => \"restore_any_incentive::rule\"\n        94 => \"restore_any_milestone\"\n        95 => \"restore_any_notification::event\"\n        96 => \"restore_any_notification::role::preference\"\n        97 => \"restore_any_payment\"\n        98 => \"restore_any_post\"\n        99 => \"restore_any_project\"\n        100 => \"restore_any_project::type\"\n        101 => \"restore_any_role::notification::settings\"\n        102 => \"restore_any_token\"\n        103 => \"restore_any_user\"\n        104 => \"restore_app::notification\"\n        105 => \"restore_book\"\n        106 => \"restore_client\"\n        107 => \"restore_contact\"\n        108 => \"restore_incentive\"\n        109 => \"restore_incentive::rule\"\n        110 => \"restore_milestone\"\n        111 => \"restore_notification::event\"\n        112 => \"restore_notification::role::preference\"\n        113 => \"restore_payment\"\n        114 => \"restore_post\"\n        115 => \"restore_project\"\n        116 => \"restore_project::type\"\n        117 => \"restore_role::notification::settings\"\n        118 => \"restore_token\"\n        119 => \"restore_user\"\n        120 => \"update_book\"\n        121 => \"update_contact\"\n        122 => \"update_post\"\n        123 => \"update_token\"\n        124 => \"view_any_book\"\n        125 => \"view_any_contact\"\n        126 => \"view_any_post\"\n        127 => \"view_any_token\"\n        128 => \"view_book\"\n        129 => \"view_contact\"\n        130 => \"view_dashboard\"\n        131 => \"view_post\"\n        132 => \"view_token\"\n        133 => \"widget_NotificationsWidget\"\n      ]\n      \"team_id\" => null\n    ]\n    \"previousUrl\" => \"http://localhost:8000/admin/shield/roles\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\Role {#3896\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 16\n        \"name\" => \"jr_bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:18\"\n        \"updated_at\" => \"2025-06-18 15:11:18\"\n      ]\n      #original: array:5 [\n        \"id\" => 16\n        \"name\" => \"jr_bde_team\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-06-18 15:11:18\"\n        \"updated_at\" => \"2025-06-18 15:11:18\"\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:1 [\n        \"id\" => \"integer\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:2 [\n        0 => \"name\"\n        1 => \"description\"\n      ]\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.role-resource.pages.edit-role\"\n  \"component\" => \"App\\Filament\\Resources\\RoleResource\\Pages\\EditRole\"\n  \"id\" => \"dDbaj3NzED4N4GiEvBES\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1584131608 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584131608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418501, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1622390797 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622390797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54553, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1639961612 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639961612\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527138, "xdebug_link": null}, {"message": "[\n  ability => view,\n  target => App\\Models\\Role(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Role)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-239143109 data-indent-pad=\"  \"><span class=sf-dump-note>view App\\Models\\Role(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Role(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Role)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239143109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527559, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\RoleResource\\Pages\\EditRole@save<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FPages%2FEditRecord.php&line=134\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Resources/Pages/EditRecord.php:134-177</a>", "middleware": "web", "duration": "1.95s", "peak_memory": "62MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-646300454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-646300454\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-578836053 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"7178 characters\">{&quot;data&quot;:{&quot;permissions&quot;:null,&quot;data&quot;:[{&quot;id&quot;:16,&quot;name&quot;:&quot;jr_bde_team&quot;,&quot;guard_name&quot;:&quot;web&quot;,&quot;created_at&quot;:&quot;2025-06-18T15:11:18.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-06-18T15:11:18.000000Z&quot;,&quot;select_all&quot;:false,&quot;app::notification&quot;:[[&quot;view_app::notification&quot;,&quot;view_any_app::notification&quot;,&quot;create_app::notification&quot;,&quot;update_app::notification&quot;,&quot;delete_app::notification&quot;,&quot;delete_any_app::notification&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;client&quot;:[[&quot;view_client&quot;,&quot;view_any_client&quot;,&quot;create_client&quot;,&quot;update_client&quot;,&quot;delete_client&quot;,&quot;delete_any_client&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive&quot;:[[&quot;view_incentive&quot;,&quot;view_any_incentive&quot;,&quot;create_incentive&quot;,&quot;update_incentive&quot;,&quot;delete_incentive&quot;,&quot;delete_any_incentive&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;incentive::rule&quot;:[[&quot;view_incentive::rule&quot;,&quot;view_any_incentive::rule&quot;,&quot;create_incentive::rule&quot;,&quot;update_incentive::rule&quot;,&quot;delete_incentive::rule&quot;,&quot;delete_any_incentive::rule&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;milestone&quot;:[[&quot;view_milestone&quot;,&quot;view_any_milestone&quot;,&quot;create_milestone&quot;,&quot;update_milestone&quot;,&quot;delete_milestone&quot;,&quot;delete_any_milestone&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::event&quot;:[[&quot;view_notification::event&quot;,&quot;view_any_notification::event&quot;,&quot;create_notification::event&quot;,&quot;update_notification::event&quot;,&quot;delete_notification::event&quot;,&quot;delete_any_notification::event&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;notification::role::preference&quot;:[[&quot;view_notification::role::preference&quot;,&quot;view_any_notification::role::preference&quot;,&quot;create_notification::role::preference&quot;,&quot;update_notification::role::preference&quot;,&quot;delete_notification::role::preference&quot;,&quot;delete_any_notification::role::preference&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;payment&quot;:[[&quot;view_payment&quot;,&quot;view_any_payment&quot;,&quot;create_payment&quot;,&quot;update_payment&quot;,&quot;delete_payment&quot;,&quot;delete_any_payment&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;pricing::model&quot;:[[&quot;view_pricing::model&quot;,&quot;create_pricing::model&quot;,&quot;update_pricing::model&quot;,&quot;delete_pricing::model&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;product&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;project&quot;:[[&quot;view_project&quot;,&quot;view_any_project&quot;,&quot;create_project&quot;,&quot;update_project&quot;,&quot;delete_project&quot;,&quot;delete_any_project&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::status::log&quot;:[[&quot;view_project::status::log&quot;,&quot;create_project::status::log&quot;,&quot;update_project::status::log&quot;,&quot;delete_project::status::log&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;project::type&quot;:[[&quot;view_project::type&quot;,&quot;view_any_project::type&quot;,&quot;create_project::type&quot;,&quot;update_project::type&quot;,&quot;delete_project::type&quot;,&quot;delete_any_project::type&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;role&quot;:[[&quot;view_role&quot;,&quot;view_any_role&quot;,&quot;create_role&quot;,&quot;update_role&quot;,&quot;delete_role&quot;,&quot;delete_any_role&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;role::notification::settings&quot;:[[&quot;view_role::notification::settings&quot;,&quot;view_any_role::notification::settings&quot;,&quot;create_role::notification::settings&quot;,&quot;update_role::notification::settings&quot;,&quot;delete_role::notification::settings&quot;,&quot;delete_any_role::notification::settings&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;user&quot;:[[&quot;view_user&quot;,&quot;view_any_user&quot;,&quot;create_user&quot;,&quot;update_user&quot;,&quot;delete_user&quot;,&quot;delete_any_user&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;pages_tab&quot;:[[&quot;page_BdeDashboard&quot;,&quot;page_DashboardSettings&quot;,&quot;page_ManageSetting&quot;,&quot;page_Themes&quot;,&quot;page_MyProfilePage&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;widgets_tab&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;custom_permissions&quot;:[[&quot;book:create_book&quot;,&quot;book:delete_book&quot;,&quot;book:detail_book&quot;,&quot;book:pagination_book&quot;,&quot;book:update_book&quot;,&quot;create_book&quot;,&quot;create_contact&quot;,&quot;create_post&quot;,&quot;create_token&quot;,&quot;delete_any_book&quot;,&quot;delete_any_contact&quot;,&quot;delete_any_post&quot;,&quot;delete_any_token&quot;,&quot;delete_book&quot;,&quot;delete_contact&quot;,&quot;delete_post&quot;,&quot;delete_token&quot;,&quot;export_book&quot;,&quot;force_delete_any_app::notification&quot;,&quot;force_delete_any_book&quot;,&quot;force_delete_any_client&quot;,&quot;force_delete_any_contact&quot;,&quot;force_delete_any_incentive&quot;,&quot;force_delete_any_incentive::rule&quot;,&quot;force_delete_any_milestone&quot;,&quot;force_delete_any_notification::event&quot;,&quot;force_delete_any_notification::role::preference&quot;,&quot;force_delete_any_payment&quot;,&quot;force_delete_any_post&quot;,&quot;force_delete_any_project&quot;,&quot;force_delete_any_project::type&quot;,&quot;force_delete_any_role::notification::settings&quot;,&quot;force_delete_any_token&quot;,&quot;force_delete_any_user&quot;,&quot;force_delete_app::notification&quot;,&quot;force_delete_book&quot;,&quot;force_delete_client&quot;,&quot;force_delete_contact&quot;,&quot;force_delete_incentive&quot;,&quot;force_delete_incentive::rule&quot;,&quot;force_delete_milestone&quot;,&quot;force_delete_notification::event&quot;,&quot;force_delete_notification::role::preference&quot;,&quot;force_delete_payment&quot;,&quot;force_delete_post&quot;,&quot;force_delete_project&quot;,&quot;force_delete_project::type&quot;,&quot;force_delete_role::notification::settings&quot;,&quot;force_delete_token&quot;,&quot;force_delete_user&quot;,&quot;page_Bde_Dashboard&quot;,&quot;post:create_post&quot;,&quot;post:delete_post&quot;,&quot;post:detail_post&quot;,&quot;post:pagination_post&quot;,&quot;post:update_post&quot;,&quot;reorder_app::notification&quot;,&quot;reorder_book&quot;,&quot;reorder_client&quot;,&quot;reorder_contact&quot;,&quot;reorder_incentive&quot;,&quot;reorder_incentive::rule&quot;,&quot;reorder_milestone&quot;,&quot;reorder_notification::event&quot;,&quot;reorder_notification::role::preference&quot;,&quot;reorder_payment&quot;,&quot;reorder_post&quot;,&quot;reorder_project&quot;,&quot;reorder_project::type&quot;,&quot;reorder_role::notification::settings&quot;,&quot;reorder_token&quot;,&quot;reorder_user&quot;,&quot;replicate_app::notification&quot;,&quot;replicate_book&quot;,&quot;replicate_client&quot;,&quot;replicate_contact&quot;,&quot;replicate_incentive&quot;,&quot;replicate_incentive::rule&quot;,&quot;replicate_milestone&quot;,&quot;replicate_notification::event&quot;,&quot;replicate_notification::role::preference&quot;,&quot;replicate_payment&quot;,&quot;replicate_post&quot;,&quot;replicate_project&quot;,&quot;replicate_project::type&quot;,&quot;replicate_role::notification::settings&quot;,&quot;replicate_token&quot;,&quot;replicate_user&quot;,&quot;restore_any_app::notification&quot;,&quot;restore_any_book&quot;,&quot;restore_any_client&quot;,&quot;restore_any_contact&quot;,&quot;restore_any_incentive&quot;,&quot;restore_any_incentive::rule&quot;,&quot;restore_any_milestone&quot;,&quot;restore_any_notification::event&quot;,&quot;restore_any_notification::role::preference&quot;,&quot;restore_any_payment&quot;,&quot;restore_any_post&quot;,&quot;restore_any_project&quot;,&quot;restore_any_project::type&quot;,&quot;restore_any_role::notification::settings&quot;,&quot;restore_any_token&quot;,&quot;restore_any_user&quot;,&quot;restore_app::notification&quot;,&quot;restore_book&quot;,&quot;restore_client&quot;,&quot;restore_contact&quot;,&quot;restore_incentive&quot;,&quot;restore_incentive::rule&quot;,&quot;restore_milestone&quot;,&quot;restore_notification::event&quot;,&quot;restore_notification::role::preference&quot;,&quot;restore_payment&quot;,&quot;restore_post&quot;,&quot;restore_project&quot;,&quot;restore_project::type&quot;,&quot;restore_role::notification::settings&quot;,&quot;restore_token&quot;,&quot;restore_user&quot;,&quot;update_book&quot;,&quot;update_contact&quot;,&quot;update_post&quot;,&quot;update_token&quot;,&quot;view_any_book&quot;,&quot;view_any_contact&quot;,&quot;view_any_post&quot;,&quot;view_any_token&quot;,&quot;view_book&quot;,&quot;view_contact&quot;,&quot;view_dashboard&quot;,&quot;view_post&quot;,&quot;view_token&quot;,&quot;widget_NotificationsWidget&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;team_id&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;previousUrl&quot;:&quot;http:\\/\\/localhost:8000\\/admin\\/shield\\/roles&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeRelationManager&quot;:null,&quot;record&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Role&quot;,&quot;key&quot;:16,&quot;s&quot;:&quot;mdl&quot;}],&quot;savedDataHash&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;dDbaj3NzED4N4GiEvBES&quot;,&quot;name&quot;:&quot;app.filament.resources.role-resource.pages.edit-role&quot;,&quot;path&quot;:&quot;admin\\/shield\\/roles\\/16\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a9221286c23e5f260f19105fb20a0c6c10c7c1f1ac5a696c793cbe8c8e0486a1&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:61</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.app::notification.2</span>\" => \"<span class=sf-dump-str title=\"28 characters\">delete_any_app::notification</span>\"\n        \"<span class=sf-dump-key>data.app::notification.3</span>\" => \"<span class=sf-dump-str title=\"24 characters\">delete_app::notification</span>\"\n        \"<span class=sf-dump-key>data.app::notification.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.app::notification.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.incentive::rule.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::event.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.notification::role::preference.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pricing::model.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::status::log.1</span>\" => \"<span class=sf-dump-str title=\"29 characters\">view_any_project::status::log</span>\"\n        \"<span class=sf-dump-key>data.project::status::log.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::status::log.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.project::type.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.role::notification::settings.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.0</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.1</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.user.5</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.1</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_MyProfilePage</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.2</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.3</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n        \"<span class=sf-dump-key>data.pages_tab.4</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__rm__</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578836053\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-250705795 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">10223</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/16/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IlA3NkU5cVBrcXJRTmNJcGtoam9qRHc9PSIsInZhbHVlIjoiZVhCWldaMVhNL3dVWmxqUVNlaHRFZUo4Y2tqV3BkWFhNcWE1SEMrUHNzNng2cGJBU3J0TzkyQ3NXTnpGVXIxVFFvOUlKUGphSEZWSm9YZ0hYQkhIM3Jzd3pDamtnKzNaU0I4OTZZd3UyYmk2NHVHYzhBOVZ6b2VDUlJJc1RYOHpmWGpMbUd3aFRRVEl0aVBId20veDZyclJ0ZlQrQ1JsOGJ5TlpxL2hnZ2t0bFBxNERZTnF6SW9uUUsrNXgyKzF5dTBDYmxzdllHQTRrQnZXTkRyRmM3VkxUNzRuekZYUlF3anZMcGRETVBFRT0iLCJtYWMiOiJlZTY2Njc5NTU2YjI2NGU3MzI2OTFkYjY4MjU5MzdkMTU4YzRiN2IzODEzZTQ4Yjc5MTIxNzc4ZmIyOWU2MDJlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikl2a2tFRFB5NHBqR3A2b09od1hDU0E9PSIsInZhbHVlIjoiS1I4ZjFZZXNrUG1rbnlTck9EUjdXTHRVNHhZN0x5dXBiOXVFL09IZmpoU01JbGwra0tXK3RnNU00SnJqNkkrSmhGNFNvRk45ZmJtN0J1Z05GQVhqZS9QcCtUTUJuOVRlZURLaU9kRnB1dkRDUFJpRzFlNko4MGpxUlVmZ0hXUWwiLCJtYWMiOiJjMDMxMzM4MmMyOTNiMmJjMjJmZGRhY2EzMjUyZGRlOWZkYjE3ZmQwY2FmMWFkOGYxNzVkODZjN2FkOGU1NDA0IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkVIbjZ4Zmg2NjNNT0RJMnBGYy9VOFE9PSIsInZhbHVlIjoiUnBEUElBT1J5RWlpRlM1NDU4M1BTaitUUGR4Q1g0Nyt4YkxxcHB6T2ErK1ZLdW5hb0N5S1dpTkt4VkYySklZZGJ6TkVsQ25TTytBeFdvOFgzNmFZaFNCOENBZXhFdS9LNTM1VlloVXI3cW5EUnoxSGNyZStONEJzK0JSZDRsOSsiLCJtYWMiOiJlOWMwMDBjY2Y4ODNjMWRlNGQwOWE3MWU4MmNlYWZkODE5YzQwYzMzNjBmZTY0NDE0MGVhYWFmMWY2NjI5ODRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250705795\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1835263185 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|zrYlHWzGiGU2OAmEx4r9XqycME5QFDI5mp8mqzVho0N1zFFAYIh2GIuUVW5B|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZljtYCWMRCywhYESH2Xz6bxKlqbb16VggLDwjOR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835263185\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-12590920 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:27:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12590920\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-496850576 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IKVU4GH1M5bBS45PA5yBPgYHgOJ9jxnlk78fM8EK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://localhost:8000/admin/shield/roles/16/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>notifications</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f4953b3-01a7-482c-a537-850f0c408a80</span>\"\n        \"<span class=sf-dump-key>actions</span>\" => []\n        \"<span class=sf-dump-key>body</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>duration</span>\" => <span class=sf-dump-num>6000</span>\n        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"23 characters\">heroicon-o-check-circle</span>\"\n        \"<span class=sf-dump-key>iconColor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Saved</span>\"\n        \"<span class=sf-dump-key>view</span>\" => \"<span class=sf-dump-str title=\"36 characters\">filament-notifications::notification</span>\"\n        \"<span class=sf-dump-key>viewData</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496850576\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}