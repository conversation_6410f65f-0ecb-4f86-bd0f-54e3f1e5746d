{"__meta": {"id": "01JZ2XNKN28NTZT8M50SJ8ST6X", "datetime": "2025-07-01 11:59:00", "utime": **********.771384, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751371139.765204, "end": **********.771402, "duration": 1.0061979293823242, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1751371139.765204, "relative_start": 0, "end": **********.157515, "relative_end": **********.157515, "duration": 0.*****************, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.157531, "relative_start": 0.*****************, "end": **********.771405, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "614ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.727954, "relative_start": 0.****************, "end": **********.740689, "relative_end": **********.740689, "duration": 0.012735128402709961, "duration_str": "12.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.767493, "relative_start": 1.***************, "end": **********.768285, "relative_end": **********.768285, "duration": 0.0007920265197753906, "duration_str": "792μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.768315, "relative_start": 1.****************, "end": **********.768351, "relative_end": **********.768351, "duration": 3.600120544433594e-05, "duration_str": "36μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "1.01s", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-178041073 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178041073\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-936283687 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-936283687\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1408534347 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/payments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InRnSUFIeWtvempaTktBRjBFWE9McGc9PSIsInZhbHVlIjoiQ2tWYXhmRjdyL1ArclpXeXFqL2JOUmJXa0tNL0ZSOVVjNDBackZSOWMvTWZtdG9JRVBjQ3JIL1dqcmxCZXRGVGwvTzBkVHp6cnFHSVBlMVFIMVZNZVY3bWEzTWlqaHRsZkUxMisyaldVMVNyOGlXT0FHajZuL2k2cGJldlI3TkZ5dFA4LysrckdjdWlZNUM1ZEFjRXgzZUlyQWQzZGZqNSsxN0JiTHRHaEZCZTlDaEhsNVFSYmtXSHphbjZZQm9oZHAwekxJZGR1KzdlV2FIbEV3UlA1Qzd5ZlkzNWkzd01XUGl5SVd3MGREWT0iLCJtYWMiOiI2NGZlZTAzMWUxNGI0ZWNjNDQ4YWFkZTgwZmE1MGRjN2U5ZDY2ZGEyZmExNzNiM2M5NTZjMzkyOTc5ODczMzhkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktlcWRiMDl3NThsejBjSUlaTUpUSUE9PSIsInZhbHVlIjoiTUo0aSs0cW9LYWxCd1VhcElYSHpJMTRsb0dpNHNncEwyR3JpUHZIeUxVN1EyQ2paSzBsN2RjeFdjMDh4QVhscEpNOUNvQ1hEaEJvVlNMWlRKSGxEMTBUREs0VmFNQXM1UnY5dFBocHcrUXFlTnBsU0g3N0hZMlZRT2xoSU9wWlgiLCJtYWMiOiJmZGYyMDQ5YmE4YzQ2NDY2NzUzMjA5MTcxMDE2MzcxOTgwOGRhNTMzMjI1ZWUyNTZmZmI3YjQ3ZGQwNDUyMjliIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkN3ejAxUmlwRVZEcitZbTBpU2ZKNFE9PSIsInZhbHVlIjoiTlVBRDJTZllPWG1JT3lQNkVsSjNLRkxwOEg4SVlpRHIxeW1TZGR3dlVSOStndFZTWkI3d2RzNTRsd0o4cmtWd2V0dU9wam9IcjFzMkxRMm1pdkhoR2d0bTJRTXl1ZU1Db0R2OE1iR3B0NzBvbG5nQ014WUpVNVNIK1VUU3pRd04iLCJtYWMiOiI2ODk3ZTllZTFiZTZlZDhkN2JmYTgxY2JkMjdlNjI1MDY3MzJmZmViMjQ1OGQwZTk3ZGE1N2Y1NjMxNzllMzBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408534347\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1807155341 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6InRnSUFIeWtvempaTktBRjBFWE9McGc9PSIsInZhbHVlIjoiQ2tWYXhmRjdyL1ArclpXeXFqL2JOUmJXa0tNL0ZSOVVjNDBackZSOWMvTWZtdG9JRVBjQ3JIL1dqcmxCZXRGVGwvTzBkVHp6cnFHSVBlMVFIMVZNZVY3bWEzTWlqaHRsZkUxMisyaldVMVNyOGlXT0FHajZuL2k2cGJldlI3TkZ5dFA4LysrckdjdWlZNUM1ZEFjRXgzZUlyQWQzZGZqNSsxN0JiTHRHaEZCZTlDaEhsNVFSYmtXSHphbjZZQm9oZHAwekxJZGR1KzdlV2FIbEV3UlA1Qzd5ZlkzNWkzd01XUGl5SVd3MGREWT0iLCJtYWMiOiI2NGZlZTAzMWUxNGI0ZWNjNDQ4YWFkZTgwZmE1MGRjN2U5ZDY2ZGEyZmExNzNiM2M5NTZjMzkyOTc5ODczMzhkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IktlcWRiMDl3NThsejBjSUlaTUpUSUE9PSIsInZhbHVlIjoiTUo0aSs0cW9LYWxCd1VhcElYSHpJMTRsb0dpNHNncEwyR3JpUHZIeUxVN1EyQ2paSzBsN2RjeFdjMDh4QVhscEpNOUNvQ1hEaEJvVlNMWlRKSGxEMTBUREs0VmFNQXM1UnY5dFBocHcrUXFlTnBsU0g3N0hZMlZRT2xoSU9wWlgiLCJtYWMiOiJmZGYyMDQ5YmE4YzQ2NDY2NzUzMjA5MTcxMDE2MzcxOTgwOGRhNTMzMjI1ZWUyNTZmZmI3YjQ3ZGQwNDUyMjliIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkN3ejAxUmlwRVZEcitZbTBpU2ZKNFE9PSIsInZhbHVlIjoiTlVBRDJTZllPWG1JT3lQNkVsSjNLRkxwOEg4SVlpRHIxeW1TZGR3dlVSOStndFZTWkI3d2RzNTRsd0o4cmtWd2V0dU9wam9IcjFzMkxRMm1pdkhoR2d0bTJRTXl1ZU1Db0R2OE1iR3B0NzBvbG5nQ014WUpVNVNIK1VUU3pRd04iLCJtYWMiOiI2ODk3ZTllZTFiZTZlZDhkN2JmYTgxY2JkMjdlNjI1MDY3MzJmZmViMjQ1OGQwZTk3ZGE1N2Y1NjMxNzllMzBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807155341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1926055839 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 11:59:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 09:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:59:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926055839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-793033246 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-793033246\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}