{"__meta": {"id": "01JZ2YHES6904E470R2BSHH4BQ", "datetime": "2025-07-01 12:14:13", "utime": ********53.28798, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********51.358804, "end": ********53.288007, "duration": 1.9292030334472656, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": ********51.358804, "relative_start": 0, "end": ********51.897752, "relative_end": ********51.897752, "duration": 0.****************, "duration_str": "539ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": ********51.897768, "relative_start": 0.***************, "end": ********53.288011, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.364927, "relative_start": 1.****************, "end": **********.367018, "relative_end": **********.367018, "duration": 0.002090930938720703, "duration_str": "2.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.655194, "relative_start": 1.****************, "end": **********.655194, "relative_end": **********.655194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.722927, "relative_start": 1.****************, "end": **********.722927, "relative_end": **********.722927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.738106, "relative_start": 1.3793020248413086, "end": **********.738106, "relative_end": **********.738106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.770034, "relative_start": 1.4112300872802734, "end": **********.770034, "relative_end": **********.770034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.773361, "relative_start": 1.4145569801330566, "end": **********.773361, "relative_end": **********.773361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.779657, "relative_start": 1.4208528995513916, "end": **********.779657, "relative_end": **********.779657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.808768, "relative_start": 1.4499640464782715, "end": **********.808768, "relative_end": **********.808768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.82538, "relative_start": 1.466576099395752, "end": **********.82538, "relative_end": **********.82538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.831391, "relative_start": 1.4725871086120605, "end": **********.831391, "relative_end": **********.831391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.846364, "relative_start": 1.4875600337982178, "end": **********.846364, "relative_end": **********.846364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.855801, "relative_start": 1.4969971179962158, "end": **********.855801, "relative_end": **********.855801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.864341, "relative_start": 1.5055370330810547, "end": **********.864341, "relative_end": **********.864341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.877538, "relative_start": 1.5187339782714844, "end": **********.877538, "relative_end": **********.877538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.886898, "relative_start": 1.5280940532684326, "end": **********.886898, "relative_end": **********.886898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.893772, "relative_start": 1.5349678993225098, "end": **********.893772, "relative_end": **********.893772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.90319, "relative_start": 1.5443859100341797, "end": **********.90319, "relative_end": **********.90319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.907576, "relative_start": 1.5487720966339111, "end": **********.907576, "relative_end": **********.907576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.913893, "relative_start": 1.555088996887207, "end": **********.913893, "relative_end": **********.913893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.920255, "relative_start": 1.5614509582519531, "end": **********.920255, "relative_end": **********.920255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.923845, "relative_start": 1.5650410652160645, "end": **********.923845, "relative_end": **********.923845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.927742, "relative_start": 1.5689380168914795, "end": **********.927742, "relative_end": **********.927742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.932938, "relative_start": 1.574134111404419, "end": **********.932938, "relative_end": **********.932938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950e37b2a26c8ff37ed662385ff017f5", "start": **********.938968, "relative_start": 1.5801639556884766, "end": **********.938968, "relative_end": **********.938968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.957315, "relative_start": 1.598510980606079, "end": **********.957315, "relative_end": **********.957315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.966793, "relative_start": 1.6079890727996826, "end": **********.966793, "relative_end": **********.966793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.969282, "relative_start": 1.6104779243469238, "end": **********.969282, "relative_end": **********.969282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.971903, "relative_start": 1.6130990982055664, "end": **********.971903, "relative_end": **********.971903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.97404, "relative_start": 1.6152360439300537, "end": **********.97404, "relative_end": **********.97404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.980698, "relative_start": 1.621894121170044, "end": **********.980698, "relative_end": **********.980698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.984101, "relative_start": 1.6252970695495605, "end": **********.984101, "relative_end": **********.984101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.988005, "relative_start": 1.6292009353637695, "end": **********.988005, "relative_end": **********.988005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.997263, "relative_start": 1.6384589672088623, "end": **********.997263, "relative_end": **********.997263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.005961, "relative_start": 1.6471569538116455, "end": ********53.005961, "relative_end": ********53.005961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.013603, "relative_start": 1.654798984527588, "end": ********53.013603, "relative_end": ********53.013603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.02139, "relative_start": 1.662585973739624, "end": ********53.02139, "relative_end": ********53.02139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.025387, "relative_start": 1.6665830612182617, "end": ********53.025387, "relative_end": ********53.025387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.029954, "relative_start": 1.6711499691009521, "end": ********53.029954, "relative_end": ********53.029954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.036143, "relative_start": 1.6773390769958496, "end": ********53.036143, "relative_end": ********53.036143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.041389, "relative_start": 1.6825850009918213, "end": ********53.041389, "relative_end": ********53.041389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.047655, "relative_start": 1.6888511180877686, "end": ********53.047655, "relative_end": ********53.047655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.052355, "relative_start": 1.6935510635375977, "end": ********53.052355, "relative_end": ********53.052355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950e37b2a26c8ff37ed662385ff017f5", "start": ********53.058207, "relative_start": 1.6994030475616455, "end": ********53.058207, "relative_end": ********53.058207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.075992, "relative_start": 1.7171881198883057, "end": ********53.075992, "relative_end": ********53.075992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.085922, "relative_start": 1.7271180152893066, "end": ********53.085922, "relative_end": ********53.085922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.08891, "relative_start": 1.7301061153411865, "end": ********53.08891, "relative_end": ********53.08891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.091708, "relative_start": 1.7329039573669434, "end": ********53.091708, "relative_end": ********53.091708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.094079, "relative_start": 1.7352750301361084, "end": ********53.094079, "relative_end": ********53.094079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.099304, "relative_start": 1.7404999732971191, "end": ********53.099304, "relative_end": ********53.099304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.102494, "relative_start": 1.743690013885498, "end": ********53.102494, "relative_end": ********53.102494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.105328, "relative_start": 1.7465240955352783, "end": ********53.105328, "relative_end": ********53.105328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.112351, "relative_start": 1.753546953201294, "end": ********53.112351, "relative_end": ********53.112351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.11873, "relative_start": 1.7599260807037354, "end": ********53.11873, "relative_end": ********53.11873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.123038, "relative_start": 1.7642340660095215, "end": ********53.123038, "relative_end": ********53.123038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.127918, "relative_start": 1.7691140174865723, "end": ********53.127918, "relative_end": ********53.127918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.130919, "relative_start": 1.7721149921417236, "end": ********53.130919, "relative_end": ********53.130919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.135671, "relative_start": 1.7768669128417969, "end": ********53.135671, "relative_end": ********53.135671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.140131, "relative_start": 1.7813270092010498, "end": ********53.140131, "relative_end": ********53.140131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.143403, "relative_start": 1.7845990657806396, "end": ********53.143403, "relative_end": ********53.143403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.146415, "relative_start": 1.7876110076904297, "end": ********53.146415, "relative_end": ********53.146415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.150587, "relative_start": 1.791783094406128, "end": ********53.150587, "relative_end": ********53.150587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950e37b2a26c8ff37ed662385ff017f5", "start": ********53.156841, "relative_start": 1.798037052154541, "end": ********53.156841, "relative_end": ********53.156841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.177976, "relative_start": 1.8191719055175781, "end": ********53.177976, "relative_end": ********53.177976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.186327, "relative_start": 1.8275229930877686, "end": ********53.186327, "relative_end": ********53.186327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.188968, "relative_start": 1.8301639556884766, "end": ********53.188968, "relative_end": ********53.188968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.191842, "relative_start": 1.833038091659546, "end": ********53.191842, "relative_end": ********53.191842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": ********53.193958, "relative_start": 1.8351540565490723, "end": ********53.193958, "relative_end": ********53.193958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.20016, "relative_start": 1.8413560390472412, "end": ********53.20016, "relative_end": ********53.20016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.205163, "relative_start": 1.8463590145111084, "end": ********53.205163, "relative_end": ********53.205163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.21053, "relative_start": 1.8517260551452637, "end": ********53.21053, "relative_end": ********53.21053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.219953, "relative_start": 1.8611490726470947, "end": ********53.219953, "relative_end": ********53.219953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": ********53.228232, "relative_start": 1.8694279193878174, "end": ********53.228232, "relative_end": ********53.228232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.236993, "relative_start": 1.8781890869140625, "end": ********53.236993, "relative_end": ********53.236993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950e37b2a26c8ff37ed662385ff017f5", "start": ********53.243131, "relative_start": 1.8843269348144531, "end": ********53.243131, "relative_end": ********53.243131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": ********53.257146, "relative_start": 1.8983418941497803, "end": ********53.257146, "relative_end": ********53.257146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": ********53.263982, "relative_start": 1.9051780700683594, "end": ********53.263982, "relative_end": ********53.263982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********53.27026, "relative_start": 1.9114561080932617, "end": ********53.27026, "relative_end": ********53.27026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": ********53.273835, "relative_start": 1.9150309562683105, "end": ********53.273835, "relative_end": ********53.273835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********53.282904, "relative_start": 1.9240999221801758, "end": ********53.284477, "relative_end": ********53.284477, "duration": 0.0015730857849121094, "duration_str": "1.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 60190192, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 77, "nb_templates": 77, "templates": [{"name": "3x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.65517, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "4x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.722907, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "16x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.779623, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 16, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "25x __components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.846338, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}, "render_count": 25, "name_original": "__components::557f112bcfd40ff4ed71d8a0603209da"}, {"name": "24x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.864322, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 24, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "4x __components::950e37b2a26c8ff37ed662385ff017f5", "param_count": null, "params": [], "start": **********.938952, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/950e37b2a26c8ff37ed662385ff017f5.blade.php__components::950e37b2a26c8ff37ed662385ff017f5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F950e37b2a26c8ff37ed662385ff017f5.blade.php&line=1", "ajax": false, "filename": "950e37b2a26c8ff37ed662385ff017f5.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::950e37b2a26c8ff37ed662385ff017f5"}, {"name": "1x __components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": ********53.263964, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::884d3416ba71745f64da4c2f0e691b0f"}]}, "queries": {"count": 107, "nb_statements": 107, "nb_visible_statements": 107, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06921000000000001, "accumulated_duration_str": "69.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 7 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `sessions` where `id` = 'OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33' limit 1", "type": "query", "params": [], "bindings": ["OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.370311, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 0.824}, {"sql": "select * from `users` where `id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.39223, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 0.824, "width_percent": 1.271}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (19) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.398227, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.095, "width_percent": 0.925}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.403119, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.02, "width_percent": 0.78}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:19', 'illuminate:cache:flexible:created:filament-excel:exports:19')", "type": "query", "params": [], "bindings": ["filament-excel:exports:19", "illuminate:cache:flexible:created:filament-excel:exports:19"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.4060938, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.8, "width_percent": 0.607}, {"sql": "select * from `clients` where `clients`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.421582, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.407, "width_percent": 0.91}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.519802, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.317, "width_percent": 1.17}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.5230951, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.488, "width_percent": 1.17}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100) and `milestones`.`id` = '100' limit 1", "type": "query", "params": [], "bindings": [26, 100, "100"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.526404, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.658, "width_percent": 1.141}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 25, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.528047, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.799, "width_percent": 0.636}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 569}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 264}], "start": **********.5338001, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:569", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=569", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "569"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.435, "width_percent": 1.416}, {"sql": "select * from `payments` where `milestone_id` = 100 limit 1", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.540819, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.851, "width_percent": 1.113}, {"sql": "select * from `payments` where `milestone_id` = 101 limit 1", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.543548, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.964, "width_percent": 0.91}, {"sql": "select * from `payments` where `milestone_id` = 102 limit 1", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.545495, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.874, "width_percent": 0.824}, {"sql": "select * from `payments` where `milestone_id` = 103 limit 1", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 570}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.5478628, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:585", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 585}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=585", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "585"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.697, "width_percent": 1.141}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.553905, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.839, "width_percent": 1.185}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.556177, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.024, "width_percent": 1.546}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5611851, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.57, "width_percent": 0.939}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5633621, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.509, "width_percent": 0.751}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5657349, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.26, "width_percent": 1.127}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.567577, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.387, "width_percent": 0.838}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5692399, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.225, "width_percent": 0.607}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.57072, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.832, "width_percent": 1.373}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.573874, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.205, "width_percent": 0.809}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5753798, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.014, "width_percent": 0.549}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.57691, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.563, "width_percent": 0.549}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.578295, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.112, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.580341, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.806, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.582062, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.499, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.584609, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 27.193, "width_percent": 0.838}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5862422, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.031, "width_percent": 0.636}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.587879, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 28.666, "width_percent": 1.084}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.589818, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 29.75, "width_percent": 0.968}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.591619, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 30.718, "width_percent": 0.766}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5931492, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.484, "width_percent": 0.665}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.595165, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.149, "width_percent": 0.694}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.596749, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 32.842, "width_percent": 0.751}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.599015, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 33.593, "width_percent": 0.997}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.6008039, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 34.59, "width_percent": 0.867}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.602468, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.457, "width_percent": 1.517}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.604701, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 36.974, "width_percent": 0.708}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 101 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [101, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.606379, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 37.682, "width_percent": 0.91}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 101 and `is_merged` = 1", "type": "query", "params": [], "bindings": [101, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.608077, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 38.593, "width_percent": 0.824}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.609687, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 39.416, "width_percent": 0.679}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.61136, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.095, "width_percent": 0.694}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.632355, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 40.789, "width_percent": 5.577}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.6377761, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 46.366, "width_percent": 1.156}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.639873, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 47.522, "width_percent": 0.722}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.6413069, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.244, "width_percent": 0.838}, {"sql": "select count(*) as aggregate from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100)", "type": "query", "params": [], "bindings": [26, 100], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.643605, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.083, "width_percent": 0.809}, {"sql": "select `milestones`.* from `milestones` inner join `projects` on `projects`.`id` = `milestones`.`project_id` where `projects`.`client_id` = 26 and `milestones`.`id` in (100) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [26, 100], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.645081, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 49.892, "width_percent": 0.78}, {"sql": "select * from `projects` where `projects`.`id` in (161)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.646519, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 50.672, "width_percent": 0.564}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.695994, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.235, "width_percent": 1.127}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.698277, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 52.362, "width_percent": 1.113}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (19) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.7055569, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 53.475, "width_percent": 1.098}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.709802, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 54.573, "width_percent": 1.011}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (19) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.7172391, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 55.584, "width_percent": 0.896}, {"sql": "select * from `projects` where `client_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.7313771, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:328", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=328", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "328"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.48, "width_percent": 1.199}, {"sql": "select * from `milestones` where `project_id` = 161 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [161, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.7333918, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:337", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 337}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=337", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "337"}, "connection": "local_kit_db", "explain": null, "start_percent": 57.68, "width_percent": 1.141}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.869508, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 58.821, "width_percent": 0.78}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.872944, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.601, "width_percent": 0.737}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.874693, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 60.338, "width_percent": 0.766}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": **********.8787918, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.104, "width_percent": 0.867}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.882598, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 61.971, "width_percent": 0.737}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.941261, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 62.708, "width_percent": 0.809}, {"sql": "select * from `milestones` where `milestones`.`id` = 100 limit 1", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 24, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.947884, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:911", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=911", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "911"}, "connection": "local_kit_db", "explain": null, "start_percent": 63.517, "width_percent": 1.228}, {"sql": "select * from `milestones` where `project_id` = 161 and `id` != 100 and `is_merged` = 0 and `merged_with_milestone_id` is null", "type": "query", "params": [], "bindings": [161, 100, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 19, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.9497209, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:921", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=921", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "921"}, "connection": "local_kit_db", "explain": null, "start_percent": 64.745, "width_percent": 0.838}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": **********.951256, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 65.583, "width_percent": 0.939}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": **********.9529061, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 66.522, "width_percent": 0.737}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": **********.9542341, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.259, "width_percent": 0.722}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 100 and `is_merged` = 1", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.96016, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 67.982, "width_percent": 0.665}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.990138, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 68.646, "width_percent": 0.954}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.992643, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 69.6, "width_percent": 0.665}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.99406, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.264, "width_percent": 0.564}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": **********.998626, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.828, "width_percent": 0.665}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.000755, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 71.493, "width_percent": 0.636}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 101 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [101, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.0607948, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 72.128, "width_percent": 1.069}, {"sql": "select * from `milestones` where `milestones`.`id` = 101 limit 1", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 24, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": ********53.0676098, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:911", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=911", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "911"}, "connection": "local_kit_db", "explain": null, "start_percent": 73.198, "width_percent": 1.127}, {"sql": "select * from `milestones` where `project_id` = 161 and `id` != 101 and `is_merged` = 0 and `merged_with_milestone_id` is null", "type": "query", "params": [], "bindings": [161, 101, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 19, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": ********53.0693848, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:921", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=921", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "921"}, "connection": "local_kit_db", "explain": null, "start_percent": 74.325, "width_percent": 0.867}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.070521, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.191, "width_percent": 0.477}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.071536, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 75.668, "width_percent": 0.405}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.072458, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 76.073, "width_percent": 1.228}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 101 and `is_merged` = 1", "type": "query", "params": [], "bindings": [101, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.079183, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.301, "width_percent": 1.994}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.1068158, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 79.295, "width_percent": 1.387}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.109217, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 80.682, "width_percent": 0.78}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.1103842, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 81.462, "width_percent": 0.636}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********53.113303, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.098, "width_percent": 0.751}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.115372, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.849, "width_percent": 0.549}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.158816, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:871", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 871}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=871", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "871"}, "connection": "local_kit_db", "explain": null, "start_percent": 83.398, "width_percent": 1.156}, {"sql": "select * from `milestones` where `milestones`.`id` = 102 limit 1", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 24, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": ********53.164642, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:911", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 911}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=911", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "911"}, "connection": "local_kit_db", "explain": null, "start_percent": 84.554, "width_percent": 0.983}, {"sql": "select * from `milestones` where `project_id` = 161 and `id` != 102 and `is_merged` = 0 and `merged_with_milestone_id` is null", "type": "query", "params": [], "bindings": [161, 102, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 19, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": ********53.1665459, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:921", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 921}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=921", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "921"}, "connection": "local_kit_db", "explain": null, "start_percent": 85.537, "width_percent": 0.852}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.16872, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.389, "width_percent": 0.694}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.1713612, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.083, "width_percent": 0.65}, {"sql": "select * from `projects` where `projects`.`id` = 161 limit 1", "type": "query", "params": [], "bindings": [161], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 922}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 27, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 28, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}], "start": ********53.17312, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:927", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 927}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=927", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "927"}, "connection": "local_kit_db", "explain": null, "start_percent": 87.733, "width_percent": 0.592}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 102 and `is_merged` = 1", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.180026, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:964", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=964", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "964"}, "connection": "local_kit_db", "explain": null, "start_percent": 88.325, "width_percent": 0.939}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.212163, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:727", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 727}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=727", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "727"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.265, "width_percent": 0.592}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 94}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.214555, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.857, "width_percent": 0.939}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": "view", "name": "filament-forms::components.text-input", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/text-input.blade.php", "line": 130}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.216202, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 90.796, "width_percent": 0.809}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeMarkedAsRequired.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeMarkedAsRequired.php", "line": 20}, {"index": 24, "namespace": "view", "name": "filament-forms::components.field-wrapper.index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/field-wrapper/index.blade.php", "line": 87}], "start": ********53.2212, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:722", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 722}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=722", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "722"}, "connection": "local_kit_db", "explain": null, "start_percent": 91.605, "width_percent": 0.78}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": "view", "name": "filament-forms::component-container", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/component-container.blade.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": ********53.224045, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:742", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ClientResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FClientResource%2FRelationManagers%2FMilestonesRelationManager.php&line=742", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "742"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.385, "width_percent": 0.679}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.2463272, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 93.065, "width_percent": 1.156}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.250952, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.22, "width_percent": 1.3}, {"sql": "select * from `milestones` where `project_id` = ? and `id` != ? and `is_merged` = ? and `merged_with_milestone_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.252267, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.521, "width_percent": 1.387}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.253767, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.908, "width_percent": 0.751}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.254531, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.659, "width_percent": 0.564}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.255137, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.223, "width_percent": 0.535}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********53.260219, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.757, "width_percent": 1.243}]}, "models": {"data": {"App\\Models\\Milestone": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\PricingModel": {"value": 38, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Project": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Payment": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Client": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FClient.php&line=1", "ajax": false, "filename": "Client.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 110, "is_counter": true}, "livewire": {"data": {"app.filament.resources.client-resource.relation-managers.milestones-relation-manager #E5dXhl92yLIuZPBN0Zu9": "array:4 [\n  \"data\" => array:40 [\n    \"ownerRecord\" => App\\Models\\Client {#3935\n      #connection: \"mysql\"\n      #table: \"clients\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"<PERSON><PERSON><PERSON>\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #original: array:16 [\n        \"id\" => 26\n        \"company_email\" => \"<EMAIL>\"\n        \"phone\" => null\n        \"address\" => null\n        \"contact_person\" => null\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 12:10:48\"\n        \"updated_at\" => \"2025-07-01 12:10:48\"\n        \"company_name\" => \" Global FinServe LLP\"\n        \"tax_id\" => \"07GFPL9988B2Z3\"\n        \"registered_address\" => \"45, Connaught Place, New Delhi\"\n        \"official_email\" => null\n        \"company_number\" => \"+91-9654321987\"\n        \"personnel_details\" => \"[{\"name\": \"Sneha Sharma\", \"skype\": null, \"department\": \"Finance\", \"designation\": \"Finance Head\", \"mobile_number\": \"8899771122\", \"official_email\": \"<EMAIL>\", \"whatsapp_number\": \"8899771122\"}]\"\n        \"social_media_access\" => \"[{\"password\": null, \"platform\": \"instagram\", \"username\": null}, {\"password\": null, \"platform\": \"youtube\", \"username\": null}, {\"password\": null, \"platform\": \"twitter\", \"username\": null}, {\"password\": null, \"platform\": \"website_gsa\", \"username\": null}, {\"password\": null, \"platform\": \"website_ga\", \"username\": null}, {\"password\": null, \"platform\": \"facebook\", \"username\": null}, {\"password\": null, \"platform\": \"gmb\", \"username\": null}, {\"password\": null, \"platform\": \"linkedin\", \"username\": null}]\"\n        \"created_by\" => 19\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:3 [\n        \"id\" => \"integer\"\n        \"personnel_details\" => \"array\"\n        \"social_media_access\" => \"array\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:13 [\n        0 => \"company_name\"\n        1 => \"company_email\"\n        2 => \"phone\"\n        3 => \"address\"\n        4 => \"contact_person\"\n        5 => \"status\"\n        6 => \"tax_id\"\n        7 => \"registered_address\"\n        8 => \"official_email\"\n        9 => \"company_number\"\n        10 => \"personnel_details\"\n        11 => \"social_media_access\"\n        12 => \"created_by\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"pageClass\" => \"App\\Filament\\Resources\\ClientResource\\Pages\\EditClient\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeTab\" => null\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => array:1 [\n      0 => \"editAll\"\n    ]\n    \"mountedTableActionsData\" => array:1 [\n      0 => array:1 [\n        \"milestones\" => array:4 [\n          \"4054b622-4cb5-49bf-9edf-616287968575\" => array:28 [\n            \"id\" => 100\n            \"project_id\" => 161\n            \"title\" => \"Week 1 Milestone\"\n            \"description\" => \"Auto-generated milestone 1 of 4\"\n            \"due_date\" => \"2025-07-08\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => \"0.00\"\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => \"2000.00\"\n            \"original_percentage\" => \"25.00\"\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:13:23.000000Z\"\n            \"payment_due_date\" => \"2025-07-08\"\n            \"payment_paid_date\" => \"2025-08-08\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"4000.00\"\n            \"payment_notes\" => null\n            \"merge_description\" => null\n            \"merged_milestones_guide\" => null\n            \"merge_milestone_ids\" => []\n            \"unmerge_milestone_ids\" => []\n          ]\n          \"618b95b0-6d78-40a5-b601-c9725554332a\" => array:28 [\n            \"id\" => 101\n            \"project_id\" => 161\n            \"title\" => \"Week 2 Milestone\"\n            \"description\" => \"Auto-generated milestone 2 of 4\"\n            \"due_date\" => \"2025-07-15\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:13:23.000000Z\"\n            \"payment_due_date\" => \"2025-07-15\"\n            \"payment_paid_date\" => \"2025-07-15\"\n            \"payment_method\" => \"bank_transfer\"\n            \"transaction_id\" => null\n            \"payment_status\" => \"paid\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n            \"merge_description\" => null\n            \"merged_milestones_guide\" => null\n            \"merge_milestone_ids\" => []\n            \"unmerge_milestone_ids\" => []\n          ]\n          \"60b6e038-7801-4c86-9f11-16bc407d14cc\" => array:28 [\n            \"id\" => 102\n            \"project_id\" => 161\n            \"title\" => \"Week 3 Milestone\"\n            \"description\" => \"Auto-generated milestone 3 of 4\"\n            \"due_date\" => \"2025-07-22\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"completed\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"payment_due_date\" => \"2025-07-22\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n            \"merge_description\" => null\n            \"merged_milestones_guide\" => null\n            \"merge_milestone_ids\" => []\n            \"unmerge_milestone_ids\" => []\n          ]\n          \"1d343963-ba47-4f3a-afa1-6e95802457be\" => array:28 [\n            \"id\" => 103\n            \"project_id\" => 161\n            \"title\" => \"Week 4 Milestone\"\n            \"description\" => \"Auto-generated milestone 4 of 4\"\n            \"due_date\" => \"2025-07-29\"\n            \"percentage\" => \"25.00\"\n            \"hours\" => null\n            \"amount\" => \"2000.00\"\n            \"status\" => \"pending\"\n            \"merged_with_milestone_id\" => null\n            \"is_merged\" => false\n            \"original_amount\" => null\n            \"original_percentage\" => null\n            \"original_due_date\" => null\n            \"original_hours\" => null\n            \"created_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"updated_at\" => \"2025-07-01T12:11:57.000000Z\"\n            \"payment_due_date\" => \"2025-07-29\"\n            \"payment_paid_date\" => null\n            \"payment_method\" => null\n            \"transaction_id\" => null\n            \"payment_status\" => \"pending\"\n            \"payment_amount\" => \"2000.00\"\n            \"payment_notes\" => null\n            \"merge_description\" => null\n            \"merged_milestones_guide\" => null\n            \"merge_milestone_ids\" => []\n            \"unmerge_milestone_ids\" => []\n          ]\n        ]\n      ]\n    ]\n    \"mountedTableActionsArguments\" => array:3 [\n      0 => []\n      1 => []\n      2 => []\n    ]\n    \"mountedTableActionRecord\" => \"100\"\n    \"defaultTableAction\" => []\n    \"defaultTableActionArguments\" => []\n    \"defaultTableActionRecord\" => []\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => null\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"milestonesRelationManagerPage\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.client-resource.relation-managers.milestones-relation-manager\"\n  \"component\" => \"App\\Filament\\Resources\\ClientResource\\RelationManagers\\MilestonesRelationManager\"\n  \"id\" => \"E5dXhl92yLIuZPBN0Zu9\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-16014302 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16014302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.719895, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1040222057 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040222057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720716, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 19,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114513856 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114513856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.737218, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=100),\n  result => true,\n  user => 19,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1524473926 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=100)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\Milestone(id=100)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524473926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73745, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.94s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1040788494 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1040788494\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"5190 characters\">{&quot;data&quot;:{&quot;ownerRecord&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Client&quot;,&quot;key&quot;:26,&quot;s&quot;:&quot;mdl&quot;}],&quot;pageClass&quot;:&quot;App\\\\Filament\\\\Resources\\\\ClientResource\\\\Pages\\\\EditClient&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeTab&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableRecordsPerPage&quot;:10,&quot;isTableReordering&quot;:false,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;editAll&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;milestones&quot;:[{&quot;4054b622-4cb5-49bf-9edf-616287968575&quot;:[{&quot;id&quot;:100,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-08&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:&quot;0.00&quot;,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:&quot;2000.00&quot;,&quot;original_percentage&quot;:&quot;25.00&quot;,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:13:23.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-08&quot;,&quot;payment_paid_date&quot;:&quot;2025-08-08&quot;,&quot;payment_method&quot;:&quot;bank_transfer&quot;,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;paid&quot;,&quot;payment_amount&quot;:&quot;4000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;618b95b0-6d78-40a5-b601-c9725554332a&quot;:[{&quot;id&quot;:101,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 2 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 2 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-15&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:13:23.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-15&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;60b6e038-7801-4c86-9f11-16bc407d14cc&quot;:[{&quot;id&quot;:102,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 3 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 3 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-22&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-22&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;1d343963-ba47-4f3a-afa1-6e95802457be&quot;:[{&quot;id&quot;:103,&quot;project_id&quot;:161,&quot;title&quot;:&quot;Week 4 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 4 of 4&quot;,&quot;due_date&quot;:&quot;2025-07-29&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T12:11:57.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-07-29&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merge_description&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:&quot;100&quot;,&quot;defaultTableAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionRecord&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableFilters&quot;:null,&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;milestonesRelationManagerPage&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;E5dXhl92yLIuZPBN0Zu9&quot;,&quot;name&quot;:&quot;app.filament.resources.client-resource.relation-managers.milestones-relation-manager&quot;,&quot;path&quot;:&quot;admin\\/clients\\/26\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b983276ec4b78ef078258bc1e67ed05faeee2e378d1e861c0a03d8a3e35fe106&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.618b95b0-6d78-40a5-b601-c9725554332a.payment_paid_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-15</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.618b95b0-6d78-40a5-b601-c9725554332a.payment_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">bank_transfer</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.618b95b0-6d78-40a5-b601-c9725554332a.payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.60b6e038-7801-4c86-9f11-16bc407d14cc.status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6341</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IjBjNUpza0JkbElON1VybGp1UENRT1E9PSIsInZhbHVlIjoiTlk4ZnZiQndFRFJWV21udDhRVkRJSHg1Z09WNG9TaDlvL3NlTWZQK3cwNWxwWHNMWXUvOFhBS3ZSaFdTNFVRbkdVSU1NWUxpd2VVaTZLdDJiaGJzVTE2dE9hOHNBY1BVcURRa05LcjRodEFyeXNkT0swaGZyR0pZVUZ0TGgxZTd1MHZ1VnVqVU01VWlxL01DSE5YYjRYcHVrR2NQTmhrUXNVQ1UvSGdSdnFrMG1Fa08ydGNRNFlFcjEvSzE3UTQ2N2VqL1FQT2JZSUM4V3pJeTdsSEs2TXF0RXFyRnJEcVZEdmkxeS8raE1qZz0iLCJtYWMiOiIxZWI0YTEyZDFmMmIxN2VkZjgxMTlkNjhhNWZkZmQ1N2JmYWFlZDdhZDJlM2YzZDg3MjkxOTI2YTE3ZDNjNDU2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklEQ0dWa01laFQyZldYSWVLTWUrK0E9PSIsInZhbHVlIjoibkF4M2IxTEIwY0t2YWpycHFoTGdWcmxYV1hreVUwdC8xajNZSDh6M043Ukp6QmxDSG9VaGR2bndkaEtJSlFkRVduZHJpRG5RekhtRjYvOG5CSjBDeVEyL3VEZktaRDRuY2twKzk4SG9ZV0VybGpRZThDMHJublZZeW43TEpVMUsiLCJtYWMiOiI0ZmE2OThjMmUwYWEzZWYyN2U1NTM0NWU4YjkyMGUzNjM4MzMyMDkzMjE5ODIwNjc2ZDg1YjM4OTNmZTFiYTZiIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ik5CQTBJbk94aE5DdnhLY2pkczY2Wmc9PSIsInZhbHVlIjoiQjVoaEVJUU1aV2xqa1dweVF2ZktXUmdBNXllNHlNUnAxUUhWRlVxZzY3SU9ZWEtVa1BTNHVOL0dsNi9NdXJjUlZyQUZvK0pXc0lGN1RpcENzVGVaWFpocVJjMzVJc0hFTXUxOTNNeHpTT0RTc0tMb1JGTFBMcUpaTUtpNnlKREMiLCJtYWMiOiIzM2VmMWRlYTU2NDllYjY2ODI5N2VjMTExZjkyODk2NGFhMWFhZmY2NWEyOWI5ZWU2Y2IxYjM3OWZjNjA0MjFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-766318775 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"124 characters\">19|ceSSE69Z6lQRjclxArTEUqvoloq2EMgMZKE49WMA10W3Vcuc7S97Dvhk23GU|$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwtjHpO28OKUrEHJyt3EVlWr9UnSKhW8CBu1Ts33</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766318775\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-945312190 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 12:14:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945312190\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1853415429 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yJsGt2Z8ZjcncIi2RScqy0gaT6lvaedWM0Z1zw9m</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/admin/clients/26/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>19</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$4i3/BF1hKHKZGMiXoNExBuFENA1rfeEAHWdtJm3y4I4CZSx0eRScW</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853415429\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}