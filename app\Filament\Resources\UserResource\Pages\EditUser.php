<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use STS\FilamentImpersonate\Pages\Actions\Impersonate;
use Illuminate\Database\Eloquent\Model;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    // Handle role update when saving the user
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $record = parent::handleRecordUpdate($record, $data);

        // Sync role if it was selected
        if (isset($data['role']) && !empty($data['role'])) {
            $record->syncRoles([$data['role']]);
        }

        return $record;
    }

    // Load current role when editing
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $user = $this->getRecord();

        // Get the first role name (assuming single role per user)
        if ($user && $user->roles->isNotEmpty()) {
            $data['role'] = $user->roles->first()->name;
        }

        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            Impersonate::make()->record($this->getRecord())
            // <--
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
