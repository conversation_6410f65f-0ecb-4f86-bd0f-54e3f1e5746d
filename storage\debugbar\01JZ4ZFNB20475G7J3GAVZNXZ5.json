{"__meta": {"id": "01JZ4ZFNB20475G7J3GAVZNXZ5", "datetime": "2025-07-02 07:09:11", "utime": *********1.906808, "method": "GET", "uri": "/admin/app-notifications", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[07:09:10] LOG.debug: RedirectByRole: Middleware entered {\n    \"path\": \"admin\\/app-notifications\",\n    \"authenticated\": \"yes\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.292864, "xdebug_link": null, "collector": "log"}, {"message": "[07:09:11] LOG.debug: RedirectByRole: User check {\n    \"user_id\": 1,\n    \"roles\": [\n        \"super_admin\"\n    ],\n    \"current_path\": \"admin\\/app-notifications\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": *********1.903256, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751440149.633908, "end": *********1.906821, "duration": 2.2729129791259766, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": 1751440149.633908, "relative_start": 0, "end": **********.028851, "relative_end": **********.028851, "duration": 0.****************, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.028863, "relative_start": 0.****************, "end": *********1.906823, "relative_end": 1.9073486328125e-06, "duration": 1.***************, "duration_str": "1.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.260131, "relative_start": 0.****************, "end": **********.264352, "relative_end": **********.264352, "duration": 0.004221200942993164, "duration_str": "4.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.366201, "relative_start": 0.***************, "end": **********.366201, "relative_end": **********.366201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.374075, "relative_start": 0.****************, "end": **********.374075, "relative_end": **********.374075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.379421, "relative_start": 0.7455129623413086, "end": **********.379421, "relative_end": **********.379421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": **********.382007, "relative_start": 0.7480988502502441, "end": **********.382007, "relative_end": **********.382007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": **********.388021, "relative_start": 0.754112958908081, "end": **********.388021, "relative_end": **********.388021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.401053, "relative_start": 0.7671449184417725, "end": **********.401053, "relative_end": **********.401053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.407319, "relative_start": 0.7734110355377197, "end": **********.407319, "relative_end": **********.407319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.412637, "relative_start": 0.7787289619445801, "end": **********.412637, "relative_end": **********.412637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.453256, "relative_start": 0.8193478584289551, "end": **********.453256, "relative_end": **********.453256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.458222, "relative_start": 0.8243138790130615, "end": **********.458222, "relative_end": **********.458222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.461176, "relative_start": 0.8272678852081299, "end": **********.461176, "relative_end": **********.461176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.498407, "relative_start": 0.8644988536834717, "end": **********.498407, "relative_end": **********.498407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.502937, "relative_start": 0.8690290451049805, "end": **********.502937, "relative_end": **********.502937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.506525, "relative_start": 0.8726170063018799, "end": **********.506525, "relative_end": **********.506525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.533101, "relative_start": 0.8991930484771729, "end": **********.533101, "relative_end": **********.533101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.536627, "relative_start": 0.9027190208435059, "end": **********.536627, "relative_end": **********.536627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.53951, "relative_start": 0.905601978302002, "end": **********.53951, "relative_end": **********.53951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.569775, "relative_start": 0.9358670711517334, "end": **********.569775, "relative_end": **********.569775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.573074, "relative_start": 0.9391660690307617, "end": **********.573074, "relative_end": **********.573074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.575786, "relative_start": 0.941878080368042, "end": **********.575786, "relative_end": **********.575786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.600364, "relative_start": 0.9664559364318848, "end": **********.600364, "relative_end": **********.600364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.603785, "relative_start": 0.9698770046234131, "end": **********.603785, "relative_end": **********.603785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.60644, "relative_start": 0.9725320339202881, "end": **********.60644, "relative_end": **********.60644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.636556, "relative_start": 1.002647876739502, "end": **********.636556, "relative_end": **********.636556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.64105, "relative_start": 1.0071420669555664, "end": **********.64105, "relative_end": **********.64105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.644549, "relative_start": 1.0106408596038818, "end": **********.644549, "relative_end": **********.644549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.675593, "relative_start": 1.041684865951538, "end": **********.675593, "relative_end": **********.675593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.679429, "relative_start": 1.0455210208892822, "end": **********.679429, "relative_end": **********.679429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.682891, "relative_start": 1.048982858657837, "end": **********.682891, "relative_end": **********.682891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.714205, "relative_start": 1.0802969932556152, "end": **********.714205, "relative_end": **********.714205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.718055, "relative_start": 1.0841469764709473, "end": **********.718055, "relative_end": **********.718055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.72161, "relative_start": 1.0877020359039307, "end": **********.72161, "relative_end": **********.72161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.756598, "relative_start": 1.122689962387085, "end": **********.756598, "relative_end": **********.756598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.760292, "relative_start": 1.1263840198516846, "end": **********.760292, "relative_end": **********.760292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.763534, "relative_start": 1.1296260356903076, "end": **********.763534, "relative_end": **********.763534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.799051, "relative_start": 1.1651430130004883, "end": **********.799051, "relative_end": **********.799051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.803093, "relative_start": 1.169184923171997, "end": **********.803093, "relative_end": **********.803093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.807104, "relative_start": 1.1731960773468018, "end": **********.807104, "relative_end": **********.807104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.838251, "relative_start": 1.2043430805206299, "end": **********.838251, "relative_end": **********.838251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.841632, "relative_start": 1.20772385597229, "end": **********.841632, "relative_end": **********.841632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.844557, "relative_start": 1.210649013519287, "end": **********.844557, "relative_end": **********.844557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.878203, "relative_start": 1.2442948818206787, "end": **********.878203, "relative_end": **********.878203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.882525, "relative_start": 1.2486169338226318, "end": **********.882525, "relative_end": **********.882525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.887371, "relative_start": 1.2534630298614502, "end": **********.887371, "relative_end": **********.887371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.924719, "relative_start": 1.2908110618591309, "end": **********.924719, "relative_end": **********.924719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.928456, "relative_start": 1.2945480346679688, "end": **********.928456, "relative_end": **********.928456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.932897, "relative_start": 1.2989890575408936, "end": **********.932897, "relative_end": **********.932897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.968194, "relative_start": 1.3342859745025635, "end": **********.968194, "relative_end": **********.968194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.971743, "relative_start": 1.3378350734710693, "end": **********.971743, "relative_end": **********.971743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.974649, "relative_start": 1.3407409191131592, "end": **********.974649, "relative_end": **********.974649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.998024, "relative_start": 1.3641159534454346, "end": **********.998024, "relative_end": **********.998024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.001492, "relative_start": 1.367583990097046, "end": *********1.001492, "relative_end": *********1.001492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.005667, "relative_start": 1.3717589378356934, "end": *********1.005667, "relative_end": *********1.005667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.042206, "relative_start": 1.4082980155944824, "end": *********1.042206, "relative_end": *********1.042206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.047394, "relative_start": 1.4134860038757324, "end": *********1.047394, "relative_end": *********1.047394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.051727, "relative_start": 1.4178190231323242, "end": *********1.051727, "relative_end": *********1.051727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.086501, "relative_start": 1.4525928497314453, "end": *********1.086501, "relative_end": *********1.086501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.09251, "relative_start": 1.458601951599121, "end": *********1.09251, "relative_end": *********1.09251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.0984, "relative_start": 1.4644920825958252, "end": *********1.0984, "relative_end": *********1.0984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.126731, "relative_start": 1.4928228855133057, "end": *********1.126731, "relative_end": *********1.126731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.130703, "relative_start": 1.4967949390411377, "end": *********1.130703, "relative_end": *********1.130703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.133477, "relative_start": 1.4995689392089844, "end": *********1.133477, "relative_end": *********1.133477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.164813, "relative_start": 1.53090500831604, "end": *********1.164813, "relative_end": *********1.164813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.16912, "relative_start": 1.5352120399475098, "end": *********1.16912, "relative_end": *********1.16912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.172586, "relative_start": 1.5386779308319092, "end": *********1.172586, "relative_end": *********1.172586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.205271, "relative_start": 1.5713629722595215, "end": *********1.205271, "relative_end": *********1.205271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.208666, "relative_start": 1.5747580528259277, "end": *********1.208666, "relative_end": *********1.208666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.211432, "relative_start": 1.577523946762085, "end": *********1.211432, "relative_end": *********1.211432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.237682, "relative_start": 1.603774070739746, "end": *********1.237682, "relative_end": *********1.237682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.241116, "relative_start": 1.607208013534546, "end": *********1.241116, "relative_end": *********1.241116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.244265, "relative_start": 1.6103570461273193, "end": *********1.244265, "relative_end": *********1.244265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.274826, "relative_start": 1.6409180164337158, "end": *********1.274826, "relative_end": *********1.274826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.280213, "relative_start": 1.6463050842285156, "end": *********1.280213, "relative_end": *********1.280213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.285564, "relative_start": 1.651655912399292, "end": *********1.285564, "relative_end": *********1.285564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.320306, "relative_start": 1.6863980293273926, "end": *********1.320306, "relative_end": *********1.320306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.323816, "relative_start": 1.6899080276489258, "end": *********1.323816, "relative_end": *********1.323816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.326636, "relative_start": 1.692728042602539, "end": *********1.326636, "relative_end": *********1.326636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.352877, "relative_start": 1.7189688682556152, "end": *********1.352877, "relative_end": *********1.352877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.357258, "relative_start": 1.7233500480651855, "end": *********1.357258, "relative_end": *********1.357258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.360015, "relative_start": 1.726106882095337, "end": *********1.360015, "relative_end": *********1.360015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.38744, "relative_start": 1.7535319328308105, "end": *********1.38744, "relative_end": *********1.38744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.390962, "relative_start": 1.7570538520812988, "end": *********1.390962, "relative_end": *********1.390962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.393802, "relative_start": 1.7598938941955566, "end": *********1.393802, "relative_end": *********1.393802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.418244, "relative_start": 1.7843358516693115, "end": *********1.418244, "relative_end": *********1.418244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.421736, "relative_start": 1.787827968597412, "end": *********1.421736, "relative_end": *********1.421736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.424458, "relative_start": 1.7905499935150146, "end": *********1.424458, "relative_end": *********1.424458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.44967, "relative_start": 1.8157620429992676, "end": *********1.44967, "relative_end": *********1.44967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.452981, "relative_start": 1.819072961807251, "end": *********1.452981, "relative_end": *********1.452981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.455834, "relative_start": 1.8219258785247803, "end": *********1.455834, "relative_end": *********1.455834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.480557, "relative_start": 1.846648931503296, "end": *********1.480557, "relative_end": *********1.480557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.484203, "relative_start": 1.850295066833496, "end": *********1.484203, "relative_end": *********1.484203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.486857, "relative_start": 1.8529489040374756, "end": *********1.486857, "relative_end": *********1.486857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.514751, "relative_start": 1.880842924118042, "end": *********1.514751, "relative_end": *********1.514751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.518323, "relative_start": 1.8844149112701416, "end": *********1.518323, "relative_end": *********1.518323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.521119, "relative_start": 1.8872110843658447, "end": *********1.521119, "relative_end": *********1.521119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.546823, "relative_start": 1.9129149913787842, "end": *********1.546823, "relative_end": *********1.546823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.551266, "relative_start": 1.9173579216003418, "end": *********1.551266, "relative_end": *********1.551266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.55577, "relative_start": 1.9218618869781494, "end": *********1.55577, "relative_end": *********1.55577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.587894, "relative_start": 1.9539859294891357, "end": *********1.587894, "relative_end": *********1.587894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.592922, "relative_start": 1.9590139389038086, "end": *********1.592922, "relative_end": *********1.592922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.599123, "relative_start": 1.9652149677276611, "end": *********1.599123, "relative_end": *********1.599123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.630797, "relative_start": 1.9968888759613037, "end": *********1.630797, "relative_end": *********1.630797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.635235, "relative_start": 2.0013270378112793, "end": *********1.635235, "relative_end": *********1.635235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.638547, "relative_start": 2.004638910293579, "end": *********1.638547, "relative_end": *********1.638547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.669102, "relative_start": 2.035193920135498, "end": *********1.669102, "relative_end": *********1.669102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.672486, "relative_start": 2.0385780334472656, "end": *********1.672486, "relative_end": *********1.672486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.675195, "relative_start": 2.0412869453430176, "end": *********1.675195, "relative_end": *********1.675195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.703005, "relative_start": 2.0690970420837402, "end": *********1.703005, "relative_end": *********1.703005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.70706, "relative_start": 2.0731520652770996, "end": *********1.70706, "relative_end": *********1.70706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": *********1.710219, "relative_start": 2.076310873031616, "end": *********1.710219, "relative_end": *********1.710219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": *********1.732661, "relative_start": 2.098752975463867, "end": *********1.732661, "relative_end": *********1.732661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": *********1.782375, "relative_start": 2.1484670639038086, "end": *********1.782375, "relative_end": *********1.782375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "start": *********1.824803, "relative_start": 2.1908950805664062, "end": *********1.824803, "relative_end": *********1.824803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.notification-components.notification-bell", "start": *********1.82869, "relative_start": 2.194782018661499, "end": *********1.82869, "relative_end": *********1.82869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a18495a6cea63788e833ce49c47263e", "start": *********1.832246, "relative_start": 2.198338031768799, "end": *********1.832246, "relative_end": *********1.832246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": *********1.835442, "relative_start": 2.2015340328216553, "end": *********1.835442, "relative_end": *********1.835442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": *********1.836444, "relative_start": 2.20253586769104, "end": *********1.836444, "relative_end": *********1.836444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": *********1.839033, "relative_start": 2.205124855041504, "end": *********1.839033, "relative_end": *********1.839033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": *********1.839308, "relative_start": 2.205399990081787, "end": *********1.839308, "relative_end": *********1.839308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": *********1.841124, "relative_start": 2.2072160243988037, "end": *********1.841124, "relative_end": *********1.841124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": *********1.841377, "relative_start": 2.2074689865112305, "end": *********1.841377, "relative_end": *********1.841377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": *********1.842917, "relative_start": 2.2090089321136475, "end": *********1.842917, "relative_end": *********1.842917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": *********1.843142, "relative_start": 2.2092339992523193, "end": *********1.843142, "relative_end": *********1.843142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e744eed566094568aeb7ab91177267f", "start": *********1.844658, "relative_start": 2.210749864578247, "end": *********1.844658, "relative_end": *********1.844658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06b49bd0f9d5edbf64858fc8606233ad", "start": *********1.844881, "relative_start": 2.210973024368286, "end": *********1.844881, "relative_end": *********1.844881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "start": *********1.890416, "relative_start": 2.2565078735351562, "end": *********1.890416, "relative_end": *********1.890416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-impersonate::components.banner", "start": *********1.89113, "relative_start": 2.2572219371795654, "end": *********1.89113, "relative_end": *********1.89113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69d93d5cde0cc1ee5603a3b96a184e40", "start": *********1.895636, "relative_start": 2.261728048324585, "end": *********1.895636, "relative_end": *********1.895636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::372196686030c8f69bd3d2ee97bc0018", "start": *********1.897121, "relative_start": 2.2632129192352295, "end": *********1.897121, "relative_end": *********1.897121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.sidebar-fix-v2", "start": *********1.898185, "relative_start": 2.2642769813537598, "end": *********1.898185, "relative_end": *********1.898185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": *********1.902844, "relative_start": 2.2689359188079834, "end": *********1.902974, "relative_end": *********1.902974, "duration": 0.00012993812561035156, "duration_str": "130μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": *********1.905162, "relative_start": 2.271254062652588, "end": *********1.905203, "relative_end": *********1.905203, "duration": 4.100799560546875e-05, "duration_str": "41μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59391096, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 130, "nb_templates": 130, "templates": [{"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.366176, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "3x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.374058, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.388009, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "3x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.401041, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "102x __components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.453244, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}, "render_count": 102, "name_original": "__components::4e08262e37252af4d0ec53b8f597c6de"}, {"name": "1x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": *********1.732638, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": *********1.782363, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x __components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "param_count": null, "params": [], "start": *********1.824787, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php__components::0934b064ccd0a1c2b1e1d14c2ca1eebd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php&line=1", "ajax": false, "filename": "0934b064ccd0a1c2b1e1d14c2ca1eebd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0934b064ccd0a1c2b1e1d14c2ca1eebd"}, {"name": "1x filament.widgets.notification-components.notification-bell", "param_count": null, "params": [], "start": *********1.828674, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.phpfilament.widgets.notification-components.notification-bell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=1", "ajax": false, "filename": "notification-bell.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.widgets.notification-components.notification-bell"}, {"name": "1x __components::0a18495a6cea63788e833ce49c47263e", "param_count": null, "params": [], "start": *********1.832229, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/0a18495a6cea63788e833ce49c47263e.blade.php__components::0a18495a6cea63788e833ce49c47263e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F0a18495a6cea63788e833ce49c47263e.blade.php&line=1", "ajax": false, "filename": "0a18495a6cea63788e833ce49c47263e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0a18495a6cea63788e833ce49c47263e"}, {"name": "5x __components::9e744eed566094568aeb7ab91177267f", "param_count": null, "params": [], "start": *********1.835425, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9e744eed566094568aeb7ab91177267f.blade.php__components::9e744eed566094568aeb7ab91177267f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9e744eed566094568aeb7ab91177267f.blade.php&line=1", "ajax": false, "filename": "9e744eed566094568aeb7ab91177267f.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::9e744eed566094568aeb7ab91177267f"}, {"name": "5x __components::06b49bd0f9d5edbf64858fc8606233ad", "param_count": null, "params": [], "start": *********1.836429, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/06b49bd0f9d5edbf64858fc8606233ad.blade.php__components::06b49bd0f9d5edbf64858fc8606233ad", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F06b49bd0f9d5edbf64858fc8606233ad.blade.php&line=1", "ajax": false, "filename": "06b49bd0f9d5edbf64858fc8606233ad.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::06b49bd0f9d5edbf64858fc8606233ad"}, {"name": "1x __components::9f29a28cb8146bd3e12bcd2b1bf61baa", "param_count": null, "params": [], "start": *********1.890404, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php__components::9f29a28cb8146bd3e12bcd2b1bf61baa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php&line=1", "ajax": false, "filename": "9f29a28cb8146bd3e12bcd2b1bf61baa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9f29a28cb8146bd3e12bcd2b1bf61baa"}, {"name": "1x filament-impersonate::components.banner", "param_count": null, "params": [], "start": *********1.891117, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\vendor\\stechstudio\\filament-impersonate\\src\\/../resources/views/components/banner.blade.phpfilament-impersonate::components.banner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fstechstudio%2Ffilament-impersonate%2Fresources%2Fviews%2Fcomponents%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-impersonate::components.banner"}, {"name": "1x __components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": *********1.895617, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69d93d5cde0cc1ee5603a3b96a184e40"}, {"name": "1x __components::372196686030c8f69bd3d2ee97bc0018", "param_count": null, "params": [], "start": *********1.897101, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/372196686030c8f69bd3d2ee97bc0018.blade.php__components::372196686030c8f69bd3d2ee97bc0018", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F372196686030c8f69bd3d2ee97bc0018.blade.php&line=1", "ajax": false, "filename": "372196686030c8f69bd3d2ee97bc0018.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::372196686030c8f69bd3d2ee97bc0018"}, {"name": "1x components.sidebar-fix-v2", "param_count": null, "params": [], "start": *********1.898169, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\resources\\views/components/sidebar-fix-v2.blade.phpcomponents.sidebar-fix-v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Fcomponents%2Fsidebar-fix-v2.blade.php&line=1", "ajax": false, "filename": "sidebar-fix-v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.sidebar-fix-v2"}]}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013470000000000003, "accumulated_duration_str": "13.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU' limit 1", "type": "query", "params": [], "bindings": ["d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.270395, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 3.712}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.274474, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.712, "width_percent": 4.306}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.2780461, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.018, "width_percent": 2.45}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.282513, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.468, "width_percent": 3.118}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:1', 'illuminate:cache:flexible:created:filament-excel:exports:1')", "type": "query", "params": [], "bindings": ["filament-excel:exports:1", "illuminate:cache:flexible:created:filament-excel:exports:1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.283664, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.586, "width_percent": 2.376}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.286521, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.961, "width_percent": 2.524}, {"sql": "select * from `cache` where `key` in ('theme_color')", "type": "query", "params": [], "bindings": ["theme_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.287469, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.486, "width_percent": 2.821}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.288496, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.307, "width_percent": 1.782}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.289315, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.088, "width_percent": 1.559}, {"sql": "select * from `cache` where `key` in ('theme')", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 249}], "start": **********.2901049, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.647, "width_percent": 1.559}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/bezhansalleh/filament-shield/src/FilamentShieldServiceProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\bezhansalleh\\filament-shield\\src\\FilamentShieldServiceProvider.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 442}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 410}], "start": **********.300323, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\wamp64\\www\\smms\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "local_kit_db", "explain": null, "start_percent": 26.206, "width_percent": 5.048}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.350785, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "local_kit_db", "explain": null, "start_percent": 31.255, "width_percent": 4.157}, {"sql": "select * from `app_notifications` where `user_id` = 1 order by `app_notifications`.`id` asc limit 34 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.352417, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 35.412, "width_percent": 6.31}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.354864, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 41.722, "width_percent": 3.118}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (1) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 27, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 28, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 29, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.356153, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 26, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 44.84, "width_percent": 3.415}, {"sql": "select * from `notification_events` where `notification_events`.`id` in (1, 2, 5, 6, 11, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.357912, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 48.255, "width_percent": 3.712}, {"sql": "select `notification_events`.`module`, `notification_events`.`id` from `notification_events` order by `notification_events`.`module` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": "view", "name": "filament-forms::components.select", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.398717, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Select.php:807", "source": {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 807}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=807", "ajax": false, "filename": "Select.php", "line": "807"}, "connection": "local_kit_db", "explain": null, "start_percent": 51.967, "width_percent": 4.306}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": *********1.8093622, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "local_kit_db", "explain": null, "start_percent": 56.273, "width_percent": 3.563}, {"sql": "select count(*) as aggregate from `app_notifications` where `user_id` = 1 and `read_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": *********1.825948, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:28", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=28", "ajax": false, "filename": "NotificationBell.php", "line": "28"}, "connection": "local_kit_db", "explain": null, "start_percent": 59.837, "width_percent": 10.765}, {"sql": "select * from `app_notifications` where `user_id` = 1 and `read_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, {"index": 16, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": *********1.8331442, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "NotificationBell.php:37", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NotificationComponents/NotificationBell.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Widgets\\NotificationComponents\\NotificationBell.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FWidgets%2FNotificationComponents%2FNotificationBell.php&line=37", "ajax": false, "filename": "NotificationBell.php", "line": "37"}, "connection": "local_kit_db", "explain": null, "start_percent": 70.601, "width_percent": 7.201}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": *********1.837467, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 77.803, "width_percent": 4.306}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": *********1.839801, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 82.108, "width_percent": 4.009}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": *********1.841852, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 86.117, "width_percent": 3.192}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": *********1.8435879, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 89.31, "width_percent": 2.821}, {"sql": "select * from `notification_events` where `notification_events`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": *********1.845339, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "filament.widgets.notification-components.notification-bell:125", "source": {"index": 21, "namespace": "view", "name": "filament.widgets.notification-components.notification-bell", "file": "D:\\wamp64\\www\\smms\\resources\\views/filament/widgets/notification-components/notification-bell.blade.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnotification-components%2Fnotification-bell.blade.php&line=125", "ajax": false, "filename": "notification-bell.blade.php", "line": "125"}, "connection": "local_kit_db", "explain": null, "start_percent": 92.131, "width_percent": 4.083}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319', `last_activity` = *********1, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'd532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiMEpZYnkwM1diSWhJVHJNRnBDUGx2RVFZeVp2eUZEUHQ1UVkxTE9XQiI7czo1MDoibG9naW5fd2ViXzNkYzdhOTEzZWY1ZmQ0Yjg5MGVjYWJlMzQ4NzA4NTU3M2UxNmNmODIiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJG5mWGtjRUo1V0ZRdEkyNWljMGpVTGU2c1V0M2ZBTVRIUnl1anh4QlJoLkc3ZGdMRUlqcWxXIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL2FwcC1ub3RpZmljYXRpb25zIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo2OiJ0YWJsZXMiO2E6Mjp7czo0MToiODI1MmNmYTU2MDgzOGVmYzAwMzk2MjgzNDFmM2E0NmZfcGVyX3BhZ2UiO3M6MzoiYWxsIjtzOjQxOiJmYjU0NWNkMjc1Mzg0MTgxNmJjNmUwY2FjMGY5Mzc1OV9wZXJfcGFnZSI7czozOiJhbGwiO31zOjg6ImZpbGFtZW50IjthOjA6e319", *********1, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": *********1.904116, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "local_kit_db", "explain": null, "start_percent": 96.214, "width_percent": 3.786}]}, "models": {"data": {"App\\Models\\AppNotification": {"value": 39, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FAppNotification.php&line=1", "ajax": false, "filename": "AppNotification.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 54, "is_counter": true}, "livewire": {"data": {"app.filament.resources.app-notification-resource.pages.list-app-notifications #NYA40ZzGuT6raRPbKF85": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:3 [\n      \"module\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n      \"read\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => \"all\"\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.app-notification-resource.pages.list-app-notifications\"\n  \"component\" => \"App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications\"\n  \"id\" => \"NYA40ZzGuT6raRPbKF85\"\n]", "filament.livewire.global-search #oLGT7Xl4acEuNDKwxDXc": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"oLGT7Xl4acEuNDKwxDXc\"\n]", "app.filament.widgets.notification-components.notification-bell #N1EcdAm92SnX1d3VunnD": "array:4 [\n  \"data\" => array:1 [\n    \"unreadCount\" => 34\n  ]\n  \"name\" => \"app.filament.widgets.notification-components.notification-bell\"\n  \"component\" => \"App\\Filament\\Widgets\\NotificationComponents\\NotificationBell\"\n  \"id\" => \"N1EcdAm92SnX1d3VunnD\"\n]", "filament.livewire.notifications #9wEB07wNxSgEX8yNmFAk": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3545\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"9wEB07wNxSgEX8yNmFAk\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 235, "messages": [{"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-297073863 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297073863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303768, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1933413799 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933413799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304799, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-475912280 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475912280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309466, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-531809816 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531809816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328376, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1126712551 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126712551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.426436, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-235329815 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235329815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.428124, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-969324148 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969324148\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448906, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-627525266 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627525266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.450021, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-953244401 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953244401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.451843, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1391347021 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391347021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.457987, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-421831467 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421831467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.466184, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-727249709 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727249709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.469603, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-302232092 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302232092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493728, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952555211 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952555211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494834, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-71326926 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71326926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497186, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-679322163 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679322163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.502654, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908984483 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908984483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510626, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1803610799 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803610799\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.512275, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1299647607 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299647607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527719, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1189162522 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189162522\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529962, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1284307917 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284307917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532007, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1041368672 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041368672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536403, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-916506553 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916506553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543325, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-475887861 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475887861\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545016, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1836782144 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836782144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.564726, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-58733947 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58733947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.566146, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-557520712 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557520712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.568569, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-123107191 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\AppNotification(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123107191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.572877, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1268069642 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268069642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.579712, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-510533450 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-510533450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581817, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-937789443 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937789443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595545, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-49711588 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49711588\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597229, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502748792 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502748792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59916, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-203367537 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203367537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.603592, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1699585270 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699585270\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.609776, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1495746957 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495746957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.611299, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-972087235 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972087235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.631686, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-542608518 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542608518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.633005, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-687992040 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687992040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635055, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=13),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1350014469 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=13)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=13)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350014469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.640781, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1181415024 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181415024\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.651892, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1555254555 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555254555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.656391, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1566712684 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566712684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.671587, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1846001089 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846001089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.672668, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-502073803 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502073803\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.674331, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=14),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1927137471 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=14)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=14)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927137471\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.678894, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383675964 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383675964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.688414, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2124258182 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124258182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.690464, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1457242888 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457242888\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.706746, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1070664578 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070664578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.709022, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-651243221 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651243221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712343, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=16),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1055535798 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=16)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=16)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055535798\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71777, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1931364687 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931364687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.726168, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-919884908 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919884908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.727818, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-766625827 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766625827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750445, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1800419689 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800419689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752147, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2052249644 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052249644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754585, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=18),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1607421499 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=18)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=18)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607421499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760047, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-236481393 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236481393\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770932, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2008507017 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008507017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774693, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1653568543 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653568543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.792676, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-412525169 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412525169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794952, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-797180852 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797180852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797927, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=20),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1463344441 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=20)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=20)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463344441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802818, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-29432980 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29432980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.81397, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1307554838 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307554838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.815713, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1324115743 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324115743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.834239, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-941165904 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941165904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.835343, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1343559116 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343559116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.837008, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=22),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-907465623 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=22)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=22)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907465623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.841407, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600883541 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600883541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.849061, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-804334812 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804334812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.850634, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-409412622 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409412622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870837, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1504640857 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504640857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87287, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-752190222 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752190222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875866, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=26),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1291764134 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=26)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=26)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291764134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882211, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-879284215 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879284215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.89347, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-107345087 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107345087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.89624, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1152836991 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152836991\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.919396, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-233426049 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233426049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.920875, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-678534913 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678534913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.923033, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=29),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-863823890 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=29)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=29)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863823890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.928217, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1043654665 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043654665\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.941291, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-919594688 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919594688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944897, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-615625950 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615625950\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.964275, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-471456630 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471456630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965261, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1088807933 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088807933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.966932, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=34),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-519827165 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=34)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=34)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-519827165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971526, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-229228083 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229228083\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.978594, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-644445852 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644445852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.980304, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-641544669 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641544669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.994123, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2118652331 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118652331\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.99503, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-940583927 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940583927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996908, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=41),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-926892771 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=41)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=41)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926892771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.001155, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-878859099 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878859099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.010595, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-343192242 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343192242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.014398, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-693482705 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693482705\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.036388, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1787266932 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787266932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.037697, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-170626628 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170626628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.039692, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=43),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-739476621 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=43)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=43)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739476621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.047012, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544047882 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544047882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.056143, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674222324 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674222324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.058746, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1898001406 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898001406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.077606, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-381583139 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381583139\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.080744, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-58628548 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58628548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.083998, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=44),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615189447 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=44)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=44)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615189447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.092095, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1905870706 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905870706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.104021, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1720603646 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720603646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.106632, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2041997021 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041997021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.122417, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-174913105 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174913105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.123724, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-444049587 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444049587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.125345, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=46),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1540494119 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=46)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=46)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540494119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.130433, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1008858108 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008858108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.138266, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1185148638 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185148638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.140738, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1695647884 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695647884\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.159171, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-399795292 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399795292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.160615, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1537196430 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537196430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.162781, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=48),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1743729706 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=48)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=48)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743729706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.168802, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2101323690 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101323690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.177184, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1925145250 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925145250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.179649, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-645746907 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645746907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.200729, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1254795684 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254795684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.202299, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1497673867 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497673867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.204021, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=49),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179046182 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=49)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=49)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179046182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.208422, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-876679792 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876679792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.216549, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-500916830 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500916830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.218413, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-859420643 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859420643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.23336, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1369039724 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369039724\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.23452, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-412117620 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412117620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.236326, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=51),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-348539795 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=51)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=51)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348539795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.240878, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-325376376 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325376376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.249784, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176796562 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176796562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.252015, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1871626009 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871626009\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.26985, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1529325627 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529325627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.271308, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1763824295 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763824295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.273239, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=52),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1631066263 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=52)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=52)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631066263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.279911, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1151547738 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151547738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.291601, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-862684755 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862684755\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.293948, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-137283421 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137283421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.316309, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-964978744 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964978744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.317441, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-599922750 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599922750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.3191, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=54),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1662619971 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=54)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=54)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662619971\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.323581, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-351106626 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351106626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.331203, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-902204797 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902204797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.332812, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-129406931 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129406931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.347627, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-102233697 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102233697\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.349085, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1879214123 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1879214123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.351183, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=56),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1098934540 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=56)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=56)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098934540\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.356993, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-988146720 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988146720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.365141, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328941240 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328941240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.367821, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1695607543 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695607543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.38326, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-97284007 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97284007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.38441, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-116864630 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116864630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.38617, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=59),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532535818 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=59)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=59)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532535818\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.390698, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2106492559 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106492559\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.398521, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1183311036 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183311036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.400067, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085397317 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085397317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.414065, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-322774913 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322774913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.415096, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-589289101 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589289101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.417067, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=61),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627332419 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=61)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=61)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627332419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.421528, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1997278635 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997278635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.428072, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1055571366 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055571366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.430095, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403677115 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403677115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.444618, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1017683576 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017683576\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.445891, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-734321813 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734321813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.448371, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=62),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1849065824 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=62)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=62)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849065824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.452771, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1810372385 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810372385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.459722, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085120417 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085120417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.461366, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-21090196 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21090196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.476185, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1085414507 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085414507\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.477324, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-763704048 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763704048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.478928, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=64),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2106360955 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=64)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=64)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106360955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.483956, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-395253405 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395253405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.490445, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102108770 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102108770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.492085, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1714915587 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714915587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.50905, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2028490351 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028490351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.510496, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-69440858 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69440858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.512556, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=65),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-242919257 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=65)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=65)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242919257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.518083, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908866438 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908866438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.52478, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805980052 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805980052\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.526451, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2105823885 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105823885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.54153, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-450704175 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450704175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.542744, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-804491984 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804491984\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.544889, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=67),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949796536 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=67)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=67)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949796536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.550888, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2080916209 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080916209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.560989, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1750484165 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750484165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.563574, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-587628309 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587628309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.582402, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395054975 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395054975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.583968, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1942655737 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942655737\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.586482, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=68),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-554160644 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=68)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=68)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554160644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.592429, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-821284718 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821284718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.604225, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1541570614 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541570614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.60661, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-646232937 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646232937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.626156, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1004166849 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004166849\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.627168, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-900027950 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900027950\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.628817, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=70),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1246555277 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=70)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=70)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246555277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.634955, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-521456582 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521456582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.642959, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-263831026 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263831026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.645125, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1269397159 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269397159\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.664173, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1511863300 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511863300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.665751, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1211787630 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211787630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.667892, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=71),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1655827225 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=71)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=71)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655827225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.672264, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2077439497 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077439497\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.678635, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1416049467 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1416049467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.68047, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1297416077 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297416077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.697676, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1702627025 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702627025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.699241, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1527167135 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527167135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.701379, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\AppNotification(id=73),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\AppNotification)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-841742099 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\AppNotification(id=73)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Models\\AppNotification(id=73)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\AppNotification)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841742099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.706802, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766859344 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766859344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.787114, "xdebug_link": null}, {"message": "[\n  ability => page_ManageSetting,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1417977661 data-indent-pad=\"  \"><span class=sf-dump-note>page_ManageSetting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">page_ManageSetting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417977661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.787464, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppNotification,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppNotification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-830325335 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppNotification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\AppNotification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\AppNotification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830325335\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.788733, "xdebug_link": null}, {"message": "[\n  ability => view_any_client,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2094351254 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094351254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.789284, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Client,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Client]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502400361 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Client]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502400361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.79014, "xdebug_link": null}, {"message": "[\n  ability => view_any_incentive,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466586666 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_incentive </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_incentive</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466586666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.790779, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Incentive,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Incentive]\n]", "message_html": "<pre class=sf-dump id=sf-dump-966579041 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Incentive</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Incentive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Incentive]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966579041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.791575, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\IncentiveRule,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\IncentiveRule]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059885887 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\IncentiveRule</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\IncentiveRule</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\IncentiveRule]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059885887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.792694, "xdebug_link": null}, {"message": "[\n  ability => view_any_milestone,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1506210377 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506210377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.793468, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Milestone,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Milestone]\n]", "message_html": "<pre class=sf-dump id=sf-dump-772779197 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Milestone</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Milestone</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Milestone]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772779197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.796363, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationEvent,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationEvent]\n]", "message_html": "<pre class=sf-dump id=sf-dump-553150141 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationEvent</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"28 characters\">App\\Models\\NotificationEvent</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; App\\Models\\NotificationEvent]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553150141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.797459, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\NotificationRolePreference,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\NotificationRolePreference]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1914323257 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\NotificationRolePreference</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Models\\NotificationRolePreference</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"44 characters\">[0 =&gt; App\\Models\\NotificationRolePreference]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914323257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.799414, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-83951078 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83951078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.799883, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-392943434 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-392943434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.800521, "xdebug_link": null}, {"message": "[\n  ability => view_any_payment,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281979108 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281979108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.802467, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Payment,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Payment]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1048147941 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Payment</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Payment</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Payment]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048147941\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.8027, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2062224010 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062224010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.803343, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Product,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-798488338 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798488338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.803882, "xdebug_link": null}, {"message": "[\n  ability => view_any_project,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-759076017 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759076017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.804253, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Project,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Project]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2144089601 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Project]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144089601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.804717, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectStatusLog,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectStatusLog]\n]", "message_html": "<pre class=sf-dump id=sf-dump-243841820 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectStatusLog</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\ProjectStatusLog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"34 characters\">[0 =&gt; App\\Models\\ProjectStatusLog]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243841820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.805305, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\ProjectType,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\ProjectType]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2042037204 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\ProjectType</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\ProjectType</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\ProjectType]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042037204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.805827, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\RoleNotificationSettings,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\RoleNotificationSettings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-379770163 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\RoleNotificationSettings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Models\\RoleNotificationSettings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; App\\Models\\RoleNotificationSettings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379770163\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.806361, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1470059743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470059743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.80684, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1491818926 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491818926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.811605, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Rupadana\\ApiService\\Models\\Token,\n  result => true,\n  user => 1,\n  arguments => [0 => Rupadana\\ApiService\\Models\\Token]\n]", "message_html": "<pre class=sf-dump id=sf-dump-122677551 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Rupadana\\ApiService\\Models\\Token</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Rupadana\\ApiService\\Models\\Token</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[0 =&gt; Rupadana\\ApiService\\Models\\Token]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122677551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.812352, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PricingModel,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PricingModel]\n]", "message_html": "<pre class=sf-dump id=sf-dump-61186529 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PricingModel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Models\\PricingModel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; App\\Models\\PricingModel]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61186529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": *********1.819412, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications", "uri": "GET admin/app-notifications", "controller": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications@render<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/app-notifications", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Hasnayeen\\Themes\\Http\\Middleware\\SetTheme, Filament\\Http\\Middleware\\Authenticate, App\\Http\\Middleware\\RedirectByRole, Jeffgreco13\\FilamentBreezy\\Middleware\\MustTwoFactor, verified:filament.admin.auth.email-verification.prompt", "duration": "2.27s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/admin/milestones</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1985 characters\">loginAgentEmail=btejoram%40mysquare.in; sb-login=dC9ZNkxkS1R2Q1BkVysrRnJibUVhQVhyY0VzYzRjcXZqUk1qMWlMTGpMdzBuekcxanpXR1lsakllcElZTnhEakZUcmU1MTIxdm90SVZUelNzaHRXRjg2cEhvZ0xrQVdMZ1ZUdW9SaERHaVJKVTlaT0VZZVl4UXFWN2NiUVpnOVE3NkJ3Rmk5cy9KZ3VZYVBZZE56ZUVlaXlmMFRLWkhHcUh6K1dOOWwxYVNXaGk1WnV4anlWQW9FR0ovdVlNUXVza2x6UjlOZVRFN052eUpNV3BScnFuYXBTRmJXOWhnZnZHY003RVA2Q1ZZRWc3em1SUnRlZXlxSGo0TmNWcHhZM2dpaTZFNkRhY0xudlFJWmZxK1dpQ2I4WE1aZmI4VklPTXhWK0xPMzhNNE1hbmV1eFp0ZHlGZm03QnVvcU9icHZKMnlKTm55NVhoK1FPeDE0eUhyVGIrd1k2dkhRbG5vSW44cW1VQmVSTUtyeTFVKytxNjI2S3pYYlZ3Y2hmblBkQ3BGbmlCQnZrVjZYWmxjaFFpV09jbHhMVE5Ka0RHVmRpcE9OUTBnYXllSzhKOHB0MXkrVWhhV1lGdzBsOTc1MkdkMWl4c1VuMVo2MG9LcXRycWUzWXBOTTBUUjJvdUtUWkpud1E4RU1BSG9iS3B5WHZ1RVZWTG1VVlRJZVFvdjM; remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6IkhJdXhuT3gxTW5VT1NnSk9SVjhFcHc9PSIsInZhbHVlIjoiY2FSUDhkZ1lFVEttdE41TFlGOXQ2TE1YZ3lFWCt1bTNtYUZnU2hGMWpqTk5MNVh0NFJrQmVCcVpJaFpYQkUwbmhhc2Y1a05LOWQ1YlFkWW54dm5hTjhXcUdaRjdLYVAxUTViL1RsYzFtMG1wcWR2d25IMnVBeG85KzBZQjRjNVkrWjAzMXpIQkZpK0NmRGxmVmQrRVdHOWl3WTFtR1RQeVVKQ2U3a0hKcG5wU2cwT3NwQnQ4S21OQWllb2dsenAxMEdmRElSYXZSZjlnVFlLVWZRMUVzaWY3cFBOa1FKU3FwS2pFMGtXMFlxWT0iLCJtYWMiOiIxMTgwY2YxMDEwNDViMzU5NjNhMGYzOThkMzRjNDVjZWQ4ZWZhMGNhMDgwYTdlYWZhZTk5ZTcxMDU3N2IyY2IwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNrR2NqblRqRTd0Uzg3aVdCb0NkeUE9PSIsInZhbHVlIjoiRGVPWjlTUHgyQ2libEFmRVdsdndlTTRvc2FYZGVBUnVBODNtNmV1TWQ4YWxrSlRjMXR0OWlsMU93TERxZWoraGYzVHVHd0hhU3owc2FNb2xIb2doOUw5d1pvdlgvaW1jQktMaE1Ca21NYjdYOW9CNFdaWFMyVXhGSHNwZVlZdngiLCJtYWMiOiI3NGFjZmMxNWUxNjdkYjQ1YmNiZGQ5YmNjNWQ4ZTBmZmM4MzczNDgyM2Q0NzVkNjI5ZWI3ODJlOTQxMjc3OTU4IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6Ild2bkZRWUdFckYreTRid0hyYnp4MlE9PSIsInZhbHVlIjoiODZ1YlZpSDdXemo0Rnc5UHp3L1RjdklkcnFSOGxkSi9YUGdnTmhWSG5mR1pLNGhYdTVKOHZPQjRjNHc5c2ZUekZhYTl4bU9hTGlrRDRlY3VFNTgyWUdSNEFEcFJ4R0ZjWEFqNG9rbzYvWEJGRHJZbnI5eGZ4cVZyQ0NBQ3RYTTQiLCJtYWMiOiJiMTQyZjcyZTZlMGFmNDZkNmM2NjI5NWM1N2U3YzcxNzU5OTA2NDUwZDYxOWJlNWQyZTJmODlkY2U4NjE2YzgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-993601556 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>loginAgentEmail</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sb-login</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|GRproYZaLbKBY8NdryfzkcMofrrDqxPI44kJTt02MwWz36vk8USnIoTCgrIS|$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d532hrL32w1fOsIeLpz5PPvbOhV68s3IPGduRILU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993601556\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-278865702 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:09:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5wN0lrNUNvaUhTTUlGY3Q5RVE4Znc9PSIsInZhbHVlIjoiR1VpNURDdmc3ZG84dDl3OStqVmNxdWh4OXU0TUZ2N2M0WlFmOWN6TnFZaVR0MTBxdjJ0WUVlL1Y5WWxuSG5CUUE4N3Eyd1A1NnUydWJJU2xvZ0VXYUx1MWNVMHY4OU1pc1FOeXFBV0lTbkMxWVgzUzBFV042RFdkN0oyL3gwdzYiLCJtYWMiOiI2MmY1M2JjNDBiMzgzOGQxMjRiZmYxNDU2YTZmNzU1M2YyNDk1ZjBhMTg3NGM5Mzc4NGJmYTExY2EwZTE4ZjhlIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:09:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"439 characters\">kit_session=eyJpdiI6IjI0UXBWeEZzblMrdHZnUFlQZUg3aWc9PSIsInZhbHVlIjoiOG1KZGJaWXJTeERUa3N0b1QwclgrcXZFTE9Eb1UvUUgzVHgxQ25tWmREc2JyY1NoQ01QNHFhcy9OMUg3R094VEpOeWZ3MGVMaUxyYVFoTldqQjV3OGtjbE9OeE5wWEZ5cjMzaVNGVkc4TjNuMDVzaHY1Rk93dVFDeVBKTi9WNDgiLCJtYWMiOiI0OTgwMWMwNjY2ZTM5NjgzYWRlOThkMmY5NzFiZjc1YjJkMzQ0MDBjYWVkYzFmZWU1MzEzNzI5NjE4ZDMzMTQ1IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:09:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5wN0lrNUNvaUhTTUlGY3Q5RVE4Znc9PSIsInZhbHVlIjoiR1VpNURDdmc3ZG84dDl3OStqVmNxdWh4OXU0TUZ2N2M0WlFmOWN6TnFZaVR0MTBxdjJ0WUVlL1Y5WWxuSG5CUUE4N3Eyd1A1NnUydWJJU2xvZ0VXYUx1MWNVMHY4OU1pc1FOeXFBV0lTbkMxWVgzUzBFV042RFdkN0oyL3gwdzYiLCJtYWMiOiI2MmY1M2JjNDBiMzgzOGQxMjRiZmYxNDU2YTZmNzU1M2YyNDk1ZjBhMTg3NGM5Mzc4NGJmYTExY2EwZTE4ZjhlIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:09:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"411 characters\">kit_session=eyJpdiI6IjI0UXBWeEZzblMrdHZnUFlQZUg3aWc9PSIsInZhbHVlIjoiOG1KZGJaWXJTeERUa3N0b1QwclgrcXZFTE9Eb1UvUUgzVHgxQ25tWmREc2JyY1NoQ01QNHFhcy9OMUg3R094VEpOeWZ3MGVMaUxyYVFoTldqQjV3OGtjbE9OeE5wWEZ5cjMzaVNGVkc4TjNuMDVzaHY1Rk93dVFDeVBKTi9WNDgiLCJtYWMiOiI0OTgwMWMwNjY2ZTM5NjgzYWRlOThkMmY5NzFiZjc1YjJkMzQ0MDBjYWVkYzFmZWU1MzEzNzI5NjE4ZDMzMTQ1IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:09:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278865702\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1637373820 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0JYby03WbIhITrMFpCPlvEQYyZvyFDPt5QY1LOWB</span>\"\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$nfXkcEJ5WFQtI25ic0jULe6sUt3fAMTHRyujxxBRh.G7dgLEIjqlW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/app-notifications</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>8252cfa560838efc0039628341f3a46f_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637373820\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/app-notifications", "action_name": "filament.admin.resources.app-notifications.index", "controller_action": "App\\Filament\\Resources\\AppNotificationResource\\Pages\\ListAppNotifications"}, "badge": null}}