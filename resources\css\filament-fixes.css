/* Fix for sidebar navigation links */
.fi-sidebar-nav {
    z-index: 10;
    position: relative;
}

.fi-sidebar-item {
    display: flex !important;
    width: 100%;
}

.fi-sidebar-item-button {
    width: 100%;
    display: flex !important;
}

.fi-sidebar-group {
    display: block !important;
    width: 100%;
}

/* Fix for notification dropdown */
.notification-dropdown {
    position: absolute;
    top: calc(100% + 10px); /* Add spacing below the bell icon */
    left: 50%; /* Center align below the bell icon */
    transform: translateX(-50%);
    z-index: 9999;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 10px;
    width: 300px;
}

/* Ensure proper stacking context */
.fi-layout {
    isolation: isolate;
}

/* Fix for sidebar collapsible groups */
.fi-sidebar-group-items {
    display: block !important;
    width: 100%;
}

/* Fix for sidebar item label */
.fi-sidebar-item-label {
    display: block !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Fix for notification bell positioning */
.fi-topbar .relative {
    position: relative !important;
    z-index: 50 !important;
}

/* Ensure notification dropdown appears correctly */
.fi-topbar .relative > div[x-show="open"] {
    position: fixed !important;
    right: 1rem !important;
    top: 4rem !important;
    z-index: 9999 !important;
    min-width: 320px !important;
    max-width: 90vw !important;
}

/* Fix for Filament user menu area */
.fi-topbar-end {
    position: relative !important;
    z-index: 45 !important;
}

/* Ensure notification dropdown stays in viewport */
@media (max-width: 768px) {
    .fi-topbar .relative > div[x-show="open"] {
        right: 0.5rem !important;
        left: 0.5rem !important;
        width: calc(100vw - 1rem) !important;
        max-width: none !important;
    }
}

/* Global hover text visibility fixes */
/* Enhanced hover states for better text visibility across all components */

/* Table row hover fixes */
.hover-fix-row:hover {
    background-color: #f9fafb !important;
}

.dark .hover-fix-row:hover {
    background-color: #374151 !important;
}

.hover-fix-row:hover .hover-fix-text {
    color: #111827 !important;
}

.dark .hover-fix-row:hover .hover-fix-text {
    color: #ffffff !important;
}

.hover-fix-row:hover .hover-fix-text-muted {
    color: #6b7280 !important;
}

.dark .hover-fix-row:hover .hover-fix-text-muted {
    color: #d1d5db !important;
}

/* Notification item hover fixes - DISABLED */
/* Remove hover background colors for notifications with higher specificity */
.notification-item:hover,
.notification-item.hover\:bg-gray-50:hover,
div.notification-item:hover {
    background-color: transparent !important;
}

.dark .notification-item:hover,
.dark .notification-item.dark\:hover\:bg-gray-700\/50:hover,
.dark div.notification-item:hover {
    background-color: transparent !important;
}

/* Override Tailwind hover classes specifically */
.notification-item.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 0 !important;
    background-color: transparent !important;
}

.dark .notification-item.dark\:hover\:bg-gray-700\/50:hover {
    --tw-bg-opacity: 0 !important;
    background-color: transparent !important;
}

/* Keep text colors unchanged on hover */
.notification-item:hover .notification-text {
    color: inherit !important;
}

.dark .notification-item:hover .notification-text {
    color: inherit !important;
}

.notification-item:hover .notification-text-muted {
    color: inherit !important;
}

.dark .notification-item:hover .notification-text-muted {
    color: inherit !important;
}

/* Button hover fixes - DISABLED for notifications */
/* Remove hover background colors for notification buttons */
.button-hover-fix:hover {
    background-color: transparent !important;
}

.dark .button-hover-fix:hover {
    background-color: transparent !important;
}

/* Keep button text colors unchanged on hover */
.button-hover-fix:hover .button-text {
    color: inherit !important;
}

.dark .button-hover-fix:hover .button-text {
    color: inherit !important;
}

/* List item hover fixes */
.list-item-hover:hover {
    background-color: #f9fafb !important;
}

.dark .list-item-hover:hover {
    background-color: #374151 !important;
}

.list-item-hover:hover .list-text {
    color: #111827 !important;
}

.dark .list-item-hover:hover .list-text {
    color: #ffffff !important;
}

.list-item-hover:hover .list-text-muted {
    color: #6b7280 !important;
}

.dark .list-item-hover:hover .list-text-muted {
    color: #d1d5db !important;
}

/* Ensure proper stacking context */
[data-slot="topbar"] {
    position: relative !important;
    z-index: 40 !important;
}

/* Force notification dropdown to stay in viewport */
@media (max-width: 640px) {
    .fi-topbar .relative > div[x-show="open"] {
        right: -1rem !important;
        left: auto !important;
        width: calc(100vw - 2rem) !important;
        max-width: 320px !important;
    }
}
