<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClientResource\Pages;
use App\Filament\Resources\ClientResource\RelationManagers;
use App\Models\Client;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class ClientResource extends Resource
{
    protected static ?string $model = Client::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->can('view_any_client');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        if (auth()->check() && !auth()->user()->hasRole('super_admin')) {
            $query->where('created_by', auth()->id());

            // OR if you want to keep your existing project-based filtering:
            // if (auth()->user()->shouldSeeOwnDataOnly()) {
            //     $query->whereHas('projects', function (Builder $query) {
            //         $query->where('user_id', auth()->id());
            //     });
            // }
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('created_by')->default(auth()->id()),
                Forms\Components\Section::make('Company Details')
                    ->schema([
                        Forms\Components\TextInput::make('company_name')
                            ->label('Company Name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('tax_id')
                            ->label('Tax ID / GST')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('registered_address')
                            ->label('Registered Address')
                            ->required()
                            ->maxLength(65535),
                        Forms\Components\TextInput::make('company_email')
                            ->label('Company Email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('company_number')
                            ->label('Company Number')
                            ->tel()
                            ->required()
                            ->maxLength(20),
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'lead' => 'Lead',
                            ])
                            ->default('active')
                            ->required(),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Personnel Details')
                    ->schema([
                        Forms\Components\Repeater::make('personnel_details')
                            ->label('Personnel')
                            ->schema([
                                Forms\Components\TextInput::make('name')->label('Name')->maxLength(255),
                                Forms\Components\TextInput::make('official_email')->label('Official Email')->email()->maxLength(255),
                                Forms\Components\TextInput::make('mobile_number')->label('Mobile No.')->tel()->maxLength(20),
                                Forms\Components\TextInput::make('whatsapp_number')->label('WhatsApp No.')->tel()->maxLength(20),
                                Forms\Components\TextInput::make('skype')->label('Skype')->maxLength(255),
                                Forms\Components\TextInput::make('designation')->label('Designation')->maxLength(255),
                                Forms\Components\TextInput::make('department')->label('Department')->maxLength(255),
                            ])
                            ->columns(2)
                            ->minItems(1)
                            ->maxItems(3)
                            ->default([
                                ['name' => '', 'official_email' => '', 'mobile_number' => '', 'whatsapp_number' => '', 'skype' => '', 'designation' => '', 'department' => ''],
                            ]),
                    ]),
                Forms\Components\Section::make('Social Media Access')
                    ->schema([
                        Forms\Components\Repeater::make('social_media_access')
                            ->label('Access to Social Media Accounts')
                            ->schema([
                                Forms\Components\Select::make('platform')
                                    ->options([
                                        'instagram' => 'Instagram',
                                        'youtube' => 'YouTube',
                                        'twitter' => 'Twitter',
                                        'facebook' => 'Facebook',
                                        'gmb' => 'GMB',
                                        'linkedin' => 'LinkedIn',
                                        'website_gsa' => 'Website - GSA',
                                        'website_ga' => 'Website - GA',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('username')
                                    ->label('Username / Gmail ID / Status')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('password')
                                    ->label('Password / Status')
                                    ->maxLength(255),
                            ])
                            ->columns(3)
                            ->default([
                                ['platform' => 'instagram', 'username' => '', 'password' => ''],
                                ['platform' => 'youtube', 'username' => '', 'password' => ''],
                                ['platform' => 'twitter', 'username' => '', 'password' => ''],
                                ['platform' => 'website_gsa', 'username' => '', 'password' => ''],
                                ['platform' => 'website_ga', 'username' => '', 'password' => ''],
                                ['platform' => 'facebook', 'username' => '', 'password' => ''],
                                ['platform' => 'gmb', 'username' => '', 'password' => ''],
                                ['platform' => 'linkedin', 'username' => '', 'password' => ''],
                            ]),
                    ]),
                ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')->label('Name')->searchable(),
                Tables\Columns\TextColumn::make('company_email')->label('Email')->searchable(),
                //Tables\Columns\TextColumn::make('company_number')->label('Number')->searchable(),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->visible(fn() => auth()->user()->hasRole('super_admin'))
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('name')
                //     ->searchable()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('email')
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('phone')
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('contact_person')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'danger',
                        'lead' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('projects_count')
                    ->counts('projects')
                    ->label('Projects')
                    ->sortable()
                    ->url(fn ($record): string => route('filament.admin.resources.projects.index', [
                        'tableFilters' => [
                            'client_id' => [
                                'value' => $record->id,
                            ],
                        ],
                    ]))
                    ->color('primary')
                    ->weight('medium'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'lead' => 'Lead',
                    ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                // Tables\Actions\Action::make('toggleStatus')
                //     ->label(fn ($record) => $record->status === 'active' ? 'Deactivate' : 'Activate')
                //     ->icon(fn ($record) => $record->status === 'active' ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                //     ->color(fn ($record) => $record->status === 'active' ? 'danger' : 'success')
                //     ->requiresConfirmation()
                //     ->action(function ($record) {
                //         $newStatus = $record->status === 'active' ? 'inactive' : 'active';
                //         $record->update(['status' => $newStatus]);
                //     }),
                Tables\Actions\Action::make('view')
                    ->label('')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn ($record) => 'Client Details - ' . $record->company_name)
                    ->modalContent(fn ($record) => view('filament.modals.client-details', ['record' => $record]))
                    ->modalWidth('7xl')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close'),
                Tables\Actions\Action::make('toggleStatus')
                    ->label('')
                    ->icon(fn ($record) => $record->status === 'active' ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn ($record) => $record->status === 'active' ? 'danger' : 'success')
                    ->tooltip(fn ($record) => $record->status === 'active' ? 'Deactivate' : 'Activate') // Tooltip text
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $newStatus = $record->status === 'active' ? 'inactive' : 'active';
                        $record->update(['status' => $newStatus]);
                    }),
                Tables\Actions\EditAction::make()->label(''),
                Tables\Actions\DeleteAction::make()->label(''),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Set Active')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'active']))
                        ->requiresConfirmation(),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Set Inactive')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'inactive']))
                        ->requiresConfirmation(),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProjectsRelationManager::class,
            RelationManagers\MilestonesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClients::route('/'),
            'create' => Pages\CreateClient::route('/create'),
            'edit' => Pages\EditClient::route('/{record}/edit'),
            'view' => Pages\ViewClient::route('/{record}/view'),
        ];
    }
}
