<div x-data="{ open: false }"
     @click.away="open = false"
     wire:poll.30s="updateUnreadCount"
     class="relative">

    <button @click="open = !open"
        class="button-hover-fix flex items-center justify-center w-10 h-10 rounded-full focus:outline-none transition-colors duration-150">
        <x-heroicon-o-bell class="w-5 h-5 text-gray-500 dark:text-gray-400" />

        @if ($unreadCount > 0)
            <span
                class="absolute top-0 right-0 flex h-5 w-5 items-center justify-center rounded-full bg-primary-500 text-[0.625rem] font-medium text-white">
                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
            </span>
        @endif
    </button>

    <!-- Notification Panel -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white border border-gray-200 rounded-lg shadow-2xl dark:bg-gray-800 dark:border-gray-700 overflow-hidden flex flex-col"
         style="display: none; position: fixed !important; right: 1rem !important; top: 4rem !important; z-index: 9999 !important; width: 22rem !important; max-width: 90vw !important; max-height: 80vh !important;"
         @click.outside="open = false">

        <div
            class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
            <h3 class="text-base font-semibold text-gray-900 dark:text-white">Notifications</h3>

            @if ($unreadCount > 0)
                <button wire:click="markAllAsRead"
                    class="text-xs font-medium transition-colors duration-200 text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                    Mark all as read
                </button>
            @endif
        </div>

        <div
            class="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
            style="max-height: calc(80vh - 140px); min-height: 200px;">
            @forelse($this->getNotifications() as $notification)
                <div
                    class="notification-item p-3 transition-colors duration-150 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-start justify-between">
                        <h4 class="notification-text pr-2 text-sm font-medium text-gray-900 dark:text-white leading-tight">
                            {{ $notification->title }}</h4>
                        <button wire:click="markAsRead({{ $notification->id }})"
                            class="button-hover-fix flex-shrink-0 p-1 text-gray-400 transition-colors duration-150 rounded-full dark:text-gray-500">
                            <x-heroicon-o-x-mark class="w-4 h-4" />
                        </button>
                    </div>
                    <p class="notification-text-muted mt-1 text-xs text-gray-600 dark:text-gray-300 line-clamp-2 leading-relaxed">
                        {{ $notification->message }}</p>
                    <div class="flex items-center justify-between mt-2">
                        <span class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <x-heroicon-o-clock class="w-3 h-3 mr-1" />
                            {{ $notification->created_at->diffForHumans() }}
                        </span>

                        <span
                            class="px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                            {{ ucfirst($notification->notificationEvent->module) }}
                        </span>
                    </div>
                </div>
            @empty
                <div class="py-8 text-center text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-bell-slash class="w-8 h-8 mx-auto mb-3 opacity-70" />
                    <p class="text-sm">No new notifications</p>
                </div>
            @endforelse
        </div>

        <div class="p-3 text-center border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
            <a href="{{ route('filament.admin.resources.app-notifications.index') }}"
                class="button-hover-fix block w-full px-3 py-2 text-sm font-medium text-center transition-all duration-200 rounded-md text-primary-600 dark:text-primary-400 border border-transparent">
                View all notifications
            </a>
        </div>
    </div>
</div>
