@php
    use Illuminate\Support\Str;

    // Helper function to get currency symbol
    function getCurrencySymbol($currency) {
        return match($currency ?? 'INR') {
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            default => '₹'
        };
    }
@endphp

<x-filament-panels::page>
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">

    <style>
        /* JavaScript-based tooltip styling */
        .js-tooltip {
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 99999;
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .js-tooltip.show {
            opacity: 1;
        }

        .js-tooltip::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1f2937 transparent transparent transparent;
        }

        /* Enhanced table row hover states for better text visibility */
        .dashboard-table-row:hover {
            background-color: #f9fafb !important;
        }

        .dark .dashboard-table-row:hover {
            background-color: #374151 !important;
        }

        .dashboard-table-row:hover .dashboard-text {
            color: #111827 !important;
        }

        .dark .dashboard-table-row:hover .dashboard-text {
            color: #ffffff !important;
        }

        .dashboard-table-row:hover .dashboard-text-muted {
            color: #6b7280 !important;
        }

        .dark .dashboard-table-row:hover .dashboard-text-muted {
            color: #d1d5db !important;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tooltip system initialized');
            let tooltip = null;

            function createTooltip() {
                if (!tooltip) {
                    tooltip = document.createElement('div');
                    tooltip.className = 'js-tooltip';
                    document.body.appendChild(tooltip);
                }
                return tooltip;
            }

            function showTooltip(element, text) {
                const tooltip = createTooltip();
                tooltip.textContent = text;
                tooltip.classList.add('show');

                const rect = element.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                tooltip.style.left = (rect.left + rect.width / 2 - tooltipRect.width / 2) + 'px';
                tooltip.style.top = (rect.top - tooltipRect.height - 10) + 'px';
            }

            function hideTooltip() {
                if (tooltip) {
                    tooltip.classList.remove('show');
                }
            }

            // Add event listeners to all elements with title attribute
            function initTooltips() {
                document.querySelectorAll('[title]').forEach(element => {
                    const title = element.getAttribute('title');
                    if (title) {
                        // Remove default browser tooltip
                        element.removeAttribute('title');
                        element.setAttribute('data-tooltip', title);

                        element.addEventListener('mouseenter', function() {
                            showTooltip(this, this.getAttribute('data-tooltip'));
                        });

                        element.addEventListener('mouseleave', hideTooltip);
                    }
                });
            }

            // Initialize tooltips
            initTooltips();

            // Re-initialize tooltips when new content is loaded (for Livewire)
            document.addEventListener('livewire:navigated', initTooltips);
            document.addEventListener('livewire:load', initTooltips);
        });
    </script>

    <div class="flex justify-between items-center mb-6">
        <div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Date Filter Dropdown -->
            <div class="relative">
                <select wire:model.live="dateFilter"
                    class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="tomorrow">Tomorrow</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="this_year">This Year</option>
                </select>
            </div>

            <button type="button" wire:click="resetDashboard"
                class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 flex items-center text-sm"
                title="Reset dashboard to default settings">
                <x-heroicon-o-arrow-path class="w-4 h-4 mr-1" />
                Reset
            </button>

            <a href="{{ route('filament.admin.pages.dashboard-settings') }}"
                class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 flex items-center"
                title="Customize dashboard widgets and layout">
                <x-heroicon-o-cog-6-tooth class="w-5 h-5 mr-1" />
                Customize
            </a>

            {{-- User Avatar --}}
            @php
                $user = auth()->user();
                $avatarUrl = $user && $user->avatar_url
                    ? (Str::startsWith($user->avatar_url, ['http://', 'https://', '/storage/'])
                        ? (Str::startsWith($user->avatar_url, '/storage/') ? asset(ltrim($user->avatar_url, '/')) : $user->avatar_url)
                        : asset('storage/' . ltrim($user->avatar_url, '/')))
                    : 'https://ui-avatars.com/api/?name=' . urlencode($user?->name ?? 'User');
            @endphp
            <img class="fi-avatar object-cover object-center fi-circular rounded-full h-8 w-8 fi-user-avatar" src="{{ $avatarUrl }}" alt="Avatar of {{ $user?->name ?? 'User' }}" onerror="this.onerror=null;this.src='https://ui-avatars.com/api/?name={{ urlencode($user?->name ?? 'User') }}';">
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        @foreach ($widgets as $widget)
            @php
                $widthClass = match ($widget['width']) {
                    3 => 'col-span-1', // 25% width (1 of 4 columns)
                    4 => 'col-span-1 sm:col-span-2 md:col-span-1', // 33% width (1 of 3 columns on medium screens)
                    6 => 'col-span-1 sm:col-span-2', // 50% width (2 of 4 columns)
                    8 => 'col-span-1 sm:col-span-2 lg:col-span-3', // 75% width (3 of 4 columns)
                    12 => 'col-span-1 sm:col-span-2 lg:col-span-4', // 100% width (4 of 4 columns)
                    default => 'col-span-1 sm:col-span-2', // Default to 50% width
                };

                $heightClass = match ($widget['height']) {
                    1 => 'h-auto',
                    2 => 'h-auto',
                    3 => 'h-auto',
                    default => 'h-auto',
                };
            @endphp

            <div class="{{ $widthClass }} {{ $heightClass }}">
                @switch($widget['component'])
                    @case('TotalProjectsWidget')
                    <a href="{{ route('filament.admin.resources.projects.index') }}"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <x-heroicon-o-briefcase class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        {{ $this->getFilteredProjectCount() }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Projects
                                        @if($dateFilter === 'today') (Today)
                                        @elseif($dateFilter === 'yesterday') (Yesterday)
                                        @elseif($dateFilter === 'tomorrow') (Tomorrow)
                                        @elseif($dateFilter === 'week') (This Week)
                                        @elseif($dateFilter === 'month') (This Month)
                                        @elseif($dateFilter === 'this_year') (This Year)
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('TotalClientsWidget')
                    <a href="{{ route('filament.admin.resources.clients.index') }}"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <x-heroicon-o-users class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        {{ $this->getFilteredClientCount() }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Clients
                                        @if($dateFilter === 'today') (Today)
                                        @elseif($dateFilter === 'yesterday') (Yesterday)
                                        @elseif($dateFilter === 'tomorrow') (Tomorrow)
                                        @elseif($dateFilter === 'week') (This Week)
                                        @elseif($dateFilter === 'month') (This Month)
                                        @elseif($dateFilter === 'this_year') (This Year)
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('PendingPaymentsWidget')
                    <a href="{{ route('filament.admin.resources.payments.index') }}"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <x-heroicon-o-credit-card class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        {{ getCurrencySymbol('INR') }}{{ number_format($this->getFilteredPendingPayments(), 2) }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Pending Payments
                                        @if($dateFilter === 'today') (Due Today)
                                        @elseif($dateFilter === 'yesterday') (Due Yesterday)
                                        @elseif($dateFilter === 'tomorrow') (Due Tomorrow)
                                        @elseif($dateFilter === 'week') (Due This Week)
                                        @elseif($dateFilter === 'month') (Due This Month)
                                        @elseif($dateFilter === 'this_year') (Due This Year)
                                        @endif
                                        <br>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('ApprovedIncentivesWidget')
                    <a href="{{ route('filament.admin.resources.incentives.index') }}"
                       class="block transition-all duration-200 hover:scale-105 hover:shadow-lg group cursor-pointer">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-transparent group-hover:border-primary-200 dark:group-hover:border-blue-600">
                            <div class="flex items-center gap-4">
                                <div class="text-primary-500 dark:text-blue-500 group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                    <x-heroicon-o-banknotes class="w-8 h-8" />
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-blue-400 transition-colors">
                                        {{ getCurrencySymbol('INR') }}{{ number_format(\App\Models\Incentive::where('status', 'approved')->sum('amount'), 2) }}
                                    </h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Approved Incentives<br></p>
                                </div>
                            </div>
                        </div>
                    </a>
                    @break

                    @case('RecentProjectsWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Recent Projects</h2>
                                <a href="{{ route('filament.admin.resources.projects.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Client
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">BDE
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($this->getFilteredRecentProjects() as $project)
                                        <tr
                                                class="dashboard-table-row border-b border-gray-200 dark:border-gray-700 transition duration-150">
                                                <td class="py-2 px-2">
                                                    <div class="flex items-center">
                                                        <div class="text-primary-500 dark:text-blue-500 mr-2">
                                                            <x-heroicon-o-document class="w-4 h-4" />
                                                        </div>
                                                        <span class="dashboard-text text-gray-900 dark:text-white"
                                                            title="{{ $project->title }}">{{ $project->title }}</span>
                                                    </div>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="dashboard-text text-gray-900 dark:text-white">{{ $project->client->company_name ?? 'No Client' }}</span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <span
                                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                        {{ $project->user->name ?? 'No User' }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right dashboard-text text-gray-900 dark:text-white">
                                                    {{ getCurrencySymbol($project->currency) }}{{ number_format($project->total_payment, 2) }}
                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="{{ route('filament.admin.resources.projects.edit', $project) }}"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Project">
                                                        <x-heroicon-o-pencil-square class="w-4 h-4" />
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach

                                        @if ($this->getFilteredRecentProjects()->isEmpty())
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No projects found
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @break

                    @case('UpcomingPaymentsWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Upcoming Payments</h2>
                                <a href="{{ route('filament.admin.resources.payments.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View All →
                                </a>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Project
                                            </th>
                                            <th class="text-left py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Due
                                                Date</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">Amount
                                            </th>
                                            <th class="text-center py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Status</th>
                                            <th class="text-right py-2 px-2 font-medium text-gray-600 dark:text-gray-400">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($this->getFilteredUpcomingPayments() as $payment)
                                            @php
                                                $isPast = $payment->due_date->isPast();
                                                $daysLeft = $isPast
                                                    ? $payment->due_date->diffInDays(now())
                                                    : now()->diffInDays($payment->due_date);
                                                $daysLeft = (int)round($daysLeft); // Convert to integer

                                                // Special case for same day
                                                if ($daysLeft == 0 && !$isPast) {
                                                    $statusText = 'Due today';
                                                    $statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                                                } else {
                                                    $statusClass = $isPast
                                                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                                        : ($daysLeft <= 7
                                                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                                            : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300');
                                                    $statusText = $isPast
                                                        ? "Overdue by {$daysLeft} " . Str::plural('day', $daysLeft)
                                                        : "{$daysLeft} " . Str::plural('day', $daysLeft) . ' left';
                                                }
                                            @endphp
                                            <tr
                                                class="dashboard-table-row border-b border-gray-200 dark:border-gray-700 transition duration-150">
                                                <td class="py-2 px-2">
                                                    <span class="dashboard-text text-gray-900 dark:text-white"
                                                        title="{{ $payment->project?->title ?? 'N/A' }}">
                                                        {{ $payment->project?->title ?? 'N/A' }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2">
                                                    <div class="dashboard-text text-gray-900 dark:text-white">
                                                        {{ $payment->due_date->format('M d, Y') }}</div>
                                                    <div class="dashboard-text-muted text-xs text-gray-500 dark:text-gray-400">
                                                        {{ $payment->due_date->diffForHumans() }}</div>
                                                </td>
                                                <td class="py-2 px-2 text-right dashboard-text text-gray-900 dark:text-white">
                                                    {{ getCurrencySymbol($payment->project->currency ?? 'INR') }}{{ number_format($payment->amount, 2) }}
                                                </td>
                                                <td class="py-2 px-2 text-center">
                                                    <span
                                                        class="text-xs px-2 py-1 rounded-full inline-block {{ $statusClass }}">
                                                        {{ $statusText }}
                                                    </span>
                                                </td>
                                                <td class="py-2 px-2 text-right">
                                                    <a href="{{ route('filament.admin.resources.payments.edit', $payment) }}"
                                                        class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 inline-flex items-center justify-center transition-colors"
                                                        title="Edit Payment">
                                                        <x-heroicon-o-pencil-square class="w-4 h-4" />
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach

                                        @if ($this->getFilteredUpcomingPayments()->isEmpty())
                                            <tr>
                                                <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                                    No upcoming payments found
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @break

                    @case('ProjectStatusWidget')
                        @php
                            // Calculate filtered project status counts
                            $statusCounts = $this->getFilteredProjectStatusCounts();
                            $activeProjects = $statusCounts['active'];
                            $completedProjects = $statusCounts['completed'];
                            $onHoldProjects = $statusCounts['on_hold'];
                            $cancelledProjects = $statusCounts['cancelled'];
                        @endphp
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Project Status</h2>
                                <a href="{{ route('filament.admin.resources.projects.index') }}"
                                    class="text-primary-600 hover:text-primary-800 dark:text-blue-500 dark:hover:text-blue-400 text-sm flex items-center">
                                    View Projects →
                                </a>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">In Progress</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $activeProjects }}</p>
                                        </div>
                                        <div class="text-primary-500 dark:text-blue-500">
                                            <x-heroicon-o-clock class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Completed</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $completedProjects }}</p>
                                        </div>
                                        <div class="text-green-600 dark:text-green-500">
                                            <x-heroicon-o-check-circle class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">On Hold</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $onHoldProjects }}</p>
                                        </div>
                                        <div class="text-yellow-600 dark:text-yellow-500">
                                            <x-heroicon-o-pause class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Cancelled</p>
                                            <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $cancelledProjects }}</p>
                                        </div>
                                        <div class="text-red-600 dark:text-red-500">
                                            <x-heroicon-o-x-circle class="w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @break
                    @case('MonthlyRevenueWidget')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            @livewire(\App\Filament\Widgets\MonthlyRevenueChart::class)
                        </div>
                    @break

                    @case('MilestoneDueVsReceivedChart')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            @livewire(\App\Filament\Widgets\MilestoneDueVsReceivedChart::class)
                        </div>
                    @break

                    @case('PaymentStatusChart')
                        {{-- This case is now handled in ClientRevenueChart case above --}}
                    @break

                    @case('RevenueForecastChart')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            @livewire(\App\Filament\Widgets\RevenueForecastChart::class)
                        </div>
                    @break

                    @case('BdePerformanceChart')
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                            @livewire(\App\Filament\Widgets\BdePerformanceChart::class)
                        </div>
                    @break

                    @default

                @endswitch
            </div>
        @endforeach
    </div>

    @if (count($widgets) === 0)
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
            <div class="text-center py-6">
                <div class="text-primary-500 dark:text-blue-500 mb-4">
                    <x-heroicon-o-squares-2x2 class="w-12 h-12 mx-auto" />
                </div>
                <h2 class="text-xl font-bold mb-2 text-gray-900 dark:text-white">No widgets configured</h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4">You haven't configured any dashboard widgets yet.</p>
                <a href="{{ route('filament.admin.pages.dashboard-settings') }}"
                    class="inline-flex items-center px-4 py-2 bg-primary-600 dark:bg-blue-600 rounded-lg text-white hover:bg-primary-700 dark:hover:bg-blue-700 transition">
                    <x-heroicon-o-cog-6-tooth class="w-5 h-5 mr-2" />
                    Configure Dashboard
                </a>
            </div>
        </div>
    @endif


</x-filament-panels::page>
