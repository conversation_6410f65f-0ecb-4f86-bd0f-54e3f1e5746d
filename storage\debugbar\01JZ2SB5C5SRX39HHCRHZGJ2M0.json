{"__meta": {"id": "01JZ2SB5C5SRX39HHCRHZGJ2M0", "datetime": "2025-07-01 10:43:24", "utime": **********.166411, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 12, "messages": [{"message": "[10:43:23] LOG.info: [NOTIFICATION_SERVICE] Sending notification {\n    \"event\": \"milestone_completed\",\n    \"user_ids\": [\n        4\n    ],\n    \"data\": {\n        \"milestone_id\": 96,\n        \"milestone_title\": \"Month 1 Milestone\",\n        \"project_title\": \"E-commerce\",\n        \"status\": \"completed\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.891553, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Payment updated {\n    \"payment_id\": 396,\n    \"status\": \"paid\",\n    \"original_status\": \"pending\",\n    \"is_dirty_status\": true,\n    \"milestone_id\": 96,\n    \"project_id\": 160\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.017989, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Payment marked as paid, creating incentive {\n    \"payment_id\": 396,\n    \"new_status\": \"paid\",\n    \"old_status\": \"pending\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.018429, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Using project-based incentive calculation for Fixed-USD {\n    \"payment_id\": 396,\n    \"project_id\": 160,\n    \"pricing_model\": \"Fixed-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.022189, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Project completion calculation {\n    \"project_id\": 160,\n    \"paid_milestones_count\": 1,\n    \"paid_milestone_ids\": [\n        96\n    ],\n    \"total_percentage\": 50\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.034615, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Project completion recalculated {\n    \"payment_id\": 396,\n    \"project_id\": 160,\n    \"total_completion_percentage\": 50\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.034913, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PROJECT] Searching for incentive rules {\n    \"project_id\": 160,\n    \"user_id\": 4,\n    \"user_role_ids\": [\n        15\n    ],\n    \"pricing_model_name\": \"Fixed-USD\",\n    \"product_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.037639, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PROJECT] Excluding product-specific rules for non-Product pricing model {\n    \"project_id\": 160,\n    \"pricing_model_name\": \"Fixed-USD\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.03911, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PROJECT] Found incentive rules {\n    \"project_id\": 160,\n    \"rules_count\": 1,\n    \"rules\": [\n        {\n            \"id\": 99,\n            \"pricing_model\": \"Fixed-USD\",\n            \"role_id\": 15,\n            \"role_name\": \"bde_team\",\n            \"product_id\": null,\n            \"product_name\": null,\n            \"currency\": \"INR\",\n            \"amount\": null\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.049252, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Found applicable incentive rules {\n    \"payment_id\": 396,\n    \"project_id\": 160,\n    \"applicable_rules_count\": 1,\n    \"rules\": [\n        99\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.049508, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Creating incentive for rule {\n    \"payment_id\": 396,\n    \"project_id\": 160,\n    \"rule_id\": 99,\n    \"rule_duration_percentage\": \"0.00\",\n    \"current_completion\": 50,\n    \"milestone_id\": 96\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.0535, "xdebug_link": null, "collector": "log"}, {"message": "[10:43:24] LOG.info: [PAYMENT_OBSERVER] Skipping Fixed-USD incentive: project not 100% complete {\n    \"payment_id\": 396,\n    \"pricing_model\": \"Fixed-USD\",\n    \"completion\": 50\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.053939, "xdebug_link": null, "collector": "log"}]}, "time": {"start": *********2.127316, "end": **********.166436, "duration": 2.0391199588775635, "duration_str": "2.04s", "measures": [{"label": "Booting", "start": *********2.127316, "relative_start": 0, "end": *********2.556298, "relative_end": *********2.556298, "duration": 0.****************, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": *********2.556314, "relative_start": 0.****************, "end": **********.166438, "relative_end": 2.1457672119140625e-06, "duration": 1.***************, "duration_str": "1.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.384841, "relative_start": 1.****************, "end": **********.387623, "relative_end": **********.387623, "duration": 0.002782106399536133, "duration_str": "2.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.087062, "relative_start": 1.****************, "end": **********.087062, "relative_end": **********.087062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.134936, "relative_start": 2.***************, "end": **********.134936, "relative_end": **********.134936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4e08262e37252af4d0ec53b8f597c6de", "start": **********.144675, "relative_start": 2.0173590183258057, "end": **********.144675, "relative_end": **********.144675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.163373, "relative_start": 2.0360569953918457, "end": **********.16434, "relative_end": **********.16434, "duration": 0.0009670257568359375, "duration_str": "967μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 60370672, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.08704, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.134923, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.144657, "type": "blade", "hash": "bladeD:\\wamp64\\www\\smms\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}]}, "queries": {"count": 268, "nb_statements": 268, "nb_visible_statements": 268, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.24203000000000008, "accumulated_duration_str": "242ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 168 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `sessions` where `id` = 'UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV' limit 1", "type": "query", "params": [], "bindings": ["UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.397916, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "local_kit_db", "explain": null, "start_percent": 0, "width_percent": 0.446}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4174762, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 0.446, "width_percent": 0.343}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.422616, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "local_kit_db", "explain": null, "start_percent": 0.789, "width_percent": 0.223}, {"sql": "select * from `cache` where `key` in ('filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 210}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.426975, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.012, "width_percent": 0.215}, {"sql": "delete from `cache` where `key` in ('filament-excel:exports:4', 'illuminate:cache:flexible:created:filament-excel:exports:4')", "type": "query", "params": [], "bindings": ["filament-excel:exports:4", "illuminate:cache:flexible:created:filament-excel:exports:4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 361}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 539}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 211}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.4294782, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:386", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "D:\\wamp64\\www\\smms\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=386", "ajax": false, "filename": "DatabaseStore.php", "line": "386"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.227, "width_percent": 0.182}, {"sql": "select * from `projects` where `projects`.`id` = 160 limit 1", "type": "query", "params": [], "bindings": [160], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.435519, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "D:\\wamp64\\www\\smms\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.409, "width_percent": 0.471}, {"sql": "select * from `milestones` where `project_id` = 160 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [160, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 206}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 26}], "start": **********.5287, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:206", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=206", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "206"}, "connection": "local_kit_db", "explain": null, "start_percent": 1.88, "width_percent": 0.917}, {"sql": "select * from `milestones` where `milestones`.`project_id` = 160 and `milestones`.`project_id` is not null and `milestones`.`id` in (96) and `milestones`.`id` = '96' limit 1", "type": "query", "params": [], "bindings": [160, 96, "96"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 301}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasActions.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 263}], "start": **********.533701, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRecords.php:121", "source": {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasRecords.php&line=121", "ajax": false, "filename": "HasRecords.php", "line": "121"}, "connection": "local_kit_db", "explain": null, "start_percent": 2.797, "width_percent": 0.277}, {"sql": "select * from `milestones` where `project_id` = 160 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [160, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 379}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 264}], "start": **********.539212, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:379", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 379}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=379", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "379"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.074, "width_percent": 0.343}, {"sql": "select * from `payments` where `milestone_id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 380}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.546412, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:395", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=395", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "395"}, "connection": "local_kit_db", "explain": null, "start_percent": 3.417, "width_percent": 1.318}, {"sql": "select * from `payments` where `milestone_id` = 98 limit 1", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 380}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.551297, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:395", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=395", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "395"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.735, "width_percent": 0.215}, {"sql": "select * from `payments` where `milestone_id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, {"index": 21, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 380}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/actions/src/Concerns/HasForm.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\actions\\src\\Concerns\\HasForm.php", "line": 55}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasActions.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Concerns\\HasActions.php", "line": 278}], "start": **********.5532231, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:395", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=395", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "395"}, "connection": "local_kit_db", "explain": null, "start_percent": 4.95, "width_percent": 0.211}, {"sql": "select * from `milestones` where `project_id` = 160 and `is_merged` = 0 order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [160, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 206}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasQuery.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasQuery.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 76}], "start": **********.558074, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:206", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=206", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "206"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.161, "width_percent": 0.496}, {"sql": "select * from `projects` where `projects`.`id` = 160 limit 1", "type": "query", "params": [], "bindings": [160], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 499}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.564172, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:499", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 499}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=499", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "499"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.656, "width_percent": 0.289}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.566659, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 5.946, "width_percent": 0.376}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.568743, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.322, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.571114, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.495, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.572487, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.652, "width_percent": 0.136}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.574428, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.788, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5762, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 6.941, "width_percent": 0.269}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.5823739, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.21, "width_percent": 0.236}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.584143, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:495", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=495", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "495"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.445, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5867622, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.602, "width_percent": 0.182}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.588164, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.784, "width_percent": 0.153}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.59096, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 7.937, "width_percent": 0.289}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.593195, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.226, "width_percent": 0.339}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 89}], "start": **********.5955408, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.565, "width_percent": 0.326}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.597606, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 8.891, "width_percent": 0.223}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.5988579, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.115, "width_percent": 0.178}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.6011212, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.292, "width_percent": 0.194}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.602592, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:495", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=495", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "495"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.486, "width_percent": 0.165}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.605396, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.652, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.607077, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 9.858, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.60907, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.032, "width_percent": 0.331}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.6111672, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.362, "width_percent": 0.335}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.613925, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.697, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 753}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 644}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeValidated.php", "line": 672}], "start": **********.615601, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:495", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 495}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=495", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "495"}, "connection": "local_kit_db", "explain": null, "start_percent": 10.904, "width_percent": 0.161}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.618484, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.065, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.619976, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.238, "width_percent": 0.149}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.6215022, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.387, "width_percent": 0.256}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 80}], "start": **********.623205, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.643, "width_percent": 0.227}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.625065, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 11.87, "width_percent": 0.347}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.6271741, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.217, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.628937, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.424, "width_percent": 0.202}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.630761, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.627, "width_percent": 0.252}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.632478, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 12.879, "width_percent": 0.244}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 58}], "start": **********.634162, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.122, "width_percent": 0.231}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.635792, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.354, "width_percent": 0.219}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.63698, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.573, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.638341, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.726, "width_percent": 0.165}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.639793, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 13.891, "width_percent": 0.161}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.640835, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.052, "width_percent": 0.161}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.642786, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.213, "width_percent": 0.372}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.645047, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.585, "width_percent": 0.31}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.6470761, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 14.895, "width_percent": 0.19}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.648656, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.085, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.650085, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.242, "width_percent": 0.153}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.651618, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.395, "width_percent": 0.223}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 49}], "start": **********.653367, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.618, "width_percent": 0.219}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.655393, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 15.837, "width_percent": 0.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.656896, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.006, "width_percent": 0.182}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.6583512, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.188, "width_percent": 0.335}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.660927, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.523, "width_percent": 0.376}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.663011, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 16.899, "width_percent": 0.302}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 27}], "start": **********.6648428, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.2, "width_percent": 0.24}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.666514, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.44, "width_percent": 0.215}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.667947, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.655, "width_percent": 0.194}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.6695669, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 17.849, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.671015, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.006, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.67253, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.159, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.67413, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.332, "width_percent": 0.252}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.676305, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.584, "width_percent": 0.359}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.678674, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 18.944, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.680434, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.188, "width_percent": 0.178}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.68195, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.365, "width_percent": 0.14}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.6834579, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.506, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeValidated.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeValidated.php", "line": 18}], "start": **********.6851492, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.733, "width_percent": 0.186}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.68767, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 19.919, "width_percent": 0.198}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.689436, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.117, "width_percent": 0.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.690916, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.287, "width_percent": 0.153}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.692835, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.44, "width_percent": 0.368}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.695457, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 20.807, "width_percent": 0.351}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\CanBeHidden.php", "line": 9}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 153}], "start": **********.697502, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.159, "width_percent": 0.248}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.698966, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.406, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.700462, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.634, "width_percent": 0.219}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.702094, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 21.853, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.703673, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.068, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.705036, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.241, "width_percent": 0.157}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.7064888, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.398, "width_percent": 0.248}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 98 and `is_merged` = 1", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.708186, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 22.646, "width_percent": 0.376}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.710471, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.022, "width_percent": 0.231}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.712514, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.253, "width_percent": 0.244}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.71415, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.497, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.715689, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.671, "width_percent": 0.24}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = 99 and `is_merged` = 1", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 19, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasState.php", "line": 461}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 148}], "start": **********.717402, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:706", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 706}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=706", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "706"}, "connection": "local_kit_db", "explain": null, "start_percent": 23.91, "width_percent": 0.281}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.7357838, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.191, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.737685, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:500", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 500}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=500", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "500"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.443, "width_percent": 0.198}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 23, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.739249, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:515", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=515", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "515"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.642, "width_percent": 0.169}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 52}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Concerns/HasState.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Concerns\\HasState.php", "line": 63}], "start": **********.7409, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 24.811, "width_percent": 0.256}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = 96 and `is_merged` = 1) as `exists`", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, {"index": 12, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 13, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/CanBeHidden.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\CanBeHidden.php", "line": 128}, {"index": 14, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 72}, {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasChildComponents.php", "file": "D:\\wamp64\\www\\smms\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasChildComponents.php", "line": 63}], "start": **********.743031, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "MilestonesRelationManager.php:641", "source": {"index": 11, "namespace": null, "name": "app/Filament/Resources/ProjectResource/RelationManagers/MilestonesRelationManager.php", "file": "D:\\wamp64\\www\\smms\\app\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager.php", "line": 641}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FFilament%2FResources%2FProjectResource%2FRelationManagers%2FMilestonesRelationManager.php&line=641", "ajax": false, "filename": "MilestonesRelationManager.php", "line": "641"}, "connection": "local_kit_db", "explain": null, "start_percent": 25.067, "width_percent": 0.359}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745405, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 25.427, "width_percent": 0.343}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746665, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 25.77, "width_percent": 0.194}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747423, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 25.964, "width_percent": 0.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748245, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.133, "width_percent": 0.149}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749038, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.282, "width_percent": 0.128}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749676, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.41, "width_percent": 0.161}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7505538, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.571, "width_percent": 0.169}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751269, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.74, "width_percent": 0.182}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.752102, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 26.922, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.752855, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.075, "width_percent": 0.132}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7535121, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.207, "width_percent": 0.132}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754257, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.34, "width_percent": 0.198}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755013, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.538, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7570448, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.711, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757931, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 27.885, "width_percent": 0.145}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759424, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 28.03, "width_percent": 0.293}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760624, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 28.323, "width_percent": 0.178}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761863, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 28.501, "width_percent": 0.289}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762949, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 28.79, "width_percent": 0.211}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763857, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.001, "width_percent": 0.149}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76461, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.149, "width_percent": 0.128}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765387, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.277, "width_percent": 0.211}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766183, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.488, "width_percent": 0.178}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766996, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.666, "width_percent": 0.149}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767722, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.814, "width_percent": 0.145}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768529, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 29.959, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7692828, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 30.162, "width_percent": 0.169}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771816, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 30.331, "width_percent": 0.215}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772981, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 30.546, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.773731, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 30.699, "width_percent": 0.132}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.774554, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 30.831, "width_percent": 0.182}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7756388, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 31.013, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7767599, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 31.265, "width_percent": 0.178}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777622, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 31.442, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7786372, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 31.616, "width_percent": 0.256}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.779623, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 31.872, "width_percent": 0.182}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780518, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.054, "width_percent": 0.145}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7812371, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.198, "width_percent": 0.124}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7819452, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.322, "width_percent": 0.182}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782634, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.504, "width_percent": 0.157}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.783309, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.661, "width_percent": 0.161}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7849479, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.822, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785772, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 32.975, "width_percent": 0.12}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.786833, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.095, "width_percent": 0.132}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7875152, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.227, "width_percent": 0.112}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7883291, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.339, "width_percent": 0.198}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.78912, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.537, "width_percent": 0.161}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.789899, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.698, "width_percent": 0.128}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7906, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.826, "width_percent": 0.124}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791476, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 33.95, "width_percent": 0.24}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.79238, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.19, "width_percent": 0.256}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793603, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.446, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.794462, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.62, "width_percent": 0.145}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.79528, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.764, "width_percent": 0.194}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796031, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 34.958, "width_percent": 0.157}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796762, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.115, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.797368, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.289, "width_percent": 0.161}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7980208, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.45, "width_percent": 0.165}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.798998, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.615, "width_percent": 0.145}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.799719, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.76, "width_percent": 0.136}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.80068, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 35.896, "width_percent": 0.76}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803051, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.657, "width_percent": 0.223}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.804183, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 36.88, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805129, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.037, "width_percent": 0.161}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.80603, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.198, "width_percent": 0.223}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8068862, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.421, "width_percent": 0.19}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807729, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.611, "width_percent": 0.14}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8087542, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.752, "width_percent": 0.186}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8098001, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 37.937, "width_percent": 0.26}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8107622, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.198, "width_percent": 0.198}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.813045, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.396, "width_percent": 0.236}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8141398, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.632, "width_percent": 0.165}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.815377, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.797, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.816196, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 38.95, "width_percent": 0.12}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817112, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.07, "width_percent": 0.202}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8179948, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.272, "width_percent": 0.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.818805, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.441, "width_percent": 0.128}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.819514, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.569, "width_percent": 0.128}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8203251, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.698, "width_percent": 0.19}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.821093, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 39.888, "width_percent": 0.202}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8220131, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.09, "width_percent": 0.132}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.822742, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.222, "width_percent": 0.149}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823607, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.371, "width_percent": 0.219}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.824451, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.59, "width_percent": 0.194}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8258328, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 40.784, "width_percent": 1.19}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.829154, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 41.974, "width_percent": 0.289}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.830498, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.263, "width_percent": 0.43}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.832387, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.693, "width_percent": 0.207}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833344, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 42.9, "width_percent": 0.145}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.834315, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.044, "width_percent": 0.215}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8351681, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.259, "width_percent": 0.186}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8359761, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.445, "width_percent": 0.132}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.836674, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.577, "width_percent": 0.124}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.837394, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.701, "width_percent": 0.198}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838213, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 43.9, "width_percent": 0.202}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839079, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.102, "width_percent": 0.14}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839828, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.242, "width_percent": 0.132}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.840667, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.375, "width_percent": 0.198}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841466, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.573, "width_percent": 0.322}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.843298, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 44.895, "width_percent": 0.252}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.844356, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.147, "width_percent": 0.14}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.845047, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.288, "width_percent": 0.112}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84594, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.399, "width_percent": 0.339}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.847059, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.738, "width_percent": 0.211}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8478742, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 45.949, "width_percent": 0.182}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.848628, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.131, "width_percent": 0.186}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849339, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.317, "width_percent": 0.169}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.850169, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.486, "width_percent": 0.153}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851042, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.639, "width_percent": 0.149}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851882, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.788, "width_percent": 0.165}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.852821, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 46.953, "width_percent": 0.215}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.853653, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.168, "width_percent": 0.174}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8544571, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.341, "width_percent": 0.136}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855221, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.478, "width_percent": 0.132}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855911, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.61, "width_percent": 0.12}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.856657, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.73, "width_percent": 0.186}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.857415, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 47.916, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.861155, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.073, "width_percent": 0.331}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862474, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.403, "width_percent": 0.174}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863569, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.577, "width_percent": 0.236}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.864469, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.812, "width_percent": 0.157}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.865211, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 48.969, "width_percent": 0.14}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.86597, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.11, "width_percent": 0.149}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.866786, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.258, "width_percent": 0.178}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.867503, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.436, "width_percent": 0.145}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.868206, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.581, "width_percent": 0.112}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.868855, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.692, "width_percent": 0.107}, {"sql": "select exists(select * from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.869543, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.8, "width_percent": 0.157}, {"sql": "select count(*) as aggregate from `milestones` where `merged_with_milestone_id` = ? and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870202, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 49.957, "width_percent": 0.149}, {"sql": "select * from `milestones` where `project_id` = ? and `id` not in (?, ?, ?) and `is_merged` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.871556, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 50.105, "width_percent": 0.227}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8724139, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 50.333, "width_percent": 0.149}, {"sql": "update `milestones` set `status` = ?, `milestones`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.874988, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 50.481, "width_percent": 1.446}, {"sql": "select exists(select * from `payments` where `milestone_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.879279, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 51.927, "width_percent": 0.227}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.880537, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.155, "width_percent": 0.198}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.881402, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.353, "width_percent": 0.186}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8822858, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.539, "width_percent": 0.161}, {"sql": "select * from `breezy_sessions` where `breezy_sessions`.`authenticatable_id` in (4) and `breezy_sessions`.`authenticatable_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.883064, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.7, "width_percent": 0.14}, {"sql": "select * from `notification_events` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.884549, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 52.841, "width_percent": 1.475}, {"sql": "select * from `notification_preferences` where `notification_preferences`.`user_id` = ? and `notification_preferences`.`user_id` is not null and `notification_event_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928856, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 54.316, "width_percent": 1.037}, {"sql": "select `id` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = ? and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936405, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 55.353, "width_percent": 0.289}, {"sql": "select * from `role_notification_settings` where `role_id` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938218, "duration": 0.06739, "duration_str": "67.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 55.642, "width_percent": 27.844}, {"sql": "insert into `app_notifications` (`user_id`, `notification_event_id`, `title`, `message`, `data`, `status`, `sent_at`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0083919, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 83.486, "width_percent": 1.69}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.013565, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 85.175, "width_percent": 0.512}, {"sql": "update `payments` set `paid_date` = ?, `status` = ?, `payment_method` = ?, `payments`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.016486, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 85.688, "width_percent": 0.306}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.01883, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 85.993, "width_percent": 0.583}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.020906, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 86.576, "width_percent": 0.264}, {"sql": "select * from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null and exists (select * from `payments` where `milestones`.`id` = `payments`.`milestone_id` and `status` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0227852, "duration": 0.01116, "duration_str": "11.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 86.84, "width_percent": 4.611}, {"sql": "select `role_id` from `model_has_roles` where `model_type` = ? and `model_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0354, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 91.451, "width_percent": 0.454}, {"sql": "select `role_id` from `model_has_roles` where `model_has_roles`.`model_type` = ? and `model_has_roles`.`model_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.036736, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 91.906, "width_percent": 0.202}, {"sql": "select * from `incentive_rules` where exists (select * from `pricing_models` where `incentive_rules`.`pricing_model_id` = `pricing_models`.`id` and `name` = ?) and `role_id` in (?) and `product_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.03934, "duration": 0.006809999999999999, "duration_str": "6.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 92.108, "width_percent": 2.814}, {"sql": "select * from `pricing_models` where `pricing_models`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.046786, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 94.922, "width_percent": 0.198}, {"sql": "select * from `roles` where `roles`.`id` in (15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.047689, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.12, "width_percent": 0.145}, {"sql": "select * from `incentives` where `project_id` = ? and `user_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.049741, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 95.265, "width_percent": 0.921}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052429, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.186, "width_percent": 0.207}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0545142, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.393, "width_percent": 0.269}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055881, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.662, "width_percent": 0.19}, {"sql": "select * from `milestones` where `milestones`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.056884, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 96.852, "width_percent": 0.153}, {"sql": "select * from `payments` where `milestone_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.05787, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.005, "width_percent": 0.153}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.157, "width_percent": 0.463}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.078872, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.62, "width_percent": 0.351}, {"sql": "select count(*) as aggregate from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null and `milestones`.`id` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0803828, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 97.971, "width_percent": 0.165}, {"sql": "select * from `milestones` where `milestones`.`project_id` = ? and `milestones`.`project_id` is not null and `milestones`.`id` in (?) order by `milestones`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0809321, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.137, "width_percent": 0.136}, {"sql": "select * from `projects` where `projects`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.112864, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.273, "width_percent": 0.297}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.12151, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.57, "width_percent": 0.351}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (4) and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1235878, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 98.922, "width_percent": 0.236}, {"sql": "select * from `cache` where `key` in (?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127353, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.157, "width_percent": 0.314}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (4) and `model_has_permissions`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.131227, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.471, "width_percent": 0.219}, {"sql": "select * from `milestones` where `project_id` = ? and `is_merged` = ? order by `due_date` asc, `created_at` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.140712, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "local_kit_db", "explain": null, "start_percent": 99.69, "width_percent": 0.31}]}, "models": {"data": {"App\\Models\\PricingModel": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPricingModel.php&line=1", "ajax": false, "filename": "PricingModel.php", "line": "?"}}, "App\\Models\\Milestone": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FMilestone.php&line=1", "ajax": false, "filename": "Milestone.php", "line": "?"}}, "App\\Models\\Payment": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\Project": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\Role": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\NotificationEvent": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FNotificationEvent.php&line=1", "ajax": false, "filename": "NotificationEvent.php", "line": "?"}}, "App\\Models\\RoleNotificationSettings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FRoleNotificationSettings.php&line=1", "ajax": false, "filename": "RoleNotificationSettings.php", "line": "?"}}, "App\\Models\\IncentiveRule": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fapp%2FModels%2FIncentiveRule.php&line=1", "ajax": false, "filename": "IncentiveRule.php", "line": "?"}}}, "count": 161, "is_counter": true}, "livewire": {"data": {"app.filament.resources.project-resource.relation-managers.milestones-relation-manager #ctK5yb8PoSUJ5ssxN81r": "array:4 [\n  \"data\" => array:40 [\n    \"ownerRecord\" => App\\Models\\Project {#3908\n      #connection: \"mysql\"\n      #table: \"projects\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:21 [\n        \"id\" => 160\n        \"client_id\" => 25\n        \"user_id\" => 4\n        \"project_type_id\" => 22\n        \"pricing_model_id\" => 2\n        \"title\" => \"E-commerce\"\n        \"description\" => null\n        \"won_date\" => \"2025-06-30\"\n        \"start_date\" => \"2025-07-01\"\n        \"end_date\" => \"2025-10-31\"\n        \"delivery_date\" => null\n        \"total_payment\" => \"4000.00\"\n        \"currency\" => \"USD\"\n        \"incentive_method\" => null\n        \"duration\" => 4\n        \"duration_unit\" => \"months\"\n        \"payment_cycle\" => \"monthly\"\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 10:42:06\"\n        \"updated_at\" => \"2025-07-01 10:42:06\"\n        \"product_id\" => null\n      ]\n      #original: array:21 [\n        \"id\" => 160\n        \"client_id\" => 25\n        \"user_id\" => 4\n        \"project_type_id\" => 22\n        \"pricing_model_id\" => 2\n        \"title\" => \"E-commerce\"\n        \"description\" => null\n        \"won_date\" => \"2025-06-30\"\n        \"start_date\" => \"2025-07-01\"\n        \"end_date\" => \"2025-10-31\"\n        \"delivery_date\" => null\n        \"total_payment\" => \"4000.00\"\n        \"currency\" => \"USD\"\n        \"incentive_method\" => null\n        \"duration\" => 4\n        \"duration_unit\" => \"months\"\n        \"payment_cycle\" => \"monthly\"\n        \"status\" => \"active\"\n        \"created_at\" => \"2025-07-01 10:42:06\"\n        \"updated_at\" => \"2025-07-01 10:42:06\"\n        \"product_id\" => null\n      ]\n      #changes: []\n      #previous: []\n      #casts: array:9 [\n        \"id\" => \"integer\"\n        \"client_id\" => \"integer\"\n        \"user_id\" => \"integer\"\n        \"project_type_id\" => \"integer\"\n        \"won_date\" => \"date\"\n        \"start_date\" => \"date\"\n        \"end_date\" => \"date\"\n        \"delivery_date\" => \"date\"\n        \"total_payment\" => \"decimal:2\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      #relationAutoloadCallback: null\n      #relationAutoloadContext: null\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:18 [\n        0 => \"client_id\"\n        1 => \"user_id\"\n        2 => \"project_type_id\"\n        3 => \"product_id\"\n        4 => \"pricing_model_id\"\n        5 => \"title\"\n        6 => \"description\"\n        7 => \"won_date\"\n        8 => \"start_date\"\n        9 => \"end_date\"\n        10 => \"delivery_date\"\n        11 => \"total_payment\"\n        12 => \"duration\"\n        13 => \"duration_unit\"\n        14 => \"payment_cycle\"\n        15 => \"status\"\n        16 => \"currency\"\n        17 => \"incentive_method\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"pageClass\" => \"App\\Filament\\Resources\\ProjectResource\\Pages\\EditProject\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeTab\" => null\n    \"isTableLoaded\" => false\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableRecordsPerPage\" => 10\n    \"isTableReordering\" => false\n    \"tableColumnSearches\" => []\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => array:2 [\n      0 => []\n      1 => []\n    ]\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => []\n    \"defaultTableActionArguments\" => []\n    \"defaultTableActionRecord\" => []\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableFilters\" => null\n    \"tableDeferredFilters\" => null\n    \"paginators\" => array:1 [\n      \"milestonesRelationManagerPage\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.resources.project-resource.relation-managers.milestones-relation-manager\"\n  \"component\" => \"App\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager\"\n  \"id\" => \"ctK5yb8PoSUJ5ssxN81r\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-238040101 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238040101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.132817, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=96),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-955338995 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=96)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\Milestone(id=96)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955338995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.133456, "xdebug_link": null}, {"message": "[\n  ability => delete_milestone,\n  target => null,\n  result => true,\n  user => 4,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-708622015 data-indent-pad=\"  \"><span class=sf-dump-note>delete_milestone </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_milestone</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708622015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.143953, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Milestone(id=96),\n  result => true,\n  user => 4,\n  arguments => [0 => Object(App\\Models\\Milestone)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-404916551 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Milestone(id=96)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Models\\Milestone(id=96)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"35 characters\">[0 =&gt; Object(App\\Models\\Milestone)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404916551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144118, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Resources\\ProjectResource\\RelationManagers\\MilestonesRelationManager@callMountedTableAction<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasActions.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasActions.php:70-161</a>", "middleware": "web", "duration": "2.05s", "peak_memory": "64MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-285745418 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-285745418\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"4314 characters\">{&quot;data&quot;:{&quot;ownerRecord&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Project&quot;,&quot;key&quot;:160,&quot;s&quot;:&quot;mdl&quot;}],&quot;pageClass&quot;:&quot;App\\\\Filament\\\\Resources\\\\ProjectResource\\\\Pages\\\\EditProject&quot;,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;activeTab&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableRecordsPerPage&quot;:10,&quot;isTableReordering&quot;:false,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[&quot;editAll&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[[{&quot;milestones&quot;:[{&quot;5b4fb8f5-7900-49e3-b3c8-a6940ef50e81&quot;:[{&quot;id&quot;:96,&quot;project_id&quot;:160,&quot;title&quot;:&quot;Month 1 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 1 of 4&quot;,&quot;due_date&quot;:&quot;2025-09-01&quot;,&quot;percentage&quot;:&quot;50.00&quot;,&quot;hours&quot;:&quot;0.00&quot;,&quot;amount&quot;:&quot;2000.00&quot;,&quot;status&quot;:&quot;completed&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:&quot;1000.00&quot;,&quot;original_percentage&quot;:&quot;25.00&quot;,&quot;original_due_date&quot;:&quot;2025-08-01T00:00:00.000000Z&quot;,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:51.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-08-01&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;2000.00&quot;,&quot;payment_notes&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;824a1a2f-b11c-4230-a27d-fee15d93627e&quot;:[{&quot;id&quot;:98,&quot;project_id&quot;:160,&quot;title&quot;:&quot;Month 3 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 3 of 4&quot;,&quot;due_date&quot;:&quot;2025-10-01&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-10-01&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;1000.00&quot;,&quot;payment_notes&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;e3e0b072-1312-472a-9861-7dc33316bb23&quot;:[{&quot;id&quot;:99,&quot;project_id&quot;:160,&quot;title&quot;:&quot;Month 4 Milestone&quot;,&quot;description&quot;:&quot;Auto-generated milestone 4 of 4&quot;,&quot;due_date&quot;:&quot;2025-11-01&quot;,&quot;percentage&quot;:&quot;25.00&quot;,&quot;hours&quot;:null,&quot;amount&quot;:&quot;1000.00&quot;,&quot;status&quot;:&quot;pending&quot;,&quot;merged_with_milestone_id&quot;:null,&quot;is_merged&quot;:false,&quot;original_amount&quot;:null,&quot;original_percentage&quot;:null,&quot;original_due_date&quot;:null,&quot;original_hours&quot;:null,&quot;created_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T10:42:06.000000Z&quot;,&quot;payment_due_date&quot;:&quot;2025-11-01&quot;,&quot;payment_paid_date&quot;:null,&quot;payment_method&quot;:null,&quot;transaction_id&quot;:null,&quot;payment_status&quot;:&quot;pending&quot;,&quot;payment_amount&quot;:&quot;1000.00&quot;,&quot;payment_notes&quot;:null,&quot;merged_milestones_guide&quot;:null,&quot;merge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;unmerge_milestone_ids&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[[[],{&quot;s&quot;:&quot;arr&quot;}],[[],{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:&quot;96&quot;,&quot;defaultTableAction&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultTableActionRecord&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableFilters&quot;:null,&quot;tableDeferredFilters&quot;:null,&quot;paginators&quot;:[{&quot;milestonesRelationManagerPage&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;ctK5yb8PoSUJ5ssxN81r&quot;,&quot;name&quot;:&quot;app.filament.resources.project-resource.relation-managers.milestones-relation-manager&quot;,&quot;path&quot;:&quot;admin\\/projects\\/160\\/edit&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;fd0941d91b4783e6094d8fad6353c191a3e086a6282d439b6875a275d6f384f5&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.5b4fb8f5-7900-49e3-b3c8-a6940ef50e81.payment_paid_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-01</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.5b4fb8f5-7900-49e3-b3c8-a6940ef50e81.payment_method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">bank_transfer</span>\"\n        \"<span class=sf-dump-key>mountedTableActionsData.0.milestones.5b4fb8f5-7900-49e3-b3c8-a6940ef50e81.payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"22 characters\">callMountedTableAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5323</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/projects/160/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InRnSUFIeWtvempaTktBRjBFWE9McGc9PSIsInZhbHVlIjoiQ2tWYXhmRjdyL1ArclpXeXFqL2JOUmJXa0tNL0ZSOVVjNDBackZSOWMvTWZtdG9JRVBjQ3JIL1dqcmxCZXRGVGwvTzBkVHp6cnFHSVBlMVFIMVZNZVY3bWEzTWlqaHRsZkUxMisyaldVMVNyOGlXT0FHajZuL2k2cGJldlI3TkZ5dFA4LysrckdjdWlZNUM1ZEFjRXgzZUlyQWQzZGZqNSsxN0JiTHRHaEZCZTlDaEhsNVFSYmtXSHphbjZZQm9oZHAwekxJZGR1KzdlV2FIbEV3UlA1Qzd5ZlkzNWkzd01XUGl5SVd3MGREWT0iLCJtYWMiOiI2NGZlZTAzMWUxNGI0ZWNjNDQ4YWFkZTgwZmE1MGRjN2U5ZDY2ZGEyZmExNzNiM2M5NTZjMzkyOTc5ODczMzhkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjhYMGwxMkxiZUlWRW1GNHFxM3VEcGc9PSIsInZhbHVlIjoiL25Hb2xpekl2SzVwTkdLaEs4UDg3aE4rek1kLzU4Q0h1ZmU1VVFHSmE2bnJWSzMzelhraTBSYmd0UDd2RHFjTDNYQkVBdE9lVVE2YWR4Y0ZZL212Tkk2bjFQQi8vREJMdmF0T0taNCtKWFhZRldBSzBIb0RKcjQ3RWhVcS9MQXoiLCJtYWMiOiIwMGYyOGI4NDkwZDM1OTA4ZWM2NjkyMDA3ZjlmYjE0ODA1NDQ4ZmIxYmM5NmNjNGRhMzhhMTdmM2FhOTY3ZjQxIiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IlZGcEV2dmd5Z09yYk4xa2xabGhwOVE9PSIsInZhbHVlIjoibEt2ZFF3OGw3Ni8xSmd5ZFE2MjhRNEVDbXlRNGl4dXM0SzN4VDF3aHZZdTJKM1U4d3BEOTdmNGhobXVDYm5oR3RLeGJlV1RXTmROZlVCMkVFWm83RTEyb3U3bG1mWVdWeXJpK1NRN01hdnRWTkwvQzBRbThWYnJkYUNaWDd1eXEiLCJtYWMiOiI1MWY2MTdkMzY1NTM1YWI4ZTZkNTBiNTkzZjEwODIwNzJiMjk4ZTMyZjAzZmYzMTY3NTIxNzQwOTBjNzY3N2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-446727769 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|N8eoFvYEnIkXq8v1Jp9cXichQYhN1SR8YPk8jKbVbaThkHwX5k1OMDiVGjY5|$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzRca2cIcSrtBm0SLWAjxZRX24b7SNdqnnJ75mXV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446727769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1504583484 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 10:43:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504583484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-219143555 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IoT5n1JtJaWNtoSdsfAvX5q2e1wiWwdxVEVb0xbD</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://localhost:8000/admin/projects/160/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$o.k3SBD1Qtz56hK2U/sCS.K9H78zqGt3P7yD0af1jOjyJGSkd9NLW</span>\"\n  \"<span class=sf-dump-key>tables</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>fb545cd2753841816bc6e0cac0f93759_per_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219143555\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}