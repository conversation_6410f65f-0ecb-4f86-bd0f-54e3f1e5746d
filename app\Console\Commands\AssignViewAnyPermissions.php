<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;
use App\Models\Permission;

class AssignViewAnyPermissions extends Command
{
    protected $signature = 'permissions:assign-view-any {role}';
    protected $description = 'Assign view_any permissions to a specific role';

    public function handle()
    {
        $roleName = $this->argument('role');

        $role = Role::where('name', $roleName)->first();

        if (!$role) {
            $this->error("Role '{$roleName}' not found!");
            return 1;
        }

        $viewAnyPermissions = [
            'view_any_client',
            'view_any_project',
            'view_any_milestone',
            'view_any_incentive',
            'view_any_payment',
            'view_any_role',
            'view_any_project_type',
            'view_any_pricing_model',
            'view_any_incentive_rule'
        ];

        $this->info("Assigning view_any permissions to role: {$roleName}");

        foreach ($viewAnyPermissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->where('guard_name', 'web')->first();

            if ($permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                    $this->info("Assigned permission: {$permissionName}");
                } else {
                    $this->comment("Permission already assigned: {$permissionName}");
                }
            } else {
                $this->warn("Permission not found: {$permissionName}");
            }
        }

        $this->info('Finished assigning view_any permissions!');

        return 0;
    }
}
