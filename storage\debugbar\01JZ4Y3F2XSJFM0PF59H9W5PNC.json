{"__meta": {"id": "01JZ4Y3F2XSJFM0PF59H9W5PNC", "datetime": "2025-07-02 06:45:03", "utime": **********.710631, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.064623, "end": **********.710652, "duration": 0.646028995513916, "duration_str": "646ms", "measures": [{"label": "Booting", "start": **********.064623, "relative_start": 0, "end": **********.433435, "relative_end": **********.433435, "duration": 0.*****************, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.433448, "relative_start": 0.*****************, "end": **********.710655, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.689938, "relative_start": 0.****************, "end": **********.695493, "relative_end": **********.695493, "duration": 0.005554914474487305, "duration_str": "5.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.706655, "relative_start": 0.****************, "end": **********.707381, "relative_end": **********.707381, "duration": 0.0007259845733642578, "duration_str": "726μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.707409, "relative_start": 0.****************, "end": **********.707452, "relative_end": **********.707452, "duration": 4.315376281738281e-05, "duration_str": "43μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "action_name": null, "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fwamp64%2Fwww%2Fsmms%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "646ms", "peak_memory": "50MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-459527620 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459527620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1370939604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1370939604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1726797966 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/admin/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1251 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6InFvWUtPTXN4VVFYWDI1aVI1NjQwMHc9PSIsInZhbHVlIjoiNE5IQS9TM1d4ZWN5L1Y5RjVwMWErVTlGcHFXMXhPa1FPZERkN3hTNk5EOVBBR0J0STZ3TmMzY0NDZFRFMlhXbmVPamptWDNrQlhKYVhqcmxsa1lMclRwQWZWUE03UVFQdjA0WHppZHVLbHExTklkT25FdzN1Mi9NZ01lTi9ZdDQwQ09SSURNZzErdE1hRGp0Slg3WkkvaGZLV3o5ZzBYZndQLzRnZ0ZWMStPS04wbnFZajJlRWhQYnNLNEFIWUxkd1dOeU9QaUtUd1dvbWNTQnBrcGdVa3FDYTM5S2MvZzgyNXFYdmk1c01TRT0iLCJtYWMiOiIzNzMyMzU0Zjg1MTc4ZjFlYTc3OTQ1MjVlMDQzOWNiNzYyNjIwNTg3MDM2YjE2YjdlNzM2NDc3YzA0NWJlOGExIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImwzM3lHSUVlSzRpVHcxRHlRakdHUHc9PSIsInZhbHVlIjoiNXl0QTRjZDhhRDJtQzFWU3UrbWpoQkJqSGoyTCtod2ZyYytpSXRMQ1JEQ3plTHY1SmpVZXVPUEw5Y0FGNkd4NmNNZEowTWhPZlZEclBpWWoyVkZjRVE2VmJMZnhyeHMxSkRVZHNZRklMWVVRS2V5bm12NVdVdXBNUkM4OVhYOTUiLCJtYWMiOiJjMWZiOWQ0NzhiYTUyMzA5MTQyODU1MTBiNWM4NzNkNGQ4ZmVmZWRjMzczMWM2MmU1NjllMWY2ZjQ3NDIwYzA4IiwidGFnIjoiIn0%3D; kit_session=eyJpdiI6IkQyTXhVcFhYQTk5M2ZLZXdiSmlVeHc9PSIsInZhbHVlIjoiT09pbTk1L2lkcC9tVFFFMkxDTmhET284WWdPaGF0QVUyN0doNXB3ZUlLS1N2OGR0KzdsTHZzNU80cXVnK09SQUNTUGUwcytldDRNWGVDWStkMkd6WXpDeFhSYmNNRjBXQW1valBDNzZFNHdGS1VHQkVZYTYxUUNGd3FQN2tkWGMiLCJtYWMiOiIwOTlhYmUwOTBhY2EzODQxNzIwMjFhOGY2NDAzMjRhOGUxYWM1YTM1NTIxMmJiODFmNjUxZDA5MDg1MTIyNjg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726797966\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342208741 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6InFvWUtPTXN4VVFYWDI1aVI1NjQwMHc9PSIsInZhbHVlIjoiNE5IQS9TM1d4ZWN5L1Y5RjVwMWErVTlGcHFXMXhPa1FPZERkN3hTNk5EOVBBR0J0STZ3TmMzY0NDZFRFMlhXbmVPamptWDNrQlhKYVhqcmxsa1lMclRwQWZWUE03UVFQdjA0WHppZHVLbHExTklkT25FdzN1Mi9NZ01lTi9ZdDQwQ09SSURNZzErdE1hRGp0Slg3WkkvaGZLV3o5ZzBYZndQLzRnZ0ZWMStPS04wbnFZajJlRWhQYnNLNEFIWUxkd1dOeU9QaUtUd1dvbWNTQnBrcGdVa3FDYTM5S2MvZzgyNXFYdmk1c01TRT0iLCJtYWMiOiIzNzMyMzU0Zjg1MTc4ZjFlYTc3OTQ1MjVlMDQzOWNiNzYyNjIwNTg3MDM2YjE2YjdlNzM2NDc3YzA0NWJlOGExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImwzM3lHSUVlSzRpVHcxRHlRakdHUHc9PSIsInZhbHVlIjoiNXl0QTRjZDhhRDJtQzFWU3UrbWpoQkJqSGoyTCtod2ZyYytpSXRMQ1JEQ3plTHY1SmpVZXVPUEw5Y0FGNkd4NmNNZEowTWhPZlZEclBpWWoyVkZjRVE2VmJMZnhyeHMxSkRVZHNZRklMWVVRS2V5bm12NVdVdXBNUkM4OVhYOTUiLCJtYWMiOiJjMWZiOWQ0NzhiYTUyMzA5MTQyODU1MTBiNWM4NzNkNGQ4ZmVmZWRjMzczMWM2MmU1NjllMWY2ZjQ3NDIwYzA4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkQyTXhVcFhYQTk5M2ZLZXdiSmlVeHc9PSIsInZhbHVlIjoiT09pbTk1L2lkcC9tVFFFMkxDTmhET284WWdPaGF0QVUyN0doNXB3ZUlLS1N2OGR0KzdsTHZzNU80cXVnK09SQUNTUGUwcytldDRNWGVDWStkMkd6WXpDeFhSYmNNRjBXQW1valBDNzZFNHdGS1VHQkVZYTYxUUNGd3FQN2tkWGMiLCJtYWMiOiIwOTlhYmUwOTBhY2EzODQxNzIwMjFhOGY2NDAzMjRhOGUxYWM1YTM1NTIxMmJiODFmNjUxZDA5MDg1MTIyNjg0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342208741\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2137684011 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 02 Jul 2026 06:45:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 09:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 06:45:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137684011\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1833254370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1833254370\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/livewire/livewire.js?id=df3a17f2", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}