Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF4710) msys-2.0.dll+0x1FE8E
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210286019, 0007FFFF56C8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF5810  000210068E24 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF5AF0  00021006A225 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD47630000 ntdll.dll
7FFD471D0000 KERNEL32.DLL
7FFD44910000 KERNELBASE.dll
7FFD45B20000 USER32.dll
7FFD45290000 win32u.dll
7FFD45430000 GDI32.dll
7FFD447E0000 gdi32full.dll
7FFD451F0000 msvcp_win.dll
7FFD45050000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD460F0000 advapi32.dll
7FFD47490000 msvcrt.dll
7FFD45A70000 sechost.dll
7FFD44E30000 bcrypt.dll
7FFD45CE0000 RPCRT4.dll
7FFD43DE0000 CRYPTBASE.DLL
7FFD45170000 bcryptPrimitives.dll
7FFD472A0000 IMM32.DLL
